<?php
/**
 * Test Dependencies
 * Quick test to verify Composer dependencies are working
 */

header('Content-Type: text/plain');

echo "Testing Composer Dependencies...\n";
echo "================================\n\n";

// Include autoloader
if (file_exists('vendor/autoload.php')) {
    require_once 'vendor/autoload.php';
    echo "✅ Autoloader found and included\n";
} else {
    echo "❌ Autoloader not found\n";
    exit(1);
}

// Test PHPMailer
try {
    $mailer = new PHPMailer\PHPMailer\PHPMailer();
    echo "✅ PHPMailer class loaded successfully\n";
    echo "   Version: " . $mailer::VERSION . "\n";
} catch (Exception $e) {
    echo "❌ PHPMailer failed: " . $e->getMessage() . "\n";
}

// Test Google2FA
try {
    $google2fa = new PragmaRX\Google2FA\Google2FA();
    echo "✅ Google2FA class loaded successfully\n";
    
    // Test secret generation
    $secret = $google2fa->generateSecretKey();
    echo "   Test secret generated: " . substr($secret, 0, 10) . "...\n";
} catch (Exception $e) {
    echo "❌ Google2FA failed: " . $e->getMessage() . "\n";
}

echo "\n🎉 Dependency test completed!\n";
?>
