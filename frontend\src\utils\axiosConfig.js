import axios from 'axios';

// Set default config for all axios requests
axios.defaults.withCredentials = true;  // Enable credentials for all requests
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Add request interceptor to add auth token and debug information
axios.interceptors.request.use(
    (config) => {
        // Log detailed request information for debugging
        console.log('Request URL:', config.url);
        console.log('Request Method:', config.method);
        console.log('Request Base URL:', config.baseURL);
        console.log('Full Request URL:', config.baseURL ? config.baseURL + config.url : config.url);

        // Fix for admin login path issue - ensure no duplicate 'handlers' in path
        if (config.url && config.url.includes('/handlers/handlers/')) {
            config.url = config.url.replace('/handlers/handlers/', '/handlers/');
            console.log('Fixed duplicate handlers in URL path:', config.url);
        }

        // Don't add token if Authorization header is already set
        if (!config.headers.Authorization) {
            const token = localStorage.getItem('userToken');
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
                console.log('Added auth token to request:', config.url);
            } else {
                console.log('No auth token found for request:', config.url);
            }
        } else {
            console.log('Using custom Authorization header:', config.url);
        }
        return config;
    },
    (error) => {
        console.error('Request interceptor error:', error);
        return Promise.reject(error);
    }
);

// Add response interceptor to handle auth errors
axios.interceptors.response.use(
    (response) => response,
    (error) => {
        console.error('Response error:', error.response?.status, error.response?.data);
  if (error.response?.status === 401 && !error.config.url.includes('join_league.php')) {
      console.log('Unauthorized access');
      
      // Check if admin is logged in
      if (localStorage.getItem('adminId')) {
          console.log('Admin session expired, redirecting to admin login');
          localStorage.removeItem('adminId');
          localStorage.removeItem('adminUsername');
          localStorage.removeItem('adminRole');
          window.location.href = '/admin/login';
      } else {
          console.log('User session expired, redirecting to user login');
          localStorage.removeItem('userToken');
          localStorage.removeItem('userId');
          window.location.href = '/login';
      }
  }
        return Promise.reject(error);
    }
);

export default axios;
