<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Total Users
    $stmt = $conn->query("SELECT COUNT(*) as totalUsers FROM users");
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['totalUsers'];

    // Total Bets
    $stmt = $conn->query("SELECT COUNT(*) as totalBets FROM bets");
    $totalBets = $stmt->fetch(PDO::FETCH_ASSOC)['totalBets'];

    // Winning Bets
    $stmt = $conn->query("SELECT COUNT(*) as winningBets FROM bets WHERE outcome = 'win'");
    $winningBets = $stmt->fetch(PDO::FETCH_ASSOC)['winningBets'];

    // Active Challenges
    $stmt = $conn->query("SELECT COUNT(*) as activeChallenges FROM challenges WHERE status = 'Open'");
    $activeChallenges = $stmt->fetch(PDO::FETCH_ASSOC)['activeChallenges'];

    // Enhanced Recent Activity Query
    $stmt = $conn->query("
        SELECT 
            u.user_id,
            u.username,
            u.balance,
            COUNT(DISTINCT b.bet_id) as totalBets,
            MAX(b.created_at) as lastActivity,
            (SELECT c.team_a FROM challenges c 
             JOIN bets b2 ON c.challenge_id = b2.challenge_id 
             WHERE b2.user1_id = u.user_id 
             ORDER BY b2.created_at DESC LIMIT 1) as latest_challenge_team_a,
            (SELECT c.team_b FROM challenges c 
             JOIN bets b2 ON c.challenge_id = b2.challenge_id 
             WHERE b2.user1_id = u.user_id 
             ORDER BY b2.created_at DESC LIMIT 1) as latest_challenge_team_b,
            MAX(b.potential_return_win_user1) as latest_potential_return
        FROM users u
        LEFT JOIN bets b ON u.user_id = b.user1_id OR u.user_id = b.user2_id
        GROUP BY u.user_id, u.username, u.balance
        ORDER BY lastActivity DESC
        LIMIT 5
    ");
    $recentActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Recent Challenges Query
$stmt = $conn->query("
SELECT 
    c.*,
    COUNT(b.bet_id) as total_bets
FROM challenges c
LEFT JOIN bets b ON c.challenge_id = b.challenge_id
WHERE c.status = 'Open'
GROUP BY c.challenge_id
ORDER BY c.challenge_date DESC
LIMIT 6
");
$recentChallenges = $stmt->fetchAll(PDO::FETCH_ASSOC);


    // Enhanced Recent Bets Query
    $stmt = $conn->query("
        SELECT 
            b.bet_id,
            u1.username as user1_name,
            u2.username as user2_name,
            b.amount_user1,
            b.amount_user2,
            b.potential_return_win_user1,
            b.potential_return_win_user2,
            c.team_a,
            c.team_b,
            b.created_at,
            b.bet_status
        FROM bets b
        JOIN users u1 ON b.user1_id = u1.user_id
        LEFT JOIN users u2 ON b.user2_id = u2.user_id
        JOIN challenges c ON b.challenge_id = c.challenge_id
        ORDER BY b.created_at DESC
        LIMIT 5
    ");
    $recentBets = $stmt->fetchAll(PDO::FETCH_ASSOC);
      $stmt = $conn->query("
          SELECT 
              teams.*,
              (SELECT COUNT(*) FROM users WHERE users.favorite_team = teams.name) as user_count
          FROM teams
          ORDER BY user_count DESC
          LIMIT 6
      ");
      $teamStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

      $dashboardData = [
          'success' => true,
          'totalUsers' => (int)$totalUsers,
          'totalBets' => (int)$totalBets,
          'activeChallenges' => (int)$activeChallenges,
          'totalWins' => number_format($winningBets * 100, 2),
          'recentActivity' => $recentActivity,
          'recentBets' => $recentBets,
          'teamStats' => $teamStats
      ];

      echo json_encode($dashboardData);
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
?>