/**
 * Currency utility functions for FanBet247 frontend
 * Handles currency conversion, formatting, and API interactions
 */

import axios from 'axios';
import { API_BASE_URL } from '../config';

/**
 * Fetch all available currencies
 * @param {boolean} activeOnly - Whether to fetch only active currencies
 * @returns {Promise<Array>} Array of currency objects
 */
export const fetchCurrencies = async (activeOnly = true) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php?active_only=${activeOnly}`);
        if (response.data.success) {
            return response.data.data.currencies || [];
        } else {
            throw new Error(response.data.message || 'Failed to fetch currencies');
        }
    } catch (error) {
        console.error('Error fetching currencies:', error);
        throw error;
    }
};

/**
 * Fetch exchange rates for all currencies
 * @returns {Promise<Array>} Array of exchange rate objects
 */
export const fetchExchangeRates = async () => {
    try {
        const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);
        if (response.data.success) {
            return response.data.data.exchange_rates || [];
        } else {
            throw new Error(response.data.message || 'Failed to fetch exchange rates');
        }
    } catch (error) {
        console.error('Error fetching exchange rates:', error);
        throw error;
    }
};

/**
 * Convert FanCoin amount to specified currency
 * @param {number} fanCoinAmount - Amount in FanCoin
 * @param {number} currencyId - Target currency ID
 * @returns {Promise<Object>} Conversion result
 */
export const convertCurrency = async (fanCoinAmount, currencyId) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/handlers/convert_currency.php`, {
            amount: fanCoinAmount,
            currency_id: currencyId
        });
        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.message || 'Currency conversion failed');
        }
    } catch (error) {
        console.error('Error converting currency:', error);
        throw error;
    }
};

/**
 * Get user's currency preference
 * @param {number} userId - User ID
 * @returns {Promise<Object>} User's currency preference
 */
export const getUserCurrencyPreference = async (userId) => {
    try {
        const response = await axios.get(`${API_BASE_URL}/handlers/get_user_currency_preference.php?user_id=${userId}`);
        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.message || 'Failed to get user currency preference');
        }
    } catch (error) {
        console.error('Error getting user currency preference:', error);
        throw error;
    }
};

/**
 * Update user's currency preference
 * @param {number} userId - User ID
 * @param {number} currencyId - New currency ID
 * @returns {Promise<Object>} Update result
 */
export const updateUserCurrencyPreference = async (userId, currencyId) => {
    try {
        const response = await axios.post(`${API_BASE_URL}/handlers/update_user_currency_preference.php`, {
            user_id: userId,
            currency_id: currencyId
        });
        if (response.data.success) {
            return response.data.data;
        } else {
            throw new Error(response.data.message || 'Failed to update currency preference');
        }
    } catch (error) {
        console.error('Error updating currency preference:', error);
        throw error;
    }
};

/**
 * Format amount with currency symbol
 * @param {number} amount - Amount to format
 * @param {string} currencySymbol - Currency symbol
 * @param {number} decimals - Number of decimal places
 * @returns {string} Formatted amount
 */
export const formatCurrencyAmount = (amount, currencySymbol = '$', decimals = 2) => {
    if (amount === null || amount === undefined || isNaN(amount)) {
        return `${currencySymbol}0.00`;
    }
    
    return `${currencySymbol}${Number(amount).toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    })}`;
};

/**
 * Format FanCoin amount with user's preferred currency
 * @param {number} fanCoinAmount - Amount in FanCoin
 * @param {Object} userCurrency - User's currency preference object
 * @param {boolean} showBoth - Whether to show both FanCoin and converted amount
 * @returns {string} Formatted amount string
 */
export const formatAmountForUser = (fanCoinAmount, userCurrency, showBoth = true) => {
    if (!userCurrency || !userCurrency.rate_to_fancoin) {
        return `${fanCoinAmount} FanCoin`;
    }
    
    const convertedAmount = fanCoinAmount * userCurrency.rate_to_fancoin;
    const formattedConverted = formatCurrencyAmount(convertedAmount, userCurrency.currency_symbol);
    
    if (showBoth && userCurrency.currency_code !== 'USD') {
        return `${formattedConverted} (${fanCoinAmount} FanCoin)`;
    } else {
        return formattedConverted;
    }
};

/**
 * Get currency by code from currencies array
 * @param {Array} currencies - Array of currency objects
 * @param {string} currencyCode - Currency code to find
 * @returns {Object|null} Currency object or null if not found
 */
export const getCurrencyByCode = (currencies, currencyCode) => {
    return currencies.find(currency => currency.currency_code === currencyCode) || null;
};

/**
 * Get currency by ID from currencies array
 * @param {Array} currencies - Array of currency objects
 * @param {number} currencyId - Currency ID to find
 * @returns {Object|null} Currency object or null if not found
 */
export const getCurrencyById = (currencies, currencyId) => {
    return currencies.find(currency => currency.id === currencyId) || null;
};

/**
 * Validate currency selection
 * @param {number} currencyId - Currency ID to validate
 * @param {Array} availableCurrencies - Array of available currencies
 * @returns {boolean} True if valid, false otherwise
 */
export const isValidCurrencySelection = (currencyId, availableCurrencies) => {
    if (!currencyId || !availableCurrencies || !Array.isArray(availableCurrencies)) {
        return false;
    }
    
    return availableCurrencies.some(currency => 
        currency.id === currencyId && currency.is_active
    );
};

/**
 * Get default currency (USD)
 * @param {Array} currencies - Array of currency objects
 * @returns {Object|null} USD currency object or null if not found
 */
export const getDefaultCurrency = (currencies) => {
    return getCurrencyByCode(currencies, 'USD');
};

/**
 * Create sample conversion examples
 * @param {Object} currency - Currency object with rate_to_fancoin
 * @param {Array} amounts - Array of FanCoin amounts to convert
 * @returns {Array} Array of conversion examples
 */
export const createConversionExamples = (currency, amounts = [10, 50, 100, 500]) => {
    if (!currency || !currency.rate_to_fancoin) {
        return [];
    }
    
    return amounts.map(amount => ({
        fanCoinAmount: amount,
        convertedAmount: amount * currency.rate_to_fancoin,
        formattedAmount: formatCurrencyAmount(
            amount * currency.rate_to_fancoin, 
            currency.currency_symbol
        ),
        conversionText: `${amount} FanCoin = ${formatCurrencyAmount(
            amount * currency.rate_to_fancoin, 
            currency.currency_symbol
        )}`
    }));
};

export default {
    fetchCurrencies,
    fetchExchangeRates,
    convertCurrency,
    getUserCurrencyPreference,
    updateUserCurrencyPreference,
    formatCurrencyAmount,
    formatAmountForUser,
    getCurrencyByCode,
    getCurrencyById,
    isValidCurrencySelection,
    getDefaultCurrency,
    createConversionExamples
};
