{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserRegistration.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction UserRegistration() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    preferred_currency_id: 1 // Default to USD\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  // Currency system integration\n  const {\n    currencies,\n    loading: currenciesLoading,\n    error: currencyError\n  } = useCurrency();\n  const {\n    execute\n  } = useApiService();\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n\n  // Handle currency error from context\n  useEffect(() => {\n    if (currencyError) {\n      setError('Failed to load currencies. Please refresh the page.');\n    }\n  }, [currencyError]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/user_registration.php`, newUser);\n      if (response.data.success) {\n        const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n        setSuccess(`Registration successful! Your preferred currency is set to ${(selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.display_name) || 'USD'}. Redirecting to login...`);\n        setTimeout(() => navigate('/login'), 3000);\n      } else {\n        setError(response.data.message || 'Registration failed');\n      }\n    } catch (err) {\n      var _err$response, _err$response$data;\n      console.error('Registration error:', err);\n      if ((_err$response = err.response) !== null && _err$response !== void 0 && (_err$response$data = _err$response.data) !== null && _err$response$data !== void 0 && _err$response$data.message) {\n        setError(err.response.data.message);\n      } else {\n        setError('An error occurred during registration');\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Create Account\",\n    subtitle: \"Join the FanBet247 community today\",\n    variant: \"registration\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"username\",\n            type: \"text\",\n            name: \"username\",\n            value: newUser.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a unique username\",\n            required: true,\n            minLength: \"3\",\n            maxLength: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"full_name\",\n          children: \"Full Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"full_name\",\n            type: \"text\",\n            name: \"full_name\",\n            value: newUser.full_name,\n            onChange: handleInputChange,\n            placeholder: \"Enter your full name\",\n            required: true,\n            minLength: \"2\",\n            maxLength: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-id-card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"email\",\n            type: \"email\",\n            name: \"email\",\n            value: newUser.email,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email address\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-envelope\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"password\",\n            type: \"password\",\n            name: \"password\",\n            value: newUser.password,\n            onChange: handleInputChange,\n            placeholder: \"Create a strong password\",\n            required: true,\n            minLength: \"6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: \"Password must be at least 6 characters long\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"favorite_team\",\n          children: \"Favorite Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"favorite_team\",\n            name: \"favorite_team\",\n            value: newUser.favorite_team,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select your favorite team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 33\n            }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: team.name,\n              children: team.name\n            }, team.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 37\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-futbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"preferred_currency_id\",\n          children: \"Preferred Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"preferred_currency_id\",\n            name: \"preferred_currency_id\",\n            value: newUser.preferred_currency_id,\n            onChange: handleInputChange,\n            required: true,\n            disabled: currenciesLoading,\n            children: currenciesLoading ? /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Loading currencies...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"USD - US Dollar (Default)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 41\n              }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: currency.id,\n                children: currency.display_name\n              }, currency.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 45\n              }, this))]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-coins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: \"This will be used to display FanCoin amounts in your local currency. You can change this later in your profile settings.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 27\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-success-message\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: loading || currenciesLoading,\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 29\n          }, this), \"Creating Account...\"]\n        }, void 0, true) : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"user-auth-link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 49\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n}\n_s(UserRegistration, \"jtCJRMM+VBnEuT/9rkETwHUrbv4=\", false, function () {\n  return [useNavigate, useCurrency, useApiService];\n});\n_c = UserRegistration;\nexport default UserRegistration;\nvar _c;\n$RefreshReg$(_c, \"UserRegistration\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Link", "useCurrency", "UserAuthLayout", "userService", "currencyService", "useApiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "UserRegistration", "_s", "teams", "setTeams", "newUser", "setNewUser", "username", "full_name", "email", "password", "favorite_team", "preferred_currency_id", "error", "setError", "success", "setSuccess", "loading", "setLoading", "navigate", "currencies", "currenciesLoading", "currencyError", "execute", "fetchTeams", "response", "axios", "get", "data", "err", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "c", "id", "parseInt", "display_name", "setTimeout", "message", "_err$response", "_err$response$data", "console", "title", "subtitle", "variant", "children", "onSubmit", "className", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "map", "team", "disabled", "currency", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserRegistration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction UserRegistration() {\n    const [teams, setTeams] = useState([]);\n    const [newUser, setNewUser] = useState({\n        username: '',\n        full_name: '',\n        email: '',\n        password: '',\n        favorite_team: '',\n        preferred_currency_id: 1 // Default to USD\n    });\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [loading, setLoading] = useState(false);\n    const navigate = useNavigate();\n\n    // Currency system integration\n    const { currencies, loading: currenciesLoading, error: currencyError } = useCurrency();\n    const { execute } = useApiService();\n\n    useEffect(() => {\n        fetchTeams();\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            setTeams(response.data.data || []);\n        } catch (err) {\n            setError('Failed to fetch teams');\n        }\n    };\n\n    // Handle currency error from context\n    useEffect(() => {\n        if (currencyError) {\n            setError('Failed to load currencies. Please refresh the page.');\n        }\n    }, [currencyError]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewUser(prev => ({ ...prev, [name]: value }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n        setLoading(true);\n\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/user_registration.php`, newUser);\n            if (response.data.success) {\n                const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n                setSuccess(\n                    `Registration successful! Your preferred currency is set to ${selectedCurrency?.display_name || 'USD'}. Redirecting to login...`\n                );\n                setTimeout(() => navigate('/login'), 3000);\n            } else {\n                setError(response.data.message || 'Registration failed');\n            }\n        } catch (err) {\n            console.error('Registration error:', err);\n            if (err.response?.data?.message) {\n                setError(err.response.data.message);\n            } else {\n                setError('An error occurred during registration');\n            }\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    return (\n        <UserAuthLayout\n            title=\"Create Account\"\n            subtitle=\"Join the FanBet247 community today\"\n            variant=\"registration\"\n        >\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"username\">Username</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"username\"\n                                type=\"text\"\n                                name=\"username\"\n                                value={newUser.username}\n                                onChange={handleInputChange}\n                                placeholder=\"Choose a unique username\"\n                                required\n                                minLength=\"3\"\n                                maxLength=\"20\"\n                            />\n                            <i className=\"fas fa-user\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"full_name\">Full Name</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"full_name\"\n                                type=\"text\"\n                                name=\"full_name\"\n                                value={newUser.full_name}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your full name\"\n                                required\n                                minLength=\"2\"\n                                maxLength=\"50\"\n                            />\n                            <i className=\"fas fa-id-card\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"email\">Email Address</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"email\"\n                                type=\"email\"\n                                name=\"email\"\n                                value={newUser.email}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your email address\"\n                                required\n                            />\n                            <i className=\"fas fa-envelope\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"password\">Password</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"password\"\n                                type=\"password\"\n                                name=\"password\"\n                                value={newUser.password}\n                                onChange={handleInputChange}\n                                placeholder=\"Create a strong password\"\n                                required\n                                minLength=\"6\"\n                            />\n                            <i className=\"fas fa-lock\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            Password must be at least 6 characters long\n                        </small>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"favorite_team\">Favorite Team</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"favorite_team\"\n                                name=\"favorite_team\"\n                                value={newUser.favorite_team}\n                                onChange={handleInputChange}\n                                required\n                            >\n                                <option value=\"\">Select your favorite team</option>\n                                {teams.map(team => (\n                                    <option key={team.id} value={team.name}>{team.name}</option>\n                                ))}\n                            </select>\n                            <i className=\"fas fa-futbol\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"preferred_currency_id\">Preferred Currency</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"preferred_currency_id\"\n                                name=\"preferred_currency_id\"\n                                value={newUser.preferred_currency_id}\n                                onChange={handleInputChange}\n                                required\n                                disabled={currenciesLoading}\n                            >\n                                {currenciesLoading ? (\n                                    <option value=\"\">Loading currencies...</option>\n                                ) : (\n                                    <>\n                                        <option value=\"1\">USD - US Dollar (Default)</option>\n                                        {currencies.map(currency => (\n                                            <option key={currency.id} value={currency.id}>\n                                                {currency.display_name}\n                                            </option>\n                                        ))}\n                                    </>\n                                )}\n                            </select>\n                            <i className=\"fas fa-coins\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            This will be used to display FanCoin amounts in your local currency.\n                            You can change this later in your profile settings.\n                        </small>\n                    </div>\n\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                {success && <div className=\"user-auth-success-message\">{success}</div>}\n\n                <button\n                    type=\"submit\"\n                    className=\"user-auth-button\"\n                    disabled={loading || currenciesLoading}\n                >\n                    {loading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Creating Account...\n                        </>\n                    ) : (\n                        'Create Account'\n                    )}\n                </button>\n\n                <div className=\"user-auth-footer\">\n                    <p>Already have an account? <Link to=\"/login\" className=\"user-auth-link\">Sign in here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,WAAW,EAAEC,eAAe,QAAQ,aAAa;AAC1D,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC;IACnCoB,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,qBAAqB,EAAE,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAE+B,UAAU;IAAEH,OAAO,EAAEI,iBAAiB;IAAER,KAAK,EAAES;EAAc,CAAC,GAAG/B,WAAW,CAAC,CAAC;EACtF,MAAM;IAAEgC;EAAQ,CAAC,GAAG5B,aAAa,CAAC,CAAC;EAEnCP,SAAS,CAAC,MAAM;IACZoC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACC,GAAG,CAAC,GAAG3B,YAAY,+BAA+B,CAAC;MAChFI,QAAQ,CAACqB,QAAQ,CAACG,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVf,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;;EAED;EACA1B,SAAS,CAAC,MAAM;IACZ,IAAIkC,aAAa,EAAE;MACfR,QAAQ,CAAC,qDAAqD,CAAC;IACnE;EACJ,CAAC,EAAE,CAACQ,aAAa,CAAC,CAAC;EAEnB,MAAMQ,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC5B,UAAU,CAAC6B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBvB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAACY,IAAI,CAAC,GAAGtC,YAAY,iCAAiC,EAAEK,OAAO,CAAC;MAC5F,IAAIoB,QAAQ,CAACG,IAAI,CAACb,OAAO,EAAE;QACvB,MAAMwB,gBAAgB,GAAGnB,UAAU,CAACoB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACtC,OAAO,CAACO,qBAAqB,CAAC,CAAC;QAC/FI,UAAU,CACN,8DAA8D,CAAAuB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,YAAY,KAAI,KAAK,2BACzG,CAAC;QACDC,UAAU,CAAC,MAAM1B,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,MAAM;QACHL,QAAQ,CAACW,QAAQ,CAACG,IAAI,CAACkB,OAAO,IAAI,qBAAqB,CAAC;MAC5D;IACJ,CAAC,CAAC,OAAOjB,GAAG,EAAE;MAAA,IAAAkB,aAAA,EAAAC,kBAAA;MACVC,OAAO,CAACpC,KAAK,CAAC,qBAAqB,EAAEgB,GAAG,CAAC;MACzC,KAAAkB,aAAA,GAAIlB,GAAG,CAACJ,QAAQ,cAAAsB,aAAA,gBAAAC,kBAAA,GAAZD,aAAA,CAAcnB,IAAI,cAAAoB,kBAAA,eAAlBA,kBAAA,CAAoBF,OAAO,EAAE;QAC7BhC,QAAQ,CAACe,GAAG,CAACJ,QAAQ,CAACG,IAAI,CAACkB,OAAO,CAAC;MACvC,CAAC,MAAM;QACHhC,QAAQ,CAAC,uCAAuC,CAAC;MACrD;IACJ,CAAC,SAAS;MACNI,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,oBACIrB,OAAA,CAACL,cAAc;IACX0D,KAAK,EAAC,gBAAgB;IACtBC,QAAQ,EAAC,oCAAoC;IAC7CC,OAAO,EAAC,cAAc;IAAAC,QAAA,eAEtBxD,OAAA;MAAMyD,QAAQ,EAAElB,YAAa;MAACmB,SAAS,EAAC,gBAAgB;MAAAF,QAAA,gBAChDxD,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,UAAU;YACbmB,IAAI,EAAC,MAAM;YACX7B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE5B,OAAO,CAACE,QAAS;YACxBuD,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF/D,OAAA;YAAG0D,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,WAAW;UAAAH,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5C/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,WAAW;YACdmB,IAAI,EAAC,MAAM;YACX7B,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAE5B,OAAO,CAACG,SAAU;YACzBsD,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,sBAAsB;YAClCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF/D,OAAA;YAAG0D,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,OAAO;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5C/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,OAAO;YACVmB,IAAI,EAAC,OAAO;YACZ7B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE5B,OAAO,CAACI,KAAM;YACrBqD,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF/D,OAAA;YAAG0D,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,UAAU;YACbmB,IAAI,EAAC,UAAU;YACf7B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE5B,OAAO,CAACK,QAAS;YACxBoD,QAAQ,EAAEhC,iBAAkB;YAC5BiC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF/D,OAAA;YAAG0D,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN/D,OAAA;UAAO0D,SAAS,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAElC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,eAAe;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,eAAe;YAClBV,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAE5B,OAAO,CAACM,aAAc;YAC7BmD,QAAQ,EAAEhC,iBAAkB;YAC5BkC,QAAQ;YAAAX,QAAA,gBAERxD,OAAA;cAAQoC,KAAK,EAAC,EAAE;cAAAoB,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClDzD,KAAK,CAACgE,GAAG,CAACC,IAAI,iBACXvE,OAAA;cAAsBoC,KAAK,EAAEmC,IAAI,CAACpC,IAAK;cAAAqB,QAAA,EAAEe,IAAI,CAACpC;YAAI,GAArCoC,IAAI,CAAC1B,EAAE;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuC,CAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACT/D,OAAA;YAAG0D,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN/D,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCxD,OAAA;UAAO2D,OAAO,EAAC,uBAAuB;UAAAH,QAAA,EAAC;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE/D,OAAA;UAAK0D,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCxD,OAAA;YACI6C,EAAE,EAAC,uBAAuB;YAC1BV,IAAI,EAAC,uBAAuB;YAC5BC,KAAK,EAAE5B,OAAO,CAACO,qBAAsB;YACrCkD,QAAQ,EAAEhC,iBAAkB;YAC5BkC,QAAQ;YACRK,QAAQ,EAAEhD,iBAAkB;YAAAgC,QAAA,EAE3BhC,iBAAiB,gBACdxB,OAAA;cAAQoC,KAAK,EAAC,EAAE;cAAAoB,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAE/C/D,OAAA,CAAAE,SAAA;cAAAsD,QAAA,gBACIxD,OAAA;gBAAQoC,KAAK,EAAC,GAAG;gBAAAoB,QAAA,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDxC,UAAU,CAAC+C,GAAG,CAACG,QAAQ,iBACpBzE,OAAA;gBAA0BoC,KAAK,EAAEqC,QAAQ,CAAC5B,EAAG;gBAAAW,QAAA,EACxCiB,QAAQ,CAAC1B;cAAY,GADb0B,QAAQ,CAAC5B,EAAE;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACX,CAAC;YAAA,eACJ;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACT/D,OAAA;YAAG0D,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACN/D,OAAA;UAAO0D,SAAS,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAGlC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAET/C,KAAK,iBAAIhB,OAAA;QAAK0D,SAAS,EAAC,yBAAyB;QAAAF,QAAA,EAAExC;MAAK;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC/D7C,OAAO,iBAAIlB,OAAA;QAAK0D,SAAS,EAAC,2BAA2B;QAAAF,QAAA,EAAEtC;MAAO;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtE/D,OAAA;QACIgE,IAAI,EAAC,QAAQ;QACbN,SAAS,EAAC,kBAAkB;QAC5Bc,QAAQ,EAAEpD,OAAO,IAAII,iBAAkB;QAAAgC,QAAA,EAEtCpC,OAAO,gBACJpB,OAAA,CAAAE,SAAA;UAAAsD,QAAA,gBACIxD,OAAA;YAAM0D,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,uBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAET/D,OAAA;QAAK0D,SAAS,EAAC,kBAAkB;QAAAF,QAAA,eAC7BxD,OAAA;UAAAwD,QAAA,GAAG,2BAAyB,eAAAxD,OAAA,CAACP,IAAI;YAACiF,EAAE,EAAC,QAAQ;YAAChB,SAAS,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAAC1D,EAAA,CAnOQD,gBAAgB;EAAA,QAaJZ,WAAW,EAG6CE,WAAW,EAChEI,aAAa;AAAA;AAAA6E,EAAA,GAjB5BvE,gBAAgB;AAqOzB,eAAeA,gBAAgB;AAAC,IAAAuE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}