-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:56 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `loan`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `name`, `email`, `password`, `role_id`, `created_at`) VALUES
(1, 'Admin User', '<EMAIL>', '$2y$10$nhdweCS/Y7gYVzLkMcr2aucbLZrKvdndFyxhWZTfPzsoHMy9CTXzK', 1, '2025-05-12 09:38:03');

-- --------------------------------------------------------

--
-- Table structure for table `admin_notifications`
--

CREATE TABLE `admin_notifications` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT 'info',
  `link` varchar(255) DEFAULT '',
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `read_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `admin_notifications`
--

INSERT INTO `admin_notifications` (`id`, `admin_id`, `title`, `message`, `type`, `link`, `is_read`, `created_at`, `read_at`) VALUES
(1, 1, 'New Loan Application', 'A new loan application has been submitted', 'application', '/admin/application-details.php?id=1', 0, '2025-05-19 21:47:49', NULL),
(2, 1, 'New Support Ticket', 'A new support ticket has been created', 'support', '/admin/support.php?ticket=1', 0, '2025-05-19 21:47:49', NULL),
(3, 1, 'Document Uploaded', 'A user has uploaded a new document', 'document', '/admin/documents.php', 0, '2025-05-19 21:47:49', NULL),
(4, 1, 'Payment Received', 'A payment has been received for loan #123', 'transaction', '/admin/transaction-details.php?id=1', 0, '2025-05-19 21:47:49', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `application_documents`
--

CREATE TABLE `application_documents` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_type` varchar(100) DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL,
  `document_type` varchar(100) DEFAULT NULL,
  `upload_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `requested_by_admin_id` int(11) DEFAULT NULL,
  `status` enum('pending','approved','rejected','admin_upload') DEFAULT NULL,
  `uploaded_by_admin_id` int(11) DEFAULT NULL,
  `document_description` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `application_documents`
--

INSERT INTO `application_documents` (`id`, `application_id`, `user_id`, `file_name`, `file_path`, `file_type`, `file_size`, `document_type`, `upload_date`, `requested_by_admin_id`, `status`, `uploaded_by_admin_id`, `document_description`) VALUES
(1, 1, 1, 'id_proof.pdf', '/uploads/documents/id_proof_1.pdf', 'application/pdf', NULL, 'ID Document', '2025-05-12 09:38:47', 1, 'pending', NULL, NULL),
(2, 1, 1, 'Sample PDF Document', '/uploads/documents/sample_document.pdf', 'application/octet-stream', NULL, NULL, '2025-05-12 18:58:22', NULL, 'approved', NULL, NULL),
(3, 1, 1, 'document', '/uploads/documents/admin_doc_1_1747077078.pdf', 'application/octet-stream', NULL, NULL, '2025-05-12 19:11:18', NULL, 'admin_upload', 1, 'document'),
(19, 21, 3, 'ID Proof.pdf', 'documents/682316cccecb3_ID Proof.pdf', 'application/pdf', NULL, 'ID Document', '2025-05-13 09:54:20', NULL, 'pending', NULL, NULL),
(20, 22, 3, 'Income Proof.pdf', 'documents/682316cccf4a6_Income Proof.pdf', 'application/pdf', NULL, NULL, '2025-05-13 09:54:20', NULL, 'approved', NULL, NULL),
(21, 23, 3, 'Address Proof.pdf', 'documents/682316cccf953_Address Proof.pdf', 'application/pdf', NULL, NULL, '2025-05-13 09:54:20', NULL, 'rejected', NULL, NULL),
(24, 21, 3, 'ID Proof.txt', 'documents/id_proof_1747148608.txt', 'text/plain', NULL, 'ID Proof', '2025-05-13 15:03:28', NULL, 'pending', NULL, NULL),
(27, 22, 3, 'Requested Document', '', NULL, NULL, NULL, '2025-05-13 15:48:54', 1, 'pending', NULL, 'Bank Statement'),
(28, 21, 3, 'income_proof.jpg', 'documents/doc_68259451d79ec.jpg', 'image/jpeg', NULL, 'Income Proof', '2025-05-15 07:14:25', NULL, 'pending', NULL, NULL),
(29, 27, 10, 'id_document_27_1747382814.jpg', 'C:\\MAMP\\htdocs\\pfloans.com/uploads/documents/10/id_document_27_1747382814.jpg', 'image/jpeg', NULL, 'ID Document', '2025-05-16 08:06:54', NULL, 'pending', NULL, NULL),
(30, 28, 10, 'id_document_28_1747408960.jpeg', 'C:\\MAMP\\htdocs\\pfloans.com/uploads/documents/10/id_document_28_1747408960.jpeg', 'image/jpeg', NULL, 'ID Document', '2025-05-16 15:22:40', NULL, 'pending', NULL, NULL),
(31, 29, 10, 'id_document_29_1747421168.png', 'C:\\MAMP\\htdocs\\pfloans.com/uploads/documents/10/id_document_29_1747421168.png', 'image/png', NULL, 'ID Document', '2025-05-16 18:46:08', NULL, 'approved', NULL, NULL),
(32, 29, 10, '0', '/uploads/documents/10/68285d189954c.jpeg', 'image/jpeg', 44025, NULL, '2025-05-17 09:55:36', 1, 'approved', NULL, 'paypal document is needed from you'),
(33, 29, 10, '0', '/uploads/documents/10/682a269c3b91a.pdf', 'application/pdf', 54556, NULL, '2025-05-18 18:27:40', 1, 'approved', NULL, 'We need  your employment verification'),
(34, 29, 10, 'Form Template.pdf', '/uploads/documents/10/682a4b92d9124.pdf', 'application/pdf', 202023, NULL, '2025-05-18 21:05:22', 1, 'approved', NULL, 'We need your bank Statement'),
(35, 29, 10, 'Form Template.pdf', '/uploads/documents/10/682a4b861c3a8.pdf', 'application/pdf', 202023, NULL, '2025-05-18 21:05:10', 1, 'approved', NULL, 'we need income proof'),
(36, 29, 10, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', '/uploads/documents/10/682a4a174f6cc.pdf', 'application/pdf', 299821, NULL, '2025-05-18 20:59:03', 1, 'approved', NULL, ''),
(37, 29, 10, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', '/uploads/documents/10/682a2c349e268.pdf', 'application/pdf', 299821, NULL, '2025-05-18 18:51:32', 1, 'approved', NULL, 'we need 3 months bank statement'),
(38, 29, 10, 'Tax Return', '/uploads/documents/10/682a2a83513bd.pdf', 'application/pdf', 299821, 'Tax Return', '2025-05-18 18:44:19', 1, 'approved', NULL, 'We need Your tax return ceritifcate');

-- --------------------------------------------------------

--
-- Table structure for table `calculator_history`
--

CREATE TABLE `calculator_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `loan_product_id` int(11) DEFAULT NULL,
  `loan_amount` decimal(10,2) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL,
  `term_months` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL DEFAULT '1',
  `monthly_payment` decimal(10,2) NOT NULL,
  `total_interest` decimal(10,2) NOT NULL,
  `total_repayment` decimal(10,2) NOT NULL,
  `calculation_date` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `calculator_history`
--

INSERT INTO `calculator_history` (`id`, `user_id`, `loan_product_id`, `loan_amount`, `interest_rate`, `term_months`, `currency_id`, `monthly_payment`, `total_interest`, `total_repayment`, `calculation_date`, `created_at`, `updated_at`) VALUES
(1, 3, 1, '5000.00', '8.50', 12, 1, '436.00', '233.19', '5233.19', '2025-05-04 09:54:21', '2025-05-13 09:54:21', '2025-05-13 09:54:21'),
(2, 3, 2, '15000.00', '7.50', 24, 1, '674.00', '1199.85', '16199.85', '2025-05-11 09:54:21', '2025-05-13 09:54:21', '2025-05-13 09:54:21'),
(3, 3, NULL, '7500.00', '9.00', 18, 1, '446.00', '545.68', '8045.68', '2025-05-05 09:54:21', '2025-05-13 09:54:21', '2025-05-13 09:54:21'),
(4, 3, 1, '5000.00', '12.50', 12, 1, '445.41', '344.97', '5344.97', '2025-05-15 09:20:12', '2025-05-15 07:20:12', '2025-05-15 07:20:12');

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` int(11) NOT NULL,
  `code` varchar(3) NOT NULL,
  `symbol` varchar(5) NOT NULL,
  `name` varchar(50) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`id`, `code`, `symbol`, `name`) VALUES
(1, 'USD', '$', 'US Dollar'),
(2, 'EUR', '€', 'Euro'),
(3, 'GBP', '£', 'British Pound'),
(11, 'JPY', '¥', 'Japanese Yen');

-- --------------------------------------------------------

--
-- Table structure for table `document_access_logs`
--

CREATE TABLE `document_access_logs` (
  `id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `access_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `document_access_logs`
--

INSERT INTO `document_access_logs` (`id`, `document_id`, `admin_id`, `access_date`) VALUES
(1, 2, 1, '2025-05-12 20:58:26'),
(2, 1, 1, '2025-05-12 21:01:38'),
(3, 2, 1, '2025-05-12 21:01:47'),
(4, 3, 1, '2025-05-12 21:15:31'),
(5, 20, 1, '2025-05-13 17:48:30'),
(6, 31, 1, '2025-05-17 09:26:39'),
(7, 31, 1, '2025-05-17 09:53:40'),
(8, 31, 1, '2025-05-17 09:53:40'),
(9, 31, 1, '2025-05-17 10:25:14'),
(10, 31, 1, '2025-05-17 10:25:14'),
(11, 31, 1, '2025-05-17 10:25:17'),
(12, 31, 1, '2025-05-17 10:25:17');

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `email_templates`
--

INSERT INTO `email_templates` (`id`, `name`, `subject`, `content`, `created_at`, `updated_at`) VALUES
(1, 'Welcome Email', 'Welcome to LendSwift Financial', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border-radius: 5px;\">\n<div style=\"text-align: center; margin-bottom: 20px;\">\n<img src=\"{site_url}/assets/images/logo.svg\" alt=\"{site_name} Logo\" style=\"max-width: 200px;\">\n</div>\n<h2 style=\"color: #4f46e5; margin-bottom: 20px;\">Welcome to {site_name}!</h2>\n<p>Dear {user_name},</p>\n<p>Thank you for joining {site_name}. We\'re excited to have you on board!</p>\n<p>Your account has been created successfully. You can now log in to your account and apply for loans.</p>\n<p>If you have any questions, please don\'t hesitate to contact us.</p>\n<p>Best regards,<br>The {site_name} Team</p>\n<div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;\">\n<p>&copy; {current_year} {site_name}. All rights reserved.</p>\n</div>\n</div>', '2025-05-12 13:24:55', '2025-05-14 07:33:59'),
(2, 'Password Reset', 'Password Reset Request', '<p>Dear {user_name},</p><p>We received a request to reset your password. Click the link below to reset your password:</p><p><a href=\"{reset_link}\">Reset Password</a></p><p>If you didn\'t request a password reset, please ignore this email.</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(3, 'Loan Application Received', 'Loan Application Received', '<p>Dear {user_name},</p><p>We have received your loan application for {loan_amount}. Our team will review your application and get back to you shortly.</p><p>Your application reference number is: {loan_reference}</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(4, 'Loan Approved', 'Loan Application Approved', '<p>Dear {user_name},</p><p>Congratulations! Your loan application for {loan_amount} has been approved.</p><p>The funds will be disbursed to your account within the next 24-48 hours.</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(5, 'Loan Rejected', 'Loan Application Status Update', '<p>Dear {user_name},</p><p>We regret to inform you that your loan application for {loan_amount} has been rejected.</p><p>If you have any questions, please contact our customer support team.</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(6, 'Payment Reminder', 'Payment Reminder', '<p>Dear {user_name},</p><p>This is a reminder that your loan payment of {payment_amount} is due on {payment_date}.</p><p>Please ensure that your account has sufficient funds for the automatic debit.</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(7, 'Payment Received', 'Payment Received', '<p>Dear {user_name},</p><p>We have received your payment of {payment_amount} for your loan.</p><p>Thank you for your prompt payment.</p><p>Best regards,<br>The LendSwift Team</p>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(8, 'Welcome Email', 'Welcome to {site_name}', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border-radius: 5px;\">\n<div style=\"text-align: center; margin-bottom: 20px;\">\n<img src=\"{site_url}/assets/images/logo.svg\" alt=\"{site_name} Logo\" style=\"max-width: 200px;\">\n</div>\n<h2 style=\"color: #4f46e5; margin-bottom: 20px;\">Welcome to {site_name}!</h2>\n<p>Dear {user_name},</p>\n<p>Thank you for joining {site_name}. We\'re excited to have you on board!</p>\n<p>Your account has been created successfully. You can now log in to your account and apply for loans.</p>\n<p>If you have any questions, please don\'t hesitate to contact us.</p>\n<p>Best regards,<br>The {site_name} Team</p>\n<div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;\">\n<p>&copy; {current_year} {site_name}. All rights reserved.</p>\n</div>\n</div>', '2025-05-14 07:24:34', '2025-05-14 07:33:59'),
(9, 'Account Status Change', 'Your {site_name} Account Status Update', '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border-radius: 5px;\">\n<div style=\"text-align: center; margin-bottom: 20px;\">\n<img src=\"{site_url}/assets/images/logo.svg\" alt=\"{site_name} Logo\" style=\"max-width: 200px;\">\n</div>\n<h2 style=\"color: #4f46e5; margin-bottom: 20px;\">Account Status Update</h2>\n<p>Dear {user_name},</p>\n<p>We are writing to inform you that your account status has been updated to <strong>{status}</strong>.</p>\n<div style=\"margin-top: 20px; margin-bottom: 20px; padding: 15px; background-color: #f9f9f9; border-radius: 5px;\">\n<h3 style=\"color: #4f46e5; margin-top: 0;\">Account Details</h3>\n<table style=\"width: 100%; border-collapse: collapse;\">\n<tr>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee; width: 40%;\"><strong>Name:</strong></td>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">{user_name}</td>\n</tr>\n<tr>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Email:</strong></td>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">{user_email}</td>\n</tr>\n<tr>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\"><strong>Status:</strong></td>\n<td style=\"padding: 8px 0; border-bottom: 1px solid #eee;\">{status}</td>\n</tr>\n</table>\n</div>\n<p>If you have any questions or concerns regarding this change, please contact our support team.</p>\n<p>Best regards,<br>The {site_name} Team</p>\n<div style=\"margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666; text-align: center;\">\n<p>&copy; {current_year} {site_name}. All rights reserved.</p>\n</div>\n</div>', '2025-05-14 07:49:43', '2025-05-14 07:49:43');

-- --------------------------------------------------------

--
-- Table structure for table `forms`
--

CREATE TABLE `forms` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_default` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `forms`
--

INSERT INTO `forms` (`id`, `name`, `description`, `is_active`, `created_at`, `updated_at`, `is_default`) VALUES
(1, 'Loan Application', 'Default loan application form', 1, '2025-05-12 13:24:56', '2025-05-14 10:20:53', 1),
(2, 'loan', 'text', 1, '2025-05-12 19:16:19', '2025-05-12 19:16:19', 0);

-- --------------------------------------------------------

--
-- Table structure for table `form_data`
--

CREATE TABLE `form_data` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `field_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `form_data`
--

INSERT INTO `form_data` (`id`, `application_id`, `field_id`, `user_id`, `value`, `created_at`, `updated_at`) VALUES
(1, 24, 1, 3, 'Test Data', '2025-05-15 17:45:21', '2025-05-15 17:45:21'),
(2, 25, 1, 3, 'Test Data', '2025-05-15 19:59:14', '2025-05-15 19:59:14'),
(3, 27, 8, 10, 'uploads/documents/10/id_document_27_1747382814.jpg', '2025-05-16 08:06:54', '2025-05-18 18:34:49'),
(4, 27, 1, 10, 'James spark', '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(5, 27, 4, 10, '50000', '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(6, 27, 6, 10, 'Employed', '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(7, 27, 7, 10, '4775757', '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(8, 27, 10, 10, 'No, this is my first loan', '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(9, 28, 8, 10, 'uploads/documents/10/id_document_28_1747408960.jpeg', '2025-05-16 15:22:40', '2025-05-18 18:34:49'),
(10, 28, 1, 10, 'Henry Stark', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(11, 28, 4, 10, '10000', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(12, 28, 6, 10, 'Self-Employed', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(13, 28, 7, 10, '50000', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(14, 28, 9, 10, 'i need this loan ', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(15, 28, 10, 10, 'No, this is my first loan', '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(16, 29, 8, 10, 'uploads/documents/10/id_document_29_1747421168.png', '2025-05-16 18:46:08', '2025-05-18 18:34:49'),
(17, 29, 1, 10, 'Henry Spark', '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(18, 29, 4, 10, '100000', '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(19, 29, 6, 10, 'Unemployed', '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(20, 29, 7, 10, '50000', '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(21, 29, 9, 10, 'i need this loan ', '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(22, 29, 10, 10, 'Yes, I have previous loans', '2025-05-16 18:46:08', '2025-05-16 18:46:08');

-- --------------------------------------------------------

--
-- Table structure for table `form_fields`
--

CREATE TABLE `form_fields` (
  `id` int(11) NOT NULL,
  `form_id` int(11) NOT NULL,
  `label` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `placeholder` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `help_text` text COLLATE utf8mb4_unicode_ci,
  `options` text COLLATE utf8mb4_unicode_ci,
  `is_required` tinyint(1) NOT NULL DEFAULT '0',
  `display_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `form_fields`
--

INSERT INTO `form_fields` (`id`, `form_id`, `label`, `name`, `type`, `placeholder`, `help_text`, `options`, `is_required`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 1, 'Full Name', 'full_name', 'text', 'Enter your full name', 'Your legal name as it appears on your ID', '', 1, 0, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(4, 1, 'Loan Amount', 'loan_amount', 'number', 'Enter the loan amount', 'How much would you like to borrow?', '', 1, 3, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(5, 1, 'Loan Purpose', 'loan_purpose', 'select', 'Select loan purpose', 'What will you use the loan for?', 'Home Improvement\nEducation\nDebt Consolidation\nBusiness\nMedical Expenses\nVacation\nOther', 1, 4, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(6, 1, 'Employment Status', 'employment_status', 'select', 'Select your employment status', '', 'Employed\nSelf-Employed\nUnemployed\nRetired\nStudent', 1, 5, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(7, 1, 'Monthly Income', 'monthly_income', 'number', 'Enter your monthly income', 'Your gross monthly income before taxes', '', 1, 6, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(8, 1, 'ID Document', 'id_document', 'file', '', 'Upload a copy of your ID (passport, driver\'s license, etc.)', '', 1, 7, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(9, 1, 'Additional Comments', 'comments', 'textarea', 'Enter any additional information', 'Any other information you would like us to know', '', 0, 8, '2025-05-12 13:24:56', '2025-05-12 13:24:56'),
(10, 1, 'Previous Loans', 'previous_loans', 'select', '', 'Please indicate if you have taken out loans before', 'Yes, I have previous loans\r\nNo, this is my first loan', 1, 9, '2025-05-14 10:22:06', '2025-05-14 18:34:07');

-- --------------------------------------------------------

--
-- Table structure for table `loan_applications`
--

CREATE TABLE `loan_applications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `loan_product_id` int(11) DEFAULT NULL,
  `applied_amount` decimal(15,2) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `purpose` text,
  `submission_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status_id` int(11) NOT NULL,
  `admin_notes` text,
  `release_date` date DEFAULT NULL,
  `total_payable_amount` decimal(15,2) DEFAULT NULL,
  `form_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `loan_applications`
--

INSERT INTO `loan_applications` (`id`, `user_id`, `loan_product_id`, `applied_amount`, `currency_id`, `purpose`, `submission_date`, `status_id`, `admin_notes`, `release_date`, `total_payable_amount`, `form_id`) VALUES
(1, 1, 1, '5000.00', 1, 'Home renovation project', '2025-05-12 09:38:30', 1, NULL, NULL, NULL, 1),
(2, 2, 1, '5000.00', 1, 'Home renovation', '2025-05-12 10:49:00', 1, NULL, NULL, NULL, 1),
(21, 3, 1, '5000.00', 1, 'Personal expenses', '2025-05-08 07:54:20', 28, NULL, NULL, NULL, 1),
(22, 3, 2, '15000.00', 1, 'Business expansion', '2025-05-03 07:54:20', 3, NULL, NULL, NULL, 1),
(23, 3, 18, '8000.00', 1, 'Tuition fees', '2025-04-28 07:54:20', 29, NULL, NULL, NULL, 1),
(24, 3, 1, '5000.00', 2, 'Test Purpose', '2025-05-15 15:45:21', 1, NULL, NULL, NULL, 1),
(25, 3, 1, '5000.00', 2, 'Test Purpose', '2025-05-15 17:59:14', 30, 'Loan has been approved and completed', NULL, NULL, 1),
(27, 10, 2, '50000.00', 2, 'Home Improvement', '2025-05-16 08:06:54', 1, NULL, NULL, NULL, 1),
(28, 10, 20, '10000.00', 2, 'Home Improvement', '2025-05-16 15:22:40', 1, NULL, NULL, NULL, 1),
(29, 10, 20, '100000.00', 11, 'Home Improvement', '2025-05-16 18:46:08', 3, 'Loan is Has been approved succesfully', '2025-05-19', NULL, 1);

-- --------------------------------------------------------

--
-- Table structure for table `loan_products`
--

CREATE TABLE `loan_products` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `min_amount` decimal(15,2) NOT NULL,
  `max_amount` decimal(15,2) NOT NULL,
  `interest_rate` decimal(5,2) NOT NULL,
  `term_months` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `loan_products`
--

INSERT INTO `loan_products` (`id`, `name`, `min_amount`, `max_amount`, `interest_rate`, `term_months`) VALUES
(1, 'Personal Loan', '1000.00', '10000.00', '12.50', 12),
(2, 'Business Loan', '5000.00', '50000.00', '10.75', 24),
(3, 'Quick Cash', '500.00', '3000.00', '15.00', 6),
(18, 'Education Loan', '2000.00', '20000.00', '6.00', 36),
(19, 'Home Improvement Loan', '3000.00', '30000.00', '7.00', 24),
(20, 'Credit Card Loan', '10000.00', '10000000.00', '10.00', 12);

-- --------------------------------------------------------

--
-- Table structure for table `loan_statuses`
--

CREATE TABLE `loan_statuses` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` text,
  `color` varchar(20) NOT NULL DEFAULT '#6b7280',
  `sort_order` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `loan_statuses`
--

INSERT INTO `loan_statuses` (`id`, `name`, `description`, `color`, `sort_order`) VALUES
(1, 'Pending Review', 'Application is pending review', '#FF5733', 0),
(2, 'Under Assessment', 'Application is being assessed by loan officers', '#33FF57', 20),
(3, 'Approved', 'Loan has been approved and is ready for disbursement', '#3357FF', 30),
(4, 'Rejected', 'Loan application has been rejected', '#F033FF', 40),
(5, 'Disbursed', 'Funds have been disbursed to the borrower', '#FF3333', 50),
(6, 'Closed', 'Loan has been fully repaid and closed', '#6b7280', 60),
(28, 'Pending', 'Application is pending review', '#f59e0b', 0),
(29, 'Active', 'Loan is active and in repayment', '#3b82f6', 0),
(30, 'Completed', 'Loan has been fully repaid', '#6366f1', 0);

-- --------------------------------------------------------

--
-- Table structure for table `login_logs`
--

CREATE TABLE `login_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `status` enum('success','failed','logout') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `login_logs`
--

INSERT INTO `login_logs` (`id`, `user_id`, `admin_id`, `email`, `ip_address`, `user_agent`, `status`, `created_at`) VALUES
(1, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-13 08:12:23'),
(2, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-14 09:50:24'),
(3, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:22:36'),
(4, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:22:44'),
(5, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:22:49'),
(6, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:22:55'),
(7, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:23:06'),
(8, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:23:43'),
(9, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 10:24:51'),
(10, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-14 10:25:00'),
(11, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 14:10:39'),
(12, 10, NULL, NULL, '::1', NULL, 'success', '2025-05-14 14:10:45'),
(13, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 14:23:36'),
(14, NULL, NULL, '<EMAIL>', '::1', NULL, 'failed', '2025-05-14 14:23:51'),
(15, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-14 14:25:09'),
(16, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-14 14:42:53'),
(17, 10, NULL, NULL, '::1', NULL, 'success', '2025-05-15 03:35:28'),
(18, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-15 05:59:17'),
(19, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-15 07:06:11'),
(20, 10, NULL, NULL, '::1', NULL, 'success', '2025-05-16 07:14:37'),
(21, 10, NULL, NULL, '::1', NULL, 'success', '2025-05-18 22:01:55'),
(22, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-18 22:16:43'),
(23, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-19 09:00:08'),
(24, 10, NULL, NULL, '::1', NULL, 'success', '2025-05-19 09:44:20'),
(25, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-20 14:24:17'),
(26, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-20 19:18:21'),
(27, 3, NULL, NULL, '::1', NULL, 'success', '2025-05-22 09:56:27');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `type` varchar(50) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `message`, `type`, `is_read`, `created_at`, `updated_at`) VALUES
(1, 3, 'Your loan application has been received and is under review.', 'loan_application', 0, '2025-05-12 07:54:20', '2025-05-13 09:54:20'),
(2, 3, 'Your loan application has been approved! Check your email for details.', 'loan_status', 1, '2025-05-11 07:54:20', '2025-05-13 09:54:20'),
(3, 3, 'Your document has been approved.', 'document', 0, '2025-05-10 07:54:21', '2025-05-13 09:54:21'),
(4, 3, 'A payment has been processed for your loan.', 'payment', 1, '2025-05-09 07:54:21', '2025-05-13 09:54:21'),
(5, 3, 'Your loan application requires additional documentation.', 'general', 0, '2025-05-08 07:54:21', '2025-05-13 09:54:21'),
(6, 0, 'New loan application submitted by Henry Stark (ID: 27)', 'loan_application', 0, '2025-05-16 08:06:54', '2025-05-16 08:06:54'),
(7, 0, 'New loan application submitted by Henry Stark (ID: 28)', 'loan_application', 0, '2025-05-16 15:22:40', '2025-05-16 15:22:40'),
(8, 0, 'New loan application submitted by Henry Stark (ID: 29)', 'loan_application', 0, '2025-05-16 18:46:08', '2025-05-16 18:46:08'),
(9, 10, 'Your loan application #29 status has been updated to: Pending', NULL, 0, '2025-05-17 08:47:40', '2025-05-17 08:47:40'),
(10, 10, 'Your loan application #29 status has been updated to: Active', NULL, 0, '2025-05-17 08:48:32', '2025-05-17 08:48:32'),
(11, 10, 'Your loan application #29 status has been updated to: Under Assessment', NULL, 0, '2025-05-18 18:45:39', '2025-05-18 18:45:39'),
(12, 10, 'Your loan application #29 status has been updated to: Approved', NULL, 0, '2025-05-19 07:59:25', '2025-05-19 07:59:25'),
(13, 10, 'Support Ticket Updated: Admin has replied to your support ticket: hi', 'info', 0, '2025-05-19 19:04:15', '2025-05-19 19:04:15'),
(14, 3, 'Your loan application #25 status has been updated to: Completed', NULL, 0, '2025-05-20 14:27:21', '2025-05-20 14:27:21');

-- --------------------------------------------------------

--
-- Table structure for table `password_resets`
--

CREATE TABLE `password_resets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `token` varchar(255) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `code` varchar(50) NOT NULL,
  `description` text,
  `instructions` text,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `display_order` int(11) NOT NULL DEFAULT '0',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `code`, `description`, `instructions`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'Bank Transfer', 'bank_transfer', 'Transfer funds directly to our bank account.', 'Please transfer the exact amount to the following account:\\nBank Name: Example Bank\\nAccount Number: **********\\nAccount Name: LendSwift\\nReference: Your transaction reference number', 1, 1, '2025-05-19 01:38:32', NULL),
(2, 'Cash', 'cash', 'Pay with cash at our office.', 'Visit our office at 123 Main Street to make a cash payment. Please bring your transaction reference number.', 1, 2, '2025-05-19 01:38:32', NULL),
(3, 'Credit Card', 'credit_card', 'Pay with your credit card.', 'You can pay with your credit card through our secure payment gateway.', 1, 3, '2025-05-19 01:38:32', NULL),
(4, 'Debit Card', 'debit_card', 'Pay with your debit card.', 'You can pay with your debit card through our secure payment gateway.', 1, 4, '2025-05-19 01:38:32', NULL),
(5, 'PayPal', 'paypal', 'Pay with PayPal.', 'Send payment to our PayPal account: <EMAIL>', 1, 5, '2025-05-19 01:38:32', NULL),
(6, 'Mobile Money', 'mobile_money', 'Pay with mobile money.', 'Send payment to our mobile money account: +**********', 1, 6, '2025-05-19 01:38:32', NULL),
(7, 'Check', 'check', 'Pay with a check.', 'Mail your check to our office at 123 Main Street. Please include your transaction reference number.', 1, 7, '2025-05-19 01:38:32', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'LendSwift', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(2, 'site_url', 'http://localhost/pfloans.com', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(3, 'admin_email', '<EMAIL>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(4, 'company_name', 'LendSwift Financial', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(5, 'company_email', '<EMAIL>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(6, 'company_phone', '+****************', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(7, 'company_address', '123 Main St, City, Country', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(8, 'currency_symbol', '$', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(9, 'date_format', 'Y-m-d', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(10, 'time_format', 'H:i:s', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(11, 'timezone', 'UTC', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(12, 'items_per_page', '10', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(13, 'use_smtp', '1', '2025-05-12 13:24:55', '2025-05-12 16:50:11'),
(14, 'smtp_host', 'smtp.hostinger.com', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(15, 'smtp_port', '465', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(16, 'smtp_username', '<EMAIL>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(17, 'smtp_password', 'Money2025@Demo#', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(18, 'smtp_secure', 'ssl', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(19, 'email_from_name', 'LendSwift Financial', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(20, 'email_from_address', '<EMAIL>', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(21, 'email_footer_text', '© 2025 LendSwift Financial. All rights reserved.', '2025-05-12 13:24:55', '2025-05-12 13:24:55'),
(22, 'show_loan_journey', '0', '2025-05-20 16:09:35', '2025-05-20 16:09:35'),
(23, 'show_loan_applications', '0', '2025-05-20 16:09:35', '2025-05-20 16:09:35'),
(24, 'show_recent_documents', '1', '2025-05-20 16:09:35', '2025-05-20 16:13:40'),
(25, 'show_recent_transactions', '0', '2025-05-20 16:13:13', '2025-05-20 16:13:13'),
(26, 'site_logo', '/assets/images/logo_1747945763.png', '2025-05-22 19:40:47', '2025-05-22 20:29:23'),
(27, 'logo_size', 'large', '2025-05-22 19:58:14', '2025-05-22 19:58:14'),
(28, 'logo_custom_size', '200', '2025-05-22 19:58:14', '2025-05-22 19:58:14'),
(29, 'show_site_name', '0', '2025-05-22 19:58:14', '2025-05-22 19:58:14'),
(30, 'site_favicon', '/assets/images/favicon_1747945742.svg', '2025-05-22 20:29:02', '2025-05-22 20:29:02');

-- --------------------------------------------------------

--
-- Table structure for table `status_history`
--

CREATE TABLE `status_history` (
  `id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `status_id` int(11) NOT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `update_date` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `status_history`
--

INSERT INTO `status_history` (`id`, `application_id`, `status_id`, `updated_by`, `notes`, `update_date`) VALUES
(1, 1, 1, NULL, '', '2025-05-12 11:38:30'),
(2, 2, 1, NULL, '', '2025-05-12 12:49:00'),
(3, 21, 28, NULL, '', '2025-05-08 09:54:20'),
(4, 22, 3, NULL, '', '2025-05-03 09:54:20'),
(5, 23, 29, NULL, '', '2025-04-28 09:54:20'),
(6, 24, 1, NULL, '', '2025-05-15 17:45:21'),
(7, 25, 1, NULL, '', '2025-05-15 19:59:14'),
(8, 27, 1, NULL, '', '2025-05-16 10:06:54'),
(9, 28, 1, NULL, '', '2025-05-16 17:22:40'),
(10, 29, 29, NULL, 'Loan is active we are processing', '2025-05-16 20:46:08'),
(16, 29, 2, 1, 'Loan is Under Assessment we are processing', '2025-05-18 20:45:39'),
(17, 29, 3, 1, 'Loan is Has been approved succesfully', '2025-05-19 09:59:25'),
(18, 25, 30, 1, 'Loan has been approved and completed', '2025-05-20 16:27:21');

-- --------------------------------------------------------

--
-- Table structure for table `support_messages`
--

CREATE TABLE `support_messages` (
  `id` int(11) NOT NULL,
  `ticket_id` int(11) NOT NULL,
  `sender_type` varchar(10) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `support_messages`
--

INSERT INTO `support_messages` (`id`, `ticket_id`, `sender_type`, `sender_id`, `message`, `created_at`, `is_read`) VALUES
(1, 1, 'user', 10, 'gi', '2025-05-19 16:27:32', 1),
(2, 1, 'admin', 1, 'what is your oder or ticket numebr', '2025-05-19 18:46:43', 1),
(3, 1, 'admin', 1, 'hi', '2025-05-19 18:47:18', 1),
(4, 1, 'admin', 1, 'what is your order number so we can report', '2025-05-19 18:54:39', 1),
(5, 1, 'admin', 1, 'tell us the day you opened account', '2025-05-19 19:04:15', 1),
(6, 1, 'user', 10, '101001010010100101', '2025-05-19 19:09:46', 1);

-- --------------------------------------------------------

--
-- Table structure for table `support_tickets`
--

CREATE TABLE `support_tickets` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `priority` varchar(20) NOT NULL DEFAULT 'medium',
  `status` varchar(20) NOT NULL DEFAULT 'open',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `support_tickets`
--

INSERT INTO `support_tickets` (`id`, `user_id`, `subject`, `priority`, `status`, `created_at`, `updated_at`) VALUES
(1, 10, 'hi', 'medium', 'open', '2025-05-19 16:27:32', '2025-05-19 19:04:15');

-- --------------------------------------------------------

--
-- Table structure for table `test_table`
--

CREATE TABLE `test_table` (
  `id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `id` int(11) NOT NULL,
  `reference_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int(11) NOT NULL,
  `application_id` int(11) NOT NULL,
  `loan_id` int(11) DEFAULT NULL,
  `type` enum('payment','disbursement','fee','refund') COLLATE utf8mb4_unicode_ci NOT NULL,
  `custom_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `currency_id` int(11) NOT NULL DEFAULT '1',
  `status` enum('pending','completed','failed','refunded') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `custom_payment_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `transaction_date` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `receipt_path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `file_size` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`id`, `reference_number`, `user_id`, `application_id`, `loan_id`, `type`, `custom_type`, `amount`, `currency_id`, `status`, `payment_method`, `custom_payment_method`, `description`, `transaction_date`, `created_at`, `updated_at`, `receipt_path`, `file_size`) VALUES
(5, 'TRX202505130954209008', 3, 0, NULL, 'payment', NULL, '416.67', 1, 'completed', NULL, NULL, NULL, '2025-05-13 11:54:20', '2025-05-13 09:54:20', '2025-05-18 22:30:52', '/uploads/receipts/receipt_5_1747607290.pdf', NULL),
(6, 'TRX202505130954209463', 3, 0, NULL, 'disbursement', NULL, '15000.00', 1, 'completed', NULL, NULL, NULL, '2025-05-13 11:54:20', '2025-05-13 09:54:20', '2025-05-13 09:54:20', NULL, NULL),
(7, 'TRX202505130954203065', 3, 0, NULL, 'payment', NULL, '666.67', 1, 'completed', NULL, NULL, NULL, '2025-05-13 11:54:20', '2025-05-13 09:54:20', '2025-05-13 09:54:20', NULL, NULL),
(8, 'PAY-9E0C0626-29', 10, 29, NULL, 'fee', NULL, '1000.00', 11, 'completed', 'bank_transfer', NULL, 'Please complete this payment as soon as possible.\n\nAdmin Notes: Payment receipt approved by admin.', '2025-05-19 00:08:06', '2025-05-18 22:08:06', '2025-05-18 23:48:36', '/uploads/receipts/receipt_8_1747612104.pdf', NULL),
(9, 'PAY-4BE7F703-22', 3, 22, NULL, 'fee', NULL, '67777.00', 1, 'pending', 'bank_transfer', NULL, '', '2025-05-19 01:46:20', '2025-05-18 23:46:20', '2025-05-18 23:46:20', NULL, NULL),
(10, 'PAY-5E1D3932-29', 10, 29, NULL, 'fee', 'documentation_fee', '45000.00', 11, 'completed', 'bank_transfer', NULL, '\n\nAdmin Notes: Payment receipt approved by admin.', '2025-05-19 02:34:21', '2025-05-19 00:34:21', '2025-05-19 07:59:40', NULL, NULL),
(11, 'PAY-9E5D9888-29', 10, 29, NULL, 'fee', 'membership_fee', '8000.00', 11, 'completed', 'bank_transfer', NULL, '\n\nAdmin Notes: Payment receipt approved by admin.', '2025-05-19 02:36:45', '2025-05-19 00:36:45', '2025-05-19 09:47:26', '/uploads/receipts/receipt_11_1747641742.pdf', 202023);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `currency_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive','suspended') DEFAULT 'active',
  `address` varchar(255) DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(100) DEFAULT NULL,
  `zip` varchar(20) DEFAULT NULL,
  `country` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `phone`, `password`, `currency_id`, `created_at`, `updated_at`, `status`, `address`, `city`, `state`, `zip`, `country`) VALUES
(1, 'John Doe', '<EMAIL>', NULL, '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '2025-05-12 09:38:15', '2025-05-12 09:38:15', 'active', NULL, NULL, NULL, NULL, NULL),
(2, 'Demo User', '<EMAIL>', NULL, '$2y$10$RTVfFvl7N/ktcrf3oLdPw.iIGKbIiJ5omWa80m4YsoPXXf6hdT2n.', 1, '2025-05-12 10:25:51', '2025-05-12 10:25:51', 'inactive', NULL, NULL, NULL, NULL, NULL),
(3, 'james Bong', '<EMAIL>', '+****************', '$2y$10$OzVAr7fDUbyCkA1KlUEu6ems0M.wmkPQuBBarNsEemgZE7c/O9OUO', 1, '2025-05-12 21:13:26', '2025-05-12 21:13:26', 'active', '123 Main Street', 'New York', 'NY', '10001', 'United States'),
(4, 'Test User', '<EMAIL>', NULL, '$2y$10$U2o9J5azxuqYLKbp2E7QEu9MVEgUwFnb5R8tl3Qp4wy8p9huMNSUW', 2, '2025-05-13 04:40:19', NULL, 'active', NULL, NULL, NULL, NULL, NULL),
(7, 'Test User 123', '<EMAIL>', '+****************', '$2y$10$.JlmELEbxdN9J2DTdWVR1utMKWK5Hgvl.FYQzakuyEG.JwyCHFKEu', 11, '2025-05-14 07:24:18', '2025-05-14 17:07:50', 'active', '44 Mission Road', 'New York', 'NY', '75211', 'Canada'),
(8, 'Demo Kingz', '<EMAIL>', '+****************', '$2y$10$j7AE3hyxR5GDRJEd.MhDz.SIboe5WLvpafTit..7z9EKXviXCMWum', 2, '2025-05-14 07:25:46', '2025-05-14 17:07:23', 'active', '123 Main Street', 'New York', 'NY', '10001', 'United States'),
(9, 'Test Logo User', '<EMAIL>', NULL, '$2y$10$C56CHfjjNdNZrCYdzbv5OOCeK3R3PGgOCSg9./7zjq0igW5XQQtyi', 1, '2025-05-14 07:36:41', '2025-05-14 07:36:41', 'active', NULL, NULL, NULL, NULL, NULL),
(10, 'Henry Stark', '<EMAIL>', '11010102827', '$2y$10$R/wODMCTsh0OC9kYYgpFjeB6bYVYJ1msEh0EM0xxgkr8IS7ROgqJq', 3, '2025-05-14 07:38:57', '2025-05-14 16:40:32', 'active', '11 Kent Road', 'Dallas', 'Texas', '75211', 'United States');

-- --------------------------------------------------------

--
-- Table structure for table `user_status_history`
--

CREATE TABLE `user_status_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `status` enum('active','inactive','suspended') NOT NULL,
  `changed_by` int(11) NOT NULL,
  `admin_note` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_status_history`
--

INSERT INTO `user_status_history` (`id`, `user_id`, `status`, `changed_by`, `admin_note`, `created_at`) VALUES
(1, 9, 'active', 1, '', '2025-05-14 07:57:10'),
(2, 9, 'suspended', 1, '', '2025-05-14 07:57:21'),
(3, 9, 'active', 1, '', '2025-05-14 07:57:31'),
(4, 9, 'suspended', 1, 'Your account has been temporarily suspended due to suspicious activity. Please contact support for more information.', '2025-05-14 07:58:18'),
(5, 9, 'active', 1, 'Your account has been reactivated. Thank you for your patience.', '2025-05-14 07:58:49'),
(6, 10, 'active', 1, 'welcome back', '2025-05-14 07:59:39'),
(7, 10, 'inactive', 1, 'account has been totaly deactivated', '2025-05-14 08:00:49'),
(8, 10, 'active', 1, '', '2025-05-14 14:10:31');

-- --------------------------------------------------------

--
-- Table structure for table `user_tokens`
--

CREATE TABLE `user_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `website_content`
--

CREATE TABLE `website_content` (
  `id` int(11) UNSIGNED NOT NULL,
  `page` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `section` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `content_json` longtext COLLATE utf8mb4_unicode_ci,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `website_content`
--

INSERT INTO `website_content` (`id`, `page`, `section`, `title`, `description`, `content_json`, `created_at`, `updated_at`) VALUES
(1, 'services', 'hero', 'Financial Solutions for Every Need', 'Discover our comprehensive range of loan products and financial services designed to help you achieve your goals and secure your financial future.', '{\"badge\":\"Our Services\"}', '2025-05-20 23:01:17', '2025-05-20 23:01:17'),
(2, 'services', 'mission', 'Empowering Your Financial Journey', 'We\'re committed to providing accessible, transparent, and personalized loan solutions that help you achieve your financial goals.', '{\"points\":[\"Providing fast and accessible loan solutions for everyone\",\"Ensuring complete transparency in all our loan processes\",\"Offering competitive rates and flexible repayment options\",\"Delivering exceptional customer service and support\"]}', '2025-05-20 23:01:17', '2025-05-20 23:01:17'),
(3, 'services', 'process', 'How Our Loans Work', 'Get your loan in three easy steps with our streamlined application process', '{\"badge\":\"Simple Process\",\"steps\":[{\"title\":\"Apply Online\",\"description\":\"Fill out our simple online application form in minutes. No paperwork, no hassle.\",\"features\":[\"Quick application process\",\"Secure document upload\",\"Mobile-friendly forms\"]},{\"title\":\"Get Approved\",\"description\":\"Receive a quick decision on your application. Most loans are approved within 24 hours.\",\"features\":[\"Fast credit assessment\",\"Transparent terms\",\"Competitive rates\"]},{\"title\":\"Receive Funds\",\"description\":\"Get your money quickly and securely. Funds are typically deposited within 1-2 business days.\",\"features\":[\"Direct deposit\",\"Secure transfers\",\"Quick disbursement\"]}]}', '2025-05-20 23:01:17', '2025-05-20 23:01:17'),
(4, 'services', 'additional', 'Beyond Loans', 'We offer more than just loans to help you manage your finances effectively', '{\"badge\":\"Additional Services\",\"services\":[{\"title\":\"Loan Refinancing\",\"description\":\"Refinance your existing loans to get better interest rates and more favorable terms. Our refinancing options can help you lower your monthly payments and save money over time.\"},{\"title\":\"Financial Planning\",\"description\":\"Get expert advice on managing your finances and planning for your future goals. Our financial advisors can help you create a personalized plan to achieve financial stability and growth.\"},{\"title\":\"Payment Scheduling\",\"description\":\"Flexible payment scheduling options to help you manage your loan repayments. Set up automatic payments, choose your payment date, and adjust your payment schedule as needed to fit your budget.\"},{\"title\":\"Customer Support\",\"description\":\"Dedicated customer support to assist you throughout your loan journey. Our team is available to answer your questions, provide guidance, and help you navigate the loan process from application to repayment.\"}]}', '2025-05-20 23:01:17', '2025-05-20 23:01:17'),
(5, 'services', 'testimonials', 'What Our Clients Say', 'Don\'t just take our word for it - hear from our satisfied customers', '{\"badge\":\"Customer Stories\",\"testimonials\":[{\"name\":\"John D.\",\"position\":\"Business Owner\",\"text\":\"The loan application process was incredibly easy. Their online application took me less than 10 minutes to complete, and I had approval within 24 hours. The funds were in my account the next day!\",\"rating\":5},{\"name\":\"Sarah M.\",\"position\":\"Homeowner\",\"text\":\"I needed a personal loan for home renovations and was dreading the paperwork. The process was completely digital and straightforward. Their customer service team was also incredibly helpful when I had questions.\",\"rating\":5},{\"name\":\"Michael T.\",\"position\":\"Financial Analyst\",\"text\":\"The interest rates were competitive and the terms were clear. I appreciated the transparency throughout the process. No hidden fees or surprises. I\'ll definitely use their services again in the future.\",\"rating\":4.5}]}', '2025-05-20 23:01:17', '2025-05-20 23:01:17');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`,`is_read`);

--
-- Indexes for table `application_documents`
--
ALTER TABLE `application_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `application_id` (`application_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `requested_by_admin_id` (`requested_by_admin_id`);

--
-- Indexes for table `calculator_history`
--
ALTER TABLE `calculator_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `loan_product_id` (`loan_product_id`),
  ADD KEY `currency_id` (`currency_id`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `document_access_logs`
--
ALTER TABLE `document_access_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `document_id` (`document_id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `forms`
--
ALTER TABLE `forms`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `form_data`
--
ALTER TABLE `form_data`
  ADD PRIMARY KEY (`id`),
  ADD KEY `application_id` (`application_id`),
  ADD KEY `field_id` (`field_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `form_fields`
--
ALTER TABLE `form_fields`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `loan_applications`
--
ALTER TABLE `loan_applications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `loan_product_id` (`loan_product_id`),
  ADD KEY `currency_id` (`currency_id`),
  ADD KEY `status_id` (`status_id`),
  ADD KEY `fk_loan_applications_form_id` (`form_id`);

--
-- Indexes for table `loan_products`
--
ALTER TABLE `loan_products`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `loan_statuses`
--
ALTER TABLE `loan_statuses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `login_logs`
--
ALTER TABLE `login_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_login_logs_user_id` (`user_id`),
  ADD KEY `idx_login_logs_admin_id` (`admin_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_password_resets_token` (`token`),
  ADD KEY `idx_password_resets_user_id` (`user_id`),
  ADD KEY `idx_password_resets_admin_id` (`admin_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `status_history`
--
ALTER TABLE `status_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `application_id` (`application_id`),
  ADD KEY `status_id` (`status_id`),
  ADD KEY `updated_by` (`updated_by`);

--
-- Indexes for table `support_messages`
--
ALTER TABLE `support_messages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `support_tickets`
--
ALTER TABLE `support_tickets`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `reference_number` (`reference_number`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `user_status_history`
--
ALTER TABLE `user_status_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_tokens_token` (`token`),
  ADD KEY `idx_user_tokens_user_id` (`user_id`);

--
-- Indexes for table `website_content`
--
ALTER TABLE `website_content`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `page_section` (`page`,`section`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `application_documents`
--
ALTER TABLE `application_documents`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `calculator_history`
--
ALTER TABLE `calculator_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `document_access_logs`
--
ALTER TABLE `document_access_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `forms`
--
ALTER TABLE `forms`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `form_data`
--
ALTER TABLE `form_data`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `form_fields`
--
ALTER TABLE `form_fields`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `loan_applications`
--
ALTER TABLE `loan_applications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `loan_products`
--
ALTER TABLE `loan_products`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `loan_statuses`
--
ALTER TABLE `loan_statuses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `login_logs`
--
ALTER TABLE `login_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `password_resets`
--
ALTER TABLE `password_resets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `status_history`
--
ALTER TABLE `status_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `support_messages`
--
ALTER TABLE `support_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `support_tickets`
--
ALTER TABLE `support_tickets`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `user_status_history`
--
ALTER TABLE `user_status_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `user_tokens`
--
ALTER TABLE `user_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `website_content`
--
ALTER TABLE `website_content`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `application_documents`
--
ALTER TABLE `application_documents`
  ADD CONSTRAINT `application_documents_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `loan_applications` (`id`),
  ADD CONSTRAINT `application_documents_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `application_documents_ibfk_3` FOREIGN KEY (`requested_by_admin_id`) REFERENCES `admins` (`id`);

--
-- Constraints for table `calculator_history`
--
ALTER TABLE `calculator_history`
  ADD CONSTRAINT `calculator_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `calculator_history_ibfk_2` FOREIGN KEY (`loan_product_id`) REFERENCES `loan_products` (`id`),
  ADD CONSTRAINT `calculator_history_ibfk_3` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`);

--
-- Constraints for table `document_access_logs`
--
ALTER TABLE `document_access_logs`
  ADD CONSTRAINT `document_access_logs_ibfk_1` FOREIGN KEY (`document_id`) REFERENCES `application_documents` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `document_access_logs_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `form_data`
--
ALTER TABLE `form_data`
  ADD CONSTRAINT `form_data_ibfk_1` FOREIGN KEY (`application_id`) REFERENCES `loan_applications` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `form_data_ibfk_2` FOREIGN KEY (`field_id`) REFERENCES `form_fields` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `form_data_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `loan_applications`
--
ALTER TABLE `loan_applications`
  ADD CONSTRAINT `fk_loan_applications_form_id` FOREIGN KEY (`form_id`) REFERENCES `forms` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `loan_applications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  ADD CONSTRAINT `loan_applications_ibfk_2` FOREIGN KEY (`loan_product_id`) REFERENCES `loan_products` (`id`),
  ADD CONSTRAINT `loan_applications_ibfk_3` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`),
  ADD CONSTRAINT `loan_applications_ibfk_4` FOREIGN KEY (`status_id`) REFERENCES `loan_statuses` (`id`);

--
-- Constraints for table `login_logs`
--
ALTER TABLE `login_logs`
  ADD CONSTRAINT `login_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `login_logs_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `password_resets`
--
ALTER TABLE `password_resets`
  ADD CONSTRAINT `password_resets_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `password_resets_ibfk_2` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_status_history`
--
ALTER TABLE `user_status_history`
  ADD CONSTRAINT `user_status_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_tokens`
--
ALTER TABLE `user_tokens`
  ADD CONSTRAINT `user_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
