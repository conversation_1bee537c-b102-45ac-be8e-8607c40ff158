/**
 * Admin Authentication Integration Tests
 * Tests the complete authentication flow and component integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import axios from 'axios';

// Import components
import AdminLoginPage from '../pages/AdminLoginPage';
import SecuritySettings from '../pages/SecuritySettings';
import { AdminOTPVerification, Admin2FAVerification, Admin2FASetup, AdminAuthPreferences } from '../components/Admin';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

describe('Admin Authentication Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  describe('AdminLoginPage Integration', () => {
    test('renders login form correctly', () => {
      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      expect(screen.getByText('Admin Login')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter your username or email')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
      expect(screen.getByText('Enhanced security enabled')).toBeInTheDocument();
    });

    test('handles password-only login successfully', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          admin_id: 1,
          username: 'testadmin',
          role: 'admin',
          auth_method: 'password_only'
        }
      });

      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      fireEvent.change(screen.getByPlaceholderText('Enter your username or email'), {
        target: { value: 'testadmin' }
      });
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'password123' }
      });
      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(localStorage.getItem('adminId')).toBe('1');
        expect(localStorage.getItem('adminUsername')).toBe('testadmin');
        expect(mockNavigate).toHaveBeenCalledWith('/admin/dashboard');
      });
    });

    test('handles OTP authentication flow', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          requires_additional_auth: true,
          next_step: 'otp',
          admin_id: 1,
          username: 'testadmin'
        }
      });

      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      fireEvent.change(screen.getByPlaceholderText('Enter your username or email'), {
        target: { value: 'testadmin' }
      });
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'password123' }
      });
      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByText('Enter OTP Code')).toBeInTheDocument();
      });
    });

    test('handles 2FA authentication flow', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          requires_additional_auth: true,
          next_step: '2fa',
          admin_id: 1,
          username: 'testadmin'
        }
      });

      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      fireEvent.change(screen.getByPlaceholderText('Enter your username or email'), {
        target: { value: 'testadmin' }
      });
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'password123' }
      });
      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByText('Two-Factor Authentication')).toBeInTheDocument();
      });
    });
  });

  describe('AdminOTPVerification Integration', () => {
    const mockProps = {
      adminId: 1,
      username: 'testadmin',
      onSuccess: jest.fn(),
      onBack: jest.fn()
    };

    test('sends OTP on component mount', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          email_masked: 'te***@example.com',
          expires_in: 300
        }
      });

      render(<AdminOTPVerification {...mockProps} />);

      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          '/backend/handlers/admin_send_otp.php',
          { admin_id: 1 }
        );
      });
    });

    test('verifies OTP correctly', async () => {
      mockedAxios.post
        .mockResolvedValueOnce({
          data: { success: true, email_masked: 'te***@example.com' }
        })
        .mockResolvedValueOnce({
          data: {
            success: true,
            admin_id: 1,
            username: 'testadmin',
            session_token: 'token123'
          }
        });

      render(<AdminOTPVerification {...mockProps} />);

      // Wait for OTP to be sent
      await waitFor(() => {
        expect(screen.getByText(/OTP sent to/)).toBeInTheDocument();
      });

      // Enter OTP
      const otpInputs = screen.getAllByRole('textbox');
      fireEvent.change(otpInputs[0], { target: { value: '1' } });
      fireEvent.change(otpInputs[1], { target: { value: '2' } });
      fireEvent.change(otpInputs[2], { target: { value: '3' } });
      fireEvent.change(otpInputs[3], { target: { value: '4' } });
      fireEvent.change(otpInputs[4], { target: { value: '5' } });
      fireEvent.change(otpInputs[5], { target: { value: '6' } });

      await waitFor(() => {
        expect(mockProps.onSuccess).toHaveBeenCalledWith({
          admin_id: 1,
          username: 'testadmin',
          session_token: 'token123'
        });
      });
    });
  });

  describe('Admin2FASetup Integration', () => {
    const mockProps = {
      adminId: 1,
      username: 'testadmin',
      onSuccess: jest.fn(),
      onBack: jest.fn()
    };

    test('initiates 2FA setup on mount', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          secret_key: 'TESTSECRET',
          qr_code_url: 'data:image/png;base64,test',
          backup_codes: ['CODE1', 'CODE2']
        }
      });

      render(<Admin2FASetup {...mockProps} />);

      await waitFor(() => {
        expect(mockedAxios.get).toHaveBeenCalledWith(
          '/backend/handlers/admin_setup_2fa.php?adminId=1'
        );
      });
    });

    test('completes 2FA setup flow', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          secret_key: 'TESTSECRET',
          qr_code_url: 'data:image/png;base64,test',
          backup_codes: ['CODE1', 'CODE2']
        }
      });

      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: true,
          backup_codes: ['CODE1', 'CODE2']
        }
      });

      render(<Admin2FASetup {...mockProps} />);

      // Wait for setup to load
      await waitFor(() => {
        expect(screen.getByText('Step 1: Scan QR Code')).toBeInTheDocument();
      });

      // Go to verification step
      fireEvent.click(screen.getByText('Next: Verify Setup'));

      // Enter verification code
      const codeInput = screen.getByPlaceholderText('000000');
      fireEvent.change(codeInput, { target: { value: '123456' } });
      fireEvent.click(screen.getByText('Verify & Enable 2FA'));

      await waitFor(() => {
        expect(screen.getByText('Step 3: Save Backup Codes')).toBeInTheDocument();
      });

      // Complete setup
      fireEvent.click(screen.getByText('Complete Setup'));

      expect(mockProps.onSuccess).toHaveBeenCalled();
    });
  });

  describe('SecuritySettings Integration', () => {
    test('loads and displays admin authentication settings', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          settings: {},
          admin_auth_settings: {
            admin_otp_enabled: { value: 'false' },
            admin_2fa_enabled: { value: 'false' }
          },
          admin_auth_available: true
        }
      });

      render(<SecuritySettings />);

      await waitFor(() => {
        expect(screen.getByText('Admin Authentication Security')).toBeInTheDocument();
        expect(screen.getByText('Enable OTP (One-Time Password) via Email')).toBeInTheDocument();
        expect(screen.getByText('Enable 2FA (Two-Factor Authentication) via Google Authenticator')).toBeInTheDocument();
      });
    });

    test('updates admin authentication settings', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          settings: {},
          admin_auth_settings: {
            admin_otp_enabled: { value: 'false' },
            admin_2fa_enabled: { value: 'false' }
          },
          admin_auth_available: true
        }
      });

      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true }
      });

      render(<SecuritySettings />);

      await waitFor(() => {
        expect(screen.getByText('Admin Authentication Security')).toBeInTheDocument();
      });

      // Enable OTP
      const otpCheckbox = screen.getByLabelText(/Enable OTP/);
      fireEvent.click(otpCheckbox);

      // Save settings
      fireEvent.click(screen.getByText('Save Settings'));

      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('update_security_settings.php'),
          expect.objectContaining({
            admin_auth_settings: expect.objectContaining({
              admin_otp_enabled: 'true'
            })
          })
        );
      });
    });
  });

  describe('AdminAuthPreferences Integration', () => {
    const mockProps = {
      adminId: 1,
      onUpdate: jest.fn()
    };

    test('loads admin preferences', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          admin: { username: 'testadmin' },
          auth_settings: { auth_method: 'password_only' },
          can_use_otp: true,
          can_use_2fa: true
        }
      });

      render(<AdminAuthPreferences {...mockProps} />);

      await waitFor(() => {
        expect(screen.getByText('Authentication Preferences')).toBeInTheDocument();
        expect(screen.getByText('Password Only')).toBeInTheDocument();
      });
    });

    test('updates authentication preferences', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        data: {
          success: true,
          admin: { username: 'testadmin' },
          auth_settings: { auth_method: 'password_only' },
          can_use_otp: true,
          can_use_2fa: true
        }
      });

      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true }
      });

      render(<AdminAuthPreferences {...mockProps} />);

      await waitFor(() => {
        expect(screen.getByText('Authentication Preferences')).toBeInTheDocument();
      });

      // Select OTP method
      const otpRadio = screen.getByDisplayValue('otp');
      fireEvent.click(otpRadio);

      // Save preferences
      fireEvent.click(screen.getByText('Save Preferences'));

      await waitFor(() => {
        expect(mockedAxios.post).toHaveBeenCalledWith(
          expect.stringContaining('admin_auth_preferences.php'),
          expect.objectContaining({
            auth_method: 'otp'
          })
        );
      });
    });
  });

  describe('Error Handling', () => {
    test('handles network errors gracefully', async () => {
      mockedAxios.post.mockRejectedValueOnce(new Error('Network Error'));

      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      fireEvent.change(screen.getByPlaceholderText('Enter your username or email'), {
        target: { value: 'testadmin' }
      });
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'password123' }
      });
      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByText(/Network error/)).toBeInTheDocument();
      });
    });

    test('handles API errors with proper messages', async () => {
      mockedAxios.post.mockResolvedValueOnce({
        data: {
          success: false,
          message: 'Invalid credentials'
        }
      });

      render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      fireEvent.change(screen.getByPlaceholderText('Enter your username or email'), {
        target: { value: 'testadmin' }
      });
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'wrongpassword' }
      });
      fireEvent.click(screen.getByText('Login'));

      await waitFor(() => {
        expect(screen.getByText('Invalid credentials')).toBeInTheDocument();
      });
    });
  });

  describe('Security Features', () => {
    test('clears sensitive data on component unmount', () => {
      const { unmount } = render(
        <BrowserRouter>
          <AdminLoginPage />
        </BrowserRouter>
      );

      // Set some form data
      fireEvent.change(screen.getByPlaceholderText('Enter your password'), {
        target: { value: 'password123' }
      });

      unmount();

      // Password should be cleared from memory
      // This is more of a conceptual test - in practice, React handles this
      expect(true).toBe(true);
    });

    test('validates input formats correctly', async () => {
      const mockProps = {
        adminId: 1,
        username: 'testadmin',
        onSuccess: jest.fn(),
        onBack: jest.fn()
      };

      mockedAxios.post.mockResolvedValueOnce({
        data: { success: true, email_masked: 'te***@example.com' }
      });

      render(<AdminOTPVerification {...mockProps} />);

      // Try to enter non-numeric characters in OTP
      const otpInput = screen.getAllByRole('textbox')[0];
      fireEvent.change(otpInput, { target: { value: 'abc' } });

      // Should not accept non-numeric input
      expect(otpInput.value).toBe('');
    });
  });
});
