declare namespace _default {
    const id: string;
    function start(chart: any, _args: any, options: any): void;
    function start(chart: any, _args: any, options: any): void;
    function stop(chart: any): void;
    function stop(chart: any): void;
    function beforeUpdate(chart: any, _args: any, options: any): void;
    function beforeUpdate(chart: any, _args: any, options: any): void;
    namespace defaults {
        export const align: string;
        export const display: boolean;
        export namespace font {
            const weight: string;
        }
        export const fullSize: boolean;
        export const padding: number;
        export const position: string;
        export const text: string;
        const weight_1: number;
        export { weight_1 as weight };
    }
    namespace defaultRoutes {
        const color: string;
    }
    namespace descriptors {
        const _scriptable: boolean;
        const _indexable: boolean;
    }
}
export default _default;
