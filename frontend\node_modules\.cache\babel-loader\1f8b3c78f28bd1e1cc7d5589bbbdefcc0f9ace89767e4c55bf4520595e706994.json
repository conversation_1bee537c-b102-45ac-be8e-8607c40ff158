{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Currency\\\\CurrencySelector.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencySelector.css';\n\n/**\n * CurrencySelector Component\n * Allows users to select and update their preferred currency\n * \n * Props:\n * - userId: User ID for updating preference\n * - onCurrencyChange: Callback when currency is changed\n * - showPreview: Show conversion preview (default: true)\n * - disabled: Disable the selector (default: false)\n * - className: Additional CSS classes\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencySelector = ({\n  userId,\n  onCurrencyChange,\n  showPreview = true,\n  disabled = false,\n  className = ''\n}) => {\n  _s();\n  const {\n    currencies,\n    userCurrency,\n    loading: currencyLoading,\n    updateUserCurrency,\n    convertToUserCurrency\n  } = useCurrency();\n  const [updating, setUpdating] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const handleCurrencyChange = async event => {\n    const newCurrencyId = parseInt(event.target.value);\n    if (!newCurrencyId || !userId) return;\n\n    // Find the selected currency\n    const selectedCurrency = currencies.find(c => c.id === newCurrencyId);\n    if (!selectedCurrency) return;\n\n    // Don't update if it's the same currency\n    if (userCurrency && userCurrency.id === newCurrencyId) return;\n    setUpdating(true);\n    setError('');\n    setSuccess('');\n    try {\n      // Update currency preference via context\n      const success = await updateUserCurrency(selectedCurrency);\n      if (success) {\n        setSuccess(`Currency updated to ${selectedCurrency.currency_name}`);\n\n        // Call callback if provided\n        if (onCurrencyChange) {\n          onCurrencyChange(selectedCurrency);\n        }\n\n        // Clear success message after 3 seconds\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        throw new Error('Failed to update currency preference');\n      }\n    } catch (err) {\n      console.error('Error updating currency preference:', err);\n      setError(err.message || 'Failed to update currency preference');\n    } finally {\n      setUpdating(false);\n    }\n  };\n  if (currencyLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `currency-selector loading ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-placeholder\",\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-selector ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"currency-select\",\n        className: \"selector-label\",\n        children: \"Preferred Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 17\n      }, this), userCurrency && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"current-currency\",\n        children: [\"Current: \", userCurrency.currency_symbol, \" \", userCurrency.currency_code]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"currency-select\",\n        value: (userCurrency === null || userCurrency === void 0 ? void 0 : userCurrency.id) || '',\n        onChange: handleCurrencyChange,\n        disabled: disabled || updating || currencies.length === 0,\n        className: \"currency-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"Select Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 21\n        }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: currency.id,\n          children: [currency.currency_code, \" - \", currency.currency_name, \" (\", currency.currency_symbol, \")\"]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 25\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 17\n      }, this), updating && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"updating-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-spinner fa-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 13\n    }, this), showPreview && userCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversion-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Conversion Examples:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-grid\",\n        children: [10, 50, 100, 500].map(amount => {\n          const conversion = convertToUserCurrency(amount);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"fancoin-amount\",\n              children: [amount, \" FC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"arrow\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"converted-amount\",\n              children: conversion.formatted_amount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 37\n            }, this)]\n          }, amount, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 17\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-message error\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-triangle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 21\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-message success\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-check-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 21\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-help\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your preferred currency is used to display FanCoin amounts throughout the platform. All betting and transactions are still processed in FanCoin.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyQuickSelector Component\n * Compact currency selector for headers/sidebars\n */\n_s(CurrencySelector, \"ELaB7egn1ij+3TM8DNlRAP2hBLU=\", false, function () {\n  return [useCurrency];\n});\n_c = CurrencySelector;\nexport const CurrencyQuickSelector = ({\n  userId,\n  className = ''\n}) => {\n  _s2();\n  const {\n    currencies,\n    userCurrency,\n    updateUserCurrency\n  } = useCurrency();\n  const [updating, setUpdating] = useState(false);\n  const handleQuickChange = async event => {\n    const newCurrencyId = parseInt(event.target.value);\n    if (!newCurrencyId || !userId) return;\n    setUpdating(true);\n    try {\n      const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n      updateUserCurrency(result.new_currency);\n    } catch (err) {\n      console.error('Error updating currency:', err);\n    } finally {\n      setUpdating(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-quick-selector ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n      value: (userCurrency === null || userCurrency === void 0 ? void 0 : userCurrency.id) || '',\n      onChange: handleQuickChange,\n      disabled: updating,\n      className: \"quick-select\",\n      title: \"Change preferred currency\",\n      children: currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: currency.id,\n        children: [currency.currency_symbol, \" \", currency.currency_code]\n      }, currency.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), updating && /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"fas fa-spinner fa-spin updating-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 203,\n      columnNumber: 26\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyInfo Component\n * Displays current currency information and exchange rate\n */\n_s2(CurrencyQuickSelector, \"MWORR4Z8NRwKu2S4/UYo+joRBS4=\", false, function () {\n  return [useCurrency];\n});\n_c2 = CurrencyQuickSelector;\nexport const CurrencyInfo = ({\n  className = ''\n}) => {\n  _s3();\n  var _currentRate$rate_to_;\n  const {\n    userCurrency,\n    exchangeRates\n  } = useCurrency();\n  if (!userCurrency) return null;\n  const currentRate = exchangeRates.find(rate => rate.currency_id === userCurrency.id);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-info ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"info-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-name\",\n        children: [userCurrency.currency_symbol, \" \", userCurrency.currency_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-code\",\n        children: [\"(\", userCurrency.currency_code, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 225,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 221,\n      columnNumber: 13\n    }, this), currentRate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exchange-rate\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"rate-label\",\n        children: \"Exchange Rate:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"rate-value\",\n        children: [\"1 FanCoin = \", userCurrency.currency_symbol, (_currentRate$rate_to_ = currentRate.rate_to_fancoin) === null || _currentRate$rate_to_ === void 0 ? void 0 : _currentRate$rate_to_.toFixed(4), \" \", userCurrency.currency_code]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 17\n    }, this), (currentRate === null || currentRate === void 0 ? void 0 : currentRate.rate_updated_at) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rate-updated\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"update-label\",\n        children: \"Last Updated:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"update-time\",\n        children: new Date(currentRate.rate_updated_at).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 220,\n    columnNumber: 9\n  }, this);\n};\n_s3(CurrencyInfo, \"DJ1Qq/ktcb1uabul/de4L636KBI=\", false, function () {\n  return [useCurrency];\n});\n_c3 = CurrencyInfo;\nexport default CurrencySelector;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CurrencySelector\");\n$RefreshReg$(_c2, \"CurrencyQuickSelector\");\n$RefreshReg$(_c3, \"CurrencyInfo\");", "map": {"version": 3, "names": ["React", "useState", "useCurrency", "jsxDEV", "_jsxDEV", "CurrencySelector", "userId", "onCurrencyChange", "showPreview", "disabled", "className", "_s", "currencies", "userCurrency", "loading", "currencyLoading", "updateUserCurrency", "convertToUserCurrency", "updating", "setUpdating", "error", "setError", "success", "setSuccess", "handleCurrencyChange", "event", "newCurrencyId", "parseInt", "target", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "c", "id", "currency_name", "setTimeout", "Error", "err", "console", "message", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "currency_symbol", "currency_code", "onChange", "length", "map", "currency", "amount", "conversion", "formatted_amount", "_c", "CurrencyQuickSelector", "_s2", "handleQuickChange", "result", "updateUserCurrencyPreference", "new_currency", "title", "_c2", "CurrencyInfo", "_s3", "_currentRate$rate_to_", "exchangeRates", "currentRate", "rate", "currency_id", "rate_to_fancoin", "toFixed", "rate_updated_at", "Date", "toLocaleDateString", "_c3", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Currency/CurrencySelector.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencySelector.css';\n\n/**\n * CurrencySelector Component\n * Allows users to select and update their preferred currency\n * \n * Props:\n * - userId: User ID for updating preference\n * - onCurrencyChange: Callback when currency is changed\n * - showPreview: Show conversion preview (default: true)\n * - disabled: Disable the selector (default: false)\n * - className: Additional CSS classes\n */\nconst CurrencySelector = ({\n    userId,\n    onCurrencyChange,\n    showPreview = true,\n    disabled = false,\n    className = ''\n}) => {\n    const {\n        currencies,\n        userCurrency,\n        loading: currencyLoading,\n        updateUserCurrency,\n        convertToUserCurrency\n    } = useCurrency();\n\n    const [updating, setUpdating] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    const handleCurrencyChange = async (event) => {\n        const newCurrencyId = parseInt(event.target.value);\n        \n        if (!newCurrencyId || !userId) return;\n        \n        // Find the selected currency\n        const selectedCurrency = currencies.find(c => c.id === newCurrencyId);\n        if (!selectedCurrency) return;\n        \n        // Don't update if it's the same currency\n        if (userCurrency && userCurrency.id === newCurrencyId) return;\n        \n        setUpdating(true);\n        setError('');\n        setSuccess('');\n        \n        try {\n            // Update currency preference via context\n            const success = await updateUserCurrency(selectedCurrency);\n\n            if (success) {\n                setSuccess(`Currency updated to ${selectedCurrency.currency_name}`);\n\n                // Call callback if provided\n                if (onCurrencyChange) {\n                    onCurrencyChange(selectedCurrency);\n                }\n\n                // Clear success message after 3 seconds\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                throw new Error('Failed to update currency preference');\n            }\n\n        } catch (err) {\n            console.error('Error updating currency preference:', err);\n            setError(err.message || 'Failed to update currency preference');\n        } finally {\n            setUpdating(false);\n        }\n    };\n\n    if (currencyLoading) {\n        return (\n            <div className={`currency-selector loading ${className}`}>\n                <div className=\"loading-placeholder\">Loading currencies...</div>\n            </div>\n        );\n    }\n\n    return (\n        <div className={`currency-selector ${className}`}>\n            <div className=\"selector-header\">\n                <label htmlFor=\"currency-select\" className=\"selector-label\">\n                    Preferred Currency\n                </label>\n                {userCurrency && (\n                    <span className=\"current-currency\">\n                        Current: {userCurrency.currency_symbol} {userCurrency.currency_code}\n                    </span>\n                )}\n            </div>\n            \n            <div className=\"selector-wrapper\">\n                <select\n                    id=\"currency-select\"\n                    value={userCurrency?.id || ''}\n                    onChange={handleCurrencyChange}\n                    disabled={disabled || updating || currencies.length === 0}\n                    className=\"currency-select\"\n                >\n                    <option value=\"\">Select Currency</option>\n                    {currencies.map(currency => (\n                        <option key={currency.id} value={currency.id}>\n                            {currency.currency_code} - {currency.currency_name} ({currency.currency_symbol})\n                        </option>\n                    ))}\n                </select>\n                \n                {updating && (\n                    <div className=\"updating-indicator\">\n                        <i className=\"fas fa-spinner fa-spin\"></i>\n                    </div>\n                )}\n            </div>\n            \n            {showPreview && userCurrency && (\n                <div className=\"conversion-preview\">\n                    <h4>Conversion Examples:</h4>\n                    <div className=\"preview-grid\">\n                        {[10, 50, 100, 500].map(amount => {\n                            const conversion = convertToUserCurrency(amount);\n                            return (\n                                <div key={amount} className=\"preview-item\">\n                                    <span className=\"fancoin-amount\">{amount} FC</span>\n                                    <span className=\"arrow\">→</span>\n                                    <span className=\"converted-amount\">\n                                        {conversion.formatted_amount}\n                                    </span>\n                                </div>\n                            );\n                        })}\n                    </div>\n                </div>\n            )}\n            \n            {error && (\n                <div className=\"selector-message error\">\n                    <i className=\"fas fa-exclamation-triangle\"></i>\n                    {error}\n                </div>\n            )}\n            \n            {success && (\n                <div className=\"selector-message success\">\n                    <i className=\"fas fa-check-circle\"></i>\n                    {success}\n                </div>\n            )}\n            \n            <div className=\"selector-help\">\n                <p>\n                    Your preferred currency is used to display FanCoin amounts throughout the platform. \n                    All betting and transactions are still processed in FanCoin.\n                </p>\n            </div>\n        </div>\n    );\n};\n\n/**\n * CurrencyQuickSelector Component\n * Compact currency selector for headers/sidebars\n */\nexport const CurrencyQuickSelector = ({ userId, className = '' }) => {\n    const { currencies, userCurrency, updateUserCurrency } = useCurrency();\n    const [updating, setUpdating] = useState(false);\n\n    const handleQuickChange = async (event) => {\n        const newCurrencyId = parseInt(event.target.value);\n        if (!newCurrencyId || !userId) return;\n\n        setUpdating(true);\n        try {\n            const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n            updateUserCurrency(result.new_currency);\n        } catch (err) {\n            console.error('Error updating currency:', err);\n        } finally {\n            setUpdating(false);\n        }\n    };\n\n    return (\n        <div className={`currency-quick-selector ${className}`}>\n            <select\n                value={userCurrency?.id || ''}\n                onChange={handleQuickChange}\n                disabled={updating}\n                className=\"quick-select\"\n                title=\"Change preferred currency\"\n            >\n                {currencies.map(currency => (\n                    <option key={currency.id} value={currency.id}>\n                        {currency.currency_symbol} {currency.currency_code}\n                    </option>\n                ))}\n            </select>\n            {updating && <i className=\"fas fa-spinner fa-spin updating-icon\"></i>}\n        </div>\n    );\n};\n\n/**\n * CurrencyInfo Component\n * Displays current currency information and exchange rate\n */\nexport const CurrencyInfo = ({ className = '' }) => {\n    const { userCurrency, exchangeRates } = useCurrency();\n\n    if (!userCurrency) return null;\n\n    const currentRate = exchangeRates.find(rate => rate.currency_id === userCurrency.id);\n\n    return (\n        <div className={`currency-info ${className}`}>\n            <div className=\"info-header\">\n                <span className=\"currency-name\">\n                    {userCurrency.currency_symbol} {userCurrency.currency_name}\n                </span>\n                <span className=\"currency-code\">({userCurrency.currency_code})</span>\n            </div>\n            \n            {currentRate && (\n                <div className=\"exchange-rate\">\n                    <span className=\"rate-label\">Exchange Rate:</span>\n                    <span className=\"rate-value\">\n                        1 FanCoin = {userCurrency.currency_symbol}\n                        {currentRate.rate_to_fancoin?.toFixed(4)} {userCurrency.currency_code}\n                    </span>\n                </div>\n            )}\n            \n            {currentRate?.rate_updated_at && (\n                <div className=\"rate-updated\">\n                    <span className=\"update-label\">Last Updated:</span>\n                    <span className=\"update-time\">\n                        {new Date(currentRate.rate_updated_at).toLocaleDateString()}\n                    </span>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencySelector;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAO,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,gBAAgB,GAAGA,CAAC;EACtBC,MAAM;EACNC,gBAAgB;EAChBC,WAAW,GAAG,IAAI;EAClBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM;IACFC,UAAU;IACVC,YAAY;IACZC,OAAO,EAAEC,eAAe;IACxBC,kBAAkB;IAClBC;EACJ,CAAC,GAAGf,WAAW,CAAC,CAAC;EAEjB,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMuB,oBAAoB,GAAG,MAAOC,KAAK,IAAK;IAC1C,MAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAElD,IAAI,CAACH,aAAa,IAAI,CAACpB,MAAM,EAAE;;IAE/B;IACA,MAAMwB,gBAAgB,GAAGlB,UAAU,CAACmB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKP,aAAa,CAAC;IACrE,IAAI,CAACI,gBAAgB,EAAE;;IAEvB;IACA,IAAIjB,YAAY,IAAIA,YAAY,CAACoB,EAAE,KAAKP,aAAa,EAAE;IAEvDP,WAAW,CAAC,IAAI,CAAC;IACjBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA;MACA,MAAMD,OAAO,GAAG,MAAMN,kBAAkB,CAACc,gBAAgB,CAAC;MAE1D,IAAIR,OAAO,EAAE;QACTC,UAAU,CAAC,uBAAuBO,gBAAgB,CAACI,aAAa,EAAE,CAAC;;QAEnE;QACA,IAAI3B,gBAAgB,EAAE;UAClBA,gBAAgB,CAACuB,gBAAgB,CAAC;QACtC;;QAEA;QACAK,UAAU,CAAC,MAAMZ,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACH,MAAM,IAAIa,KAAK,CAAC,sCAAsC,CAAC;MAC3D;IAEJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAAClB,KAAK,CAAC,qCAAqC,EAAEiB,GAAG,CAAC;MACzDhB,QAAQ,CAACgB,GAAG,CAACE,OAAO,IAAI,sCAAsC,CAAC;IACnE,CAAC,SAAS;MACNpB,WAAW,CAAC,KAAK,CAAC;IACtB;EACJ,CAAC;EAED,IAAIJ,eAAe,EAAE;IACjB,oBACIX,OAAA;MAAKM,SAAS,EAAE,6BAA6BA,SAAS,EAAG;MAAA8B,QAAA,eACrDpC,OAAA;QAAKM,SAAS,EAAC,qBAAqB;QAAA8B,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAEd;EAEA,oBACIxC,OAAA;IAAKM,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAAA8B,QAAA,gBAC7CpC,OAAA;MAAKM,SAAS,EAAC,iBAAiB;MAAA8B,QAAA,gBAC5BpC,OAAA;QAAOyC,OAAO,EAAC,iBAAiB;QAACnC,SAAS,EAAC,gBAAgB;QAAA8B,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACP/B,YAAY,iBACTT,OAAA;QAAMM,SAAS,EAAC,kBAAkB;QAAA8B,QAAA,GAAC,WACtB,EAAC3B,YAAY,CAACiC,eAAe,EAAC,GAAC,EAACjC,YAAY,CAACkC,aAAa;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAENxC,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAA8B,QAAA,gBAC7BpC,OAAA;QACI6B,EAAE,EAAC,iBAAiB;QACpBJ,KAAK,EAAE,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB,EAAE,KAAI,EAAG;QAC9Be,QAAQ,EAAExB,oBAAqB;QAC/Bf,QAAQ,EAAEA,QAAQ,IAAIS,QAAQ,IAAIN,UAAU,CAACqC,MAAM,KAAK,CAAE;QAC1DvC,SAAS,EAAC,iBAAiB;QAAA8B,QAAA,gBAE3BpC,OAAA;UAAQyB,KAAK,EAAC,EAAE;UAAAW,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACxChC,UAAU,CAACsC,GAAG,CAACC,QAAQ,iBACpB/C,OAAA;UAA0ByB,KAAK,EAAEsB,QAAQ,CAAClB,EAAG;UAAAO,QAAA,GACxCW,QAAQ,CAACJ,aAAa,EAAC,KAAG,EAACI,QAAQ,CAACjB,aAAa,EAAC,IAAE,EAACiB,QAAQ,CAACL,eAAe,EAAC,GACnF;QAAA,GAFaK,QAAQ,CAAClB,EAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhB,CACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAER1B,QAAQ,iBACLd,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAA8B,QAAA,eAC/BpC,OAAA;UAAGM,SAAS,EAAC;QAAwB;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAELpC,WAAW,IAAIK,YAAY,iBACxBT,OAAA;MAAKM,SAAS,EAAC,oBAAoB;MAAA8B,QAAA,gBAC/BpC,OAAA;QAAAoC,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BxC,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAA8B,QAAA,EACxB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAACU,GAAG,CAACE,MAAM,IAAI;UAC9B,MAAMC,UAAU,GAAGpC,qBAAqB,CAACmC,MAAM,CAAC;UAChD,oBACIhD,OAAA;YAAkBM,SAAS,EAAC,cAAc;YAAA8B,QAAA,gBACtCpC,OAAA;cAAMM,SAAS,EAAC,gBAAgB;cAAA8B,QAAA,GAAEY,MAAM,EAAC,KAAG;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDxC,OAAA;cAAMM,SAAS,EAAC,OAAO;cAAA8B,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChCxC,OAAA;cAAMM,SAAS,EAAC,kBAAkB;cAAA8B,QAAA,EAC7Ba,UAAU,CAACC;YAAgB;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA,GALDQ,MAAM;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CAAC;QAEd,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAxB,KAAK,iBACFhB,OAAA;MAAKM,SAAS,EAAC,wBAAwB;MAAA8B,QAAA,gBACnCpC,OAAA;QAAGM,SAAS,EAAC;MAA6B;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC9CxB,KAAK;IAAA;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAtB,OAAO,iBACJlB,OAAA;MAAKM,SAAS,EAAC,0BAA0B;MAAA8B,QAAA,gBACrCpC,OAAA;QAAGM,SAAS,EAAC;MAAqB;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACtCtB,OAAO;IAAA;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAEDxC,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAA8B,QAAA,eAC1BpC,OAAA;QAAAoC,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHAjC,EAAA,CArJMN,gBAAgB;EAAA,QAadH,WAAW;AAAA;AAAAqD,EAAA,GAbblD,gBAAgB;AAyJtB,OAAO,MAAMmD,qBAAqB,GAAGA,CAAC;EAAElD,MAAM;EAAEI,SAAS,GAAG;AAAG,CAAC,KAAK;EAAA+C,GAAA;EACjE,MAAM;IAAE7C,UAAU;IAAEC,YAAY;IAAEG;EAAmB,CAAC,GAAGd,WAAW,CAAC,CAAC;EACtE,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAMyD,iBAAiB,GAAG,MAAOjC,KAAK,IAAK;IACvC,MAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAClD,IAAI,CAACH,aAAa,IAAI,CAACpB,MAAM,EAAE;IAE/Ba,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACA,MAAMwC,MAAM,GAAG,MAAMC,4BAA4B,CAACtD,MAAM,EAAEoB,aAAa,CAAC;MACxEV,kBAAkB,CAAC2C,MAAM,CAACE,YAAY,CAAC;IAC3C,CAAC,CAAC,OAAOxB,GAAG,EAAE;MACVC,OAAO,CAAClB,KAAK,CAAC,0BAA0B,EAAEiB,GAAG,CAAC;IAClD,CAAC,SAAS;MACNlB,WAAW,CAAC,KAAK,CAAC;IACtB;EACJ,CAAC;EAED,oBACIf,OAAA;IAAKM,SAAS,EAAE,2BAA2BA,SAAS,EAAG;IAAA8B,QAAA,gBACnDpC,OAAA;MACIyB,KAAK,EAAE,CAAAhB,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB,EAAE,KAAI,EAAG;MAC9Be,QAAQ,EAAEU,iBAAkB;MAC5BjD,QAAQ,EAAES,QAAS;MACnBR,SAAS,EAAC,cAAc;MACxBoD,KAAK,EAAC,2BAA2B;MAAAtB,QAAA,EAEhC5B,UAAU,CAACsC,GAAG,CAACC,QAAQ,iBACpB/C,OAAA;QAA0ByB,KAAK,EAAEsB,QAAQ,CAAClB,EAAG;QAAAO,QAAA,GACxCW,QAAQ,CAACL,eAAe,EAAC,GAAC,EAACK,QAAQ,CAACJ,aAAa;MAAA,GADzCI,QAAQ,CAAClB,EAAE;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhB,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACR1B,QAAQ,iBAAId,OAAA;MAAGM,SAAS,EAAC;IAAsC;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpE,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHAa,GAAA,CAvCaD,qBAAqB;EAAA,QAC2BtD,WAAW;AAAA;AAAA6D,GAAA,GAD3DP,qBAAqB;AA2ClC,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEtD,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAuD,GAAA;EAAA,IAAAC,qBAAA;EAChD,MAAM;IAAErD,YAAY;IAAEsD;EAAc,CAAC,GAAGjE,WAAW,CAAC,CAAC;EAErD,IAAI,CAACW,YAAY,EAAE,OAAO,IAAI;EAE9B,MAAMuD,WAAW,GAAGD,aAAa,CAACpC,IAAI,CAACsC,IAAI,IAAIA,IAAI,CAACC,WAAW,KAAKzD,YAAY,CAACoB,EAAE,CAAC;EAEpF,oBACI7B,OAAA;IAAKM,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IAAA8B,QAAA,gBACzCpC,OAAA;MAAKM,SAAS,EAAC,aAAa;MAAA8B,QAAA,gBACxBpC,OAAA;QAAMM,SAAS,EAAC,eAAe;QAAA8B,QAAA,GAC1B3B,YAAY,CAACiC,eAAe,EAAC,GAAC,EAACjC,YAAY,CAACqB,aAAa;MAAA;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACPxC,OAAA;QAAMM,SAAS,EAAC,eAAe;QAAA8B,QAAA,GAAC,GAAC,EAAC3B,YAAY,CAACkC,aAAa,EAAC,GAAC;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,EAELwB,WAAW,iBACRhE,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAA8B,QAAA,gBAC1BpC,OAAA;QAAMM,SAAS,EAAC,YAAY;QAAA8B,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClDxC,OAAA;QAAMM,SAAS,EAAC,YAAY;QAAA8B,QAAA,GAAC,cACb,EAAC3B,YAAY,CAACiC,eAAe,GAAAoB,qBAAA,GACxCE,WAAW,CAACG,eAAe,cAAAL,qBAAA,uBAA3BA,qBAAA,CAA6BM,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAAC3D,YAAY,CAACkC,aAAa;MAAA;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR,EAEA,CAAAwB,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEK,eAAe,kBACzBrE,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAA8B,QAAA,gBACzBpC,OAAA;QAAMM,SAAS,EAAC,cAAc;QAAA8B,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnDxC,OAAA;QAAMM,SAAS,EAAC,aAAa;QAAA8B,QAAA,EACxB,IAAIkC,IAAI,CAACN,WAAW,CAACK,eAAe,CAAC,CAACE,kBAAkB,CAAC;MAAC;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACqB,GAAA,CApCWD,YAAY;EAAA,QACmB9D,WAAW;AAAA;AAAA0E,GAAA,GAD1CZ,YAAY;AAsCzB,eAAe3D,gBAAgB;AAAC,IAAAkD,EAAA,EAAAQ,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAAtB,EAAA;AAAAsB,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}