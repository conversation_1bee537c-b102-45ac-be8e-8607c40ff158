<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    error_log("Connecting to database in welcome_recent_bets.php");

    // Simple query to get recent bets
    $query = "SELECT 
        b.bet_id,
        b.amount_user1,
        b.amount_user2,
        b.bet_status,
        b.created_at as bet_date,
        b.unique_code,
        COALESCE(u1.username, 'User 1') as user1_name,
        COALESCE(u2.username, 'User 2') as user2_name,
        b.bet_choice_user1,
        b.bet_choice_user2,
        c.team_a,
        c.team_b,
        t1.logo as team_a_logo,
        t2.logo as team_b_logo,
        c.odds_team_a,
        c.odds_team_b,
        c.odds_draw
    FROM bets b
    LEFT JOIN users u1 ON b.user1_id = u1.user_id
    LEFT JOIN users u2 ON b.user2_id = u2.user_id
    LEFT JOIN challenges c ON b.challenge_id = c.challenge_id
    LEFT JOIN teams t1 ON c.team_a = t1.name
    LEFT JOIN teams t2 ON c.team_b = t2.name
    WHERE b.bet_status IN ('pending', 'joined', 'completed')
    ORDER BY b.created_at DESC
    LIMIT 6";

    error_log("Executing query: " . $query);

    $stmt = $conn->prepare($query);
    $stmt->execute();

    $bets = array();
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $bet = array(
            'bet_id' => $row['bet_id'],
            'amount_user1' => $row['amount_user1'],
            'amount_user2' => $row['amount_user2'],
            'bet_status' => $row['bet_status'],
            'bet_date' => $row['bet_date'],
            'unique_code' => $row['unique_code'],
            'user1_name' => $row['user1_name'],
            'user2_name' => $row['user2_name'],
            'bet_choice_user1' => $row['bet_choice_user1'],
            'bet_choice_user2' => $row['bet_choice_user2'],
            'team_a' => $row['team_a'],
            'team_b' => $row['team_b'],
            'team_a_logo' => $row['team_a_logo'],
            'team_b_logo' => $row['team_b_logo'],
            'odds_team_a' => $row['odds_team_a'],
            'odds_team_b' => $row['odds_team_b'],
            'odds_draw' => $row['odds_draw']
        );
        array_push($bets, $bet);
    }

    error_log("Found " . count($bets) . " bets");

    echo json_encode(array(
        'success' => true,
        'bets' => $bets
    ));

} catch(PDOException $e) {
    error_log("Database Error in welcome_recent_bets.php: " . $e->getMessage());
    echo json_encode(array(
        'success' => false,
        'message' => 'Database Error: ' . $e->getMessage()
    ));
} 