/* Custom Modal Styles */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: 20px;
    animation: fadeIn 0.2s ease-out;
}

.custom-modal {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    animation: slideIn 0.3s ease-out;
}

.custom-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 16px 24px;
    border-bottom: 1px solid #e5e7eb;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.modal-icon {
    font-size: 20px;
}

.modal-icon.success {
    color: #16a34a;
}

.modal-icon.error {
    color: #dc2626;
}

.modal-icon.warning {
    color: #d97706;
}

.modal-icon.info {
    color: #2563eb;
}

.modal-close-btn {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close-btn:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.custom-modal-body {
    padding: 16px 24px 24px 24px;
}

.modal-message {
    margin: 0;
    color: #4b5563;
    line-height: 1.6;
    font-size: 14px;
}

.custom-modal-footer {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    padding: 0 24px 24px 24px;
}

/* Button Styles */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
}

.btn-success {
    background-color: #16a34a;
    color: white;
}

.btn-success:hover {
    background-color: #15803d;
}

.btn-danger {
    background-color: #dc2626;
    color: white;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

.btn-warning {
    background-color: #d97706;
    color: white;
}

.btn-warning:hover {
    background-color: #b45309;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive Design */
@media (max-width: 640px) {
    .custom-modal {
        margin: 20px;
        max-width: calc(100vw - 40px);
    }
    
    .custom-modal-header,
    .custom-modal-body,
    .custom-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
    
    .custom-modal-footer {
        flex-direction: column-reverse;
    }
    
    .btn {
        width: 100%;
    }
}
