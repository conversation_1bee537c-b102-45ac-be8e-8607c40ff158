import { useCallback } from 'react';
import { useError } from '../contexts/ErrorContext';
import { handleError } from '../utils/errorHandler';

export const useErrorHandler = (componentName) => {
  const { addError, withErrorHandling } = useError();

  const handleError = useCallback((error, operation = '') => {
    const context = {
      component: componentName,
      operation,
      timestamp: new Date().toISOString()
    };
    
    const appError = handleError(error, context);
    addError(appError);
    return appError;
  }, [componentName, addError]);

  const withErrorHandlingCallback = useCallback(async (operation, operationName = '') => {
    return withErrorHandling(operation, {
      component: componentName,
      operation: operationName
    });
  }, [componentName, withErrorHandling]);

  return {
    handleError,
    withErrorHandling: withErrorHandlingCallback
  };
}; 