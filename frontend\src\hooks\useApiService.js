/**
 * Custom hook for API service usage
 * 
 * Provides a consistent interface for making API calls with loading states,
 * error handling, and automatic retries.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { apiService } from '../services';

/**
 * Custom hook for API operations
 * @param {object} options - Hook configuration options
 * @returns {object} API operation utilities
 */
const useApiService = (options = {}) => {
    const {
        autoRetry = true,
        maxRetries = 3,
        retryDelay = 1000,
        onError = null,
        onSuccess = null
    } = options;

    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [data, setData] = useState(null);
    const abortControllerRef = useRef(null);

    // Cleanup on unmount
    useEffect(() => {
        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
        };
    }, []);

    /**
     * Execute API operation with error handling and retries
     * @param {function} operation - API operation function
     * @param {object} operationOptions - Operation-specific options
     * @returns {Promise<any>} Operation result
     */
    const execute = useCallback(async (operation, operationOptions = {}) => {
        const {
            retries = autoRetry ? maxRetries : 0,
            delay = retryDelay,
            abortable = true
        } = operationOptions;

        // Create abort controller for cancellable requests
        if (abortable) {
            abortControllerRef.current = new AbortController();
        }

        setLoading(true);
        setError(null);

        let lastError = null;
        let attempt = 0;

        while (attempt <= retries) {
            try {
                const result = await operation(abortControllerRef.current?.signal);
                
                if (result.success) {
                    setData(result.data);
                    if (onSuccess) {
                        onSuccess(result);
                    }
                    return result;
                } else {
                    throw new Error(result.message || 'Operation failed');
                }
            } catch (err) {
                lastError = err;
                
                // Don't retry if request was aborted
                if (err.name === 'AbortError') {
                    break;
                }
                
                // Don't retry on certain error types
                if (err.response?.status === 401 || err.response?.status === 403) {
                    break;
                }
                
                attempt++;
                
                // Wait before retry (except on last attempt)
                if (attempt <= retries) {
                    await new Promise(resolve => setTimeout(resolve, delay * attempt));
                }
            }
        }

        // Handle final error
        setError(lastError);
        if (onError) {
            onError(lastError);
        }
        
        throw lastError;
    }, [autoRetry, maxRetries, retryDelay, onError, onSuccess]);

    /**
     * Cancel ongoing request
     */
    const cancel = useCallback(() => {
        if (abortControllerRef.current) {
            abortControllerRef.current.abort();
        }
    }, []);

    /**
     * Reset hook state
     */
    const reset = useCallback(() => {
        setLoading(false);
        setError(null);
        setData(null);
    }, []);

    /**
     * GET request wrapper
     */
    const get = useCallback((endpoint, params = {}, options = {}) => {
        return execute(
            (signal) => apiService.get(endpoint, params, { ...options, signal }),
            options
        );
    }, [execute]);

    /**
     * POST request wrapper
     */
    const post = useCallback((endpoint, data = {}, options = {}) => {
        return execute(
            (signal) => apiService.post(endpoint, data, { ...options, signal }),
            options
        );
    }, [execute]);

    /**
     * PUT request wrapper
     */
    const put = useCallback((endpoint, data = {}, options = {}) => {
        return execute(
            (signal) => apiService.put(endpoint, data, { ...options, signal }),
            options
        );
    }, [execute]);

    /**
     * DELETE request wrapper
     */
    const del = useCallback((endpoint, options = {}) => {
        return execute(
            (signal) => apiService.delete(endpoint, { ...options, signal }),
            options
        );
    }, [execute]);

    /**
     * Upload request wrapper
     */
    const upload = useCallback((endpoint, formData, onProgress = null, options = {}) => {
        return execute(
            (signal) => apiService.upload(endpoint, formData, onProgress, { ...options, signal }),
            options
        );
    }, [execute]);

    // Finally block to always set loading to false
    useEffect(() => {
        const originalExecute = execute;
        return () => {
            setLoading(false);
        };
    }, [execute]);

    // Set loading to false when operation completes
    const wrappedExecute = useCallback(async (...args) => {
        try {
            return await execute(...args);
        } finally {
            setLoading(false);
        }
    }, [execute]);

    return {
        // State
        loading,
        error,
        data,
        
        // Actions
        execute: wrappedExecute,
        cancel,
        reset,
        
        // HTTP methods
        get,
        post,
        put,
        delete: del,
        upload,
        
        // Utilities
        isLoading: loading,
        hasError: !!error,
        hasData: !!data
    };
};

export default useApiService;
