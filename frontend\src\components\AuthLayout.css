:root {
    --primary-green: #2c5f2d;
    --light-green: #52b788;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-600: #718096;
    --gray-800: #2d3748;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --border-radius: 12px;
    --transition: all 0.2s ease;
}

/* Main Auth Page Container */
.auth-page {
    min-height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--light-green) 100%);
    padding: 1rem;
    overflow-y: auto;
}

.auth-main-container {
    width: 100%;
    max-width: 1200px;
    display: flex;
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    min-height: 600px;
}

/* Left Column - Form */
.auth-left-column {
    flex: 1;
    min-width: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.auth-container {
    width: 100%;
    max-width: 420px;
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.brand-logo {
    display: inline-block;
    font-size: 28px;
    font-weight: bold;
    color: var(--primary-green);
    text-decoration: none;
    margin-bottom: 1rem;
    transition: var(--transition);
}

.brand-logo:hover {
    color: var(--light-green);
    transform: scale(1.05);
}

.auth-title {
    font-size: 24px;
    color: var(--gray-800);
    margin: 0 0 0.5rem;
    font-weight: 600;
}

.auth-subtitle {
    color: var(--gray-600);
    font-size: 14px;
    margin: 0;
    line-height: 1.4;
}

.auth-content {
    width: 100%;
}

/* Right Column - Image */
.auth-right-column {
    flex: 1;
    min-width: 400px;
    position: relative;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--light-green) 100%);
}

.auth-image-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.auth-image {
    /* Removed as we're using background-image on placeholder */
}

.auth-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 95, 45, 0.8) 0%, rgba(82, 183, 136, 0.8) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.auth-image-content {
    text-align: center;
    color: var(--white);
}

.auth-image-content h2 {
    font-size: 32px;
    margin-bottom: 1rem;
    font-weight: 700;
}

.auth-image-content p {
    font-size: 18px;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.auth-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.auth-feature {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 16px;
    font-weight: 500;
}

.auth-feature i {
    font-size: 20px;
    opacity: 0.9;
}

/* Form Styles */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    color: var(--gray-800);
    font-size: 13px;
    font-weight: 500;
}

.input-wrapper {
    position: relative;
    width: 100%;
}

.input-wrapper i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
    font-size: 14px;
    z-index: 1;
}

.input-wrapper input,
.input-wrapper select {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: var(--white);
    box-sizing: border-box;
}

.input-wrapper input:focus,
.input-wrapper select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1);
}

.input-wrapper input::placeholder {
    color: var(--gray-600);
}

/* Button Styles */
.auth-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-button:hover {
    background: var(--light-green);
    transform: translateY(-1px);
}

.auth-button:disabled {
    background: var(--gray-300);
    color: var(--gray-600);
    cursor: not-allowed;
    transform: none;
}

.auth-button:disabled:hover {
    background: var(--gray-300);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.forgot-password,
.auth-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
}

.forgot-password:hover,
.auth-link:hover {
    color: var(--light-green);
}

/* Footer */
.auth-footer {
    margin-top: 1.5rem;
    text-align: center;
}

.auth-footer p {
    color: var(--gray-600);
    font-size: 13px;
    margin: 0;
}

/* Error and Success Messages */
.error-message,
.success-message {
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 13px;
    text-align: center;
    font-weight: 500;
}

.error-message {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.success-message {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .auth-main-container {
        flex-direction: column;
        max-width: 500px;
    }
    
    .auth-right-column {
        min-height: 200px;
        min-width: auto;
    }
    
    .auth-left-column {
        min-width: auto;
    }
    
    .auth-image-content h2 {
        font-size: 24px;
    }
    
    .auth-image-content p {
        font-size: 16px;
    }
    
    .auth-features {
        flex-direction: row;
        justify-content: space-around;
    }
}

@media screen and (max-width: 768px) {
    .auth-page {
        padding: 0.5rem;
    }
    
    .auth-left-column {
        padding: 1.5rem;
    }
    
    .auth-container {
        max-width: 100%;
    }
    
    .auth-features {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .auth-feature {
        font-size: 14px;
    }
}

@media screen and (max-width: 480px) {
    .auth-left-column {
        padding: 1rem;
    }
    
    .auth-header {
        margin-bottom: 1.5rem;
    }
    
    .brand-logo {
        font-size: 24px;
    }
    
    .auth-title {
        font-size: 20px;
    }
    
    .form-group {
        margin-bottom: 0.75rem;
    }
}
