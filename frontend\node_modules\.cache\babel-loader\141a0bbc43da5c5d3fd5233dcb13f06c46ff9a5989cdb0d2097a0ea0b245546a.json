{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Currency\\\\CurrencySelector.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencySelector.css';\n\n/**\n * CurrencySelector Component\n * Allows users to select and update their preferred currency\n * \n * Props:\n * - userId: User ID for updating preference\n * - onCurrencyChange: Callback when currency is changed\n * - showPreview: Show conversion preview (default: true)\n * - disabled: Disable the selector (default: false)\n * - className: Additional CSS classes\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencySelector = ({\n  userId,\n  onCurrencyChange,\n  showPreview = true,\n  disabled = false,\n  className = ''\n}) => {\n  _s();\n  var _userCurrency;\n  // DISABLED: Currency system being recoded - return simple placeholder\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-selector disabled ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-message\",\n      children: \"Currency selection temporarily disabled - system being updated\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 9\n  }, this);\n\n  /* DISABLED CODE:\n  const {\n      currencies,\n      userCurrency,\n      loading: currencyLoading,\n      updateUserCurrency,\n      convertToUserCurrency\n  } = useCurrency();\n  */\n\n  const [updating, setUpdating] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const handleCurrencyChange = async event => {\n    const newCurrencyId = parseInt(event.target.value);\n    if (!newCurrencyId || !userId) return;\n\n    // Find the selected currency\n    const selectedCurrency = currencies.find(c => c.id === newCurrencyId);\n    if (!selectedCurrency) return;\n\n    // Don't update if it's the same currency\n    if (userCurrency && userCurrency.id === newCurrencyId) return;\n    setUpdating(true);\n    setError('');\n    setSuccess('');\n    try {\n      // Update currency preference via API\n      const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n\n      // Update local context\n      updateUserCurrency(result.new_currency);\n      setSuccess(`Currency updated to ${selectedCurrency.display_name}`);\n\n      // Call callback if provided\n      if (onCurrencyChange) {\n        onCurrencyChange(result.new_currency);\n      }\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(''), 3000);\n    } catch (err) {\n      console.error('Error updating currency preference:', err);\n      setError(err.message || 'Failed to update currency preference');\n    } finally {\n      setUpdating(false);\n    }\n  };\n  if (currencyLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `currency-selector loading ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-placeholder\",\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 13\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-selector ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"currency-select\",\n        className: \"selector-label\",\n        children: \"Preferred Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 17\n      }, this), userCurrency && /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"current-currency\",\n        children: [\"Current: \", userCurrency.currency_symbol, \" \", userCurrency.currency_code]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"select\", {\n        id: \"currency-select\",\n        value: ((_userCurrency = userCurrency) === null || _userCurrency === void 0 ? void 0 : _userCurrency.id) || '',\n        onChange: handleCurrencyChange,\n        disabled: disabled || updating || currencies.length === 0,\n        className: \"currency-select\",\n        children: [/*#__PURE__*/_jsxDEV(\"option\", {\n          value: \"\",\n          children: \"Select Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 21\n        }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n          value: currency.id,\n          children: currency.display_name\n        }, currency.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 25\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 17\n      }, this), updating && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"updating-indicator\",\n        children: /*#__PURE__*/_jsxDEV(\"i\", {\n          className: \"fas fa-spinner fa-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 13\n    }, this), showPreview && userCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversion-preview\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        children: \"Conversion Examples:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"preview-grid\",\n        children: [10, 50, 100, 500].map(amount => {\n          const conversion = convertToUserCurrency(amount);\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"preview-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"fancoin-amount\",\n              children: [amount, \" FC\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"arrow\",\n              children: \"\\u2192\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"converted-amount\",\n              children: conversion.formatted_amount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 37\n            }, this)]\n          }, amount, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 33\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 17\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-message error\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-exclamation-triangle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 21\n      }, this), error]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-message success\",\n      children: [/*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-check-circle\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 21\n      }, this), success]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"selector-help\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your preferred currency is used to display FanCoin amounts throughout the platform. All betting and transactions are still processed in FanCoin.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 165,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyQuickSelector Component\n * Compact currency selector for headers/sidebars\n */\n_s(CurrencySelector, \"5y6t7idIDjG/0I177lubjzBBDuw=\");\n_c = CurrencySelector;\nexport const CurrencyQuickSelector = ({\n  userId,\n  className = ''\n}) => {\n  _s2();\n  const {\n    currencies,\n    userCurrency,\n    updateUserCurrency\n  } = useCurrency();\n  const [updating, setUpdating] = useState(false);\n  const handleQuickChange = async event => {\n    const newCurrencyId = parseInt(event.target.value);\n    if (!newCurrencyId || !userId) return;\n    setUpdating(true);\n    try {\n      const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n      updateUserCurrency(result.new_currency);\n    } catch (err) {\n      console.error('Error updating currency:', err);\n    } finally {\n      setUpdating(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-quick-selector ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"select\", {\n      value: (userCurrency === null || userCurrency === void 0 ? void 0 : userCurrency.id) || '',\n      onChange: handleQuickChange,\n      disabled: updating,\n      className: \"quick-select\",\n      title: \"Change preferred currency\",\n      children: currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n        value: currency.id,\n        children: [currency.currency_symbol, \" \", currency.currency_code]\n      }, currency.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 13\n    }, this), updating && /*#__PURE__*/_jsxDEV(\"i\", {\n      className: \"fas fa-spinner fa-spin updating-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 26\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyInfo Component\n * Displays current currency information and exchange rate\n */\n_s2(CurrencyQuickSelector, \"MWORR4Z8NRwKu2S4/UYo+joRBS4=\", false, function () {\n  return [useCurrency];\n});\n_c2 = CurrencyQuickSelector;\nexport const CurrencyInfo = ({\n  className = ''\n}) => {\n  _s3();\n  var _currentRate$rate_to_;\n  const {\n    userCurrency,\n    exchangeRates\n  } = useCurrency();\n  if (!userCurrency) return null;\n  const currentRate = exchangeRates.find(rate => rate.currency_id === userCurrency.id);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-info ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"info-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-name\",\n        children: [userCurrency.currency_symbol, \" \", userCurrency.currency_name]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-code\",\n        children: [\"(\", userCurrency.currency_code, \")\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 13\n    }, this), currentRate && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"exchange-rate\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"rate-label\",\n        children: \"Exchange Rate:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"rate-value\",\n        children: [\"1 FanCoin = \", userCurrency.currency_symbol, (_currentRate$rate_to_ = currentRate.rate_to_fancoin) === null || _currentRate$rate_to_ === void 0 ? void 0 : _currentRate$rate_to_.toFixed(4), \" \", userCurrency.currency_code]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 17\n    }, this), (currentRate === null || currentRate === void 0 ? void 0 : currentRate.rate_updated_at) && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"rate-updated\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"update-label\",\n        children: \"Last Updated:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"update-time\",\n        children: new Date(currentRate.rate_updated_at).toLocaleDateString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 230,\n    columnNumber: 9\n  }, this);\n};\n_s3(CurrencyInfo, \"DJ1Qq/ktcb1uabul/de4L636KBI=\", false, function () {\n  return [useCurrency];\n});\n_c3 = CurrencyInfo;\nexport default CurrencySelector;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"CurrencySelector\");\n$RefreshReg$(_c2, \"CurrencyQuickSelector\");\n$RefreshReg$(_c3, \"CurrencyInfo\");", "map": {"version": 3, "names": ["React", "useState", "useCurrency", "jsxDEV", "_jsxDEV", "CurrencySelector", "userId", "onCurrencyChange", "showPreview", "disabled", "className", "_s", "_userCurrency", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "updating", "setUpdating", "error", "setError", "success", "setSuccess", "handleCurrencyChange", "event", "newCurrencyId", "parseInt", "target", "value", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "currencies", "find", "c", "id", "userCurrency", "result", "updateUserCurrencyPreference", "updateUserCurrency", "new_currency", "display_name", "setTimeout", "err", "console", "message", "currencyLoading", "htmlFor", "currency_symbol", "currency_code", "onChange", "length", "map", "currency", "amount", "conversion", "convertToUserCurrency", "formatted_amount", "_c", "CurrencyQuickSelector", "_s2", "handleQuickChange", "title", "_c2", "CurrencyInfo", "_s3", "_currentRate$rate_to_", "exchangeRates", "currentRate", "rate", "currency_id", "currency_name", "rate_to_fancoin", "toFixed", "rate_updated_at", "Date", "toLocaleDateString", "_c3", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Currency/CurrencySelector.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencySelector.css';\n\n/**\n * CurrencySelector Component\n * Allows users to select and update their preferred currency\n * \n * Props:\n * - userId: User ID for updating preference\n * - onCurrencyChange: Callback when currency is changed\n * - showPreview: Show conversion preview (default: true)\n * - disabled: Disable the selector (default: false)\n * - className: Additional CSS classes\n */\nconst CurrencySelector = ({\n    userId,\n    onCurrencyChange,\n    showPreview = true,\n    disabled = false,\n    className = ''\n}) => {\n    // DISABLED: Currency system being recoded - return simple placeholder\n    return (\n        <div className={`currency-selector disabled ${className}`}>\n            <div className=\"selector-message\">\n                Currency selection temporarily disabled - system being updated\n            </div>\n        </div>\n    );\n\n    /* DISABLED CODE:\n    const {\n        currencies,\n        userCurrency,\n        loading: currencyLoading,\n        updateUserCurrency,\n        convertToUserCurrency\n    } = useCurrency();\n    */\n    \n    const [updating, setUpdating] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    const handleCurrencyChange = async (event) => {\n        const newCurrencyId = parseInt(event.target.value);\n        \n        if (!newCurrencyId || !userId) return;\n        \n        // Find the selected currency\n        const selectedCurrency = currencies.find(c => c.id === newCurrencyId);\n        if (!selectedCurrency) return;\n        \n        // Don't update if it's the same currency\n        if (userCurrency && userCurrency.id === newCurrencyId) return;\n        \n        setUpdating(true);\n        setError('');\n        setSuccess('');\n        \n        try {\n            // Update currency preference via API\n            const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n            \n            // Update local context\n            updateUserCurrency(result.new_currency);\n            \n            setSuccess(`Currency updated to ${selectedCurrency.display_name}`);\n            \n            // Call callback if provided\n            if (onCurrencyChange) {\n                onCurrencyChange(result.new_currency);\n            }\n            \n            // Clear success message after 3 seconds\n            setTimeout(() => setSuccess(''), 3000);\n            \n        } catch (err) {\n            console.error('Error updating currency preference:', err);\n            setError(err.message || 'Failed to update currency preference');\n        } finally {\n            setUpdating(false);\n        }\n    };\n\n    if (currencyLoading) {\n        return (\n            <div className={`currency-selector loading ${className}`}>\n                <div className=\"loading-placeholder\">Loading currencies...</div>\n            </div>\n        );\n    }\n\n    return (\n        <div className={`currency-selector ${className}`}>\n            <div className=\"selector-header\">\n                <label htmlFor=\"currency-select\" className=\"selector-label\">\n                    Preferred Currency\n                </label>\n                {userCurrency && (\n                    <span className=\"current-currency\">\n                        Current: {userCurrency.currency_symbol} {userCurrency.currency_code}\n                    </span>\n                )}\n            </div>\n            \n            <div className=\"selector-wrapper\">\n                <select\n                    id=\"currency-select\"\n                    value={userCurrency?.id || ''}\n                    onChange={handleCurrencyChange}\n                    disabled={disabled || updating || currencies.length === 0}\n                    className=\"currency-select\"\n                >\n                    <option value=\"\">Select Currency</option>\n                    {currencies.map(currency => (\n                        <option key={currency.id} value={currency.id}>\n                            {currency.display_name}\n                        </option>\n                    ))}\n                </select>\n                \n                {updating && (\n                    <div className=\"updating-indicator\">\n                        <i className=\"fas fa-spinner fa-spin\"></i>\n                    </div>\n                )}\n            </div>\n            \n            {showPreview && userCurrency && (\n                <div className=\"conversion-preview\">\n                    <h4>Conversion Examples:</h4>\n                    <div className=\"preview-grid\">\n                        {[10, 50, 100, 500].map(amount => {\n                            const conversion = convertToUserCurrency(amount);\n                            return (\n                                <div key={amount} className=\"preview-item\">\n                                    <span className=\"fancoin-amount\">{amount} FC</span>\n                                    <span className=\"arrow\">→</span>\n                                    <span className=\"converted-amount\">\n                                        {conversion.formatted_amount}\n                                    </span>\n                                </div>\n                            );\n                        })}\n                    </div>\n                </div>\n            )}\n            \n            {error && (\n                <div className=\"selector-message error\">\n                    <i className=\"fas fa-exclamation-triangle\"></i>\n                    {error}\n                </div>\n            )}\n            \n            {success && (\n                <div className=\"selector-message success\">\n                    <i className=\"fas fa-check-circle\"></i>\n                    {success}\n                </div>\n            )}\n            \n            <div className=\"selector-help\">\n                <p>\n                    Your preferred currency is used to display FanCoin amounts throughout the platform. \n                    All betting and transactions are still processed in FanCoin.\n                </p>\n            </div>\n        </div>\n    );\n};\n\n/**\n * CurrencyQuickSelector Component\n * Compact currency selector for headers/sidebars\n */\nexport const CurrencyQuickSelector = ({ userId, className = '' }) => {\n    const { currencies, userCurrency, updateUserCurrency } = useCurrency();\n    const [updating, setUpdating] = useState(false);\n\n    const handleQuickChange = async (event) => {\n        const newCurrencyId = parseInt(event.target.value);\n        if (!newCurrencyId || !userId) return;\n\n        setUpdating(true);\n        try {\n            const result = await updateUserCurrencyPreference(userId, newCurrencyId);\n            updateUserCurrency(result.new_currency);\n        } catch (err) {\n            console.error('Error updating currency:', err);\n        } finally {\n            setUpdating(false);\n        }\n    };\n\n    return (\n        <div className={`currency-quick-selector ${className}`}>\n            <select\n                value={userCurrency?.id || ''}\n                onChange={handleQuickChange}\n                disabled={updating}\n                className=\"quick-select\"\n                title=\"Change preferred currency\"\n            >\n                {currencies.map(currency => (\n                    <option key={currency.id} value={currency.id}>\n                        {currency.currency_symbol} {currency.currency_code}\n                    </option>\n                ))}\n            </select>\n            {updating && <i className=\"fas fa-spinner fa-spin updating-icon\"></i>}\n        </div>\n    );\n};\n\n/**\n * CurrencyInfo Component\n * Displays current currency information and exchange rate\n */\nexport const CurrencyInfo = ({ className = '' }) => {\n    const { userCurrency, exchangeRates } = useCurrency();\n\n    if (!userCurrency) return null;\n\n    const currentRate = exchangeRates.find(rate => rate.currency_id === userCurrency.id);\n\n    return (\n        <div className={`currency-info ${className}`}>\n            <div className=\"info-header\">\n                <span className=\"currency-name\">\n                    {userCurrency.currency_symbol} {userCurrency.currency_name}\n                </span>\n                <span className=\"currency-code\">({userCurrency.currency_code})</span>\n            </div>\n            \n            {currentRate && (\n                <div className=\"exchange-rate\">\n                    <span className=\"rate-label\">Exchange Rate:</span>\n                    <span className=\"rate-value\">\n                        1 FanCoin = {userCurrency.currency_symbol}\n                        {currentRate.rate_to_fancoin?.toFixed(4)} {userCurrency.currency_code}\n                    </span>\n                </div>\n            )}\n            \n            {currentRate?.rate_updated_at && (\n                <div className=\"rate-updated\">\n                    <span className=\"update-label\">Last Updated:</span>\n                    <span className=\"update-time\">\n                        {new Date(currentRate.rate_updated_at).toLocaleDateString()}\n                    </span>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencySelector;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAO,wBAAwB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAC,MAAA,IAAAC,OAAA;AAWA,MAAMC,gBAAgB,GAAGA,CAAC;EACtBC,MAAM;EACNC,gBAAgB;EAChBC,WAAW,GAAG,IAAI;EAClBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG;AAChB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,aAAA;EACF;EACA,oBACIR,OAAA;IAAKM,SAAS,EAAE,8BAA8BA,SAAS,EAAG;IAAAG,QAAA,eACtDT,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAG,QAAA,EAAC;IAElC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;;EAGV;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEI,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAE1C,MAAMuB,oBAAoB,GAAG,MAAOC,KAAK,IAAK;IAC1C,MAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAElD,IAAI,CAACH,aAAa,IAAI,CAACpB,MAAM,EAAE;;IAE/B;IACA,MAAMwB,gBAAgB,GAAGC,UAAU,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,aAAa,CAAC;IACrE,IAAI,CAACI,gBAAgB,EAAE;;IAEvB;IACA,IAAIK,YAAY,IAAIA,YAAY,CAACD,EAAE,KAAKR,aAAa,EAAE;IAEvDP,WAAW,CAAC,IAAI,CAAC;IACjBE,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA;MACA,MAAMa,MAAM,GAAG,MAAMC,4BAA4B,CAAC/B,MAAM,EAAEoB,aAAa,CAAC;;MAExE;MACAY,kBAAkB,CAACF,MAAM,CAACG,YAAY,CAAC;MAEvChB,UAAU,CAAC,uBAAuBO,gBAAgB,CAACU,YAAY,EAAE,CAAC;;MAElE;MACA,IAAIjC,gBAAgB,EAAE;QAClBA,gBAAgB,CAAC6B,MAAM,CAACG,YAAY,CAAC;MACzC;;MAEA;MACAE,UAAU,CAAC,MAAMlB,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IAE1C,CAAC,CAAC,OAAOmB,GAAG,EAAE;MACVC,OAAO,CAACvB,KAAK,CAAC,qCAAqC,EAAEsB,GAAG,CAAC;MACzDrB,QAAQ,CAACqB,GAAG,CAACE,OAAO,IAAI,sCAAsC,CAAC;IACnE,CAAC,SAAS;MACNzB,WAAW,CAAC,KAAK,CAAC;IACtB;EACJ,CAAC;EAED,IAAI0B,eAAe,EAAE;IACjB,oBACIzC,OAAA;MAAKM,SAAS,EAAE,6BAA6BA,SAAS,EAAG;MAAAG,QAAA,eACrDT,OAAA;QAAKM,SAAS,EAAC,qBAAqB;QAAAG,QAAA,EAAC;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/D,CAAC;EAEd;EAEA,oBACIb,OAAA;IAAKM,SAAS,EAAE,qBAAqBA,SAAS,EAAG;IAAAG,QAAA,gBAC7CT,OAAA;MAAKM,SAAS,EAAC,iBAAiB;MAAAG,QAAA,gBAC5BT,OAAA;QAAO0C,OAAO,EAAC,iBAAiB;QAACpC,SAAS,EAAC,gBAAgB;QAAAG,QAAA,EAAC;MAE5D;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,EACPkB,YAAY,iBACT/B,OAAA;QAAMM,SAAS,EAAC,kBAAkB;QAAAG,QAAA,GAAC,WACtB,EAACsB,YAAY,CAACY,eAAe,EAAC,GAAC,EAACZ,YAAY,CAACa,aAAa;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAENb,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAG,QAAA,gBAC7BT,OAAA;QACI8B,EAAE,EAAC,iBAAiB;QACpBL,KAAK,EAAE,EAAAjB,aAAA,GAAAuB,YAAY,cAAAvB,aAAA,uBAAZA,aAAA,CAAcsB,EAAE,KAAI,EAAG;QAC9Be,QAAQ,EAAEzB,oBAAqB;QAC/Bf,QAAQ,EAAEA,QAAQ,IAAIS,QAAQ,IAAIa,UAAU,CAACmB,MAAM,KAAK,CAAE;QAC1DxC,SAAS,EAAC,iBAAiB;QAAAG,QAAA,gBAE3BT,OAAA;UAAQyB,KAAK,EAAC,EAAE;UAAAhB,QAAA,EAAC;QAAe;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACxCc,UAAU,CAACoB,GAAG,CAACC,QAAQ,iBACpBhD,OAAA;UAA0ByB,KAAK,EAAEuB,QAAQ,CAAClB,EAAG;UAAArB,QAAA,EACxCuC,QAAQ,CAACZ;QAAY,GADbY,QAAQ,CAAClB,EAAE;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEhB,CACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAERC,QAAQ,iBACLd,OAAA;QAAKM,SAAS,EAAC,oBAAoB;QAAAG,QAAA,eAC/BT,OAAA;UAAGM,SAAS,EAAC;QAAwB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAELT,WAAW,IAAI2B,YAAY,iBACxB/B,OAAA;MAAKM,SAAS,EAAC,oBAAoB;MAAAG,QAAA,gBAC/BT,OAAA;QAAAS,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7Bb,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAAG,QAAA,EACxB,CAAC,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAACsC,GAAG,CAACE,MAAM,IAAI;UAC9B,MAAMC,UAAU,GAAGC,qBAAqB,CAACF,MAAM,CAAC;UAChD,oBACIjD,OAAA;YAAkBM,SAAS,EAAC,cAAc;YAAAG,QAAA,gBACtCT,OAAA;cAAMM,SAAS,EAAC,gBAAgB;cAAAG,QAAA,GAAEwC,MAAM,EAAC,KAAG;YAAA;cAAAvC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACnDb,OAAA;cAAMM,SAAS,EAAC,OAAO;cAAAG,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChCb,OAAA;cAAMM,SAAS,EAAC,kBAAkB;cAAAG,QAAA,EAC7ByC,UAAU,CAACE;YAAgB;cAAA1C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC;UAAA,GALDoC,MAAM;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAMX,CAAC;QAEd,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAG,KAAK,iBACFhB,OAAA;MAAKM,SAAS,EAAC,wBAAwB;MAAAG,QAAA,gBACnCT,OAAA;QAAGM,SAAS,EAAC;MAA6B;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC9CG,KAAK;IAAA;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR,EAEAK,OAAO,iBACJlB,OAAA;MAAKM,SAAS,EAAC,0BAA0B;MAAAG,QAAA,gBACrCT,OAAA;QAAGM,SAAS,EAAC;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACtCK,OAAO;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACR,eAEDb,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAG,QAAA,eAC1BT,OAAA;QAAAS,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHAN,EAAA,CA/JMN,gBAAgB;AAAAoD,EAAA,GAAhBpD,gBAAgB;AAmKtB,OAAO,MAAMqD,qBAAqB,GAAGA,CAAC;EAAEpD,MAAM;EAAEI,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAiD,GAAA;EACjE,MAAM;IAAE5B,UAAU;IAAEI,YAAY;IAAEG;EAAmB,CAAC,GAAGpC,WAAW,CAAC,CAAC;EACtE,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAE/C,MAAM2D,iBAAiB,GAAG,MAAOnC,KAAK,IAAK;IACvC,MAAMC,aAAa,GAAGC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAACC,KAAK,CAAC;IAClD,IAAI,CAACH,aAAa,IAAI,CAACpB,MAAM,EAAE;IAE/Ba,WAAW,CAAC,IAAI,CAAC;IACjB,IAAI;MACA,MAAMiB,MAAM,GAAG,MAAMC,4BAA4B,CAAC/B,MAAM,EAAEoB,aAAa,CAAC;MACxEY,kBAAkB,CAACF,MAAM,CAACG,YAAY,CAAC;IAC3C,CAAC,CAAC,OAAOG,GAAG,EAAE;MACVC,OAAO,CAACvB,KAAK,CAAC,0BAA0B,EAAEsB,GAAG,CAAC;IAClD,CAAC,SAAS;MACNvB,WAAW,CAAC,KAAK,CAAC;IACtB;EACJ,CAAC;EAED,oBACIf,OAAA;IAAKM,SAAS,EAAE,2BAA2BA,SAAS,EAAG;IAAAG,QAAA,gBACnDT,OAAA;MACIyB,KAAK,EAAE,CAAAM,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAED,EAAE,KAAI,EAAG;MAC9Be,QAAQ,EAAEW,iBAAkB;MAC5BnD,QAAQ,EAAES,QAAS;MACnBR,SAAS,EAAC,cAAc;MACxBmD,KAAK,EAAC,2BAA2B;MAAAhD,QAAA,EAEhCkB,UAAU,CAACoB,GAAG,CAACC,QAAQ,iBACpBhD,OAAA;QAA0ByB,KAAK,EAAEuB,QAAQ,CAAClB,EAAG;QAAArB,QAAA,GACxCuC,QAAQ,CAACL,eAAe,EAAC,GAAC,EAACK,QAAQ,CAACJ,aAAa;MAAA,GADzCI,QAAQ,CAAClB,EAAE;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEhB,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACRC,QAAQ,iBAAId,OAAA;MAAGM,SAAS,EAAC;IAAsC;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpE,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHA0C,GAAA,CAvCaD,qBAAqB;EAAA,QAC2BxD,WAAW;AAAA;AAAA4D,GAAA,GAD3DJ,qBAAqB;AA2ClC,OAAO,MAAMK,YAAY,GAAGA,CAAC;EAAErD,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAsD,GAAA;EAAA,IAAAC,qBAAA;EAChD,MAAM;IAAE9B,YAAY;IAAE+B;EAAc,CAAC,GAAGhE,WAAW,CAAC,CAAC;EAErD,IAAI,CAACiC,YAAY,EAAE,OAAO,IAAI;EAE9B,MAAMgC,WAAW,GAAGD,aAAa,CAAClC,IAAI,CAACoC,IAAI,IAAIA,IAAI,CAACC,WAAW,KAAKlC,YAAY,CAACD,EAAE,CAAC;EAEpF,oBACI9B,OAAA;IAAKM,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IAAAG,QAAA,gBACzCT,OAAA;MAAKM,SAAS,EAAC,aAAa;MAAAG,QAAA,gBACxBT,OAAA;QAAMM,SAAS,EAAC,eAAe;QAAAG,QAAA,GAC1BsB,YAAY,CAACY,eAAe,EAAC,GAAC,EAACZ,YAAY,CAACmC,aAAa;MAAA;QAAAxD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC,eACPb,OAAA;QAAMM,SAAS,EAAC,eAAe;QAAAG,QAAA,GAAC,GAAC,EAACsB,YAAY,CAACa,aAAa,EAAC,GAAC;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC,EAELkD,WAAW,iBACR/D,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAG,QAAA,gBAC1BT,OAAA;QAAMM,SAAS,EAAC,YAAY;QAAAG,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAClDb,OAAA;QAAMM,SAAS,EAAC,YAAY;QAAAG,QAAA,GAAC,cACb,EAACsB,YAAY,CAACY,eAAe,GAAAkB,qBAAA,GACxCE,WAAW,CAACI,eAAe,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BO,OAAO,CAAC,CAAC,CAAC,EAAC,GAAC,EAACrC,YAAY,CAACa,aAAa;MAAA;QAAAlC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR,EAEA,CAAAkD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEM,eAAe,kBACzBrE,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAAG,QAAA,gBACzBT,OAAA;QAAMM,SAAS,EAAC,cAAc;QAAAG,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACnDb,OAAA;QAAMM,SAAS,EAAC,aAAa;QAAAG,QAAA,EACxB,IAAI6D,IAAI,CAACP,WAAW,CAACM,eAAe,CAAC,CAACE,kBAAkB,CAAC;MAAC;QAAA7D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAAC+C,GAAA,CApCWD,YAAY;EAAA,QACmB7D,WAAW;AAAA;AAAA0E,GAAA,GAD1Cb,YAAY;AAsCzB,eAAe1D,gBAAgB;AAAC,IAAAoD,EAAA,EAAAK,GAAA,EAAAc,GAAA;AAAAC,YAAA,CAAApB,EAAA;AAAAoB,YAAA,CAAAf,GAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}