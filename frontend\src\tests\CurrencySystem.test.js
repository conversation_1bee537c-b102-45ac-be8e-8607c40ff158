/**
 * FanBet247 Currency System Frontend Test Suite
 * 
 * Tests all currency-related React components and functionality
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import '@testing-library/jest-dom';

// Mock axios
import axios from 'axios';
jest.mock('axios');
const mockedAxios = axios;

// Import components to test
import { CurrencyProvider } from '../contexts/CurrencyContext';
import { UserProvider } from '../context/UserContext';
import { 
    CurrencyAmount, 
    CurrencyBalance, 
    CurrencyBetAmount,
    CurrencySelector,
    CurrencyQuickSelector 
} from '../components/Currency';

// Test wrapper component
const TestWrapper = ({ children }) => (
    <BrowserRouter>
        <UserProvider>
            <CurrencyProvider>
                {children}
            </CurrencyProvider>
        </UserProvider>
    </BrowserRouter>
);

// Mock data
const mockCurrencies = [
    {
        id: 1,
        currency_code: 'USD',
        currency_name: 'US Dollar',
        currency_symbol: '$',
        is_active: true,
        display_name: '$ USD - US Dollar'
    },
    {
        id: 2,
        currency_code: 'ZAR',
        currency_name: 'South African Rand',
        currency_symbol: 'R',
        is_active: true,
        display_name: 'R ZAR - South African Rand'
    }
];

const mockExchangeRates = [
    {
        currency_id: 1,
        currency_code: 'USD',
        currency_symbol: '$',
        rate_to_fancoin: 1.0,
        has_rate: true
    },
    {
        currency_id: 2,
        currency_code: 'ZAR',
        currency_symbol: 'R',
        rate_to_fancoin: 18.0,
        has_rate: true
    }
];

const mockUserCurrency = {
    id: 2,
    currency_code: 'ZAR',
    currency_name: 'South African Rand',
    currency_symbol: 'R',
    rate_to_fancoin: 18.0
};

describe('Currency System Tests', () => {
    beforeEach(() => {
        // Reset mocks
        mockedAxios.get.mockClear();
        mockedAxios.post.mockClear();
        
        // Mock localStorage
        Object.defineProperty(window, 'localStorage', {
            value: {
                getItem: jest.fn(() => '123'), // Mock user ID
                setItem: jest.fn(),
                removeItem: jest.fn(),
            },
            writable: true,
        });
    });

    describe('CurrencyAmount Component', () => {
        test('displays FanCoin amount correctly', () => {
            render(
                <TestWrapper>
                    <CurrencyAmount amount={100} showFanCoinOnly={true} />
                </TestWrapper>
            );
            
            expect(screen.getByText('100.00')).toBeInTheDocument();
            expect(screen.getByText('FanCoin')).toBeInTheDocument();
        });

        test('displays loading state', () => {
            render(
                <TestWrapper>
                    <CurrencyAmount amount={100} loading={true} />
                </TestWrapper>
            );
            
            expect(screen.getByText('Loading...')).toBeInTheDocument();
        });

        test('handles invalid amount', () => {
            render(
                <TestWrapper>
                    <CurrencyAmount amount={null} />
                </TestWrapper>
            );
            
            expect(screen.getByText('Invalid amount')).toBeInTheDocument();
        });

        test('displays different sizes correctly', () => {
            const { rerender } = render(
                <TestWrapper>
                    <CurrencyAmount amount={100} size="small" showFanCoinOnly={true} />
                </TestWrapper>
            );
            
            let element = screen.getByText('100.00').closest('.currency-amount');
            expect(element).toHaveClass('small');

            rerender(
                <TestWrapper>
                    <CurrencyAmount amount={100} size="large" showFanCoinOnly={true} />
                </TestWrapper>
            );
            
            element = screen.getByText('100.00').closest('.currency-amount');
            expect(element).toHaveClass('large');
        });
    });

    describe('CurrencyBalance Component', () => {
        test('renders with correct styling', () => {
            render(
                <TestWrapper>
                    <CurrencyBalance amount={500} />
                </TestWrapper>
            );
            
            const element = screen.getByText('500.00').closest('.currency-amount');
            expect(element).toHaveClass('currency-balance');
        });
    });

    describe('CurrencyBetAmount Component', () => {
        test('renders with bet-specific styling', () => {
            render(
                <TestWrapper>
                    <CurrencyBetAmount amount={50} />
                </TestWrapper>
            );
            
            const element = screen.getByText('50.00').closest('.currency-amount');
            expect(element).toHaveClass('currency-bet-amount');
        });
    });

    describe('CurrencySelector Component', () => {
        beforeEach(() => {
            // Mock API responses
            mockedAxios.get.mockImplementation((url) => {
                if (url.includes('get_currencies.php')) {
                    return Promise.resolve({
                        data: {
                            success: true,
                            data: { currencies: mockCurrencies }
                        }
                    });
                }
                if (url.includes('get_user_currency_preference.php')) {
                    return Promise.resolve({
                        data: {
                            success: true,
                            data: { preferred_currency: mockUserCurrency }
                        }
                    });
                }
                return Promise.reject(new Error('Unknown endpoint'));
            });

            mockedAxios.post.mockImplementation((url) => {
                if (url.includes('update_user_currency_preference.php')) {
                    return Promise.resolve({
                        data: {
                            success: true,
                            data: { new_currency: mockUserCurrency }
                        }
                    });
                }
                return Promise.reject(new Error('Unknown endpoint'));
            });
        });

        test('renders currency selector with options', async () => {
            render(
                <TestWrapper>
                    <CurrencySelector userId={123} />
                </TestWrapper>
            );

            await waitFor(() => {
                expect(screen.getByLabelText('Preferred Currency')).toBeInTheDocument();
            });

            // Check if select element is present
            const selectElement = screen.getByRole('combobox');
            expect(selectElement).toBeInTheDocument();
        });

        test('shows conversion preview when enabled', async () => {
            render(
                <TestWrapper>
                    <CurrencySelector userId={123} showPreview={true} />
                </TestWrapper>
            );

            await waitFor(() => {
                expect(screen.getByText('Conversion Examples:')).toBeInTheDocument();
            });
        });

        test('handles currency change', async () => {
            const onCurrencyChange = jest.fn();
            
            render(
                <TestWrapper>
                    <CurrencySelector 
                        userId={123} 
                        onCurrencyChange={onCurrencyChange}
                    />
                </TestWrapper>
            );

            await waitFor(() => {
                const selectElement = screen.getByRole('combobox');
                fireEvent.change(selectElement, { target: { value: '1' } });
            });

            // Wait for API call to complete
            await waitFor(() => {
                expect(mockedAxios.post).toHaveBeenCalledWith(
                    expect.stringContaining('update_user_currency_preference.php'),
                    expect.objectContaining({
                        user_id: 123,
                        currency_id: 1
                    })
                );
            });
        });
    });

    describe('CurrencyQuickSelector Component', () => {
        beforeEach(() => {
            mockedAxios.get.mockImplementation(() => 
                Promise.resolve({
                    data: {
                        success: true,
                        data: { currencies: mockCurrencies }
                    }
                })
            );
        });

        test('renders compact selector', async () => {
            render(
                <TestWrapper>
                    <CurrencyQuickSelector userId={123} />
                </TestWrapper>
            );

            await waitFor(() => {
                const selectElement = screen.getByRole('combobox');
                expect(selectElement).toHaveClass('quick-select');
            });
        });
    });

    describe('Currency Context Integration', () => {
        test('provides currency data to components', async () => {
            mockedAxios.get.mockImplementation((url) => {
                if (url.includes('get_currencies.php')) {
                    return Promise.resolve({
                        data: {
                            success: true,
                            data: { currencies: mockCurrencies }
                        }
                    });
                }
                if (url.includes('get_exchange_rates.php')) {
                    return Promise.resolve({
                        data: {
                            success: true,
                            data: { exchange_rates: mockExchangeRates }
                        }
                    });
                }
                return Promise.reject(new Error('Unknown endpoint'));
            });

            const TestComponent = () => {
                const { currencies, loading } = require('../contexts/CurrencyContext').useCurrency();
                
                if (loading) return <div>Loading currencies...</div>;
                
                return (
                    <div>
                        {currencies.map(currency => (
                            <div key={currency.id}>{currency.currency_code}</div>
                        ))}
                    </div>
                );
            };

            render(
                <TestWrapper>
                    <TestComponent />
                </TestWrapper>
            );

            // Should show loading initially
            expect(screen.getByText('Loading currencies...')).toBeInTheDocument();

            // Wait for currencies to load
            await waitFor(() => {
                expect(screen.getByText('USD')).toBeInTheDocument();
                expect(screen.getByText('ZAR')).toBeInTheDocument();
            });
        });
    });

    describe('Error Handling', () => {
        test('handles API errors gracefully', async () => {
            mockedAxios.get.mockRejectedValue(new Error('Network error'));

            render(
                <TestWrapper>
                    <CurrencySelector userId={123} />
                </TestWrapper>
            );

            await waitFor(() => {
                expect(screen.getByText(/Failed to load currencies/)).toBeInTheDocument();
            });
        });

        test('shows fallback for invalid currency data', () => {
            render(
                <TestWrapper>
                    <CurrencyAmount amount="invalid" />
                </TestWrapper>
            );

            expect(screen.getByText('Invalid amount')).toBeInTheDocument();
        });
    });

    describe('Responsive Design', () => {
        test('adapts to mobile viewport', () => {
            // Mock mobile viewport
            Object.defineProperty(window, 'innerWidth', {
                writable: true,
                configurable: true,
                value: 375,
            });

            render(
                <TestWrapper>
                    <CurrencyBalance amount={100} />
                </TestWrapper>
            );

            // Component should render without errors on mobile
            expect(screen.getByText('100.00')).toBeInTheDocument();
        });
    });

    describe('Performance', () => {
        test('memoizes currency conversions', () => {
            const { rerender } = render(
                <TestWrapper>
                    <CurrencyAmount amount={100} />
                </TestWrapper>
            );

            // Re-render with same props
            rerender(
                <TestWrapper>
                    <CurrencyAmount amount={100} />
                </TestWrapper>
            );

            // Should not cause additional API calls
            expect(mockedAxios.get).toHaveBeenCalledTimes(2); // Initial currencies and exchange rates
        });
    });
});

// Integration tests
describe('Currency System Integration', () => {
    test('complete user flow: registration with currency selection', async () => {
        // Mock successful registration
        mockedAxios.post.mockResolvedValue({
            data: {
                success: true,
                data: {
                    user_id: 123,
                    preferred_currency: mockUserCurrency
                }
            }
        });

        mockedAxios.get.mockResolvedValue({
            data: {
                success: true,
                data: { currencies: mockCurrencies }
            }
        });

        // This would be a more complex test involving the actual registration form
        // For now, we'll test that the components can work together
        render(
            <TestWrapper>
                <div>
                    <CurrencySelector userId={123} />
                    <CurrencyBalance amount={100} />
                </div>
            </TestWrapper>
        );

        await waitFor(() => {
            expect(screen.getByLabelText('Preferred Currency')).toBeInTheDocument();
            expect(screen.getByText('100.00')).toBeInTheDocument();
        });
    });
});

export default {};
