import React from 'react';
import { Link } from 'react-router-dom';
import './UserAuthLayout.css';

const UserAuthLayout = ({
    title,
    subtitle,
    children,
    variant = 'login' // 'login' or 'registration'
}) => {
    return (
        <div className={`fullwidth-auth-page ${variant}`}>
            {/* Header */}
            <header className="fullwidth-auth-header">
                <div className="fullwidth-auth-header-content">
                    <Link to="/" className="fullwidth-auth-logo">
                        FanBet247
                    </Link>
                </div>
            </header>

            {/* Main Content */}
            <main className="fullwidth-auth-main">
                <div className="fullwidth-auth-content">
                    <div className="fullwidth-auth-form-section">
                        <div className="fullwidth-auth-form-container">
                            <div className="fullwidth-auth-title-section">
                                <h1 className="fullwidth-auth-title">{title}</h1>
                                {subtitle && (
                                    <p className="fullwidth-auth-subtitle">{subtitle}</p>
                                )}
                            </div>

                            <div className="fullwidth-auth-form-content">
                                {children}
                            </div>
                        </div>
                    </div>
                </div>
            </main>

            {/* Footer */}
            <footer className="fullwidth-auth-footer">
                <div className="fullwidth-auth-footer-content">
                    <p>&copy; 2024 FanBet247. All rights reserved.</p>
                </div>
            </footer>
        </div>
    );
};

export default UserAuthLayout;
