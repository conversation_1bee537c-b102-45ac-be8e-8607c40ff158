import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaShieldAlt, FaCheck, FaTimes, FaSave, FaKey, FaUserShield, FaExclamationTriangle, FaInfoCircle } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function SecuritySettings() {
    const [settings, setSettings] = useState({
        enable_2fa: 'false',
        allowed_auth_methods: 'email_otp,google_auth',
        otp_expiry_time: '300',
        max_otp_attempts: '3',
        lockout_time: '1800',
        password_min_length: '8',
        require_special_chars: 'true',
        session_timeout: '3600',
        max_login_attempts: '5'
    });

    // Admin authentication settings
    const [adminAuthSettings, setAdminAuthSettings] = useState({
        admin_auth_method: 'password_only',
        admin_otp_enabled: 'false',
        admin_2fa_enabled: 'false',
        admin_otp_expiry_time: '300',
        admin_max_otp_attempts: '3',
        admin_max_login_attempts: '5',
        admin_lockout_time: '1800',
        admin_require_2fa_for: 'login,password_change',
        admin_backup_codes_count: '10',
        admin_session_timeout: '3600'
    });

    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [adminAuthAvailable, setAdminAuthAvailable] = useState(false);
    const [smtpConfigured, setSmtpConfigured] = useState(false);
    const [adminStats, setAdminStats] = useState({});

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);

            // Fetch regular security settings
            const response = await axios.get(`${API_BASE_URL}/handlers/get_security_settings.php`);

            if (response.data.success && response.data.settings) {
                const settingsData = {};
                Object.keys(response.data.settings).forEach(key => {
                    settingsData[key] = response.data.settings[key].value;
                });
                setSettings(settingsData);

                // Check if admin auth settings are available
                if (response.data.admin_auth_available && response.data.admin_auth_settings) {
                    const adminSettingsData = {};
                    Object.keys(response.data.admin_auth_settings).forEach(key => {
                        adminSettingsData[key] = response.data.admin_auth_settings[key].value;
                    });
                    setAdminAuthSettings(adminSettingsData);
                    setAdminAuthAvailable(true);
                } else {
                    // Try to fetch admin auth settings separately
                    try {
                        const adminResponse = await axios.get(`${API_BASE_URL}/handlers/get_admin_auth_settings.php`);
                        if (adminResponse.data.success) {
                            setAdminAuthSettings(adminResponse.data.settings);
                            setAdminAuthAvailable(adminResponse.data.table_exists);
                            setSmtpConfigured(adminResponse.data.smtp_configured);
                            setAdminStats(adminResponse.data.admin_stats || {});
                        }
                    } catch (adminErr) {
                        console.log('Admin auth settings not available yet');
                        setAdminAuthAvailable(false);
                    }
                }
            }
        } catch (err) {
            setError('Failed to load security settings');
            console.error('Error fetching settings:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value
        }));
    };

    const handleAdminAuthChange = (e) => {
        const { name, value, type, checked } = e.target;
        setAdminAuthSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');

        try {
            setSaving(true);

            // Prepare data for submission
            const submitData = {
                settings: settings
            };

            // Include admin auth settings if available
            if (adminAuthAvailable) {
                submitData.admin_auth_settings = adminAuthSettings;
            }

            const response = await axios.post(`${API_BASE_URL}/handlers/update_security_settings.php`, submitData);

            if (response.data.success) {
                setSuccess('Security settings saved successfully!');

                // Show SMTP warning if OTP is enabled but SMTP not configured
                if (response.data.smtp_warning) {
                    setError(response.data.smtp_warning);
                }

                setTimeout(() => {
                    setSuccess('');
                    setError('');
                }, 5000);
            } else {
                setError(response.data.message || 'Failed to save security settings');
            }
        } catch (err) {
            setError('Failed to save security settings');
            console.error('Error saving settings:', err);
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading security settings...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                    <FaShieldAlt className="text-blue-500" />
                    Security Settings
                </h1>
                <p className="text-gray-600 mt-2">
                    Configure two-factor authentication (2FA) options and security features for both users and administrators.
                </p>
                {!adminAuthAvailable && (
                    <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3">
                        <FaInfoCircle className="text-blue-500 mt-0.5" />
                        <div>
                            <p className="text-blue-800 font-medium">Enhanced Admin Authentication Available</p>
                            <p className="text-blue-700 text-sm mt-1">
                                To enable advanced admin authentication features (OTP & 2FA), please run the database setup first.
                            </p>
                        </div>
                    </div>
                )}
            </div>

            {/* Alerts */}
            {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <FaTimes className="text-red-500" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                    <FaCheck className="text-green-500" />
                    <span className="text-green-700">{success}</span>
                </div>
            )}

            {/* Settings Form */}
            <div className="bg-white rounded-lg shadow-sm border">
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    {/* Two-Factor Authentication */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Two-Factor Authentication</h2>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="enable_2fa"
                                    name="enable_2fa"
                                    checked={settings.enable_2fa === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="enable_2fa" className="ml-2 block text-sm text-gray-900">
                                    Enable Two-Factor Authentication for users
                                </label>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Allowed Authentication Methods
                                </label>
                                <input
                                    type="text"
                                    name="allowed_auth_methods"
                                    value={settings.allowed_auth_methods}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="email_otp,google_auth"
                                />
                                <p className="text-xs text-gray-500 mt-1">Comma-separated list of allowed methods</p>
                            </div>
                        </div>
                    </div>

                    {/* OTP Settings */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">OTP Settings</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    OTP Expiry Time (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="otp_expiry_time"
                                    value={settings.otp_expiry_time}
                                    onChange={handleInputChange}
                                    min="60"
                                    max="3600"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Max OTP Attempts
                                </label>
                                <input
                                    type="number"
                                    name="max_otp_attempts"
                                    value={settings.max_otp_attempts}
                                    onChange={handleInputChange}
                                    min="1"
                                    max="10"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Password Security */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Password Security</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Minimum Password Length
                                </label>
                                <input
                                    type="number"
                                    name="password_min_length"
                                    value={settings.password_min_length}
                                    onChange={handleInputChange}
                                    min="6"
                                    max="50"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                            
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="require_special_chars"
                                    name="require_special_chars"
                                    checked={settings.require_special_chars === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="require_special_chars" className="ml-2 block text-sm text-gray-900">
                                    Require special characters in passwords
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Login Security */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Login Security</h2>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Max Login Attempts
                                </label>
                                <input
                                    type="number"
                                    name="max_login_attempts"
                                    value={settings.max_login_attempts}
                                    onChange={handleInputChange}
                                    min="1"
                                    max="20"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Lockout Time (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="lockout_time"
                                    value={settings.lockout_time}
                                    onChange={handleInputChange}
                                    min="300"
                                    max="86400"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Session Timeout (seconds)
                                </label>
                                <input
                                    type="number"
                                    name="session_timeout"
                                    value={settings.session_timeout}
                                    onChange={handleInputChange}
                                    min="300"
                                    max="86400"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Admin Authentication Security */}
                    {adminAuthAvailable && (
                        <div className="border-t pt-6">
                            <div className="flex items-center gap-3 mb-6">
                                <FaUserShield className="text-red-500 text-xl" />
                                <div>
                                    <h2 className="text-lg font-semibold text-gray-800">Admin Authentication Security</h2>
                                    <p className="text-sm text-gray-600">Configure enhanced authentication methods for admin accounts</p>
                                </div>
                            </div>

                            {/* SMTP Warning */}
                            {!smtpConfigured && (
                                <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start gap-3">
                                    <FaExclamationTriangle className="text-yellow-500 mt-0.5" />
                                    <div>
                                        <p className="text-yellow-800 font-medium">SMTP Configuration Required</p>
                                        <p className="text-yellow-700 text-sm mt-1">
                                            OTP authentication requires SMTP to be configured. Please set up email settings first.
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Admin Stats */}
                            {adminStats.total_admins && (
                                <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div className="flex items-center gap-2 mb-2">
                                        <FaInfoCircle className="text-blue-500" />
                                        <span className="font-medium text-blue-800">Current Admin Status</span>
                                    </div>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                        <div>
                                            <span className="text-blue-600">Total Admins:</span>
                                            <span className="ml-2 font-medium">{adminStats.total_admins}</span>
                                        </div>
                                        <div>
                                            <span className="text-blue-600">Password Only:</span>
                                            <span className="ml-2 font-medium">{adminStats.password_only || 0}</span>
                                        </div>
                                        <div>
                                            <span className="text-blue-600">OTP Enabled:</span>
                                            <span className="ml-2 font-medium">{adminStats.otp_enabled || 0}</span>
                                        </div>
                                        <div>
                                            <span className="text-blue-600">2FA Enabled:</span>
                                            <span className="ml-2 font-medium">{adminStats.tfa_enabled || 0}</span>
                                        </div>
                                    </div>
                                </div>
                            )}

                            {/* Authentication Method Selection */}
                            <div className="space-y-6">
                                <div>
                                    <h3 className="text-md font-medium text-gray-800 mb-4">Authentication Methods</h3>
                                    <div className="space-y-4">
                                        <div className="flex items-center">
                                            <input
                                                type="checkbox"
                                                id="admin_otp_enabled"
                                                name="admin_otp_enabled"
                                                checked={adminAuthSettings.admin_otp_enabled === 'true'}
                                                onChange={handleAdminAuthChange}
                                                disabled={!smtpConfigured}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                                            />
                                            <label htmlFor="admin_otp_enabled" className="ml-2 block text-sm text-gray-900">
                                                Enable OTP (One-Time Password) via Email
                                                {!smtpConfigured && <span className="text-red-500 ml-1">(Requires SMTP)</span>}
                                            </label>
                                        </div>

                                        <div className="flex items-center">
                                            <input
                                                type="checkbox"
                                                id="admin_2fa_enabled"
                                                name="admin_2fa_enabled"
                                                checked={adminAuthSettings.admin_2fa_enabled === 'true'}
                                                onChange={handleAdminAuthChange}
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                            />
                                            <label htmlFor="admin_2fa_enabled" className="ml-2 block text-sm text-gray-900">
                                                Enable 2FA (Two-Factor Authentication) via Google Authenticator
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                {/* OTP Configuration */}
                                {adminAuthSettings.admin_otp_enabled === 'true' && (
                                    <div>
                                        <h3 className="text-md font-medium text-gray-800 mb-4">OTP Configuration</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    OTP Expiry Time (seconds)
                                                </label>
                                                <input
                                                    type="number"
                                                    name="admin_otp_expiry_time"
                                                    value={adminAuthSettings.admin_otp_expiry_time}
                                                    onChange={handleAdminAuthChange}
                                                    min="60"
                                                    max="3600"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">Default: 300 seconds (5 minutes)</p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Max OTP Attempts
                                                </label>
                                                <input
                                                    type="number"
                                                    name="admin_max_otp_attempts"
                                                    value={adminAuthSettings.admin_max_otp_attempts}
                                                    onChange={handleAdminAuthChange}
                                                    min="1"
                                                    max="10"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">Attempts before account lockout</p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Lockout Time (seconds)
                                                </label>
                                                <input
                                                    type="number"
                                                    name="admin_lockout_time"
                                                    value={adminAuthSettings.admin_lockout_time}
                                                    onChange={handleAdminAuthChange}
                                                    min="300"
                                                    max="86400"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">Default: 1800 seconds (30 minutes)</p>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* 2FA Configuration */}
                                {adminAuthSettings.admin_2fa_enabled === 'true' && (
                                    <div>
                                        <h3 className="text-md font-medium text-gray-800 mb-4">2FA Configuration</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Backup Codes Count
                                                </label>
                                                <input
                                                    type="number"
                                                    name="admin_backup_codes_count"
                                                    value={adminAuthSettings.admin_backup_codes_count}
                                                    onChange={handleAdminAuthChange}
                                                    min="5"
                                                    max="20"
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">Number of backup codes to generate</p>
                                            </div>

                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Require 2FA For
                                                </label>
                                                <input
                                                    type="text"
                                                    name="admin_require_2fa_for"
                                                    value={adminAuthSettings.admin_require_2fa_for}
                                                    onChange={handleAdminAuthChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                                    placeholder="login,password_change"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">Comma-separated list of actions</p>
                                            </div>
                                        </div>
                                    </div>
                                )}

                                {/* General Admin Security */}
                                <div>
                                    <h3 className="text-md font-medium text-gray-800 mb-4">General Admin Security</h3>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Max Login Attempts
                                            </label>
                                            <input
                                                type="number"
                                                name="admin_max_login_attempts"
                                                value={adminAuthSettings.admin_max_login_attempts}
                                                onChange={handleAdminAuthChange}
                                                min="1"
                                                max="20"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Failed attempts before lockout</p>
                                        </div>

                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Session Timeout (seconds)
                                            </label>
                                            <input
                                                type="number"
                                                name="admin_session_timeout"
                                                value={adminAuthSettings.admin_session_timeout}
                                                onChange={handleAdminAuthChange}
                                                min="300"
                                                max="86400"
                                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            />
                                            <p className="text-xs text-gray-500 mt-1">Default: 3600 seconds (1 hour)</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Setup Instructions */}
                                <div className="bg-gray-50 rounded-lg p-4">
                                    <h3 className="text-md font-medium text-gray-800 mb-3 flex items-center gap-2">
                                        <FaKey className="text-gray-600" />
                                        Setup Instructions
                                    </h3>
                                    <div className="space-y-2 text-sm text-gray-700">
                                        <p><strong>For OTP Authentication:</strong></p>
                                        <ul className="list-disc list-inside ml-4 space-y-1">
                                            <li>Ensure SMTP settings are configured in Email Settings</li>
                                            <li>Enable OTP authentication above</li>
                                            <li>Admins will receive OTP codes via email during login</li>
                                        </ul>

                                        <p className="mt-3"><strong>For 2FA Authentication:</strong></p>
                                        <ul className="list-disc list-inside ml-4 space-y-1">
                                            <li>Enable 2FA authentication above</li>
                                            <li>Each admin must set up Google Authenticator individually</li>
                                            <li>Admins can access 2FA setup from their profile settings</li>
                                            <li>Backup codes will be generated for account recovery</li>
                                        </ul>

                                        <p className="mt-3 text-yellow-700 bg-yellow-50 p-2 rounded">
                                            <strong>Important:</strong> Test the authentication methods thoroughly before enforcing them for all admins.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Submit Button */}
                    <div className="flex justify-end pt-6 border-t">
                        <button
                            type="submit"
                            disabled={saving}
                            className="flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            <FaSave />
                            {saving ? 'Saving...' : 'Save Security Settings'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default SecuritySettings;
