import React, { useState } from 'react';
import axios from '../utils/axiosConfig';
import { useNavigate } from 'react-router-dom';
import UserAuthLayout from '../components/UserAuthLayout';
import '../styles/UserAuth.css';

const TestLogin = () => {
    const [formData, setFormData] = useState({
        usernameOrEmail: '',
        password: ''
    });
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);
    const navigate = useNavigate();

    const handleChange = (e) => {
        setFormData({
            ...formData,
            [e.target.name]: e.target.value
        });
        setError(''); // Clear error when user types
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            console.log('🔄 Attempting login with:', formData.usernameOrEmail);
            
            const response = await axios.post('simple_user_login.php', formData);
            
            console.log('✅ Login response:', response.data);

            if (response.data.success) {
                // Store user data in localStorage in the format expected by ProtectedRoute
                localStorage.setItem('userId', response.data.user.user_id);
                localStorage.setItem('user', JSON.stringify(response.data.user));
                localStorage.setItem('isLoggedIn', 'true');
                localStorage.setItem('username', response.data.user.username);

                console.log('🎯 User data stored:', response.data.user);
                console.log('🎯 Redirecting to UserDashboard...');

                // Redirect to UserDashboard
                navigate('/user-dashboard');
            } else {
                setError(response.data.message || 'Login failed');
            }
        } catch (error) {
            console.error('❌ Login error:', error);
            setError('Login failed. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const testCredentials = [
        { username: 'demohomexx', password: 'loving12' },
        { username: 'testuser', password: 'testpass123' },
        { username: 'lilwayne', password: 'loving12' },
        { username: 'jameslink01', password: 'loving12' },
        { username: 'Bobyanka01', password: 'loving12' }
    ];

    const fillTestCredentials = (username, password) => {
        setFormData({ usernameOrEmail: username, password: password });
        setError('');
    };

    return (
        <UserAuthLayout>
            <div className="auth-container">
                <div className="auth-header">
                    <h1>Test Login</h1>
                    <p>Simple login system for testing UserDashboard redirect</p>
                </div>

                <div className="auth-form-container">
                    {error && (
                        <div className="error-message">
                            {error}
                        </div>
                    )}

                    <form onSubmit={handleSubmit} className="auth-form">
                        <div className="form-group">
                            <label>Email or Username</label>
                            <div className="input-container">
                                <input
                                    type="text"
                                    name="usernameOrEmail"
                                    value={formData.usernameOrEmail}
                                    onChange={handleChange}
                                    placeholder="Enter your email or username"
                                    required
                                />
                            </div>
                        </div>

                        <div className="form-group">
                            <label>Password</label>
                            <div className="input-container">
                                <input
                                    type="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    placeholder="Enter your password"
                                    required
                                />
                            </div>
                        </div>

                        <button 
                            type="submit" 
                            className="auth-button"
                            disabled={loading}
                        >
                            {loading ? 'Signing In...' : 'Sign In'}
                        </button>
                    </form>

                    <div className="test-credentials">
                        <h3>Test Credentials (Click to Fill)</h3>
                        <div className="credentials-grid">
                            {testCredentials.map((cred, index) => (
                                <button
                                    key={index}
                                    type="button"
                                    className="credential-button"
                                    onClick={() => fillTestCredentials(cred.username, cred.password)}
                                >
                                    <strong>{cred.username}</strong>
                                    <br />
                                    <small>{cred.password}</small>
                                </button>
                            ))}
                        </div>
                    </div>

                    <div className="auth-links">
                        <p>
                            Want to test the original login? 
                            <a href="/login"> Go to Original Login</a>
                        </p>
                    </div>
                </div>
            </div>

            <style jsx>{`
                .test-credentials {
                    margin-top: 2rem;
                    padding: 1rem;
                    background: #f8f9fa;
                    border-radius: 8px;
                    border: 1px solid #e9ecef;
                }

                .test-credentials h3 {
                    margin: 0 0 1rem 0;
                    color: #495057;
                    font-size: 1rem;
                }

                .credentials-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 0.5rem;
                }

                .credential-button {
                    padding: 0.75rem;
                    background: white;
                    border: 1px solid #dee2e6;
                    border-radius: 4px;
                    cursor: pointer;
                    transition: all 0.2s;
                    text-align: center;
                }

                .credential-button:hover {
                    background: #e9ecef;
                    border-color: #adb5bd;
                }

                .credential-button strong {
                    color: #495057;
                }

                .credential-button small {
                    color: #6c757d;
                }
            `}</style>
        </UserAuthLayout>
    );
};

export default TestLogin;
