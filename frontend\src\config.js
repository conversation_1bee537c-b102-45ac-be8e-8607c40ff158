// Dynamic API Configuration - Auto-detects correct paths
const isDevelopment = process.env.NODE_ENV === 'development' ||
                     window.location.hostname === 'localhost' ||
                     window.location.hostname === '127.0.0.1';

// Auto-detect the project folder name from the current URL
const getProjectPath = () => {
    // If we have an environment variable, use it
    if (process.env.REACT_APP_PROJECT_PATH) {
        return process.env.REACT_APP_PROJECT_PATH;
    }

    // Auto-detect from current URL path
    const currentPath = window.location.pathname;

    // If we're in development and using React dev server
    if (isDevelopment && window.location.port === '3000') {
        // Check if there's a proxy configuration or use default
        return process.env.REACT_APP_BACKEND_PATH || '/FanBet247';
    }

    // For production, extract project folder from current path
    const pathParts = currentPath.split('/').filter(part => part);
    if (pathParts.length > 0) {
        return '/' + pathParts[0];
    }

    // Default fallback
    return '';
};

// Dynamic API URL configuration
const getApiBaseUrl = () => {
    // Priority 1: Environment variable (highest priority)
    if (process.env.REACT_APP_API_BASE_URL) {
        console.log('🔧 Using API_BASE_URL from environment:', process.env.REACT_APP_API_BASE_URL);
        return process.env.REACT_APP_API_BASE_URL;
    }

    // Priority 2: Auto-detect based on environment
    const projectPath = getProjectPath();

    if (isDevelopment) {
        // In development, use the proxy path - FIXED: Remove /handlers duplication
        const apiUrl = `${projectPath}/backend`;
        console.log('🔧 Auto-detected development API URL:', apiUrl);
        return apiUrl;
    } else {
        // In production, construct the full URL - FIXED: Remove /handlers duplication
        const apiUrl = `${window.location.origin}${projectPath}/backend`;
        console.log('🔧 Auto-detected production API URL:', apiUrl);
        return apiUrl;
    }
};

export const API_BASE_URL = getApiBaseUrl();

// Log API configuration for debugging
console.log('🔧 Dynamic API Configuration:');
console.log('Environment:', process.env.NODE_ENV);
console.log('isDevelopment:', isDevelopment);
console.log('Current URL:', window.location.href);
console.log('Project Path:', getProjectPath());
console.log('API_BASE_URL:', API_BASE_URL);

// Asset URLs with dynamic path detection
export const getAssetUrl = (path) => {
    const projectPath = getProjectPath();
    const baseUrl = process.env.REACT_APP_ASSET_BASE_URL ||
                   window.location.origin + projectPath;
    return baseUrl + path;
};