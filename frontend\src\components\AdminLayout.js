import React, { useState } from 'react';
import { Outlet, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import AdminHeader from './AdminHeader';
import AdminFooter from './AdminFooter';
import AlertMessage from './AlertMessage';
import './AdminLayout.css';

const AdminLayout = () => {
  const navigate = useNavigate();
  const [notification, setNotification] = useState(null);

  const handleLogout = () => {
    // Show notification
    setNotification({
      message: 'Successfully logged out',
      type: 'success'
    });

    // Clear admin data from localStorage
    setTimeout(() => {
      localStorage.removeItem('adminId');
      localStorage.removeItem('adminUsername');
      localStorage.removeItem('adminRole');
      navigate('/admin/login');
    }, 1500);
  };

  return (
    <div className="admin-layout">
      {notification && (
        <div className="notification-container">
          <AlertMessage
            message={notification.message}
            type={notification.type}
            onClose={() => setNotification(null)}
          />
        </div>
      )}
      <Sidebar />
      <div className="main-wrapper">
        <AdminHeader
          onLogout={handleLogout}
        />
        <div className="content">
          <main>
            <Outlet />
          </main>
        </div>
        <AdminFooter />
      </div>
    </div>
  );
};

export default AdminLayout;
