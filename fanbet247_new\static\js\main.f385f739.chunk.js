(window.webpackJsonp=window.webpackJsonp||[]).push([[0],[,,,,,,,,,,,,function(e,a,t){},,,function(e,a,t){},,,,function(e,a,t){},function(e,a,t){},,function(e,a,t){e.exports=t(81)},,,,,,function(e,a,t){},,,,,function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},,,function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){},function(e,a,t){"use strict";t.r(a);var l=t(0),n=t.n(l),s=t(21),r=t.n(s),c=(t(28),t(5)),m=t(2),o=t(82);o.a.defaults.withCredentials=!0,o.a.defaults.headers.common["X-Requested-With"]="XMLHttpRequest",o.a.interceptors.request.use(e=>{if(e.headers.Authorization)console.log("Using custom Authorization header:",e.url);else{const a=localStorage.getItem("userToken");a?(e.headers.Authorization=`Bearer ${a}`,console.log("Added auth token to request:",e.url)):console.log("No auth token found for request:",e.url)}return e},e=>(console.error("Request interceptor error:",e),Promise.reject(e))),o.a.interceptors.response.use(e=>e,e=>{var a,t,l;return console.error("Response error:",null===(a=e.response)||void 0===a?void 0:a.status,null===(t=e.response)||void 0===t?void 0:t.data),401!==(null===(l=e.response)||void 0===l?void 0:l.status)||e.config.url.includes("join_league.php")||(console.log("Unauthorized access, redirecting to login"),localStorage.removeItem("userToken"),localStorage.removeItem("userId"),window.location.href="/login"),Promise.reject(e)});var i=o.a;const d=Object(l.createContext)(),u=()=>{try{const a=localStorage.getItem("userData");if(a)return JSON.parse(a)}catch(e){console.error("Error reading user data from localStorage:",e)}return{balance:0,points:0,username:""}};function E(e){let{children:a}=e;const[t,s]=Object(l.useState)(u());Object(l.useEffect)(()=>{try{localStorage.setItem("userData",JSON.stringify(t))}catch(e){console.error("Error saving user data to localStorage:",e)}},[t]);return n.a.createElement(d.Provider,{value:{userData:t,setUserData:e=>{s(a=>({...a,...e,balance:"string"===typeof e.balance?parseFloat(e.balance):e.balance,points:"string"===typeof e.points?parseFloat(e.points):e.points}))}}},a)}function p(){const e=Object(l.useContext)(d);if(!e)throw new Error("useUser must be used within a UserProvider");return e}const g={NETWORK:"NETWORK_ERROR",AUTH:"AUTHENTICATION_ERROR",VALIDATION:"VALIDATION_ERROR",SERVER:"SERVER_ERROR",CLIENT:"CLIENT_ERROR",UNKNOWN:"UNKNOWN_ERROR"},b={INFO:"info",WARNING:"warning",ERROR:"error",CRITICAL:"critical"};class v extends Error{constructor(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:g.UNKNOWN,t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:b.ERROR,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(e),this.name="AppError",this.type=a,this.severity=t,this.details=l,this.timestamp=new Date}}const h=function(e){let a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e instanceof v)a=e;else if(e.response){var l;a=new v((null===(l=e.response.data)||void 0===l?void 0:l.message)||"Server error occurred",401===e.response.status?g.AUTH:400===e.response.status?g.VALIDATION:g.SERVER,e.response.status>=500?b.CRITICAL:b.ERROR,{status:e.response.status,data:e.response.data,...t})}else a=e.request?new v("Network error occurred",g.NETWORK,b.ERROR,{originalError:e,...t}):new v(e.message||"An unexpected error occurred",g.UNKNOWN,b.ERROR,{originalError:e,...t});return N(a),a},N=e=>{const a={message:e.message,type:e.type,severity:e.severity,timestamp:e.timestamp,details:e.details,stack:e.stack};return console.group("%cApplication Error","color: red; font-weight: bold;"),console.log("%cTimestamp:","font-weight: bold;",a.timestamp),console.log("%cType:","font-weight: bold;",a.type),console.log("%cSeverity:","font-weight: bold;",a.severity),console.log("%cMessage:","font-weight: bold;",a.message),console.log("%cDetails:","font-weight: bold;",a.details),a.stack&&(console.log("%cStack Trace:","font-weight: bold;"),console.log(a.stack)),console.groupEnd(),a},_=async function(e){let a,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1e3;for(let s=1;s<=t;s++)try{return await e()}catch(n){if(a=n,s===t)break;await new Promise(e=>setTimeout(e,l*s)),console.log(`Retry attempt ${s} of ${t}`)}throw h(a,{retryAttempts:t})};t(33);var f=e=>{let{error:a,onClose:t}=e;if(!a)return null;const l=(e=>{switch(e){case b.INFO:return"info";case b.WARNING:return"warning";case b.ERROR:return"error";case b.CRITICAL:return"critical";default:return"error"}})(a.severity);return n.a.createElement("div",{className:`error-alert ${l}`},n.a.createElement("div",{className:"error-alert-content"},n.a.createElement("div",{className:"error-alert-header"},n.a.createElement("span",{className:"error-alert-type"},a.type),t&&n.a.createElement("button",{className:"error-alert-close",onClick:t},"\xd7")),n.a.createElement("div",{className:"error-alert-message"},a.message),a.details&&Object.keys(a.details).length>0&&n.a.createElement("div",{className:"error-alert-details"},n.a.createElement("pre",null,JSON.stringify(a.details,null,2)))))};const y=Object(l.createContext)(null),w=e=>{let{children:a}=e;const[t,s]=Object(l.useState)([]),r=Object(l.useCallback)(e=>{const a="AppError"===e.__proto__.constructor.name?e:h(e);s(e=>[...e,{id:Date.now(),error:a}]),"critical"!==a.severity&&setTimeout(()=>{c(a.id)},5e3)},[]),c=Object(l.useCallback)(e=>{s(a=>a.filter(a=>a.id!==e))},[]),m=Object(l.useCallback)(()=>{s([])},[]),o=Object(l.useCallback)(async function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{return await _(e)}catch(t){throw r(h(t,a)),t}},[r]);return n.a.createElement(y.Provider,{value:{addError:r,removeError:c,clearErrors:m,withErrorHandling:o}},a,n.a.createElement("div",{className:"error-alerts-container"},t.map(e=>{let{id:a,error:t}=e;return n.a.createElement(f,{key:a,error:t,onClose:()=>c(a)})})))};var S=t(3);t(34);var C=function(e){let{isCollapsed:a,toggleCollapse:t}=e;Object(c.q)();const s=Object(c.o)(),[r,o]=Object(l.useState)({dashboard:!1,users:!1,challenges:!1,leagues:!1,finance:!1,system:!1}),i={dashboard:{title:"Dashboard",icon:n.a.createElement(S.h,null),items:[{link:"/admin/dashboard",text:"Overview",icon:n.a.createElement(S.z,null)}]},challenges:{title:"Challenges",icon:n.a.createElement(S.x,null),items:[{link:"/admin/challenge-system",text:"Challenge System",icon:n.a.createElement(S.w,null)},{link:"/admin/challenge-management",text:"Challenge Management",icon:n.a.createElement(S.J,null)},{link:"/admin/credit-challenge",text:"Credit Challenge",icon:n.a.createElement(S.n,null)}]},users:{title:"Users",icon:n.a.createElement(S.T,null),items:[{link:"/admin/users",text:"User Management",icon:n.a.createElement(S.T,null)},{link:"/admin/add-user",text:"Add User",icon:n.a.createElement(S.R,null)},{link:"/admin/credit-user",text:"Credit User",icon:n.a.createElement(S.p,null)},{link:"/admin/debit-user",text:"Debit User",icon:n.a.createElement(S.D,null)}]},leagues:{title:"Leagues",icon:n.a.createElement(S.L,null),items:[{link:"/admin/league-management",text:"League Management",icon:n.a.createElement(S.L,null)},{link:"/admin/league-seasons",text:"Season Management",icon:n.a.createElement(S.f,null)},{link:"/admin/league-users",text:"League Users",icon:n.a.createElement(S.P,null)}]},finance:{title:"Finance",icon:n.a.createElement(S.E,null),items:[{link:"/admin/payment-methods",text:"Payment Methods",icon:n.a.createElement(S.g,null)},{link:"/admin/transactions",text:"Transactions",icon:n.a.createElement(S.t,null)}]},system:{title:"System",icon:n.a.createElement(S.m,null),items:[{link:"/admin/leaderboard",text:"Leaderboard Management",icon:n.a.createElement(S.C,null)},{link:"/admin/settings",text:"System Settings",icon:n.a.createElement(S.U,null)},{link:"/admin/reports",text:"Reports and Analytics",icon:n.a.createElement(S.i,null)}]}};return n.a.createElement("div",{className:"admin-sidebar"},n.a.createElement("div",{className:"logo"},"FanBet247"),n.a.createElement("nav",{className:"admin-sidebar-nav"},Object.entries(i).map(e=>{let[a,t]=e;return n.a.createElement("div",{key:a,className:"admin-menu-group"},n.a.createElement("div",{className:`admin-menu-header ${s.pathname.includes(a)?"active":""}`,onClick:()=>(e=>{o(a=>({...a,[e]:!a[e]}))})(a)},n.a.createElement("span",{className:"admin-menu-icon"},t.icon),n.a.createElement("span",{className:"admin-menu-title"},t.title),n.a.createElement(S.j,{className:`admin-menu-arrow ${r[a]?"rotated":""}`})),n.a.createElement("div",{className:`admin-menu-items ${r[a]?"open":""}`},t.items.map((e,a)=>n.a.createElement(m.c,{key:a,to:e.link,className:e=>{let{isActive:a}=e;return`admin-nav-item ${a?"active":""}`},end:!0},n.a.createElement("span",{className:"admin-nav-item-icon"},e.icon),n.a.createElement("span",{className:"admin-nav-item-text"},e.text)))))})))};t(35);var k=function(){const e=Object(c.o)(),a=Object(c.q)(),t=localStorage.getItem("adminUsername");return n.a.createElement("header",{className:"admin-header"},n.a.createElement("div",{className:"header-content"},n.a.createElement("div",{className:"header-left"},n.a.createElement("h1",{className:"page-title"},(()=>{const a=e.pathname;return"/admin"===a||"/admin/dashboard"===a?"Dashboard":a.includes("challenge-system")?"Challenge System":a.includes("challenge-management")?"Challenge Management":a.includes("team-management")?"Team Management":a.includes("league-management")?"League Management":a.includes("users")?"User Management":a.includes("credit-user")?"Credit User":a.includes("debit-user")?"Debit User":a.includes("payment-methods")?"Payment Methods":a.includes("bets")?"Bet Management":a.includes("transactions")?"Transaction Management":a.includes("leaderboard")?"Leaderboard":a.includes("settings")?"System Settings":a.includes("reports")?"Reports & Analytics":"Admin Panel"})())),n.a.createElement("div",{className:"header-right"},n.a.createElement("div",{className:"admin-info"},n.a.createElement("span",{className:"admin-name"},n.a.createElement("i",{className:"admin-icon"},"\ud83d\udc64"),t)),n.a.createElement("div",{className:"header-actions"},n.a.createElement("button",{className:"action-button notifications"},n.a.createElement("i",{className:"action-icon"},"\ud83d\udd14")),n.a.createElement("button",{className:"action-button settings"},n.a.createElement(m.b,{to:"/admin/settings"},n.a.createElement("i",{className:"action-icon"},"\u2699\ufe0f"))),n.a.createElement("button",{onClick:()=>{localStorage.removeItem("adminId"),localStorage.removeItem("adminUsername"),localStorage.removeItem("adminRole"),a("/admin/login")},className:"action-button logout"},n.a.createElement("i",{className:"action-icon"},"\ud83d\udeaa"))))))};t(36);var O=function(){const e=(new Date).getFullYear();return n.a.createElement("footer",{className:"admin-footer"},n.a.createElement("div",{className:"footer-content"},n.a.createElement("div",{className:"footer-info"},n.a.createElement("p",null,"\xa9 ",e," FanBet247. All rights reserved.")),n.a.createElement("div",{className:"footer-links"},n.a.createElement("a",{href:"/admin/help"},"Help"),n.a.createElement("a",{href:"/admin/terms"},"Terms"),n.a.createElement("a",{href:"/admin/privacy"},"Privacy"))))};t(37);var F=()=>{const[e,a]=Object(l.useState)(!1),t=()=>{a(!e)};return n.a.createElement("div",{className:`admin-layout ${e?"collapsed":""}`},n.a.createElement(C,{isCollapsed:e,toggleCollapse:t}),n.a.createElement("div",{className:"main-wrapper"},n.a.createElement(k,{toggleSidebar:t}),n.a.createElement("div",{className:"content"},n.a.createElement("main",null,n.a.createElement(c.b,null))),n.a.createElement(O,null)))};t(38),t(39);var j=function(){const e=Object(c.o)(),a=Object(c.q)(),{userData:t,setUserData:s}=p(),[r,o]=Object(l.useState)(!1),[d,u]=Object(l.useState)(!1),[E,g]=Object(l.useState)(window.innerWidth<=768),[b,v]=Object(l.useState)(0),[h,N]=Object(l.useState)(0),[_,f]=Object(l.useState)(0),[y,w]=Object(l.useState)(null),C=localStorage.getItem("userId"),k=localStorage.getItem("username"),O=localStorage.getItem("userToken"),F=["/login","/register","/","/about"];Object(l.useEffect)(()=>{const e=()=>{const e=window.innerWidth<=768;g(e),e||u(!1)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),Object(l.useEffect)(()=>{u(!1)},[e.pathname]);const j=Object(l.useCallback)(async()=>{if(O&&C)try{const n=await i.get("/backend/handlers/user_data.php",{params:{userId:C},headers:{Authorization:`Bearer ${O}`,"Content-Type":"application/json"}});n.data.success?s({balance:n.data.balance,points:n.data.points,username:n.data.username}):console.error("Failed to fetch user data:",n.data.message)}catch(l){var t;console.error("Error fetching user data:",l),401===(null===(t=l.response)||void 0===t?void 0:t.status)&&a("/login",{state:{from:e.pathname}})}},[O,C,a,e.pathname]),L=Object(l.useCallback)(async()=>{if(O&&C)try{const n=await i.get("/backend/handlers/messages.php",{params:{user_id:C,type:"unread_count"}});n.data.success&&v(n.data.unread_count),N(0)}catch(l){var t;console.error("Error fetching notifications:",l),401!==(null===(t=l.response)||void 0===t?void 0:t.status)||F.some(a=>e.pathname.startsWith(a))||a("/login",{state:{from:e.pathname}})}},[O,C,a,e.pathname,F]);Object(l.useEffect)(()=>{const t=F.some(a=>e.pathname.startsWith(a));if(t||O&&C){if(!t&&O&&C){j(),L();const e=setInterval(j,3e4),a=setInterval(L,6e4);return()=>{clearInterval(e),clearInterval(a)}}}else a("/login",{state:{from:e.pathname}})},[O,C,a,e.pathname,j,L]),Object(l.useEffect)(()=>{b>_&&(w({message:`You have ${b} new message${b>1?"s":""}`,type:"info"}),setTimeout(()=>w(null),5e3)),f(b)},[b,_]);const T=()=>{localStorage.removeItem("userId"),localStorage.removeItem("userToken"),a("/login")},x=()=>{E?u(!d):o(!r)};return n.a.createElement("div",{className:`user-layout ${E?"mobile-view":""} ${d?"mobile-sidebar-open":""}`},E&&n.a.createElement("div",{className:"mobile-header"},n.a.createElement("button",{className:"mobile-menu-toggle",onClick:x,"aria-label":"Toggle menu","aria-expanded":d},d?n.a.createElement(S.K,null):n.a.createElement(S.d,null)),n.a.createElement("div",{className:"mobile-logo"},n.a.createElement(m.b,{to:"/"},"FanBet247")),n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-balance"},"\u20a6",t.balance.toLocaleString()))),E&&d&&n.a.createElement("div",{className:"sidebar-overlay",onClick:()=>u(!1)}),n.a.createElement("aside",{className:`user-dashboard-sidebar ${r?"collapsed":""} ${E?"mobile":""} ${d?"open":""}`},n.a.createElement("div",{className:"sidebar-header"},n.a.createElement("div",{className:"user-dashboard-logo"},n.a.createElement(m.b,{to:"/"},r&&!E?"FB":"FanBet247")),!E&&n.a.createElement("button",{className:"sidebar-toggle",onClick:x},r?n.a.createElement(S.d,{className:"toggle-icon"}):n.a.createElement(S.k,{className:"toggle-icon"}))),n.a.createElement("div",{className:"user-dashboard-profile"},n.a.createElement("div",{className:"profile-section",title:k},n.a.createElement("span",{role:"img","aria-label":"User profile",className:"profile-icon"},"\ud83d\udc64"),(!r||E)&&n.a.createElement("span",{className:"profile-name"},k)),n.a.createElement("div",{className:"profile-balance"},n.a.createElement("div",{className:"balance-item",title:`Naira Balance: \u20a6${t.balance.toLocaleString()}`},n.a.createElement("span",{role:"img","aria-label":"Naira currency",className:"balance-icon"},"\u20a6"),(!r||E)&&n.a.createElement("span",null,t.balance.toLocaleString())),n.a.createElement("div",{className:"balance-item",title:`Fancoins: ${t.points.toLocaleString()} FC`},n.a.createElement("span",{role:"img","aria-label":"Coin",className:"balance-icon"},"\ud83e\ude99"),(!r||E)&&n.a.createElement("span",null,t.points.toLocaleString()," FC")))),n.a.createElement("nav",{className:"user-dashboard-nav"},n.a.createElement("ul",{className:"dashboard-nav-list"},n.a.createElement("li",{className:"/user/dashboard"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/dashboard",title:"Dashboard"},n.a.createElement("span",{role:"img","aria-label":"Home",className:"nav-icon"},"\ud83c\udfe0"),(!r||E)&&n.a.createElement("span",null,"Dashboard"))),n.a.createElement("li",{className:"/user/profile"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/profile",title:"Profile"},n.a.createElement("span",{role:"img","aria-label":"User",className:"nav-icon"},"\ud83d\udc64"),(!r||E)&&n.a.createElement("span",null,"Profile"))),n.a.createElement("li",{className:"/user/bets/outgoing"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/bets/outgoing",title:"Outgoing Bets"},n.a.createElement("span",{role:"img","aria-label":"Outbox",className:"nav-icon"},"\ud83d\udce4"),(!r||E)&&n.a.createElement("span",null,"Outgoing Bets"))),n.a.createElement("li",{className:"/user/challenges"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/challenges",title:"Challenges"},n.a.createElement("span",{role:"img","aria-label":"Target",className:"nav-icon"},"\ud83c\udfaf"),(!r||E)&&n.a.createElement("span",null,"Challenges"))),n.a.createElement("li",{className:"/user/recent-bets"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/recent-bets",title:"Recent Bets"},n.a.createElement("span",{role:"img","aria-label":"Chart",className:"nav-icon"},"\ud83d\udcca"),(!r||E)&&n.a.createElement("span",null,"Recent Bets"))),n.a.createElement("li",{className:"/user/bets/incoming"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/bets/incoming",title:"Incoming Bets"},n.a.createElement("span",{role:"img","aria-label":"Inbox",className:"nav-icon"},"\ud83d\udce5"),(!r||E)&&n.a.createElement("span",null,"Incoming Bets"))),n.a.createElement("li",{className:"/user/bets/accepted"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/bets/accepted",title:"Accepted Bets"},n.a.createElement("span",{role:"img","aria-label":"Check mark",className:"nav-icon"},"\u2705"),(!r||E)&&n.a.createElement("span",null,"Accepted Bets"))),n.a.createElement("li",{className:"/user/friends"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/friends",title:"Friends"},n.a.createElement("span",{role:"img","aria-label":"People",className:"nav-icon"},"\ud83d\udc65"),(!r||E)&&n.a.createElement("span",null,"Friends"),h>0&&n.a.createElement("span",{className:"notification-badge"},h))),n.a.createElement("li",{className:"/user/friend-requests"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/friend-requests",title:"Friend Requests"},n.a.createElement("span",{role:"img","aria-label":"Bell",className:"nav-icon"},"\ud83d\udd14"),(!r||E)&&n.a.createElement("span",null,"Friend Requests"),h>0&&n.a.createElement("span",{className:"notification-badge"},h))),n.a.createElement("li",{className:"/user/messages"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/messages",title:"Messages"},n.a.createElement("span",{role:"img","aria-label":"Speech bubble",className:"nav-icon"},"\ud83d\udcac"),(!r||E)&&n.a.createElement("span",{className:"menu-text"},"Messages"),b>0&&n.a.createElement("span",{className:"menu-notification"},b))),n.a.createElement("li",{className:"/user/transfer"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/transfer",title:"Transfer"},n.a.createElement("span",{role:"img","aria-label":"Transfer arrows",className:"nav-icon"},"\u2194\ufe0f"),(!r||E)&&n.a.createElement("span",null,"Transfer"))),n.a.createElement("li",{className:"/user/leaderboard"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/leaderboard",title:"Leaderboard"},n.a.createElement("span",{role:"img","aria-label":"Trophy",className:"nav-icon"},"\ud83c\udfc6"),(!r||E)&&n.a.createElement("span",null,"Leaderboard"))),n.a.createElement("li",{className:"/user/league"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/league",title:"247 League"},n.a.createElement("span",{role:"img","aria-label":"Soccer ball",className:"nav-icon"},"\u26bd"),(!r||E)&&n.a.createElement("span",null,"247 League"))),n.a.createElement("li",{className:"/user/my-leagues"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/my-leagues",title:"My Leagues"},n.a.createElement("span",{role:"img","aria-label":"Trophy",className:"nav-icon"},"\ud83c\udfc6"),(!r||E)&&n.a.createElement("span",null,"My Leagues"))),n.a.createElement("li",{className:"/user/wallet"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/wallet",title:"Credit Wallet"},n.a.createElement("span",{role:"img","aria-label":"Wallet",className:"nav-icon"},"\ud83d\udc5b"),(!r||E)&&n.a.createElement("span",null,"Credit Wallet"))),n.a.createElement("li",{className:"/user/credit-history"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/credit-history",title:"Credit History"},n.a.createElement("span",{role:"img","aria-label":"Scroll",className:"nav-icon"},"\ud83d\udcdc"),(!r||E)&&n.a.createElement("span",null,"Credit History"))),n.a.createElement("li",{className:"/user/settings"===e.pathname?"active":""},n.a.createElement(m.b,{to:"/user/settings",title:"Settings"},n.a.createElement("span",{role:"img","aria-label":"Gear",className:"nav-icon"},"\u2699\ufe0f"),(!r||E)&&n.a.createElement("span",null,"Settings"))))),n.a.createElement("button",{onClick:T,className:"sidebar-logout-btn",title:"Logout"},n.a.createElement("span",{role:"img","aria-label":"Door",className:"logout-icon"},"\ud83d\udeaa"),(!r||E)&&"Logout")),n.a.createElement("main",{className:`main-container ${r&&!E?"expanded":""}`},y&&n.a.createElement("div",{className:`notification-banner ${y.type}`},n.a.createElement("div",{className:"notification-content"},n.a.createElement(S.s,{className:"notification-icon"}),n.a.createElement("span",null,y.message))),!E&&n.a.createElement("header",{className:"dashboard-header"},n.a.createElement("div",{className:"dashboard-nav"},n.a.createElement("div",{className:"nav-left"},n.a.createElement("h1",{className:"page-title"},(()=>{const a=e.pathname;return"/user/dashboard"===a?"Dashboard":"/user/profile"===a?"Profile":"/user/bets/outgoing"===a?"Outgoing Bets":"/user/bets/incoming"===a?"Incoming Bets":"/user/bets/accepted"===a?"Accepted Bets":"/user/friends"===a?"Friends":"/user/messages"===a?"Messages":"/user/transfer"===a?"Transfer":"/user/leaderboard"===a?"Leaderboard":"/user/league"===a?"247 League":"/user/my-leagues"===a?"My Leagues":"/user/wallet"===a?"Wallet":"/user/settings"===a?"Settings":"/user/friend-requests"===a?"Friend Requests":"/user/challenges"===a?"Challenges":"/user/recent-bets"===a?"Recent Bets":"Dashboard"})())),n.a.createElement("div",{className:"nav-right"},n.a.createElement("div",{className:"user-balances"},n.a.createElement("div",{className:"balance-item"},n.a.createElement("span",{className:"balance-label"},"Balance:"),n.a.createElement("span",{className:"balance-amount"},"\u20a6",t.balance.toLocaleString())),n.a.createElement("div",{className:"balance-item"},n.a.createElement("span",{className:"balance-label"},"Points:"),n.a.createElement("span",{className:"balance-amount"},t.points.toLocaleString()," FC"))),n.a.createElement("button",{onClick:T,className:"nav-logout-btn"},n.a.createElement("i",{className:"nav-icon"},"\ud83d\udeaa"),"Logout")))),n.a.createElement("div",{className:"dashboard-content"},n.a.createElement(c.b,null))))};t(40);var L=()=>{const[e,a]=Object(l.useState)("inbox"),[t,s]=Object(l.useState)([]),[r,c]=Object(l.useState)(null),[m,o]=Object(l.useState)([]),[d,u]=Object(l.useState)(""),[E,p]=Object(l.useState)(null),[g,b]=Object(l.useState)(!1),[v,h]=Object(l.useState)(!1),N=Object(l.useRef)(null),_=localStorage.getItem("userId"),f=localStorage.getItem("username");Object(l.useEffect)(()=>{y();const e=setInterval(y,3e4);return()=>clearInterval(e)},[e]),Object(l.useEffect)(()=>{if(r){w(r.user_id),C(r.user_id);const e=setInterval(()=>w(r.user_id),5e3);return()=>clearInterval(e)}},[r]),Object(l.useEffect)(()=>{N.current&&N.current.scrollIntoView({behavior:"smooth"})},[m]);const y=async()=>{try{const a=await i.get("/backend/handlers/messages.php",{params:{user_id:_,type:e}});a.data.success&&s(a.data.conversations||[])}catch(E){console.error("Error fetching conversations:",E)}},w=async e=>{try{const a=await i.get("/backend/handlers/messages.php",{params:{user_id:_,other_user_id:e,type:"conversation"}});a.data.success&&o(a.data.messages||[])}catch(E){console.error("Error fetching messages:",E)}},C=async e=>{try{await i.post("/backend/handlers/messages.php",{action:"mark_read",recipient_id:_,sender_id:e})}catch(E){console.error("Error marking messages as read:",E)}},k=async e=>{if(e.preventDefault(),d.trim()&&r){b(!0);try{const e=await i.post("/backend/handlers/messages.php",{sender_id:_,recipient_id:r.user_id,content:d.trim()});e.data.success?(u(""),w(r.user_id)):p(e.data.message||"Failed to send message")}catch(E){console.error("Error sending message:",E),p("Failed to send message. Please try again.")}finally{b(!1)}}},O=e=>new Date(e).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",hour12:!0}),F=e=>{const a={};let t=null,l=[],n=null;return e.forEach(e=>{const s=(e=>{const a=new Date(e),t=new Date,l=new Date(t);return l.setDate(l.getDate()-1),a.toDateString()===t.toDateString()?"Today":a.toDateString()===l.toDateString()?"Yesterday":a.toLocaleDateString()})(e.created_at);s!==t||e.sender_id!==n?(l.length>0&&(a[t]||(a[t]=[]),a[t].push(l)),l=[e],t=s):l.push(e),n=e.sender_id}),l.length>0&&t&&(a[t]||(a[t]=[]),a[t].push(l)),a};return n.a.createElement("div",{className:"messages-container"},n.a.createElement("div",{className:`messages-sidebar ${r?"":"active"}`},n.a.createElement("div",{className:"messages-header"},n.a.createElement("h2",null,"Messages")),n.a.createElement("div",{className:"messages-tabs"},n.a.createElement("div",{className:`tab ${"inbox"===e?"active":""}`,onClick:()=>a("inbox")},n.a.createElement(S.B,null)," Inbox"),n.a.createElement("div",{className:`tab ${"sent"===e?"active":""}`,onClick:()=>a("sent")},n.a.createElement(S.F,null)," Sent")),n.a.createElement("div",{className:"conversations-list"},n.a.createElement("div",{className:"conversations-list"},t.map(e=>n.a.createElement("div",{key:e.user_id,className:`conversation-item ${(null===r||void 0===r?void 0:r.user_id)===e.user_id?"active":""} ${e.is_read?"":"unread"}`,onClick:()=>(e=>{c(e),C(e.user_id)})(e)},n.a.createElement("div",{className:"avatar"},e.username[0].toUpperCase()),n.a.createElement("div",{className:"conversation-info"},n.a.createElement("div",{className:"conversation-name"},e.username,!e.is_read&&n.a.createElement("span",{className:"unread-indicator"})),n.a.createElement("div",{className:"conversation-preview"},e.last_message),n.a.createElement("div",{className:"conversation-time"},O(e.last_message_time)))))))),n.a.createElement("div",{className:"chat-area"},r?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"chat-header"},n.a.createElement("button",{className:"back-button",onClick:()=>c(null)},n.a.createElement(S.a,null)),n.a.createElement("div",{className:"chat-title"},n.a.createElement("div",{className:"avatar"},r.username[0].toUpperCase()),n.a.createElement("h3",null,"Chat with ",r.username.charAt(0).toUpperCase()+r.username.slice(1).toLowerCase())),n.a.createElement("button",{className:"delete-button",onClick:()=>h(!0)},n.a.createElement(S.K,null))),v&&n.a.createElement("div",{className:"delete-confirm"},n.a.createElement("p",null,"Are you sure you want to delete this conversation?"),n.a.createElement("div",{className:"delete-actions"},n.a.createElement("button",{onClick:async()=>{try{(await i.post("/backend/handlers/messages.php",{action:"delete_conversation",user_id:_,other_user_id:r.user_id})).data.success&&(c(null),y(),h(!1))}catch(E){console.error("Error deleting conversation:",E)}}},"Yes, Delete"),n.a.createElement("button",{onClick:()=>h(!1)},"Cancel"))),n.a.createElement("div",{className:"messages-content"},E&&n.a.createElement("div",{className:"error-message"},E),(()=>{const e=F(m);return Object.entries(e).map(e=>{let[a,t]=e;return n.a.createElement(n.a.Fragment,{key:a},n.a.createElement("div",{className:"message-date-divider"},n.a.createElement("span",{className:"message-date"},a)),t.map((e,t)=>n.a.createElement("div",{key:`${a}-${t}`,className:"message-group"},e.map((e,a)=>{const t=e.sender_id===_,l=t?f:e.sender_username,s=0===a,r=!e.is_read&&!t;return n.a.createElement("div",{key:e.id||a,className:`message ${e.message_type} ${r?"unread":""}`},s&&n.a.createElement("div",{className:"message-user-info"},n.a.createElement("div",{className:"message-username"},n.a.createElement("span",{className:"username-text"},l.toUpperCase()))),n.a.createElement("div",{className:"message-content"},n.a.createElement("div",{className:"message-bubble"},e.content),n.a.createElement("div",{className:"message-time"},O(e.created_at))))}))),n.a.createElement("div",{ref:N}))})})(),n.a.createElement("div",{ref:N})),n.a.createElement("form",{onSubmit:k,className:"message-input-container"},n.a.createElement("textarea",{className:"message-input",value:d,onChange:e=>u(e.target.value),placeholder:"Type a message...",onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),k(e))}}),n.a.createElement("button",{type:"submit",className:"send-button",disabled:g||!d.trim()},n.a.createElement(S.F,null)))):n.a.createElement("div",{className:"no-conversation"},n.a.createElement(S.o,null),n.a.createElement("p",null,"Select a conversation to start messaging"))))},T=t(9);t(43);const x="/backend";var $=function(e){let{handleLogout:a,isCollapsed:t,onToggle:s}=e;const[r,c]=Object(l.useState)([]),[i,d]=Object(l.useState)(!1),[u,E]=Object(l.useState)(t||!1);Object(l.useEffect)(()=>{g(),p()},[]),Object(l.useEffect)(()=>{E(t)},[t]);const p=()=>{const e=localStorage.getItem("userId");d(!!e)},g=async()=>{try{const a=await o.a.get(`${x}/handlers/get_leagues.php`);a.data.success&&c(a.data.leagues)}catch(e){console.error("Error fetching leagues:",e)}},b=r.slice(0,7);return n.a.createElement("div",{className:`old-sidebar ${u?"collapsed":""}`},n.a.createElement("div",{className:"sidebar-header"},n.a.createElement("h2",null,u?"AL":"AVAILABLE LEAGUES")),n.a.createElement("nav",{className:"leagues-nav"},n.a.createElement("div",{className:"leagues-section"},n.a.createElement("div",{className:"leagues-list"},b.map(e=>n.a.createElement(m.b,{key:e.league_id,to:i?`/league/${e.league_id}`:"/login",className:"league-item",title:e.name},n.a.createElement("span",{className:"league-icon"},(e=>e.icon_url?n.a.createElement("img",{src:e.icon_url,alt:e.name,className:"league-icon-img"}):e.icon_path?n.a.createElement("img",{src:`${x}/uploads/leagues/icons/${e.icon_path}`,alt:e.name,className:"league-icon-img"}):"\ud83c\udfc6")(e)),!u&&n.a.createElement("div",{className:"league-info"},n.a.createElement("span",{className:"league-name"},e.name),n.a.createElement("span",{className:"league-min-bet"},"Min Bet: \u20a6",Number(e.min_bet_amount).toLocaleString()))))),n.a.createElement("button",{onClick:()=>{window.location.href=i?"/league-home":"/login"},className:"view-all-btn",title:i?"View All Leagues":"Login to View All"},n.a.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 576 512",width:"20",height:"20",fill:"#0F8A42",style:{minWidth:"20px"}},n.a.createElement("path",{d:"M552 64H448V24c0-13.3-10.7-24-24-24H152c-13.3 0-24 10.7-24 24v40H24C10.7 64 0 74.7 0 88v56c0 35.7 22.5 72.4 61.9 100.7 31.5 22.7 69.8 37.1 110 41.7C203.3 338.5 240 360 240 360v72h-48c-35.3 0-64 20.7-64 56v12c0 6.6 5.4 12 12 12h296c6.6 0 12-5.4 12-12v-12c0-35.3-28.7-56-64-56h-48v-72s36.7-21.5 68.1-73.6c40.3-4.6 78.6-19 110-41.7 39.3-28.3 61.9-65 61.9-100.7V88c0-13.3-10.7-24-24-24zM99.3 192.8C74.9 175.2 64 155.6 64 144v-16h64.2c1 32.6 5.8 61.2 12.8 86.2-15.1-5.2-29.2-12.4-41.7-21.4zM512 144c0 16.1-17.7 36.1-35.3 48.8-12.5 9-26.7 16.2-41.8 21.4 7-25 11.8-53.6 12.8-86.2H512v16z"})),!u&&n.a.createElement("span",{className:"view-all-text"},i?"View All Leagues":"Login to View All")),n.a.createElement("button",{onClick:()=>{s?s():E(!u)},className:"toggle-btn",title:u?"Expand Menu":"Collapse Menu"},n.a.createElement("span",{className:"toggle-icon"},u?"\u2192":"\u2190"),!u&&n.a.createElement("span",{className:"toggle-text"},"Collapse Menu")))))};t(44);var D=function(e){let{userName:a,handleLogout:t}=e;const[s,r]=Object(l.useState)(!1),[c,o]=Object(l.useState)(!1);Object(l.useEffect)(()=>{const e=()=>{o(window.scrollY>20)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);const i=!!localStorage.getItem("userId");return n.a.createElement("header",{className:`main-header ${c?"scrolled":""} ${s?"menu-open":""}`},n.a.createElement("div",{className:"header-container"},n.a.createElement("div",{className:"header-left"},n.a.createElement(m.b,{to:"/",className:"logo-link"},"FanBet247"),n.a.createElement("button",{className:"mobile-menu-btn",onClick:()=>{r(!s)},"aria-label":"Toggle menu","aria-expanded":s},n.a.createElement("span",{className:"menu-icon"}))),n.a.createElement("nav",{className:`header-nav ${s?"active":""}`},n.a.createElement(m.b,{to:"/",onClick:()=>r(!1)},"Home"),n.a.createElement(m.b,{to:"/live-challenges",onClick:()=>r(!1)},"Live"),n.a.createElement(m.b,{to:i?"/league-home":"/login",onClick:()=>r(!1)},"Leagues"),n.a.createElement(m.b,{to:"/leaderboard",onClick:()=>r(!1)},"Leaders"),n.a.createElement(m.b,{to:"/about",onClick:()=>r(!1)},"About"),n.a.createElement("button",{onClick:e=>{(e=>{e.preventDefault();const a=document.querySelector(".layout-content, .content"),t=document.querySelector(".main-footer");if(t&&a){const e=t.offsetTop;a.scrollTo({top:e,behavior:"smooth"})}r(!1)})(e),r(!1)},className:"nav-link"},"Contact")),n.a.createElement("div",{className:"header-right"},a?n.a.createElement("div",{className:"user-menu"},n.a.createElement("div",{className:"user-info"},n.a.createElement("span",{className:"user-icon"},n.a.createElement("i",{className:"fas fa-user-circle"})),n.a.createElement("span",{className:"username"},a)),n.a.createElement(m.b,{to:"/dashboard",className:"header-btn dashboard-btn"},n.a.createElement("i",{className:"fas fa-tachometer-alt"}),n.a.createElement("span",null,"Dashboard")),n.a.createElement("button",{onClick:t,className:"header-btn logout-btn"},n.a.createElement("i",{className:"fas fa-sign-out-alt"}),n.a.createElement("span",null,"Logout"))):n.a.createElement("div",{className:"auth-buttons"},n.a.createElement(m.b,{to:"/login",className:"header-btn login-btn"},"Login"),n.a.createElement(m.b,{to:"/register",className:"header-btn register-btn"},"Register")))))};t(45);var I=function(){const[e,a]=Object(l.useState)("desktop");switch(Object(l.useEffect)(()=>{const e=()=>{const e=window.innerWidth,t=window.innerHeight;a(e<=768?"mobile":t<=768?"small":t<=900?"medium":"desktop")};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),e){case"mobile":return n.a.createElement("footer",{className:"main-footer mobile-footer"},n.a.createElement("div",{className:"mobile-footer-content"},n.a.createElement("div",{className:"mobile-footer-top"},n.a.createElement("h3",null,"FanBet247"),n.a.createElement("div",{className:"age-restriction"},n.a.createElement("span",{className:"age-badge"},"18+"))),n.a.createElement("div",{className:"mobile-footer-links"},n.a.createElement(m.b,{to:"/"},"Home"),n.a.createElement(m.b,{to:"/live-challenges"},"Live"),n.a.createElement(m.b,{to:"/leaderboard"},"Leaderboard"),n.a.createElement(m.b,{to:"/help"},"Help")),n.a.createElement("div",{className:"mobile-footer-bottom"},n.a.createElement("div",{className:"social-links"},n.a.createElement("a",{href:"https://twitter.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Twitter"),n.a.createElement("a",{href:"https://t.me/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Telegram")),n.a.createElement("p",{className:"copyright"}," 2024 FanBet247"))));case"small":return n.a.createElement("footer",{className:"main-footer small-footer"},n.a.createElement("div",{className:"small-footer-content"},n.a.createElement("div",{className:"small-footer-top"},n.a.createElement("div",{className:"small-footer-main"},n.a.createElement("div",{className:"small-footer-brand"},n.a.createElement("h3",null,"FanBet247"),n.a.createElement("div",{className:"age-restriction"},n.a.createElement("span",{className:"age-badge"},"18+"),n.a.createElement("p",null,"Bet Responsibly"))),n.a.createElement("div",{className:"small-footer-features"},n.a.createElement("span",null,"Live Betting"),n.a.createElement("span",null,"\u2022"),n.a.createElement("span",null,"Best Odds"),n.a.createElement("span",null,"\u2022"),n.a.createElement("span",null,"Fast Payouts"))),n.a.createElement("div",{className:"small-footer-nav"},n.a.createElement("div",{className:"small-nav-column"},n.a.createElement(m.b,{to:"/live-challenges"},"Live Matches"),n.a.createElement(m.b,{to:"/upcoming-matches"},"Upcoming"),n.a.createElement(m.b,{to:"/leaderboard"},"Leaderboard")),n.a.createElement("div",{className:"small-nav-column"},n.a.createElement(m.b,{to:"/help"},"Help Center"),n.a.createElement(m.b,{to:"/responsible-gambling"},"Responsible Gaming"),n.a.createElement(m.b,{to:"/terms"},"Terms")))),n.a.createElement("div",{className:"small-footer-bottom"},n.a.createElement("div",{className:"social-links"},n.a.createElement("a",{href:"https://twitter.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Twitter"),n.a.createElement("a",{href:"https://facebook.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Facebook"),n.a.createElement("a",{href:"https://t.me/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Telegram")),n.a.createElement("div",{className:"security-badges"},n.a.createElement("span",{className:"security-badge"},"SSL Secured"),n.a.createElement("span",{className:"security-badge"},"Licensed")),n.a.createElement("p",{className:"copyright"}," 2024 FanBet247. All rights reserved."))));case"medium":return n.a.createElement("footer",{className:"main-footer medium-footer"},n.a.createElement("div",{className:"medium-footer-content"},n.a.createElement("div",{className:"medium-footer-main"},n.a.createElement("div",{className:"footer-section"},n.a.createElement("h3",null,"FanBet247"),n.a.createElement("div",{className:"age-restriction"},n.a.createElement("span",{className:"age-badge"},"18+"),n.a.createElement("p",null,"Please gamble responsibly"))),n.a.createElement("div",{className:"footer-links-grid"},n.a.createElement("div",{className:"footer-column"},n.a.createElement("h4",null,"Quick Links"),n.a.createElement(m.b,{to:"/"},"Home"),n.a.createElement(m.b,{to:"/live-challenges"},"Live Matches"),n.a.createElement(m.b,{to:"/leaderboard"},"Leaderboard"),n.a.createElement(m.b,{to:"/promotions"},"Promotions")),n.a.createElement("div",{className:"footer-column"},n.a.createElement("h4",null,"Support"),n.a.createElement(m.b,{to:"/help"},"Help Center"),n.a.createElement(m.b,{to:"/responsible-gambling"},"Responsible Gaming"),n.a.createElement(m.b,{to:"/terms"},"Terms"),n.a.createElement(m.b,{to:"/privacy"},"Privacy")))),n.a.createElement("div",{className:"medium-footer-bottom"},n.a.createElement("div",{className:"medium-bottom-content"},n.a.createElement("div",{className:"social-links"},n.a.createElement("a",{href:"https://twitter.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Twitter"),n.a.createElement("a",{href:"https://facebook.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Facebook"),n.a.createElement("a",{href:"https://t.me/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Telegram")),n.a.createElement("p",{className:"copyright"}," 2024 FanBet247. All rights reserved.")))));default:return n.a.createElement("footer",{className:"main-footer desktop-footer"},n.a.createElement("div",{className:"footer-content"},n.a.createElement("div",{className:"footer-section main-info"},n.a.createElement("h3",null,"FanBet247"),n.a.createElement("p",null,"Your premier destination for sports betting. Experience live matches, competitive odds, and instant payouts. Join thousands of sports fans making winning predictions daily."),n.a.createElement("div",{className:"betting-features"},n.a.createElement("div",{className:"feature"},n.a.createElement("span",{role:"img","aria-label":"Trophy",className:"feature-icon"},"\ud83c\udfc6"),n.a.createElement("span",null,"Live Betting")),n.a.createElement("div",{className:"feature"},n.a.createElement("span",{role:"img","aria-label":"Money bag",className:"feature-icon"},"\ud83d\udcb0"),n.a.createElement("span",null,"Instant Payouts")),n.a.createElement("div",{className:"feature"},n.a.createElement("span",{role:"img","aria-label":"Chart",className:"feature-icon"},"\ud83d\udcca"),n.a.createElement("span",null,"Best Odds"))),n.a.createElement("div",{className:"age-restriction"},n.a.createElement("span",{className:"age-badge"},"18+"),n.a.createElement("p",null,"Please gamble responsibly. Betting can be addictive."))),n.a.createElement("div",{className:"footer-section"},n.a.createElement("h4",null,"Sports"),n.a.createElement("nav",{className:"footer-nav"},n.a.createElement(m.b,{to:"/sports/football"},"Football"),n.a.createElement(m.b,{to:"/sports/basketball"},"Basketball"),n.a.createElement(m.b,{to:"/sports/tennis"},"Tennis"),n.a.createElement(m.b,{to:"/sports/cricket"},"Cricket"),n.a.createElement(m.b,{to:"/sports/esports"},"Esports"))),n.a.createElement("div",{className:"footer-section"},n.a.createElement("h4",null,"Betting"),n.a.createElement("nav",{className:"footer-nav"},n.a.createElement(m.b,{to:"/live-challenges"},"Live Matches"),n.a.createElement(m.b,{to:"/upcoming-matches"},"Upcoming"),n.a.createElement(m.b,{to:"/promotions"},"Promotions"),n.a.createElement(m.b,{to:"/leaderboard"},"Leaderboard"),n.a.createElement(m.b,{to:"/statistics"},"Statistics"))),n.a.createElement("div",{className:"footer-section"},n.a.createElement("h4",null,"Support"),n.a.createElement("nav",{className:"footer-nav"},n.a.createElement(m.b,{to:"/responsible-gambling"},"Responsible Gaming"),n.a.createElement(m.b,{to:"/help"},"Help Center"),n.a.createElement(m.b,{to:"/terms"},"Terms & Conditions"),n.a.createElement(m.b,{to:"/privacy"},"Privacy Policy"),n.a.createElement(m.b,{to:"/contact"},"Contact Us")))),n.a.createElement("div",{className:"footer-bottom"},n.a.createElement("div",{className:"footer-bottom-content"},n.a.createElement("div",{className:"social-links"},n.a.createElement("a",{href:"https://twitter.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Twitter"),n.a.createElement("a",{href:"https://facebook.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Facebook"),n.a.createElement("a",{href:"https://instagram.com/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Instagram"),n.a.createElement("a",{href:"https://t.me/fanbet247",target:"_blank",rel:"noopener noreferrer"},"Telegram")),n.a.createElement("div",{className:"security-badges"},n.a.createElement("span",{className:"security-badge"},"SSL Secured"),n.a.createElement("span",{className:"security-badge"},"Licensed Gaming")),n.a.createElement("p",{className:"copyright"}," 2024 FanBet247. All rights reserved."))))}};t(46);var A=()=>{const[e,a]=Object(l.useState)(!1),t=()=>{window.pageYOffset>300?a(!0):a(!1)};return Object(l.useEffect)(()=>(window.addEventListener("scroll",t),()=>{window.removeEventListener("scroll",t)}),[]),n.a.createElement(n.a.Fragment,null,e&&n.a.createElement("div",{className:"scroll-to-top",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})}},"\u2191"))};t(47);const P="/backend";var R=function(e){let{children:a}=e;const[t,s]=Object(l.useState)(null),[r,m]=Object(l.useState)(window.innerWidth<=1440),[i,d]=Object(l.useState)(E()),u=Object(c.q)();function E(){const e=window.innerWidth;return e<=768?"mobile":e<=1366?"13inch":e<=1440?"14inch":"large"}Object(l.useEffect)(()=>{const e=localStorage.getItem("userId");e&&p(e);const a=()=>{const e=E();d(e),"14inch"!==e&&"13inch"!==e||m(!0)};return window.addEventListener("resize",a),()=>window.removeEventListener("resize",a)},[]);const p=async e=>{try{const t=await o.a.get(`${P}/handlers/get_username.php?user_id=${e}`);t.data.success&&s(t.data.username)}catch(a){console.error("Error fetching username:",a)}},g=()=>{localStorage.removeItem("userId"),s(null),u("/")},b=()=>{m(!r)};return n.a.createElement("div",{className:`main-layout ${i}`},n.a.createElement($,{handleLogout:g,isCollapsed:r,onToggle:b,screenSize:i}),n.a.createElement("div",{className:`layout-content ${r?"sidebar-collapsed":""}`},n.a.createElement(D,{userName:t,handleLogout:g,isSidebarCollapsed:r,toggleSidebar:b,screenSize:i}),n.a.createElement("main",{className:"layout-main"},n.a.createElement("div",{className:"content-container"},a),n.a.createElement(I,null))),n.a.createElement(A,null))};t(48);const q=e=>{let{days:a,hours:t,minutes:l,seconds:s,completed:r}=e;return r?n.a.createElement("span",{className:"status status--expired"},"ENDED"):n.a.createElement("div",{className:"countdown"},a>0&&n.a.createElement("span",null,a,"d"),t>0&&n.a.createElement("span",null,t,"h"),n.a.createElement("span",null,l,"m"),n.a.createElement("span",null,s,"s"))},B=e=>{let{message:a}=e;return n.a.createElement("div",{className:"empty-state"},n.a.createElement("img",{src:"/empty-challenges.png",alt:"No data",className:"empty-state__image",onError:e=>e.target.style.display="none"}),n.a.createElement("p",null,a))};var M=()=>{const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]),[r,i]=Object(l.useState)({challenges:!0,bets:!0}),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)(0),[g,b]=Object(l.useState)(!1),v=Object(c.q)(),N=["/slider/Slider1.png","/slider/Slider2.png"],y=Object(l.useCallback)(()=>{p(e=>(e+1)%N.length)},[N.length]);Object(l.useEffect)(()=>{const e=setInterval(y,5e3);return()=>clearInterval(e)},[y]),Object(l.useEffect)(()=>{(async()=>{await Promise.all([w(),S()])})(),b(!!localStorage.getItem("userId"))},[]);const w=async()=>{try{i(e=>({...e,challenges:!0}));const t=(await _(async()=>{const e=await o.a.get("/backend/handlers/recent_challenges.php");if(!e.data.success)throw new Error(e.data.message||"Failed to fetch recent challenges");return e})).data.challenges.map(e=>({...e,end_time:new Date(e.end_time)}));a(t)}catch(e){const a=h(e,{component:"WelcomeSplash",operation:"fetchRecentChallenges"});u(a)}finally{i(e=>({...e,challenges:!1}))}},S=async()=>{try{i(e=>({...e,bets:!0}));const a=await _(async()=>{const e=await o.a.get("/backend/handlers/welcome_recent_bets.php");if(!e.data.success)throw new Error(e.data.message||"Failed to fetch recent bets");return e});s(a.data.bets||[])}catch(e){const a=h(e,{component:"WelcomeSplash",operation:"fetchRecentBets"});u(a)}finally{i(e=>({...e,bets:!1}))}};return n.a.createElement(R,null,n.a.createElement("div",{className:"welcome"},n.a.createElement("div",{className:"welcome__content"},d&&n.a.createElement(f,{error:d,onClose:()=>u(null)}),n.a.createElement("section",{className:"hero"},n.a.createElement("div",{className:"hero__slider"},N.map((e,a)=>n.a.createElement("img",{key:a,src:window.location.origin+e,alt:`Slide ${a+1}`,className:"hero__image",style:{transform:`translateX(${100*(a-E)}%)`,display:Math.abs(E-a)<=1?"block":"none"},onError:e=>{e.target.src=window.location.origin+"/slider/Slider1.png"}})))),n.a.createElement("section",{className:"section challenges-section"},n.a.createElement("div",{className:"section__header"},n.a.createElement("h2",{className:"section__title"},"Live Challenges"),n.a.createElement("div",{className:"section__actions"},g?n.a.createElement(m.b,{to:"/user/dashboard",className:"section__link"},"Dashboard"):n.a.createElement(m.b,{to:"/login",className:"section__link"},"Login"))),r.challenges?n.a.createElement("div",{className:"loading"},"Loading challenges..."):0===e.length?n.a.createElement(B,{message:"No active challenges at the moment. Check back later!"}):n.a.createElement("div",{className:"challenges-list"},n.a.createElement("div",{className:"challenge-list-header"},n.a.createElement("div",{className:"header-teams"},"Teams"),n.a.createElement("div",{className:"header-odds"},n.a.createElement("span",null,"Win"),n.a.createElement("span",null,"Draw"),n.a.createElement("span",null,"Win")),n.a.createElement("div",{className:"header-action"},"Action"),n.a.createElement("div",{className:"header-status"},"Status")),e.map(e=>{const a=((e,a)=>{try{const t=new Date,l=new Date(e),n=new Date(a);return t<l?"upcoming":t>=l&&t<=n?"live":"expired"}catch(d){return console.error("Error calculating match status:",d),"unknown"}})(e.start_time,e.end_time);return n.a.createElement("div",{key:e.challenge_id,className:"challenge-card"},n.a.createElement("div",{className:"teams-container"},n.a.createElement("div",{className:"team"},n.a.createElement("img",{src:`/backend/${e.team_a_logo}`,alt:e.team_a,className:"team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"team-name"},e.team_a)),n.a.createElement("div",{className:"vs-container"},n.a.createElement("span",{className:"vs"},"VS"),n.a.createElement("span",{className:`match-type ${e.match_type}`},"full_time"===e.match_type?"FULL TIME":"half_time"===e.match_type?"HALF TIME":"FT"===e.match_type?"FULL TIME":"HT"===e.match_type?"HALF TIME":"FULL TIME"),n.a.createElement(T.a,{date:new Date(e.end_time),renderer:q})),n.a.createElement("div",{className:"team"},n.a.createElement("img",{src:`/backend/${e.team_b_logo}`,alt:e.team_b,className:"team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"team-name"},e.team_b))),n.a.createElement("div",{className:"odds-container"},n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-value"},Number(e.odds_team_a).toFixed(2))),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-value"},Number(e.odds_draw).toFixed(2))),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-value"},Number(e.odds_team_b).toFixed(2)))),n.a.createElement("div",{className:"action-container"},n.a.createElement("button",{className:`action-button ${g?"place-bet":"login"}`,onClick:()=>(e=>{g?v(`/user/join-challenge/${e}`):(sessionStorage.setItem("redirectAfterLogin",`/user/join-challenge/${e}`),v("/login"))})(e.challenge_id)},g?"Place Bet":"Login to Bet")),n.a.createElement("div",{className:"status-container"},"upcoming"===a?n.a.createElement(T.a,{date:new Date(e.start_time),renderer:q}):n.a.createElement("span",{className:`status-badge ${a}`},"live"===a?"LIVE":"ENDED")))}))),n.a.createElement("section",{className:"section recent-bets-section"},n.a.createElement("div",{className:"section__header"},n.a.createElement("h2",{className:"section__title"},"Recent Bets"),n.a.createElement(m.b,{to:"/user/recent-bets",className:"section__link"},"View All")),r.bets?n.a.createElement("div",{className:"loading"},"Loading recent bets..."):0===t.length?n.a.createElement(B,{message:"No recent bets available. Start betting now!"}):n.a.createElement("div",{className:"recent-bets-grid"},t.slice(0,6).map((e,a)=>n.a.createElement("div",{key:e.bet_id||a,className:"recent-bet-card"},n.a.createElement("div",{className:"bet-header"},n.a.createElement("span",{className:"bet-ref"},"REF: ",e.unique_code),n.a.createElement("div",{className:"bet-amount"},n.a.createElement("span",null,Number(e.amount_user1).toFixed(2)," FC"),n.a.createElement("span",null,Number(e.amount_user2).toFixed(2)," FC"))),n.a.createElement("div",{className:"bet-teams"},n.a.createElement("div",{className:"bet-team"},n.a.createElement("img",{src:`/backend/${e.team_a_logo}`,alt:e.team_a,className:"bet-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"bet-team-name"},e.team_a)),n.a.createElement("div",{className:"bet-vs"},"VS"),n.a.createElement("div",{className:"bet-team"},n.a.createElement("img",{src:`/backend/${e.team_b_logo}`,alt:e.team_b,className:"bet-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"bet-team-name"},e.team_b))),n.a.createElement("div",{className:"bet-odds"},n.a.createElement("div",{className:"bet-odds-item"},n.a.createElement("span",{className:"bet-odds-value"},Number(e.odds_team_a).toFixed(2)),n.a.createElement("span",{className:"bet-odds-label"},"Win")),n.a.createElement("div",{className:"bet-odds-item"},n.a.createElement("span",{className:"bet-odds-value"},Number(e.odds_draw).toFixed(2)),n.a.createElement("span",{className:"bet-odds-label"},"Draw")),n.a.createElement("div",{className:"bet-odds-item"},n.a.createElement("span",{className:"bet-odds-value"},Number(e.odds_team_b).toFixed(2)),n.a.createElement("span",{className:"bet-odds-label"},"Win"))),n.a.createElement("div",{className:"bet-users"},n.a.createElement("div",{className:"bet-user"},n.a.createElement("span",{className:"bet-username"},e.user1_name)),n.a.createElement("div",{className:"bet-user"},n.a.createElement("span",{className:"bet-username"},e.user2_name))))))))))};t(49);var U=()=>{const e=Object(c.q)(),[a,t]=Object(l.useState)(""),[s,r]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(!1);return n.a.createElement("div",{className:"admin-login-page"},n.a.createElement("h2",null,"Admin Login"),m&&n.a.createElement("div",{className:"error-message"},m),n.a.createElement("form",{onSubmit:async t=>{t.preventDefault(),i(""),u(!0);try{const t=await o.a.post("/backend/handlers/admin_login_handler.php",{identifier:a,password:s});t.data.success?(localStorage.setItem("adminId",t.data.admin_id),localStorage.setItem("adminUsername",t.data.username),localStorage.setItem("adminRole",t.data.role),e("/admin/dashboard")):i(t.data.message||"Login failed")}catch(m){console.error("Login error:",m),m.response?i(m.response.data.message||"Invalid credentials"):m.request?i("Network error. Please check your connection."):i("An error occurred. Please try again.")}finally{u(!1)}}},n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"identifier"},"Username or Email:"),n.a.createElement("input",{type:"text",id:"identifier",value:a,onChange:e=>t(e.target.value),disabled:d,required:!0})),n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"password"},"Password:"),n.a.createElement("input",{type:"password",id:"password",value:s,onChange:e=>r(e.target.value),disabled:d,required:!0})),n.a.createElement("button",{type:"submit",disabled:d},d?"Logging in...":"Login")))};t(50);const W="/backend";var H=function(){const[e,a]=Object(l.useState)({usernameOrEmail:"",password:""}),[t,s]=Object(l.useState)(""),[r,i]=Object(l.useState)(!1),d=Object(c.q)();Object(l.useEffect)(()=>{localStorage.getItem("userId")&&d("/user/dashboard")},[d]);const u=e=>{const{name:l,value:n}=e.target;a(e=>({...e,[l]:n})),t&&s("")};return n.a.createElement("div",{className:"login-page"},n.a.createElement("div",{className:"login-container"},n.a.createElement("div",{className:"login-header"},n.a.createElement(m.b,{to:"/",className:"brand-logo"},"FanBet247"),n.a.createElement("h1",null,"User Login"),n.a.createElement("p",{className:"login-subtitle"},"Welcome back! Please login to your account")),t&&n.a.createElement("div",{className:"error-message"},t),n.a.createElement("form",{onSubmit:async a=>{a.preventDefault(),s(""),i(!0);try{const a=await o.a.post(`${W}/handlers/user_login.php`,e);if(a.data.success){localStorage.setItem("userId",a.data.userId),localStorage.setItem("username",a.data.username);const e=sessionStorage.getItem("redirectAfterLogin")||"/user/dashboard";sessionStorage.removeItem("redirectAfterLogin"),d(e)}else s(a.data.message||"Login failed. Please try again.")}catch(c){var t,l,n,r;console.error("Login error:",c),(null===(t=c.response)||void 0===t?void 0:null===(l=t.data)||void 0===l?void 0:l.message)?s(c.response.data.message):401===(null===(n=c.response)||void 0===n?void 0:n.status)?s("Invalid username/email or password"):400===(null===(r=c.response)||void 0===r?void 0:r.status)?s("Please enter both username/email and password"):s("An error occurred. Please try again later.")}finally{i(!1)}},className:"login-form"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"usernameOrEmail"},"Username or Email"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-user"}),n.a.createElement("input",{type:"text",id:"usernameOrEmail",name:"usernameOrEmail",value:e.usernameOrEmail,onChange:u,placeholder:"Enter your username or email",required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"password"},"Password"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-lock"}),n.a.createElement("input",{type:"password",id:"password",name:"password",value:e.password,onChange:u,placeholder:"Enter your password",required:!0}))),n.a.createElement("div",{className:"form-options"},n.a.createElement(m.b,{to:"/forgot-password",className:"forgot-password"},"Forgot Password?")),n.a.createElement("button",{type:"submit",className:"login-button",disabled:r},r?"Logging in...":"Login to Your Account"),n.a.createElement("div",{className:"login-footer"},n.a.createElement("p",null,"Don't have an account? ",n.a.createElement(m.b,{to:"/register",className:"register-link"},"Register here"))))))};t(51);const V="/backend";var z=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)({username:"",full_name:"",email:"",password:"",favorite_team:""}),[r,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(""),E=Object(c.q)();Object(l.useEffect)(()=>{p()},[]);const p=async()=>{try{const t=await o.a.get(`${V}/handlers/team_management.php`);a(t.data.data||[])}catch(e){i("Failed to fetch teams")}},g=e=>{const{name:a,value:t}=e.target;s(e=>({...e,[a]:t}))};return n.a.createElement("div",{className:"user-registration"},n.a.createElement("div",{className:"user-registration-container"},n.a.createElement("div",{className:"user-registration-header"},n.a.createElement(m.b,{to:"/",className:"brand-logo"},"FanBet247"),n.a.createElement("h1",null,"Create Account"),n.a.createElement("p",{className:"user-registration-subtitle"},"Join FanBet247 today")),n.a.createElement("form",{onSubmit:async e=>{e.preventDefault(),i(""),u("");try{const e=await o.a.post(`${V}/handlers/user_registration.php`,t);e.data.success?(u("Registration successful! Redirecting to login..."),setTimeout(()=>E("/login"),3e3)):i(e.data.message||"Registration failed")}catch(a){i("An error occurred during registration")}},className:"user-registration-form"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"username"},"Username"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-user"}),n.a.createElement("input",{id:"username",type:"text",name:"username",value:t.username,onChange:g,placeholder:"Choose a username",required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"full_name"},"Full Name"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-id-card"}),n.a.createElement("input",{id:"full_name",type:"text",name:"full_name",value:t.full_name,onChange:g,placeholder:"Enter your full name",required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"email"},"Email"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-envelope"}),n.a.createElement("input",{id:"email",type:"email",name:"email",value:t.email,onChange:g,placeholder:"Enter your email",required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"password"},"Password"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-lock"}),n.a.createElement("input",{id:"password",type:"password",name:"password",value:t.password,onChange:g,placeholder:"Choose a password",required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"favorite_team"},"Favorite Team"),n.a.createElement("div",{className:"input-wrapper"},n.a.createElement("i",{className:"fas fa-futbol"}),n.a.createElement("select",{id:"favorite_team",name:"favorite_team",value:t.favorite_team,onChange:g,required:!0},n.a.createElement("option",{value:""},"Select Favorite Team"),e.map(e=>n.a.createElement("option",{key:e.id,value:e.name},e.name))))),r&&n.a.createElement("div",{className:"error-message"},r),d&&n.a.createElement("div",{className:"success-message"},d),n.a.createElement("button",{type:"submit",className:"login-button"},"Create Account"),n.a.createElement("div",{className:"user-registration-footer"},n.a.createElement("p",null,"Already have an account? ",n.a.createElement(m.b,{to:"/login",className:"login-link"},"Login here"))))))};t(52);const Y="/backend";var G=function(){const[e,a]=Object(l.useState)(null),[t,s]=Object(l.useState)([]),[r,c]=Object(l.useState)(null),[m,i]=Object(l.useState)(!1);Object(l.useEffect)(()=>{d(),u()},[]);const d=async()=>{try{const t=await o.a.get(`${Y}/handlers/admin_dashboard_data.php`);t.data.success?a(t.data):console.error("Failed to fetch dashboard data:",t.data.message)}catch(e){console.error("Error fetching dashboard data:",e)}},u=async()=>{try{const a=await o.a.get(`${Y}/handlers/team_management.php`);200===a.data.status&&s(a.data.data)}catch(e){console.error("Error fetching teams:",e)}},E=e=>{const a=t.find(a=>a.name===e);return a?`${Y}/${a.logo}`:""};return e?n.a.createElement("div",{className:"dashboard"},n.a.createElement("div",{className:"card card-half"},n.a.createElement("h2",null,"User Overview"),n.a.createElement("div",{className:"stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-value"},e.totalUsers),n.a.createElement("div",{className:"stat-label"},"Total Users")))),n.a.createElement("div",{className:"card card-half"},n.a.createElement("h2",null,"Challenge Overview"),n.a.createElement("div",{className:"stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-value"},e.activeChallenges),n.a.createElement("div",{className:"stat-label"},"Active Challenges")))),n.a.createElement("div",{className:"card card-half"},n.a.createElement("h2",null,"Total Wins"),n.a.createElement("div",{className:"stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-value"},"$",e.totalWins),n.a.createElement("div",{className:"stat-label"},"Total Winnings")))),n.a.createElement("div",{className:"card card-half"},n.a.createElement("h2",null,"Total Bets"),n.a.createElement("div",{className:"stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-value"},e.totalBets),n.a.createElement("div",{className:"stat-label"},"Bets Placed")))),n.a.createElement("div",{className:"card card-full"},n.a.createElement("h2",null,"Top Teams Overview"),n.a.createElement("div",{className:"teams-container"},e.teamStats&&e.teamStats.map(e=>n.a.createElement("div",{key:e.id,className:"team-card"},n.a.createElement("img",{src:`${Y}/${e.logo}`,alt:e.name,className:"team-logo-circle"}),n.a.createElement("p",{className:"team-name"},e.name),n.a.createElement("div",{className:"team-stats"},n.a.createElement("span",{className:"user-count"},e.user_count),n.a.createElement("span",{className:"user-label"},"Users")))))),n.a.createElement("div",{className:"card card-full"},n.a.createElement("h2",null,"Latest User Activity"),n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Username"),n.a.createElement("th",null,"Total Bets"),n.a.createElement("th",null,"Balance"),n.a.createElement("th",null,"Latest Challenge"),n.a.createElement("th",null,"Potential Return"),n.a.createElement("th",null,"Last Activity"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.recentActivity.map((e,a)=>n.a.createElement("tr",{key:a},n.a.createElement("td",null,e.username),n.a.createElement("td",null,e.totalBets),n.a.createElement("td",null,e.balance," FanCoins"),n.a.createElement("td",null,n.a.createElement("div",{className:"team-matchup-mini"},n.a.createElement("img",{src:E(e.latest_challenge_team_a),alt:e.latest_challenge_team_a}),"vs",n.a.createElement("img",{src:E(e.latest_challenge_team_b),alt:e.latest_challenge_team_b}))),n.a.createElement("td",null,e.latest_potential_return," FanCoins"),n.a.createElement("td",null,new Date(e.lastActivity).toLocaleString()),n.a.createElement("td",null,n.a.createElement("button",{onClick:()=>{c(e),i(!0)}},"Details"))))))),n.a.createElement("div",{className:"card card-full"},n.a.createElement("h2",null,"Recent Bets"),n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Bet ID"),n.a.createElement("th",null,"User 1"),n.a.createElement("th",null,"User 2"),n.a.createElement("th",null,"Match"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.recentBets.map((e,a)=>n.a.createElement("tr",{key:a},n.a.createElement("td",null,e.bet_id),n.a.createElement("td",null,e.user1_name),n.a.createElement("td",null,e.user2_name||"Waiting..."),n.a.createElement("td",null,n.a.createElement("div",{className:"team-matchup-mini"},n.a.createElement("img",{src:E(e.team_a),alt:e.team_a}),"vs",n.a.createElement("img",{src:E(e.team_b),alt:e.team_b}))),n.a.createElement("td",null,e.amount_user1," FanCoins"),n.a.createElement("td",null,e.bet_status),n.a.createElement("td",null,n.a.createElement("button",{onClick:()=>{c(e),i(!0)}},"View Details"))))))),m&&(r.bet_id?n.a.createElement(e=>{let{bet:a,onClose:t}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"bet-details-modal"},n.a.createElement("button",{className:"close-button",onClick:t},"\xd7"),n.a.createElement("div",{className:"modal-left"},n.a.createElement("h3",{className:"reference-title"},"Bet ID: ",a.bet_id),n.a.createElement("div",{className:"teams-match"},n.a.createElement("div",{className:"team-card user1"},n.a.createElement("img",{src:E(a.team_a),alt:a.team_a}),n.a.createElement("div",{className:"team-name"},a.team_a),n.a.createElement("div",{className:"team-odds"},"Odds: ",a.odds_user1),n.a.createElement("div",{className:"team-user"},a.user1_name)),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"team-card user2"},n.a.createElement("img",{src:E(a.team_b),alt:a.team_b}),n.a.createElement("div",{className:"team-name"},a.team_b),n.a.createElement("div",{className:"team-odds"},"Odds: ",a.odds_user2),n.a.createElement("div",{className:"team-user"},a.user2_name||"WAITING FOR OPPONENT")))),n.a.createElement("div",{className:"modal-right"},n.a.createElement("div",{className:"status-badge-large","data-status":a.bet_status},a.bet_status.toUpperCase()),n.a.createElement("div",{className:"bet-details-grid"},n.a.createElement("div",{className:"detail-item amount"},n.a.createElement("div",{className:"detail-label"},"User 1 Bet Amount"),n.a.createElement("div",{className:"detail-value"},a.amount_user1," FanCoins")),n.a.createElement("div",{className:"detail-item return"},n.a.createElement("div",{className:"detail-label"},"User 1 Potential Return"),n.a.createElement("div",{className:"detail-value"},a.potential_return_win_user1," FanCoins")),a.user2_name&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"detail-label"},"User 2 Bet Amount"),n.a.createElement("div",{className:"detail-value"},a.amount_user2," FanCoins")),n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"detail-label"},"User 2 Potential Return"),n.a.createElement("div",{className:"detail-value"},a.potential_return_win_user2," FanCoins"))),n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"detail-label"},"Created At"),n.a.createElement("div",{className:"detail-value"},new Date(a.created_at).toLocaleString()))))))},{bet:r,onClose:()=>i(!1)}):n.a.createElement(e=>{let{activity:a,onClose:t}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"bet-details-modal"},n.a.createElement("button",{className:"close-button",onClick:t},"\xd7"),n.a.createElement("div",{className:"modal-left"},n.a.createElement("h3",{className:"reference-title"},"User: ",a.username),n.a.createElement("div",{className:"teams-match"},n.a.createElement("div",{className:"team-card user1"},n.a.createElement("img",{src:E(a.latest_challenge_team_a),alt:a.latest_challenge_team_a}),n.a.createElement("div",{className:"team-name"},a.latest_challenge_team_a),n.a.createElement("div",{className:"team-odds"},"Odds: ",a.odds_team_a||"1.80"),n.a.createElement("div",{className:"team-advantage"},"Goal Advantage: ",a.team_a_goal_advantage||"0")),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"team-card user2"},n.a.createElement("img",{src:E(a.latest_challenge_team_b),alt:a.latest_challenge_team_b}),n.a.createElement("div",{className:"team-name"},a.latest_challenge_team_b),n.a.createElement("div",{className:"team-odds"},"Odds: ",a.odds_team_b||"1.80"),n.a.createElement("div",{className:"team-advantage"},"Goal Advantage: ",a.team_b_goal_advantage||"0")))),n.a.createElement("div",{className:"modal-right"},n.a.createElement("div",{className:"bet-details-grid"},n.a.createElement("div",{className:"detail-item amount"},n.a.createElement("div",{className:"detail-label"},"Total Bets"),n.a.createElement("div",{className:"detail-value"},a.totalBets)),n.a.createElement("div",{className:"detail-item balance"},n.a.createElement("div",{className:"detail-label"},"Balance"),n.a.createElement("div",{className:"detail-value"},a.balance," FanCoins")),n.a.createElement("div",{className:"detail-item return"},n.a.createElement("div",{className:"detail-label"},"Latest Potential Return"),n.a.createElement("div",{className:"detail-value"},a.latest_potential_return," FanCoins")),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("div",{className:"detail-label"},"Match Odds"),n.a.createElement("div",{className:"detail-value"},a.odds_team_a||"1.80"," - ",a.odds_team_b||"1.80")),n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"detail-label"},"Last Activity"),n.a.createElement("div",{className:"detail-value"},new Date(a.lastActivity).toLocaleString()))))))},{activity:r,onClose:()=>i(!1)}))):n.a.createElement("div",null,"Loading...")};t(53);const J="/backend";var K=function(){var e,a,t,s;const[r,c]=Object(l.useState)([]),[m,i]=Object(l.useState)([]),[d,u]=Object(l.useState)({team1:"",team2:"",odds1:1,odds2:1,goalAdvantage1:0,goalAdvantage2:0,startTime:"",endTime:"",matchTime:"",matchType:"full_time",oddsDraw:.8,oddsLost:.2}),[E,p]=Object(l.useState)(""),[g,b]=Object(l.useState)("");Object(l.useEffect)(()=>{v()},[]);const v=async()=>{try{const a=await o.a.get(`${J}/handlers/team_management.php`);i(a.data.data)}catch(e){console.error("Error fetching teams:",e)}},h=e=>{const{name:a,value:t}=e.target;u(e=>({...e,[a]:t}))},N=async e=>{if(e.preventDefault(),p(""),b(""),d.team1&&d.team2&&d.odds1&&d.odds2)try{const e=new FormData,t=m.find(e=>e.name===d.team1),l=m.find(e=>e.name===d.team2);e.append("team1",d.team1),e.append("team2",d.team2),e.append("odds1",d.odds1),e.append("odds2",d.odds2),e.append("goalAdvantage1",d.goalAdvantage1),e.append("goalAdvantage2",d.goalAdvantage2),e.append("startTime",d.startTime),e.append("endTime",d.endTime),e.append("matchTime",d.matchTime),e.append("matchType",d.matchType),e.append("oddsDraw",d.oddsDraw),e.append("oddsLost",d.oddsLost),e.append("logo1",t?`${J}/${t.logo}`:""),e.append("logo2",l?`${J}/${l.logo}`:"");const n=await o.a.post(`${J}/handlers/create_challenge.php`,e,{headers:{"Content-Type":"multipart/form-data"}});n.data.success?(b("Challenge created successfully!"),u({team1:"",team2:"",odds1:1,odds2:1,goalAdvantage1:0,goalAdvantage2:0,startTime:"",endTime:"",matchTime:"",matchType:"full_time",oddsDraw:.8,oddsLost:.2}),setTimeout(()=>{b("")},3e3)):(p(n.data.message||"Failed to create challenge"),setTimeout(()=>{p("")},3e3))}catch(a){p("Failed to create challenge")}else p("Team names and odds are required.")};return n.a.createElement("div",{className:"challenge-system"},n.a.createElement("h1",null,"Create a New Challenge"),n.a.createElement("div",{className:"header-actions"},n.a.createElement("button",{onClick:N},"Save Challenge"),n.a.createElement("button",{onClick:()=>{u({team1:"",team2:"",odds1:1,odds2:1,goalAdvantage1:0,goalAdvantage2:0,startTime:"",endTime:"",matchTime:"",matchType:"full_time",oddsDraw:.8,oddsLost:.2}),p(""),b("")}},"Discard Changes")),E&&n.a.createElement("div",{className:"error-message"},E),g&&n.a.createElement("div",{className:"success-message"},g),n.a.createElement("form",{onSubmit:N,className:"challenge-form"},n.a.createElement("div",{className:"match-settings"},n.a.createElement("div",{className:"match-type-section"},n.a.createElement("h3",null,"Match Settings"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"matchType"},"Match Type:"),n.a.createElement("select",{id:"matchType",name:"matchType",value:d.matchType,onChange:h,required:!0},n.a.createElement("option",{value:"full_time"},"Full Time"),n.a.createElement("option",{value:"half_time"},"Half Time"))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"oddsDraw"},"Draw Odds:"),n.a.createElement("input",{type:"number",id:"oddsDraw",name:"oddsDraw",value:d.oddsDraw,onChange:h,step:"0.1",min:"0"}),n.a.createElement("p",{className:"odds-explanation"},"Default: 0.8")),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"oddsLost"},"Lost Odds:"),n.a.createElement("input",{type:"number",id:"oddsLost",name:"oddsLost",value:d.oddsLost,onChange:h,step:"0.1",min:"0"}),n.a.createElement("p",{className:"odds-explanation"},"Default: 0.2")))),n.a.createElement("div",{className:"team-container"},n.a.createElement("div",{className:"team-section team1"},n.a.createElement("h3",null,"Team 1"),n.a.createElement("label",{htmlFor:"team1"},"Select Team 1:"),n.a.createElement("select",{id:"team1",name:"team1",value:d.team1,onChange:h,required:!0},n.a.createElement("option",{value:""},"Select a team"),m.map(e=>n.a.createElement("option",{key:e.id,value:e.name},e.name))),d.team1&&n.a.createElement("div",{className:"logo-container"},n.a.createElement("img",{src:`${J}/${null===(e=m.find(e=>e.name===d.team1))||void 0===e?void 0:e.logo}`,alt:"Team 1 Logo",className:"logo-preview"})),n.a.createElement("label",{htmlFor:"odds1"},"Odds for Team 1:"),n.a.createElement("input",{type:"number",id:"odds1",name:"odds1",value:d.odds1,onChange:h,required:!0,step:"0.01",min:"1"}),n.a.createElement("p",{className:"odds-explanation"},"User's bet x ",d.odds1," = Potential winnings"),n.a.createElement("label",{htmlFor:"goalAdvantage1"},"Goal Advantage for Team 1:"),n.a.createElement("input",{type:"number",id:"goalAdvantage1",name:"goalAdvantage1",value:d.goalAdvantage1,onChange:h})),n.a.createElement("div",{className:"team-section team2"},n.a.createElement("h3",null,"Team 2"),n.a.createElement("label",{htmlFor:"team2"},"Select Team 2:"),n.a.createElement("select",{id:"team2",name:"team2",value:d.team2,onChange:h,required:!0},n.a.createElement("option",{value:""},"Select a team"),m.map(e=>n.a.createElement("option",{key:e.id,value:e.name},e.name))),d.team2&&n.a.createElement("div",{className:"logo-container"},n.a.createElement("img",{src:`${J}/${null===(a=m.find(e=>e.name===d.team2))||void 0===a?void 0:a.logo}`,alt:"Team 2 Logo",className:"logo-preview"})),n.a.createElement("label",{htmlFor:"odds2"},"Odds for Team 2:"),n.a.createElement("input",{type:"number",id:"odds2",name:"odds2",value:d.odds2,onChange:h,required:!0,step:"0.01",min:"1"}),n.a.createElement("p",{className:"odds-explanation"},"User's bet x ",d.odds2," = Potential winnings"),n.a.createElement("label",{htmlFor:"goalAdvantage2"},"Goal Advantage for Team 2:"),n.a.createElement("input",{type:"number",id:"goalAdvantage2",name:"goalAdvantage2",value:d.goalAdvantage2,onChange:h}))),n.a.createElement("div",{className:"time-section"},n.a.createElement("label",{htmlFor:"startTime"},"Challenge Start Time:"),n.a.createElement("input",{type:"datetime-local",id:"startTime",name:"startTime",value:d.startTime,onChange:h,required:!0}),n.a.createElement("label",{htmlFor:"endTime"},"Challenge End Time:"),n.a.createElement("input",{type:"datetime-local",id:"endTime",name:"endTime",value:d.endTime,onChange:h,required:!0}),n.a.createElement("label",{htmlFor:"matchTime"},"Actual Match Time:"),n.a.createElement("input",{type:"datetime-local",id:"matchTime",name:"matchTime",value:d.matchTime,onChange:h,required:!0}))),n.a.createElement("div",{className:"challenge-preview"},n.a.createElement("h3",null,"Challenge Preview"),n.a.createElement("div",{className:"preview-teams-container"},n.a.createElement("div",{className:"preview-team"},d.team1&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"logo-container"},n.a.createElement("img",{src:`${J}/${null===(t=m.find(e=>e.name===d.team1))||void 0===t?void 0:t.logo}`,alt:"Team 1 Logo",className:"logo-preview"})),n.a.createElement("div",{className:"preview-team-name"},d.team1),n.a.createElement("div",{className:"preview-odds"},"Odds: ",d.odds1))),n.a.createElement("div",{className:"preview-vs"},"VS"),n.a.createElement("div",{className:"preview-team"},d.team2&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"logo-container"},n.a.createElement("img",{src:`${J}/${null===(s=m.find(e=>e.name===d.team2))||void 0===s?void 0:s.logo}`,alt:"Team 2 Logo",className:"logo-preview"})),n.a.createElement("div",{className:"preview-team-name"},d.team2),n.a.createElement("div",{className:"preview-odds"},"Odds: ",d.odds2)))),n.a.createElement("div",{className:"preview-time-details"},n.a.createElement("p",null,n.a.createElement("span",null,"Start Time"),d.startTime),n.a.createElement("p",null,n.a.createElement("span",null,"End Time"),d.endTime),n.a.createElement("p",null,n.a.createElement("span",null,"Match Time"),d.matchTime),n.a.createElement("p",null,n.a.createElement("span",null,"Match Type"),d.matchType.replace("_"," ").toUpperCase()))))};t(54);const Q="/backend";var X=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)({username:"",full_name:"",email:"",favorite_team:"",balance:0});Object(l.useEffect)(()=>{g(),b()},[]);const g=async()=>{try{const t=await o.a.get(`${Q}/handlers/user_management.php`);t.data.success?a(t.data.data||[]):c(t.data.message||"Failed to fetch users")}catch(e){c("Failed to fetch users. Please check your network connection and try again."),console.error("Error fetching users:",e)}},b=async()=>{try{const a=await o.a.get(`${Q}/handlers/team_management.php`);s(a.data.data||[])}catch(e){c("Failed to fetch teams")}},v=e=>{const{name:a,value:t}=e.target;p(e=>({...e,[a]:t}))};return n.a.createElement("div",{className:"user-management"},n.a.createElement("h1",null,"User Management"),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m),n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Username"),n.a.createElement("th",null,"Full Name"),n.a.createElement("th",null,"Email"),n.a.createElement("th",null,"Favorite Team"),n.a.createElement("th",null,"Balance"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.map(e=>n.a.createElement("tr",{key:e.user_id},n.a.createElement("td",null,e.username),n.a.createElement("td",null,e.full_name),n.a.createElement("td",null,e.email),n.a.createElement("td",null,e.favorite_team),n.a.createElement("td",null,e.balance),n.a.createElement("td",null,n.a.createElement("button",{onClick:()=>(e=>{u(e.user_id),p(e)})(e)},"Edit")))))),d&&n.a.createElement("div",{className:"edit-user-form"},n.a.createElement("h2",null,"Edit User"),n.a.createElement("form",{onSubmit:async e=>{e.preventDefault();try{const e=await o.a.put(`${Q}/handlers/user_management.php?id=${d}`,E);e.data.success?(i("User updated successfully!"),g(),u(null)):c(e.data.message||"Failed to update user")}catch(a){c("Failed to update user")}}},n.a.createElement("input",{type:"text",name:"username",value:E.username,onChange:v,placeholder:"Username",required:!0}),n.a.createElement("input",{type:"text",name:"full_name",value:E.full_name,onChange:v,placeholder:"Full Name",required:!0}),n.a.createElement("input",{type:"email",name:"email",value:E.email,onChange:v,placeholder:"Email",required:!0}),n.a.createElement("select",{name:"favorite_team",value:E.favorite_team,onChange:v,required:!0},n.a.createElement("option",{value:""},"Select Favorite Team"),t.map(e=>n.a.createElement("option",{key:e.id,value:e.name},e.name))),n.a.createElement("input",{type:"number",name:"balance",value:E.balance,onChange:v,placeholder:"Balance",required:!0}),n.a.createElement("button",{type:"submit"},"Update User"),n.a.createElement("button",{type:"button",onClick:()=>u(null)},"Cancel"))))};t(55);const Z="/backend";var ee=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]);Object(l.useEffect)(()=>{r(),c()},[]);const r=async()=>{try{const t=await o.a.get(`${Z}/handlers/team_management.php`);200===t.data.status&&a(t.data.data)}catch(e){console.error("Error fetching teams:",e)}},c=async()=>{try{const a=await o.a.get(`${Z}/handlers/get_all_bets.php`);a.data.success&&s(a.data.bets.map(e=>({...e,user1_pick:"team_a_win"===e.bet_choice_user1?e.team_a:e.team_b,user2_pick:"team_a_win"===e.bet_choice_user2?e.team_a:e.team_b})))}catch(e){console.error("Error fetching bets:",e)}},m=a=>{const t=e.find(e=>e.name===a);return t?`${Z}/${t.logo}`:""};return n.a.createElement("div",{className:"bet-management"},n.a.createElement("h1",null,"Bet Management"),n.a.createElement("div",{className:"bets-list"},n.a.createElement("h2",null,"All System Bets"),n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Ref"),n.a.createElement("th",null,"User 1"),n.a.createElement("th",null,"Pick"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"User 2"),n.a.createElement("th",null,"Pick"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Returns"))),n.a.createElement("tbody",null,t.map(e=>{var a;return n.a.createElement("tr",{key:e.bet_id,className:e.bet_status},n.a.createElement("td",null,(null===(a=e.unique_code)||void 0===a?void 0:a.toUpperCase())||`${e.bet_id}DNRBKCC`),n.a.createElement("td",null,e.user1_name),n.a.createElement("td",null,n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:m(e.user1_pick),alt:e.user1_pick}),n.a.createElement("span",null,e.user1_pick))),n.a.createElement("td",null,e.amount_user1," FC"),n.a.createElement("td",null,e.user2_name||"Waiting"),n.a.createElement("td",null,e.user2_name&&n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:m(e.user2_pick),alt:e.user2_pick}),n.a.createElement("span",null,e.user2_pick))),n.a.createElement("td",null,e.amount_user2||"-"," FC"),n.a.createElement("td",null,n.a.createElement("span",{className:`status ${e.bet_status}`},e.bet_status)),n.a.createElement("td",null,n.a.createElement("div",{className:"returns-info"},n.a.createElement("div",null,"Win: ",e.potential_return_win_user1," FC"),e.user2_name&&n.a.createElement("div",null,"Win: ",e.potential_return_win_user2," FC"))))})))))};t(56);var ae=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(!1),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)("");Object(l.useEffect)(()=>{g()},[]);const g=async()=>{try{s(!0),c("");const t=await o.a.get("/backend/handlers/admin/get_credit_requests.php");if(!t.data.success)throw new Error(t.data.message||"Failed to fetch credit requests");a(t.data.requests||[])}catch(e){console.error("Error fetching credit requests:",e),c(e.message||"Failed to load credit requests. Please try again.")}finally{s(!1)}},b=function(e){let a=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];a?(p(e),c("")):(c(e),p("")),setTimeout(()=>{a?p(""):c("")},3e3)},v=async(e,a,t)=>{try{const n=await o.a.post("/backend/handlers/admin/update_credit_request.php",{request_id:e,status:a});if(!n.data.success)throw new Error(n.data.message||"Failed to update request status");b(`Successfully ${"approved"===a?"approved":"rejected"} credit request for ${t}`),g()}catch(l){console.error("Error updating request status:",l),b(l.message||"Failed to update request status. Please try again.",!1)}},h=e=>{switch(e){case"approved":return"status-badge success";case"rejected":return"status-badge danger";case"expired":return"status-badge warning";default:return"status-badge pending"}},N=e=>new Date(e).toLocaleString();return t?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"transaction-management-container"},n.a.createElement("h1",null,"Credit Request Management"),r&&n.a.createElement("div",{className:"error-message"},r),E&&n.a.createElement("div",{className:"success-message"},E),e.length>0?n.a.createElement("div",{className:"credit-requests-table"},n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Date"),n.a.createElement("th",null,"User"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Payment Method"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Expires"),n.a.createElement("th",null,"Proof"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.map(e=>n.a.createElement("tr",{key:e.request_id},n.a.createElement("td",null,N(e.created_at)),n.a.createElement("td",null,e.username),n.a.createElement("td",null,"\u20a6",parseFloat(e.amount).toLocaleString()),n.a.createElement("td",null,"bank"===e.payment_method_type?n.a.createElement(n.a.Fragment,null,e.bank_name," - ",e.account_number):n.a.createElement(n.a.Fragment,null,e.wallet_name," - ",e.wallet_address)),n.a.createElement("td",null,n.a.createElement("span",{className:h(e.status)},e.status.charAt(0).toUpperCase()+e.status.slice(1))),n.a.createElement("td",null,e.expires_at?N(e.expires_at):"N/A"),n.a.createElement("td",null,n.a.createElement("button",{className:"view-proof-btn",onClick:()=>(e=>{u({url:`/backend/handlers/get_proof_image.php?request_id=${e.request_id}&user_id=${e.user_id}`,amount:e.amount,date:e.created_at,status:e.status,username:e.username}),i(!0)})(e)},"View Proof")),n.a.createElement("td",null,"pending"===e.status&&n.a.createElement("div",{className:"action-buttons"},n.a.createElement("button",{className:"approve-btn",onClick:()=>v(e.request_id,"approved",e.username)},"Approve"),n.a.createElement("button",{className:"reject-btn",onClick:()=>v(e.request_id,"rejected",e.username)},"Reject")))))))):n.a.createElement("div",{className:"no-requests"},n.a.createElement("p",null,"No credit requests found.")),m&&n.a.createElement(e=>{let{proof:a,onClose:t}=e;return a?n.a.createElement("div",{className:"modal-overlay",onClick:t},n.a.createElement("div",{className:"proof-modal",onClick:e=>e.stopPropagation()},n.a.createElement("button",{className:"close-modal",onClick:t},"\xd7"),n.a.createElement("div",{className:"proof-details"},n.a.createElement("h3",null,"Payment Proof - ",a.username),n.a.createElement("p",null,"Amount: \u20a6",parseFloat(a.amount).toLocaleString()),n.a.createElement("p",null,"Date: ",N(a.date)),n.a.createElement("p",null,"Status: ",n.a.createElement("span",{className:h(a.status)},a.status.charAt(0).toUpperCase()+a.status.slice(1)))),n.a.createElement("div",{className:"proof-image-container"},n.a.createElement("img",{src:a.url,alt:"Payment Proof"})))):null},{proof:d,onClose:()=>{i(!1),u(null)}}))};var te=function(){return n.a.createElement("div",{className:"layout"},n.a.createElement(C,null),n.a.createElement("div",{className:"main-content"},n.a.createElement(D,null),n.a.createElement("h1",null,"Leaderboard Management"),n.a.createElement("p",null,"This page is under construction.")))};var le=function(){return n.a.createElement("div",{className:"layout"},n.a.createElement(C,null),n.a.createElement("div",{className:"main-content"},n.a.createElement(D,null),n.a.createElement("h1",null,"System Settings"),n.a.createElement("p",null,"This page is under construction.")))};var ne=function(){return n.a.createElement("div",{className:"layout"},n.a.createElement(C,null),n.a.createElement("div",{className:"main-content"},n.a.createElement(D,null),n.a.createElement("h1",null,"Reports and Analytics"),n.a.createElement("p",null,"This page is under construction.")))};t(57);const se="/backend";var re=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)({username:"",full_name:"",email:"",password:"",favorite_team:"",balance:0}),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)("");Object(l.useEffect)(()=>{d()},[]);const d=async()=>{try{const t=await o.a.get(`${se}/handlers/team_management.php`);a(t.data.data||[])}catch(e){c("Failed to fetch teams")}},u=e=>{const{name:a,value:t}=e.target;s(e=>({...e,[a]:t}))};return n.a.createElement("div",{className:"content-card"},n.a.createElement("div",{className:"add-user"},n.a.createElement("h1",null,"Add New User"),n.a.createElement("form",{onSubmit:async e=>{e.preventDefault(),c(""),i("");try{const e=await o.a.post(`${se}/handlers/add_user.php`,t);e.data.success?(i("User added successfully!"),s({username:"",full_name:"",email:"",password:"",favorite_team:"",balance:0})):c(e.data.message||"Failed to add user")}catch(a){c("Failed to add user")}}},n.a.createElement("label",null,"Username:",n.a.createElement("input",{type:"text",name:"username",value:t.username,onChange:u,placeholder:"Username",required:!0})),n.a.createElement("label",null,"Full Name:",n.a.createElement("input",{type:"text",name:"full_name",value:t.full_name,onChange:u,placeholder:"Full Name",required:!0})),n.a.createElement("label",null,"Email:",n.a.createElement("input",{type:"email",name:"email",value:t.email,onChange:u,placeholder:"Email",required:!0})),n.a.createElement("label",null,"Password:",n.a.createElement("input",{type:"password",name:"password",value:t.password,onChange:u,placeholder:"Password",required:!0})),n.a.createElement("label",null,"Favorite Team:",n.a.createElement("select",{name:"favorite_team",value:t.favorite_team,onChange:u,required:!0},n.a.createElement("option",{value:""},"Select Favorite Team"),e.map(e=>n.a.createElement("option",{key:e.id,value:e.name},e.name)))),n.a.createElement("label",null,"Initial Balance:",n.a.createElement("input",{type:"number",name:"balance",value:t.balance,onChange:u,placeholder:"Initial Balance",required:!0})),n.a.createElement("button",{type:"submit"},"Add User")),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m)))};t(58);const ce="/backend";var me=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)({type:"bank",bank_name:"",account_number:"",account_name:"",account_type:"",wallet_name:"",wallet_address:""}),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(null);Object(l.useEffect)(()=>{E()},[]);const E=async()=>{try{const t=await o.a.get(`${ce}/handlers/payment_methods.php`);a(t.data.data||[])}catch(e){c("Failed to fetch payment methods"),a([])}},p=e=>{const{name:a,value:t}=e.target;s(e=>({...e,[a]:t}))};return n.a.createElement("div",{className:"layout"},n.a.createElement(C,null),n.a.createElement("div",{className:"main-content"},n.a.createElement(D,null),n.a.createElement("div",{className:"payment-methods"},n.a.createElement("h1",null,"Payment Methods"),n.a.createElement("form",{onSubmit:d?async e=>{e.preventDefault();try{const e=await o.a.put(`${ce}/handlers/payment_methods.php?id=${d}`,t);e.data.success?(i("Payment method updated successfully!"),E(),u(null),s({type:"bank",bank_name:"",account_number:"",account_name:"",account_type:"",wallet_name:"",wallet_address:""})):c(e.data.message||"Failed to update payment method")}catch(a){c("Failed to update payment method")}}:async e=>{e.preventDefault(),c(""),i("");try{const e=await o.a.post(`${ce}/handlers/payment_methods.php`,t);e.data.success?(i("Payment method added successfully!"),E(),s({type:"bank",bank_name:"",account_number:"",account_name:"",account_type:"",wallet_name:"",wallet_address:""})):c(e.data.message||"Failed to add payment method")}catch(a){c("Failed to add payment method")}}},n.a.createElement("select",{name:"type",value:t.type,onChange:p},n.a.createElement("option",{value:"bank"},"Bank Account"),n.a.createElement("option",{value:"crypto"},"Crypto Wallet")),"bank"===t.type?n.a.createElement(n.a.Fragment,null,n.a.createElement("input",{type:"text",name:"bank_name",placeholder:"Bank Name",value:t.bank_name,onChange:p,required:!0}),n.a.createElement("input",{type:"text",name:"account_number",placeholder:"Account Number",value:t.account_number,onChange:p,required:!0}),n.a.createElement("input",{type:"text",name:"account_name",placeholder:"Account Name",value:t.account_name,onChange:p,required:!0}),n.a.createElement("select",{name:"account_type",value:t.account_type,onChange:p,required:!0},n.a.createElement("option",{value:""},"Select Account Type"),n.a.createElement("option",{value:"savings"},"Savings"),n.a.createElement("option",{value:"checking"},"Checking"),n.a.createElement("option",{value:"business"},"Business"))):n.a.createElement(n.a.Fragment,null,n.a.createElement("input",{type:"text",name:"wallet_name",placeholder:"Wallet Name",value:t.wallet_name,onChange:p,required:!0}),n.a.createElement("input",{type:"text",name:"wallet_address",placeholder:"Wallet Address",value:t.wallet_address,onChange:p,required:!0})),n.a.createElement("button",{type:"submit"},d?"Update":"Add"," Payment Method")),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m),n.a.createElement("h2",null,"Existing Payment Methods"),e.length>0?n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Type"),n.a.createElement("th",null,"Details"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.map(e=>n.a.createElement("tr",{key:e.id},n.a.createElement("td",null,e.type),n.a.createElement("td",null,"bank"===e.type?n.a.createElement(n.a.Fragment,null,e.bank_name," - ",e.account_number," - ",e.account_name," - ",e.account_type):n.a.createElement(n.a.Fragment,null,e.wallet_name," - ",e.wallet_address)),n.a.createElement("td",null,n.a.createElement("button",{onClick:()=>(e=>{u(e.id),s(e)})(e)},"Edit"),n.a.createElement("button",{onClick:()=>(async e=>{try{await o.a.delete(`${ce}/handlers/payment_methods.php?id=${e}`),E()}catch(a){c("Failed to delete payment method")}})(e.id)},"Delete")))))):n.a.createElement("p",null,"No payment methods available."))))};t(59);const oe="/backend";var ie=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(""),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)("");Object(l.useEffect)(()=>{E()},[]);const E=async()=>{try{const t=await o.a.get(`${oe}/handlers/get_users.php`);t.data.success&&a(t.data.users)}catch(e){u("Failed to fetch users")}};return n.a.createElement("div",{className:"credit-user"},n.a.createElement("h1",null,"Credit User"),m&&n.a.createElement("div",{className:"success-message"},m),d&&n.a.createElement("div",{className:"error-message"},d),n.a.createElement("div",{className:"credit-form-container"},n.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),t&&r)try{const e=new FormData;e.append("user_id",t),e.append("amount",r);const l=await o.a.post(`${oe}/handlers/credit_user.php`,e,{headers:{"Content-Type":"multipart/form-data"}});l.data.success?(i("User credited successfully!"),c(""),s(""),E()):u(l.data.message)}catch(a){u("Failed to credit user")}else u("Please fill in all fields")},className:"credit-form"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Select User:"),n.a.createElement("select",{value:t,onChange:e=>s(e.target.value),required:!0},n.a.createElement("option",{value:""},"Select a user"),e.map(e=>n.a.createElement("option",{key:e.user_id,value:e.user_id},e.username," - Current Balance: ",e.balance," FC")))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Credit Amount (FC):"),n.a.createElement("input",{type:"number",value:r,onChange:e=>c(e.target.value),min:"1",required:!0,placeholder:"Enter amount"})),n.a.createElement("button",{type:"submit",className:"credit-btn"},"Credit User"))))};var de=function(){return n.a.createElement("div",{className:"admin-page"},n.a.createElement(C,null),n.a.createElement("div",{className:"content"},n.a.createElement("h1",null,"Debit User"),n.a.createElement("p",null,"Deduct credits from user accounts here.")))};t(60);const ue="/backend";var Ee=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)({name:"",logo:null}),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)({name:"",logo:null});Object(l.useEffect)(()=>{g()},[]);const g=async()=>{try{const t=await o.a.get(`${ue}/handlers/team_management.php`);a(t.data.data)}catch(e){c("Failed to fetch teams"),a([])}},b=e=>{const{name:a,value:t,type:l}=e.target;s(n=>({...n,[a]:"file"===l?e.target.files[0]:t}))},v=e=>{const{name:a,value:t,type:l}=e.target;p(n=>({...n,[a]:"file"===l?e.target.files[0]:t}))};return n.a.createElement("div",{className:"layout"},n.a.createElement(C,null),n.a.createElement("div",{className:"main-content"},n.a.createElement(D,null),n.a.createElement("div",{className:"team-management"},n.a.createElement("div",{className:"card"},n.a.createElement("h1",null,"Team Management"),n.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),c(""),i(""),t.name&&t.logo)if(["image/svg+xml","image/png"].includes(t.logo.type))try{const e=new FormData;for(const a in t)e.append(a,t[a]);const l=await o.a.post(`${ue}/handlers/team_management.php`,e,{headers:{"Content-Type":"multipart/form-data"}});l.data.success?(i("Team created successfully!"),g(),s({name:"",logo:null}),setTimeout(()=>{window.location.reload()},1e3)):c(l.data.message||"Failed to create team")}catch(a){c("Failed to create team")}else c("Only SVG and PNG files are allowed.");else c("Team name and logo are required.")},encType:"multipart/form-data"},n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"name"},"Team Name:"),n.a.createElement("input",{type:"text",id:"name",name:"name",value:t.name,onChange:b,required:!0})),n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"logo"},"Team Logo:"),n.a.createElement("input",{type:"file",id:"logo",name:"logo",onChange:b,accept:"image/*",required:!0})),n.a.createElement("button",{type:"submit"},"Add Team"))),n.a.createElement("div",{className:"card"},n.a.createElement("h2",null,"Existing Teams"),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m),n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"ID"),n.a.createElement("th",null,"Logo"),n.a.createElement("th",null,"Name"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.map(a=>n.a.createElement("tr",{key:a.id},n.a.createElement("td",null,a.id),n.a.createElement("td",null,n.a.createElement("img",{src:`${ue}/${a.logo}`,alt:a.name,className:"team-logo",style:{width:"50px",height:"50px"}})),n.a.createElement("td",null,a.name),n.a.createElement("td",null,n.a.createElement("button",{onClick:()=>(async e=>{try{await o.a.delete(`${ue}/handlers/team_management.php?id=${e}`),g()}catch(a){c("Failed to delete team")}})(a.id)},"Delete"),n.a.createElement("button",{onClick:()=>(async a=>{u(a);const t=e.find(e=>e.id===a);p({name:t.name,logo:t.logo})})(a.id)},"Edit")))))),d&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"modal"},n.a.createElement("h3",null,"Edit Team"),n.a.createElement("form",{onSubmit:async e=>{e.preventDefault();try{const e=new FormData;e.append("name",E.name),E.logo instanceof File&&e.append("logo",E.logo);const t=await o.a.post(`${ue}/handlers/update_team.php?id=${d}`,e,{headers:{"Content-Type":"multipart/form-data"}});t.data.success?(i("Team updated successfully!"),g(),u(null),p({name:"",logo:null})):c(t.data.message||"Failed to update team")}catch(a){c("Failed to update team"),console.error(a)}},encType:"multipart/form-data"},n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"editName"},"Team Name:"),n.a.createElement("input",{type:"text",id:"editName",name:"name",value:E.name,onChange:v,required:!0})),n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"editLogo"},"Team Logo:"),n.a.createElement("input",{type:"file",id:"editLogo",name:"logo",onChange:v,accept:".svg, .png"})),n.a.createElement("button",{type:"submit"},"Update Team"),n.a.createElement("button",{type:"button",onClick:()=>u(null)},"Cancel"))))))))};t(61);const pe="/backend";const ge=e=>{let{days:a,hours:t,minutes:l,seconds:s,completed:r,date:c}=e;return r?n.a.createElement("span",{className:"countdown-expired"},"Expired"):n.a.createElement("div",{className:"countdown-container"},n.a.createElement("div",{className:"countdown-time"},a>0?`${a}d `:"",t,"h ",l,"m ",s,"s"))},be=e=>{let{challenge:a,onClose:t,teams:l,getTeamLogo:s}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"preview-modal"},n.a.createElement("button",{className:"close-button",onClick:t},"\xd7"),n.a.createElement("h2",null,"Challenge Preview"),n.a.createElement("div",{className:"match-type-section"},n.a.createElement("span",{className:"match-type-badge"},"full_time"===a.match_type?"Full Time":"Half Time")),n.a.createElement("div",{className:"match-preview-display"},n.a.createElement("div",{className:"match-team-preview left-team"},n.a.createElement("img",{src:s(a.team_a),alt:a.team_a,className:"team-logo-large"}),n.a.createElement("h3",{className:"team-name"},a.team_a),n.a.createElement("p",{className:"team-odds"},"Win: ",a.odds_team_a)),n.a.createElement("div",{className:"match-vs-preview"},n.a.createElement("span",{className:"vs-text"},"VS"),n.a.createElement("div",{className:"match-odds"},n.a.createElement("p",{className:"draw-odds"},"Draw: ",a.odds_draw||"0.8"),n.a.createElement("p",{className:"lost-odds"},"Lost: ",a.odds_lost||"0.2"))),n.a.createElement("div",{className:"match-team-preview right-team"},n.a.createElement("img",{src:s(a.team_b),alt:a.team_b,className:"team-logo-large"}),n.a.createElement("h3",{className:"team-name"},a.team_b),n.a.createElement("p",{className:"team-odds"},"Win: ",a.odds_team_b))),n.a.createElement("div",{className:"match-details"},n.a.createElement("div",{className:"detail-row"},n.a.createElement("div",{className:"detail-item"},n.a.createElement("span",{className:"label"},"Start Time"),n.a.createElement("span",{className:"value"},new Date(a.start_time).toLocaleString())),n.a.createElement("div",{className:"detail-item"},n.a.createElement("span",{className:"label"},"End Time"),n.a.createElement("span",{className:"value"},new Date(a.end_time).toLocaleString()))),n.a.createElement("div",{className:"detail-row"},n.a.createElement("div",{className:"detail-item"},n.a.createElement("span",{className:"label"},"Match Date"),n.a.createElement("span",{className:"value"},new Date(a.match_date).toLocaleString())),n.a.createElement("div",{className:"detail-item"},n.a.createElement("span",{className:"label"},"Status"),n.a.createElement("span",{className:"value status-badge"},a.status))))))},ve=e=>{let{challenge:a,onClose:t,onUpdate:l,onChange:s}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"edit-modal"},n.a.createElement("button",{className:"close-button",onClick:t},"\xd7"),n.a.createElement("h2",null,"Edit Challenge"),n.a.createElement("div",{className:"form-content"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Match Type"),n.a.createElement("select",{value:a.match_type||"full_time",onChange:e=>s("match_type",e.target.value)},n.a.createElement("option",{value:"full_time"},"Full Time"),n.a.createElement("option",{value:"half_time"},"Half Time"))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Odds Team A"),n.a.createElement("input",{type:"number",step:"0.01",value:a.odds_team_a,onChange:e=>s("odds_team_a",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Odds Team B"),n.a.createElement("input",{type:"number",step:"0.01",value:a.odds_team_b,onChange:e=>s("odds_team_b",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Draw Odds"),n.a.createElement("input",{type:"number",step:"0.01",value:a.odds_draw||.8,onChange:e=>s("odds_draw",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Lost Odds"),n.a.createElement("input",{type:"number",step:"0.01",value:a.odds_lost||.2,onChange:e=>s("odds_lost",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Status"),n.a.createElement("select",{value:a.status,onChange:e=>s("status",e.target.value)},n.a.createElement("option",{value:"Open"},"Open"),n.a.createElement("option",{value:"Closed"},"Closed"),n.a.createElement("option",{value:"Settled"},"Settled"))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Start Time"),n.a.createElement("input",{type:"datetime-local",value:a.start_time.slice(0,16),onChange:e=>s("start_time",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"End Time"),n.a.createElement("input",{type:"datetime-local",value:a.end_time.slice(0,16),onChange:e=>s("end_time",e.target.value)})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Match Date"),n.a.createElement("input",{type:"datetime-local",value:a.match_date.slice(0,16),onChange:e=>s("match_date",e.target.value)}))),n.a.createElement("div",{className:"form-actions"},n.a.createElement("button",{className:"cancel-button",onClick:t},"Cancel"),n.a.createElement("button",{className:"save-button",onClick:l},"Save Changes"))))},he=e=>{let{challenge:a,onConfirm:t,onCancel:l}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"delete-confirmation-modal"},n.a.createElement("h2",null,"Delete Challenge"),n.a.createElement("div",{className:"modal-content"},n.a.createElement("p",null,"Are you sure you want to delete this expired challenge?"),n.a.createElement("div",{className:"challenge-preview"},n.a.createElement("div",{className:"teams"},n.a.createElement("span",null,a.team_a),n.a.createElement("span",{className:"vs"},"vs"),n.a.createElement("span",null,a.team_b)),n.a.createElement("div",{className:"match-date"},"Match Date: ",new Date(a.match_date).toLocaleString()))),n.a.createElement("div",{className:"modal-actions"},n.a.createElement("button",{className:"cancel-button",onClick:l},"Cancel"),n.a.createElement("button",{className:"confirm-delete-button",onClick:t},"Delete Challenge"))))};var Ne=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(!1),[E,p]=Object(l.useState)(!1),[g,b]=Object(l.useState)(null),[v,h]=Object(l.useState)(1),[N,_]=Object(l.useState)({team_a:"",team_b:"",odds_team_a:1.8,odds_team_b:1.8,start_time:"",end_time:"",match_date:"",status:"Open"}),[f,y]=Object(l.useState)(!1),[w,S]=Object(l.useState)(null);Object(l.useEffect)(()=>{k(),C();const e=setInterval(()=>{O()},5e3);return()=>clearInterval(e)},[]);const C=async()=>{try{const a=await o.a.get(`${pe}/handlers/team_management.php`);200===a.data.status&&s(a.data.data)}catch(e){console.error("Error fetching teams:",e)}},k=async()=>{try{const t=await o.a.get(`${pe}/handlers/challenge_management.php`);t.data.success&&a(t.data.challenges)}catch(e){c("Failed to fetch challenges")}},O=async()=>{try{const a=await o.a.get(`${pe}/handlers/check_challenge_status.php`);a.data.remaining_expired>0&&console.log(`Found ${a.data.remaining_expired} expired challenges that need closing`),a.data.affected_rows>0&&(console.log(`Closed ${a.data.affected_rows} expired challenges`),k())}catch(e){console.error("Error checking challenge status:",e)}},F=e=>{const a=t.find(a=>a.name===e);return a?`${pe}/${a.logo}`:""},j=Math.ceil(e.length/10),L=10*v,x=L-10,$=e.slice(x,L);return n.a.createElement("div",{className:"challenge-management"},n.a.createElement("h1",null,"Challenge Management"),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m),n.a.createElement("div",{className:"challenges-list"},n.a.createElement("div",{className:"table-container"},n.a.createElement("table",{className:"challenge-management-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"#"),n.a.createElement("th",null,"Matchup"),n.a.createElement("th",null,"Time/Date"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,$.map((e,a)=>n.a.createElement("tr",{key:e.challenge_id},n.a.createElement("td",{className:"index-column"},x+a+1),n.a.createElement("td",null,n.a.createElement("div",{className:"matchup-grid"},n.a.createElement("div",{className:"team-block"},n.a.createElement("img",{src:F(e.team_a),alt:e.team_a,className:"team-logo"}),n.a.createElement("div",{className:"team-name"},e.team_a),n.a.createElement("div",{className:"team-odds"},"Odds: ",e.odds_team_a)),n.a.createElement("div",{className:"vs-center"},"VS"),n.a.createElement("div",{className:"team-block"},n.a.createElement("img",{src:F(e.team_b),alt:e.team_b,className:"team-logo"}),n.a.createElement("div",{className:"team-name"},e.team_b),n.a.createElement("div",{className:"team-odds"},"Odds: ",e.odds_team_b)))),n.a.createElement("td",{className:"time-cell"},n.a.createElement("div",{className:"match-time-display"},n.a.createElement("div",{className:"match-time"},"Match: ",new Date(e.match_date).toLocaleString("en-US",{month:"short",day:"numeric",hour:"numeric",minute:"2-digit",hour12:!0})),n.a.createElement("div",{className:"end-time"},"Time Left: ",n.a.createElement(T.a,{date:new Date(e.end_time),renderer:a=>{let{days:t,hours:l,minutes:s,seconds:r,completed:c}=a;return n.a.createElement(ge,{days:t,hours:l,minutes:s,seconds:r,completed:c,date:e.end_time})}})))),n.a.createElement("td",{className:"status-cell"},n.a.createElement("span",{className:`status-badge ${e.status}`},e.status)),n.a.createElement("td",null,n.a.createElement("div",{className:"action-buttons"},n.a.createElement("button",{className:"btn btn-preview",onClick:()=>{b(e),p(!0)}},"Preview"),n.a.createElement("button",{className:"btn btn-edit",onClick:()=>(e=>{_(e),u(!0)})(e)},"Edit"),(e=>new Date(e.end_time)<new Date||"Expired"===e.status)(e)&&n.a.createElement("button",{className:"btn btn-delete",onClick:()=>(e=>{S(e),y(!0)})(e)},"Delete")))))))),n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>h(e=>Math.max(e-1,1)),disabled:1===v},"Previous"),n.a.createElement("span",{className:"page-info"},"Page ",v," of ",j),n.a.createElement("button",{onClick:()=>h(e=>Math.min(e+1,j)),disabled:v===j},"Next"))),E&&g&&n.a.createElement(be,{challenge:g,onClose:()=>p(!1),teams:t,getTeamLogo:F}),d&&n.a.createElement(ve,{challenge:N,onClose:()=>u(!1),onUpdate:async()=>{try{const a=await o.a.post(`${pe}/handlers/challenge_management.php`,N);a.data.success?(i("Challenge updated successfully!"),k(),u(!1)):c(a.data.message||"Failed to update challenge")}catch(e){c("Failed to update challenge"),console.error("Update error:",e)}},onChange:(e,a)=>_(t=>({...t,[e]:a}))}),f&&w&&n.a.createElement(he,{challenge:w,onConfirm:async()=>{if(w){try{const a=await o.a.delete(`${pe}/handlers/challenge_management.php?challenge_id=${w.challenge_id}`);a.data.success?(i("Challenge deleted successfully!"),k()):c(a.data.message||"Failed to delete challenge")}catch(e){c("Failed to delete challenge"),console.error("Delete error:",e)}y(!1),S(null)}},onCancel:()=>{y(!1),S(null)}}))};t(19);var _e=e=>{let{message:a,type:t="success",duration:s=3e3,onClose:r}=e;const[c,m]=Object(l.useState)(!0);return Object(l.useEffect)(()=>{const e=setTimeout(()=>{m(!1),setTimeout(()=>{r&&r()},500)},s);return()=>clearTimeout(e)},[s,r]),n.a.createElement("div",{className:`alert-message ${t} ${c?"":"fade-out"}`},n.a.createElement("span",{className:"alert-icon"},"success"===t?"\u2713":"\u26a0"),n.a.createElement("span",{className:"alert-text"},a))};var fe=e=>{let{alerts:a,removeAlert:t}=e;return n.a.createElement("div",{className:"alert-message-container"},a.map(e=>n.a.createElement(_e,{key:e.id,message:e.message,type:e.type,duration:e.duration,onClose:()=>t(e.id)})))};t(62);const ye="/backend",we={selectedChallenge:"",teamAScore:0,teamBScore:0,selectedWinner:"",selectedLoser:"",newStatus:"Settled",oddsTeamA:0,oddsTeamB:0,oddsDraw:0,oddsLost:0};var Se=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(we),[r,c]=Object(l.useState)(!1),[m,i]=Object(l.useState)(null),[d,u]=Object(l.useState)([]),[E,p]=Object(l.useState)(null),[g,b]=Object(l.useState)({}),v=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3;const l=Date.now();u(n=>[...n,{id:l,message:e,type:a,duration:t}])},h=Object(l.useCallback)(async()=>{try{const t=await o.a.get(`${ye}/handlers/challenge_management.php`,{params:{status:"Closed"}});if(t.data.success){const e=t.data.challenges.filter(e=>"Closed"===e.status).map(e=>({...e,match_date:new Date(e.match_date).toLocaleDateString(),isLeague:Boolean(e.league_id)}));a(e)}else v("Failed to fetch challenges","error")}catch(e){console.error("Error fetching challenges:",e),v("Failed to fetch challenges","error")}},[]),N=Object(l.useCallback)(async e=>{if(e)try{const t=await o.a.get(`${ye}/handlers/get_challenge_bets.php`,{params:{challenge_id:e}});if(t.data.success){i(t.data.bets);const e=new Set(t.data.bets.flatMap(e=>[e.user1_id,e.user2_id]));await _(Array.from(e))}}catch(a){console.error("Error fetching bets preview:",a),v("Failed to load bet details","error")}},[]),_=async e=>{try{const t=await o.a.get(`${ye}/handlers/league_details.php`,{params:{user_ids:e.join(",")}});t.data.success&&p(t.data.leagueDetails)}catch(a){console.error("Error fetching league details:",a),v("Failed to load league information","error")}};Object(l.useEffect)(()=>{h()},[h]);const f=Object(l.useMemo)(()=>e.find(e=>e.challenge_id===parseInt(t.selectedChallenge)),[e,t.selectedChallenge]);Object(l.useEffect)(()=>{f?(s(e=>({...e,oddsTeamA:f.odds_team_a||0,oddsTeamB:f.odds_team_b||0,oddsDraw:f.odds_draw||0,oddsLost:f.odds_lost||0})),N(f.challenge_id)):i(null)},[f,N]);const y=e=>{const{name:a,value:t}=e.target;s(e=>({...e,[a]:a.includes("Score")||a.includes("odds")?parseFloat(t)||0:t}))},w=(e,a)=>{if(!t.selectedWinner)return null;if("draw"===t.selectedWinner)return"draw"===a?e.potential_returns_user1.draw:(e.amount_user1*f.odds_lost).toFixed(2);if(t.selectedWinner!==a)return(e.amount_user1*f.odds_lost).toFixed(2);switch(a){case"team_a_win":case"team_b_win":return e.potential_returns_user1.win;default:return null}};return n.a.createElement("div",{className:"credit-challenge-container"},E&&(null===f||void 0===f?void 0:f.isLeague)?n.a.createElement("section",{className:"league-info-panel"},n.a.createElement("div",{className:"heading-container"},n.a.createElement("h2",{className:"page-heading"},"League Information")),n.a.createElement("div",{className:"league-info-header"},n.a.createElement("img",{src:E.league_logo||"/default-league-logo.png",alt:"League Logo",className:"league-logo"}),n.a.createElement("h3",{className:"league-name"},E.league_name)),n.a.createElement("div",{className:"league-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-label"},"Wins"),n.a.createElement("div",{className:"stat-value"},E.wins||0)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-label"},"Draws"),n.a.createElement("div",{className:"stat-value"},E.draws||0)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("div",{className:"stat-label"},"Losses"),n.a.createElement("div",{className:"stat-value"},E.losses||0))),n.a.createElement("div",{className:"league-position"},n.a.createElement("div",{className:"stat-label"},"Current Position"),n.a.createElement("div",{className:"stat-value"},E.position||"N/A"))):null,n.a.createElement("div",{className:"credit-challenge-content"},n.a.createElement("section",{className:"challenge-form-section"},n.a.createElement("div",{className:"heading-container"},n.a.createElement("h2",{className:"page-heading"},"Settle Challenge")),n.a.createElement("form",{onSubmit:async e=>{if(e.preventDefault(),t.selectedWinner)if("draw"===t.selectedWinner||t.selectedLoser)if("draw"===t.selectedWinner||t.selectedWinner!==t.selectedLoser){c(!0);try{const e=new FormData;e.append("challenge_id",t.selectedChallenge),e.append("team_a_score",t.teamAScore),e.append("team_b_score",t.teamBScore),e.append("winner",t.selectedWinner),e.append("loser",t.selectedLoser),e.append("new_status",t.newStatus);const r=await o.a.post(`${ye}/handlers/complete_challenge.php`,e,{headers:{"Content-Type":"multipart/form-data"}});r.data.success?(v("Challenge settled successfully!"),h(),s(we),i(null)):v(r.data.message||"Failed to complete challenge","error")}catch(n){var a,l;const e=(null===(a=n.response)||void 0===a?void 0:null===(l=a.data)||void 0===l?void 0:l.message)||"Failed to complete challenge";v(e,"error")}finally{c(!1)}}else v("Winner and loser cannot be the same team","error");else v("Please select a loser","error");else v("Please select a winner","error")},className:"form-content"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{className:"form-label"},"Select Challenge:"),n.a.createElement("select",{name:"selectedChallenge",value:t.selectedChallenge,onChange:y,className:"form-select"},n.a.createElement("option",{value:""},"Select a challenge..."),e.map(e=>n.a.createElement("option",{key:e.challenge_id,value:e.challenge_id},e.team_a," vs ",e.team_b,e.isLeague?" (League Match)":""," -",e.match_date)))),f&&n.a.createElement(n.a.Fragment,null,f?n.a.createElement("div",{className:"score-display"},n.a.createElement("div",{className:"team-score-container"},n.a.createElement("div",{className:"team-name"},f.team_a),n.a.createElement("input",{type:"number",name:"teamAScore",value:t.teamAScore,onChange:y,min:"0",className:"score-input"})),n.a.createElement("div",{className:"vs-indicator"},"VS"),n.a.createElement("div",{className:"team-score-container"},n.a.createElement("div",{className:"team-name"},f.team_b),n.a.createElement("input",{type:"number",name:"teamBScore",value:t.teamBScore,onChange:y,min:"0",className:"score-input"}))):null,n.a.createElement("div",{className:"dropdown-section"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{className:"form-label"},"Select Winner:"),n.a.createElement("select",{name:"selectedWinner",value:t.selectedWinner,onChange:y,className:"form-select"},n.a.createElement("option",{value:""},"Select Winner"),n.a.createElement("option",{value:"team_a_win"},f.team_a),n.a.createElement("option",{value:"team_b_win"},f.team_b),n.a.createElement("option",{value:"draw"},"Draw"))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{className:"form-label"},"Select Loser:"),n.a.createElement("select",{name:"selectedLoser",value:t.selectedLoser,onChange:y,className:"form-select",disabled:"draw"===t.selectedWinner},n.a.createElement("option",{value:""},"Select Loser"),"team_a_win"!==t.selectedWinner&&n.a.createElement("option",{value:"team_a_win"},f.team_a),"team_b_win"!==t.selectedWinner&&n.a.createElement("option",{value:"team_b_win"},f.team_b)))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{className:"form-label"},"Challenge Status:"),n.a.createElement("select",{name:"newStatus",value:t.newStatus,onChange:y,className:"form-select"},n.a.createElement("option",{value:"Settled"},"Settled"),n.a.createElement("option",{value:"Completed"},"Completed"),n.a.createElement("option",{value:"Cancelled"},"Cancelled"))),n.a.createElement("button",{type:"submit",className:`submit-button ${r?"loading":""}`,disabled:r},r?"Processing...":"Settle Challenge")))),n.a.createElement("section",{className:"live-preview"},n.a.createElement("div",{className:"heading-container"},n.a.createElement("h2",{className:"page-heading"},"Live Preview")),m&&m.length>0?n.a.createElement("div",{className:"bets-list",role:"list"},m.map((e,a)=>{const l=w(e,e.bet_choice_user1),s=w(e,e.bet_choice_user2),r=l>e.amount_user1?"winning":"losing",c=s>e.amount_user2?"winning":"losing",m=((e,a)=>{const t=e-a;return t>0?{value:`+${t}`,class:"goal-advantage-positive"}:t<0?{value:t.toString(),class:"goal-advantage-negative"}:{value:"0",class:"goal-advantage-neutral"}})(t.teamAScore,t.teamBScore);return n.a.createElement("div",{key:e.bet_id||a,className:"bet-item",role:"listitem"},n.a.createElement("div",{className:"score-summary"},n.a.createElement("div",{className:"admin-score"},n.a.createElement("span",{className:"admin-score-label"},"Final Score:"),n.a.createElement("span",{className:"admin-score-value"},t.teamAScore," - ",t.teamBScore)),n.a.createElement("div",{className:`goal-advantage ${m.class}`},n.a.createElement("span",null,"Goal Advantage:"),n.a.createElement("span",null,m.value))),n.a.createElement("div",{className:"bet-users-container"},n.a.createElement("div",{className:`bet-user ${r}`},n.a.createElement("div",{className:"bet-user-header"},n.a.createElement("div",{className:"user-avatar","aria-hidden":"true"},e.user1_name.charAt(0).toUpperCase()),n.a.createElement("span",{className:"user-name"},e.user1_name)),n.a.createElement("div",{className:"bet-details"},n.a.createElement("span",{className:"bet-choice"},"Choice: ",e.bet_choice_user1),n.a.createElement("span",{className:"bet-amount"},"Stake: ",e.amount_user1)),n.a.createElement("div",{className:"payout-preview"},n.a.createElement("span",{className:"payout-label"},"Payout:"),n.a.createElement("span",{className:"payout-value"},l))),n.a.createElement("div",{className:`bet-user ${c}`},n.a.createElement("div",{className:"bet-user-header"},n.a.createElement("div",{className:"user-avatar","aria-hidden":"true"},e.user2_name.charAt(0).toUpperCase()),n.a.createElement("span",{className:"user-name"},e.user2_name)),n.a.createElement("div",{className:"bet-details"},n.a.createElement("span",{className:"bet-choice"},"Choice: ",e.bet_choice_user2),n.a.createElement("span",{className:"bet-amount"},"Stake: ",e.amount_user2)),n.a.createElement("div",{className:"payout-preview"},n.a.createElement("span",{className:"payout-label"},"Payout:"),n.a.createElement("span",{className:"payout-value"},s)))))})):n.a.createElement("div",{className:"no-bets-message",role:"status"},f?"No bets found for this challenge":"Select a challenge to view bets"))),n.a.createElement(fe,{alerts:d,removeAlert:e=>{u(a=>a.filter(a=>a.id!==e))}}))};t(63);const Ce="/backend";function ke(e){let{league:a,onChange:t,onSubmit:l,onClose:s}=e;return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"league-modal"},n.a.createElement("button",{className:"close-button",onClick:s},"\xd7"),n.a.createElement("h2",null,"Create New League"),n.a.createElement("form",{onSubmit:l},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"League Name"),n.a.createElement("input",{type:"text",value:a.name,onChange:e=>t({...a,name:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-row"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Minimum Bet (\u20a6)"),n.a.createElement("input",{type:"number",value:a.min_bet_amount,onChange:e=>t({...a,min_bet_amount:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Maximum Bet (\u20a6)"),n.a.createElement("input",{type:"number",value:a.max_bet_amount,onChange:e=>t({...a,max_bet_amount:e.target.value}),required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Description"),n.a.createElement("textarea",{value:a.description,onChange:e=>t({...a,description:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"League Rules"),n.a.createElement("textarea",{value:a.league_rules,onChange:e=>t({...a,league_rules:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Reward Description"),n.a.createElement("textarea",{value:a.reward_description,onChange:e=>t({...a,reward_description:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-row"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"League Icon"),n.a.createElement("input",{type:"file",id:"league_icon",accept:"image/*"})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"League Banner"),n.a.createElement("input",{type:"file",id:"league_banner",accept:"image/*"}))),n.a.createElement("div",{className:"form-actions"},n.a.createElement("button",{type:"button",className:"cancel-button",onClick:s},"Cancel"),n.a.createElement("button",{type:"submit",className:"submit-button"},"Create League")))))}const Oe=e=>{let{league:a,onClose:t,onSave:s}=e;const[r,c]=Object(l.useState)({name:a.name||"",description:a.description||"",min_bet_amount:a.min_bet_amount||"",max_bet_amount:a.max_bet_amount||"",season_duration:a.season_duration||90,league_rules:a.league_rules||"",reward_description:a.reward_description||"",status:a.status||"upcoming",theme_color:a.theme_color||"#007bff"}),[m,o]=Object(l.useState)(a.icon_url||null),[i,d]=Object(l.useState)(a.banner_url||null),[u,E]=Object(l.useState)(""),[p,g]=Object(l.useState)(!1),b=e=>{const{name:a,value:t}=e.target;c(e=>({...e,[a]:t}))},v=e=>{const{name:a,files:t}=e.target;if(t&&t[0]){const e=t[0],l=new FileReader;l.onloadend=(()=>{"icon"===a?o(l.result):"banner"===a&&d(l.result)}),l.readAsDataURL(e),c(t=>({...t,[a]:e}))}};return n.a.createElement("div",{className:"edit-league-modal",onClick:e=>"edit-league-modal"===e.target.className&&t()},n.a.createElement("div",{className:"edit-league-content"},n.a.createElement("div",{className:"edit-league-header"},n.a.createElement("h2",null,"Edit League"),n.a.createElement("button",{className:"close-button",onClick:t},"\xd7")),u&&n.a.createElement("div",{className:"error-message"},u),n.a.createElement("form",{onSubmit:async e=>{e.preventDefault(),g(!0),E("");try{const e=["name","min_bet_amount","max_bet_amount","description"];for(const a of e)if(!r[a])throw new Error(`${a} is required`);const l=new FormData;Object.keys(r).forEach(e=>{null!==r[e]&&void 0!==r[e]&&l.append(e,r[e])}),await s(l),t()}catch(a){E(a.message||"Failed to update league")}finally{g(!1)}},className:"edit-league-form"},n.a.createElement("div",{className:"form-section"},n.a.createElement("h3",null,"League Media"),n.a.createElement("div",{className:"media-preview-section"},n.a.createElement("div",{className:"icon-preview-container"},n.a.createElement("label",{htmlFor:"icon"},m?n.a.createElement("img",{src:m,alt:"League icon preview",className:"preview-image"}):n.a.createElement("div",{className:"preview-placeholder"},n.a.createElement(S.A,null),n.a.createElement("span",null,"Upload League Icon"),n.a.createElement("small",null,"Recommended: 300x300px"))),n.a.createElement("input",{type:"file",id:"icon",name:"icon",onChange:v,accept:"image/*",style:{display:"none"}})),n.a.createElement("div",{className:"banner-preview-container"},n.a.createElement("label",{htmlFor:"banner"},i?n.a.createElement("img",{src:i,alt:"League banner preview",className:"preview-image"}):n.a.createElement("div",{className:"preview-placeholder"},n.a.createElement(S.A,null),n.a.createElement("span",null,"Upload League Banner"),n.a.createElement("small",null,"Recommended: 1200x400px"))),n.a.createElement("input",{type:"file",id:"banner",name:"banner",onChange:v,accept:"image/*",style:{display:"none"}}))),n.a.createElement("div",{className:"color-preview-section",style:{"--theme-color":r.theme_color}},n.a.createElement("label",{htmlFor:"theme_color"},"Theme Color"),n.a.createElement("input",{type:"color",id:"theme_color",name:"theme_color",value:r.theme_color,onChange:b,className:"color-picker"}),n.a.createElement("span",{className:"color-value"},r.theme_color))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h3",null,"Basic Information"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"name"},"League Name"),n.a.createElement("input",{type:"text",id:"name",name:"name",value:r.name,onChange:b,required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"description"},"Description"),n.a.createElement("textarea",{id:"description",name:"description",value:r.description,onChange:b,required:!0}))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h3",null,"League Settings"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"min_bet_amount"},"Minimum Bet Amount"),n.a.createElement("input",{type:"number",id:"min_bet_amount",name:"min_bet_amount",value:r.min_bet_amount,onChange:b,required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"max_bet_amount"},"Maximum Bet Amount"),n.a.createElement("input",{type:"number",id:"max_bet_amount",name:"max_bet_amount",value:r.max_bet_amount,onChange:b,required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"season_duration"},"Season Duration (days)"),n.a.createElement("input",{type:"number",id:"season_duration",name:"season_duration",value:r.season_duration,onChange:b,min:"1",required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"status"},"Status"),n.a.createElement("select",{id:"status",name:"status",value:r.status,onChange:b},n.a.createElement("option",{value:"upcoming"},"Upcoming"),n.a.createElement("option",{value:"active"},"Active"),n.a.createElement("option",{value:"completed"},"Completed")))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h3",null,"Additional Information"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"league_rules"},"League Rules"),n.a.createElement("textarea",{id:"league_rules",name:"league_rules",value:r.league_rules,onChange:b})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"reward_description"},"Reward Description"),n.a.createElement("textarea",{id:"reward_description",name:"reward_description",value:r.reward_description,onChange:b}))),n.a.createElement("div",{className:"form-actions"},n.a.createElement("button",{type:"button",onClick:t,className:"cancel-button",disabled:p},"Cancel"),n.a.createElement("button",{type:"submit",className:"save-button",disabled:p},p?"Saving...":"Save Changes")))))};var Fe=function(){const e=Object(c.q)(),[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)(""),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(!0),[E,p]=Object(l.useState)(!1),[g,b]=Object(l.useState)(!1),[v,h]=Object(l.useState)(null),[N,_]=Object(l.useState)({name:"",min_bet_amount:"",max_bet_amount:"",description:"",season_duration:90,league_rules:"",reward_description:"",status:"upcoming"}),f=(e,a)=>e?e.includes("uploads")?`${Ce}/${e}`:`${Ce}/uploads/leagues/${a}s/${e}`:null,y=async()=>{try{u(!0),r("");const a=await o.a.get(`${Ce}/handlers/league_management.php`);200===a.data.status?t(a.data.data||[]):r(a.data.message||"Failed to fetch leagues")}catch(e){console.error("Error fetching leagues:",e),r("Failed to fetch leagues. Please try again.")}finally{u(!1)}};return Object(l.useEffect)(()=>{y()},[]),n.a.createElement("div",{className:"league-management"},n.a.createElement("header",{className:"page-header"},n.a.createElement("div",{className:"header-content"},n.a.createElement("h1",null,"League Management"),n.a.createElement("button",{className:"btn primary create-league-btn",onClick:()=>e("/admin/league-management/create")},"Create New League")),s&&n.a.createElement("div",{className:"error-message"},s),m&&n.a.createElement("div",{className:"success-message"},m)),d?n.a.createElement("div",{className:"loading"},"Loading leagues..."):0===a.length?n.a.createElement("div",{className:"no-leagues"},"No leagues found. Create your first league!"):n.a.createElement("div",{className:"leagues-grid"},a.map(a=>n.a.createElement("div",{key:a.league_id,className:"league-card"},n.a.createElement("img",{src:f(a.banner_path,"banner"),alt:`${a.name} banner`,className:"league-banner",onError:e=>{e.target.onerror=null,e.target.style.backgroundColor=a.theme_color||"#f0f0f0"}}),n.a.createElement("div",{className:"status",style:{background:"active"===a.status?"#e8f5e9":"upcoming"===a.status?"#e3f2fd":"#fafafa",color:"active"===a.status?"#2e7d32":"upcoming"===a.status?"#1565c0":"#616161"}},a.status),n.a.createElement("div",{className:"league-header"},n.a.createElement("img",{src:f(a.icon_path,"icon"),alt:`${a.name} icon`,className:"league-icon",onError:e=>{e.target.onerror=null,e.target.style.backgroundColor=a.theme_color||"#f0f0f0",e.target.style.padding="8px"}}),n.a.createElement("h2",null,a.name)),n.a.createElement("div",{className:"league-content"},n.a.createElement("div",{className:"description"},a.description),n.a.createElement("div",{className:"league-info"},n.a.createElement("span",{className:"info-label"},"Min Bet"),n.a.createElement("span",{className:"info-value"},"$",a.min_bet_amount)),n.a.createElement("div",{className:"league-info"},n.a.createElement("span",{className:"info-label"},"Max Bet"),n.a.createElement("span",{className:"info-value"},"$",a.max_bet_amount)),n.a.createElement("div",{className:"league-info"},n.a.createElement("span",{className:"info-label"},"Duration"),n.a.createElement("span",{className:"info-value"},a.season_duration," days")),n.a.createElement("div",{className:"league-actions"},n.a.createElement("button",{onClick:()=>{h(a),b(!0)},className:"edit-button"},"Edit League"),n.a.createElement("button",{onClick:()=>(a=>{e(`/admin/league-management/${a.league_id}/seasons`)})(a)},"Manage Seasons")))))),E&&n.a.createElement(ke,{league:N,onChange:_,onSubmit:async e=>{e.preventDefault();try{r("");const e=new FormData;Object.keys(N).forEach(a=>{e.append(a,N[a])});const t=await o.a.post(`${Ce}/handlers/league_management.php`,e,{headers:{"Content-Type":"multipart/form-data"}});200===t.data.status||201===t.data.status?(i("League created successfully"),p(!1),y(),_({name:"",min_bet_amount:"",max_bet_amount:"",description:"",season_duration:90,league_rules:"",reward_description:"",status:"upcoming"})):r(t.data.message||"Failed to create league")}catch(a){console.error("Error creating league:",a),r("Failed to create league. Please try again.")}},onClose:()=>p(!1)}),g&&v&&n.a.createElement(Oe,{league:v,onClose:()=>{b(!1),h(null)},onSave:async e=>{try{r(""),i(""),console.log("Updated data received:",e);const t=new FormData;t.append("action","update"),t.append("league_id",v.league_id);const l=["name","min_bet_amount","max_bet_amount","description"];l.forEach(e=>{t.append(e,v[e])});for(let[a,r]of e.entries())t.append(a,r);let n=[];if(l.forEach(e=>{t.get(e)||n.push(e)}),n.length>0)throw new Error(`Missing required fields: ${n.join(", ")}`);console.log("Form data being sent:");for(let[e,a]of t.entries())console.log(e,":",a);const s=await o.a.post(`${Ce}/handlers/league_management.php`,t,{headers:{"Content-Type":"multipart/form-data"}});200===s.data.status?(i("League updated successfully"),b(!1),await y()):r(s.data.message||"Failed to update league")}catch(a){console.error("Error updating league:",a),r(a.message||"Failed to update league. Please try again.")}}}))};t(64);const je="/backend";function Le(e){let{onSubmit:a,onClose:t}=e;const[s,r]=Object(l.useState)({season_name:"",start_date:"",end_date:"",prize_pool:""});return n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"season-modal"},n.a.createElement("button",{className:"close-button",onClick:t},"\xd7"),n.a.createElement("h2",null,"Create New Season"),n.a.createElement("form",{onSubmit:e=>{e.preventDefault(),a(s)}},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Season Name"),n.a.createElement("input",{type:"text",value:s.season_name,onChange:e=>r({...s,season_name:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-row"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Start Date"),n.a.createElement("input",{type:"datetime-local",value:s.start_date,onChange:e=>r({...s,start_date:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"End Date"),n.a.createElement("input",{type:"datetime-local",value:s.end_date,onChange:e=>r({...s,end_date:e.target.value}),required:!0}))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Prize Pool (\u20a6)"),n.a.createElement("input",{type:"number",value:s.prize_pool,onChange:e=>r({...s,prize_pool:e.target.value}),required:!0})),n.a.createElement("div",{className:"form-actions"},n.a.createElement("button",{type:"button",className:"cancel-button",onClick:t},"Cancel"),n.a.createElement("button",{type:"submit",className:"submit-button"},"Create Season")))))}var Te=function(){const{leagueId:e}=Object(c.s)(),[a,t]=Object(l.useState)(null),[s,r]=Object(l.useState)([]),[m,i]=Object(l.useState)(""),[d,u]=Object(l.useState)(""),[E,p]=Object(l.useState)(!1),[g,b]=Object(l.useState)(!0),v=Object(l.useCallback)(async()=>{try{const l=await o.a.get(`${je}/handlers/league_management.php?league_id=${e}`);200===l.data.status?t(l.data.data):i(l.data.message||"Failed to fetch league details")}catch(a){console.error("Error fetching league details:",a),i("Failed to fetch league details")}},[e]),h=Object(l.useCallback)(async()=>{try{const t=await o.a.get(`${je}/handlers/league_season_management.php?league_id=${e}`);200===t.data.status?r(t.data.data||[]):i(t.data.message||"Failed to fetch seasons")}catch(a){console.error("Error fetching seasons:",a),i("Failed to fetch seasons")}finally{b(!1)}},[e]),N=Object(l.useCallback)(async a=>{try{i("");const l=await o.a.post(`${je}/handlers/league_season_management.php`,{...a,league_id:e});201===l.data.status?(u("Season created successfully"),p(!1),h()):i(l.data.message||"Failed to create season")}catch(t){console.error("Error creating season:",t),i("Failed to create season. Please try again.")}},[e,h]),_=Object(l.useCallback)(async e=>{try{i("");const t=await o.a.put(`${je}/handlers/league_season_management.php`,{season_id:e,action:"end"});200===t.data.status?(u("Season ended successfully"),h()):i(t.data.message||"Failed to end season")}catch(a){console.error("Error ending season:",a),i("Failed to end season. Please try again.")}},[h]);return Object(l.useEffect)(()=>{(async()=>{try{await Promise.all([v(),h()])}catch(m){console.error("Error loading data:",m)}})()},[e,v,h]),n.a.createElement("div",{className:"league-season-management"},n.a.createElement("div",{className:"header"},n.a.createElement("h1",null,null===a||void 0===a?void 0:a.name," - Season Management"),n.a.createElement("button",{className:"create-button",onClick:()=>p(!0)},"Create New Season")),m&&n.a.createElement("div",{className:"error-message"},m),d&&n.a.createElement("div",{className:"success-message"},d),g?n.a.createElement("div",{className:"loading"},"Loading seasons..."):n.a.createElement("div",{className:"seasons-list"},0===s.length?n.a.createElement("div",{className:"no-seasons"},"No seasons found for this league"):s.map(e=>n.a.createElement("div",{key:e.season_id,className:"season-card"},n.a.createElement("div",{className:"season-header"},n.a.createElement("h3",null,e.season_name),n.a.createElement("span",{className:`status ${e.status.toLowerCase()}`},e.status)),n.a.createElement("div",{className:"season-details"},n.a.createElement("p",null,"Start Date: ",new Date(e.start_date).toLocaleDateString()),n.a.createElement("p",null,"End Date: ",new Date(e.end_date).toLocaleDateString()),n.a.createElement("p",null,"Prize Pool: ",e.prize_pool)),"active"===e.status&&n.a.createElement("div",{className:"season-actions"},n.a.createElement("button",{className:"end-season-button",onClick:()=>_(e.season_id)},"End Season"))))),E&&n.a.createElement(Le,{onSubmit:N,onClose:()=>p(!1)}))};t(65);var xe=()=>{const e=Object(c.q)(),[a,t]=Object(l.useState)(""),[s,r]=Object(l.useState)(!1),[m,i]=Object(l.useState)(null),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)({name:"",min_bet_amount:"",max_bet_amount:"",description:"",season_duration:90,league_rules:"",reward_description:"",status:"upcoming",theme_color:"#007bff",icon:null,banner:null}),g=e=>{const{name:a,value:t}=e.target;p(e=>({...e,[a]:t}))},b=e=>{const{name:a,files:t}=e.target;if(t&&t[0]){const e=t[0],l=new FileReader;l.onloadend=(()=>{"icon"===a?i(l.result):"banner"===a&&u(l.result)}),l.readAsDataURL(e),p(t=>({...t,[a]:e}))}};return n.a.createElement("div",{className:"create-league-container"},n.a.createElement("div",{className:"create-league-header"},n.a.createElement("h1",null,"Create New League"),n.a.createElement("p",null,"Set up a new league competition")),a&&n.a.createElement("div",{className:"error-message"},a),n.a.createElement("form",{onSubmit:async a=>{a.preventDefault(),r(!0),t("");try{const a=new FormData;Object.keys(E).forEach(e=>{"icon"===e||"banner"===e?E[e]&&a.append(e,E[e]):a.append(e,E[e])});const n=await o.a.post("/backend/handlers/league_management.php",a,{headers:{"Content-Type":"multipart/form-data"}});200===n.data.status||201===n.data.status?e("/admin/league-management",{state:{message:"League created successfully"}}):t(n.data.message||"Failed to create league")}catch(l){console.error("Error creating league:",l),t("Failed to create league. Please try again.")}finally{r(!1)}},className:"create-league-form"},n.a.createElement("div",{className:"form-section media-section"},n.a.createElement("h2",null,"League Media"),n.a.createElement("div",{className:"media-preview"},n.a.createElement("div",{className:"icon-upload"},n.a.createElement("label",{htmlFor:"icon",className:"upload-label"},m?n.a.createElement("img",{src:m,alt:"League icon preview",className:"icon-preview"}):n.a.createElement("div",{className:"upload-placeholder"},n.a.createElement(S.A,null),n.a.createElement("span",null,"Upload League Icon"),n.a.createElement("small",null,"Recommended size: 300x300px"))),n.a.createElement("input",{type:"file",id:"icon",name:"icon",accept:"image/*",onChange:b,className:"file-input"})),n.a.createElement("div",{className:"banner-upload"},n.a.createElement("label",{htmlFor:"banner",className:"upload-label"},d?n.a.createElement("img",{src:d,alt:"League banner preview",className:"banner-preview"}):n.a.createElement("div",{className:"upload-placeholder"},n.a.createElement(S.M,null),n.a.createElement("span",null,"Upload League Banner"),n.a.createElement("small",null,"Recommended size: 1200x400px"))),n.a.createElement("input",{type:"file",id:"banner",name:"banner",accept:"image/*",onChange:b,className:"file-input"}))),n.a.createElement("div",{className:"color-picker-section"},n.a.createElement("label",{htmlFor:"theme_color"},"Theme Color"),n.a.createElement("input",{type:"color",id:"theme_color",name:"theme_color",value:E.theme_color,onChange:g}),n.a.createElement("div",{className:"color-preview",style:{"--selected-color":E.theme_color,background:E.theme_color}},"Selected Color: ",E.theme_color))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h2",null,"Basic Information"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"name"},"League Name *"),n.a.createElement("input",{type:"text",id:"name",name:"name",value:E.name,onChange:g,required:!0,placeholder:"Enter league name"})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"description"},"Description *"),n.a.createElement("textarea",{id:"description",name:"description",value:E.description,onChange:g,required:!0,placeholder:"Describe the league",rows:"4"}))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h2",null,"Betting Limits"),n.a.createElement("div",{className:"form-row"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"min_bet_amount"},"Minimum Bet Amount *"),n.a.createElement("input",{type:"number",id:"min_bet_amount",name:"min_bet_amount",value:E.min_bet_amount,onChange:g,required:!0,min:"0",step:"0.01"})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"max_bet_amount"},"Maximum Bet Amount *"),n.a.createElement("input",{type:"number",id:"max_bet_amount",name:"max_bet_amount",value:E.max_bet_amount,onChange:g,required:!0,min:"0",step:"0.01"})))),n.a.createElement("div",{className:"form-section"},n.a.createElement("h2",null,"League Settings"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"season_duration"},"Season Duration (days)"),n.a.createElement("input",{type:"number",id:"season_duration",name:"season_duration",value:E.season_duration,onChange:g,min:"1",max:"365"})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"league_rules"},"League Rules *"),n.a.createElement("textarea",{id:"league_rules",name:"league_rules",value:E.league_rules,onChange:g,required:!0,placeholder:"Enter league rules and guidelines",rows:"6"})),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",{htmlFor:"reward_description"},"Reward Structure *"),n.a.createElement("textarea",{id:"reward_description",name:"reward_description",value:E.reward_description,onChange:g,required:!0,placeholder:"Describe the reward structure",rows:"4"}))),n.a.createElement("div",{className:"form-actions"},n.a.createElement("button",{type:"button",className:"btn secondary",onClick:()=>e("/admin/league-management")},"Cancel"),n.a.createElement("button",{type:"submit",className:"btn primary",disabled:s},s?"Creating...":"Create League"))))};t(66);var $e=()=>{const{leagueId:e}=Object(c.s)(),a=Object(c.q)(),[t,s]=Object(l.useState)(null),[r,m]=Object(l.useState)([]),[o,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(!0),[p,g]=Object(l.useState)(""),b=localStorage.getItem("userId"),v=e=>new Intl.NumberFormat("en-US",{minimumFractionDigits:0,maximumFractionDigits:0}).format(e),h=e=>{const a=e%10,t=e%100;return 1===a&&11!==t?e+"st":2===a&&12!==t?e+"nd":3===a&&13!==t?e+"rd":e+"th"};return Object(l.useEffect)(()=>{e&&(async()=>{try{E(!0),g("");const n=await i.get(`/backend/handlers/league_details.php?league_id=${e}${b?`&user_id=${b}`:""}`);if(console.log("API Response:",n.data),200===n.data.status){const{league:e,leaderboard:a,user_position:t}=n.data.data;if(console.log("League data:",e),!e||"number"!==typeof e.id)throw new Error("Invalid league data received");const l=a.map((e,a)=>({...e,deposit_amount_formatted:v(e.deposit_amount)+" FC",rank_display:h(e.rank_position),rank_position:a+1})),r=t?{...t,deposit_amount_formatted:v(t.deposit_amount)+" FC",rank_display:h(t.rank)}:null;s(e),m(l),d(r)}else g(n.data.message||"Failed to load league details")}catch(l){var t;console.error("Error fetching league details:",l),401===(null===(t=l.response)||void 0===t?void 0:t.status)?a("/login"):g("Failed to load league details. Please try again.")}finally{E(!1)}})()},[e,b,a]),n.a.createElement("div",{className:"league-details__container"},u?n.a.createElement("div",null,"Loading..."):p?n.a.createElement("div",{className:"error-message"},p):t?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"league-details__banner-wrapper"},n.a.createElement("img",{src:t.league_banner,alt:`${t.name} banner`,className:"league-details__banner-image",onError:e=>{e.target.src="/images/default-league-banner.jpg",e.target.onerror=null}}),n.a.createElement("div",{className:"league-details__banner-overlay"},n.a.createElement("div",{className:"league-details__icon-wrapper"},n.a.createElement("img",{src:t.league_icon,alt:`${t.name} icon`,className:"league-details__icon-image",onError:e=>{e.target.src="/images/default-league-icon.png",e.target.onerror=null}})),n.a.createElement("div",{className:"league-details__banner-content"},n.a.createElement("h1",{className:"league-details__title"},t.name),n.a.createElement("p",{className:"league-details__description"},t.description)))),o&&n.a.createElement("div",{className:"league-details__position-card"},n.a.createElement("h2",{className:"league-details__position-title"},n.a.createElement(S.L,null)," Your Position"),n.a.createElement("div",{className:"league-details__stats-grid"},n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"Rank"),n.a.createElement("span",{className:"league-details__stat-value"},o.rank_display)),n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"Points"),n.a.createElement("span",{className:"league-details__stat-value"},o.points)),n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"Streak"),n.a.createElement("span",{className:"league-details__stat-value"},o.current_streak)),n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"W/D/L"),n.a.createElement("span",{className:"league-details__stat-value"},o.wins,"/",o.draws,"/",o.losses)),n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"Total Bets"),n.a.createElement("span",{className:"league-details__stat-value"},o.total_bets)),n.a.createElement("div",{className:"league-details__stat-item"},n.a.createElement("span",{className:"league-details__stat-label"},"Deposit"),n.a.createElement("span",{className:"league-details__stat-value"},o.deposit_amount_formatted))),n.a.createElement("div",{className:"league-details__progress-section"},n.a.createElement("div",{className:"league-details__progress-info"},n.a.createElement("span",null,"Progress to Next Rank"),n.a.createElement("span",null,Math.max(50-o.points,0)," points needed")),n.a.createElement("div",{className:"league-details__progress-container"},n.a.createElement("div",{className:"league-details__progress-bar",style:{width:`${Math.min(o.points/50*100,100)}%`}})))),n.a.createElement("div",{className:"league-details__leaderboard"},n.a.createElement("h2",{className:"league-details__leaderboard-title"},n.a.createElement(S.L,null)," League Leaderboard"),n.a.createElement("div",{className:"league-details__table-container"},n.a.createElement("table",{className:"league-details__table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Rank"),n.a.createElement("th",null,"Player"),n.a.createElement("th",null,"Points"),n.a.createElement("th",null,"Streak"),n.a.createElement("th",null,"W/D/L"),n.a.createElement("th",null,"Total Bets"),n.a.createElement("th",null,"Deposit"),n.a.createElement("th",null,"Join Date"))),n.a.createElement("tbody",null,r.map((e,a)=>{var t;return n.a.createElement("tr",{key:`${e.user_id}-${null!==(t=e.membership_id)&&void 0!==t?t:a}`,className:(null===o||void 0===o?void 0:o.user_id)===e.user_id?"current-user":""},n.a.createElement("td",null,n.a.createElement("div",{className:"league-details__rank"},e.rank_display,1===e.rank_position&&n.a.createElement(S.L,{className:"league-details__rank-icon gold"}),2===e.rank_position&&n.a.createElement(S.C,{className:"league-details__rank-icon silver"}),3===e.rank_position&&n.a.createElement(S.C,{className:"league-details__rank-icon bronze"}))),n.a.createElement("td",null,e.username),n.a.createElement("td",{className:"league-details__points"},e.points,n.a.createElement("div",{className:"league-details__progress-container"},n.a.createElement("div",{className:"league-details__progress-bar",style:{width:`${Math.min(e.points/50*100,100)}%`}}))),n.a.createElement("td",null,e.current_streak),n.a.createElement("td",null,e.wins,"/",e.draws,"/",e.losses),n.a.createElement("td",null,e.total_bets),n.a.createElement("td",null,e.deposit_amount_formatted),n.a.createElement("td",null,new Date(e.join_date).toLocaleDateString()))})))))):n.a.createElement("div",null,"No league found"))};t(67);var De=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,c]=Object(l.useState)(""),[m,o]=Object(l.useState)(""),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)(!1),[g,b]=Object(l.useState)([]),[v,h]=Object(l.useState)(!1),[N,_]=Object(l.useState)(1),[f]=Object(l.useState)(10);Object(l.useEffect)(()=>{y()},[]);const y=async()=>{try{s(!0),c("");const t=await i.get("/backend/handlers/league_management.php");200===t.data.status?a(t.data.data||[]):c(t.data.message||"Failed to fetch leagues")}catch(e){console.error("Error fetching leagues:",e),c("Failed to fetch leagues. Please try again.")}finally{s(!1)}},w=async a=>{try{h(!0),c("");const s=await i.get(`/backend/handlers/admin/get_league_users.php?league_id=${a}`,{headers:{Authorization:`Bearer ${localStorage.getItem("adminToken")}`}});console.log("League users response:",s.data),200===s.data.status?(b(s.data.data||[]),u(e.find(e=>e.league_id===a)),p(!0),_(1)):c(s.data.message||"Failed to fetch league users")}catch(n){var t,l;console.error("Error fetching league users:",n),c("Failed to fetch league users: "+((null===(t=n.response)||void 0===t?void 0:null===(l=t.data)||void 0===l?void 0:l.message)||n.message))}finally{h(!1)}},C=N*f,k=C-f,O=(g.slice(k,C),Math.ceil(g.length/f)),F=e=>_(e);return t?n.a.createElement("div",{className:"loading"},"Loading leagues..."):n.a.createElement("div",{className:"league-user-management"},n.a.createElement("header",{className:"page-header"},n.a.createElement("h1",null,"League User Management"),r&&n.a.createElement("div",{className:"error-message"},r),m&&n.a.createElement("div",{className:"success-message"},m)),t?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"leagues-table"},n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",{className:"table-header"},n.a.createElement("th",null,"League"),n.a.createElement("th",null,"Description"),n.a.createElement("th",null,"Members"),n.a.createElement("th",null,"Bet Range"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,e.map(e=>n.a.createElement("tr",{key:e.league_id,className:"table-row"},n.a.createElement("td",null,n.a.createElement("div",{className:"name"},e.name)),n.a.createElement("td",null,n.a.createElement("div",{className:"league-description"},e.description||"No description available")),n.a.createElement("td",null,n.a.createElement("div",{className:"members"},n.a.createElement(S.T,null),e.member_count||0)),n.a.createElement("td",{className:"bet-range"},n.a.createElement("div",{className:"bet-range-container"},n.a.createElement("div",null,"FC",parseFloat(e.min_bet_amount).toLocaleString()),n.a.createElement("div",null,"FC",parseFloat(e.max_bet_amount).toLocaleString()))),n.a.createElement("td",null,n.a.createElement("span",{className:`status-badge ${e.status.toLowerCase()}`},e.status)),n.a.createElement("td",null,n.a.createElement("button",{className:"view-users-btn",onClick:()=>w(e.league_id)},"View Users"))))))),E&&n.a.createElement(()=>n.a.createElement("div",{className:"modal-overlay",onClick:()=>p(!1)},n.a.createElement("div",{className:"leaderboard-modal",onClick:e=>e.stopPropagation()},n.a.createElement("div",{className:"modal-header"},n.a.createElement("h2",null,"League Users"),n.a.createElement("button",{className:"close-modal",onClick:()=>p(!1)},"\xd7")),n.a.createElement("div",{className:"leaderboard-table"},n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",{className:"table-header"},n.a.createElement("th",null,"Rank"),n.a.createElement("th",null,"User"),n.a.createElement("th",null,"Points"),n.a.createElement("th",null,"W/D/L"),n.a.createElement("th",null,"Deposit"),n.a.createElement("th",null,"Join Date"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,g.map((e,a)=>n.a.createElement("tr",{key:e.user_id,className:"table-row"},n.a.createElement("td",null,a+1),n.a.createElement("td",null,e.username),n.a.createElement("td",{className:"points"},e.points||0),n.a.createElement("td",{className:"stats"},n.a.createElement("span",null,e.wins||0),"/",n.a.createElement("span",null,e.draws||0),"/",n.a.createElement("span",null,e.losses||0)),n.a.createElement("td",{className:"deposit"},"FC",parseFloat(e.deposit_amount||0).toLocaleString()),n.a.createElement("td",null,new Date(e.join_date).toLocaleDateString()),n.a.createElement("td",null,n.a.createElement("button",{className:"delete-btn",onClick:()=>(async(e,a)=>{if(window.confirm("Are you sure you want to remove this user from the league? Their registration fee will be refunded."))try{c("");const l=await i.post("/backend/handlers/admin/remove_league_user.php",{user_id:e,league_id:a});200===l.data.status?(o("User successfully removed from the league"),await w(a)):c(l.data.message||"Failed to remove user from league")}catch(t){console.error("Error removing user:",t),c("Failed to remove user. Please try again.")}})(e.user_id,d)},"Delete"))))))))),null),n.a.createElement(()=>n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>F(N-1),disabled:1===N,className:"pagination-button"},"Previous"),n.a.createElement("span",{className:"page-info"},"Page ",N," of ",O," (",g.length," total users)"),n.a.createElement("button",{onClick:()=>F(N+1),disabled:N===O,className:"pagination-button"},"Next")),null))};t(68);const Ie="/backend";var Ae=function(){const e=Object(c.q)(),[a,t]=Object(l.useState)({}),{setUserData:s}=p(),[r,i]=Object(l.useState)(null),[d,u]=Object(l.useState)(""),[E,g]=Object(l.useState)([]),[b,v]=Object(l.useState)([]),[h,N]=Object(l.useState)([]),_=Object(l.useCallback)(async()=>{try{console.log("Fetching user data...");const e=localStorage.getItem("userId"),a=await o.a.get(`${Ie}/handlers/user_data.php?id=${e}`);if(console.log("User data response:",a.data),!a.data.success)throw console.error("Failed to fetch user data:",a.data.message),new Error(a.data.message||"Failed to fetch user data");t(a.data.user),s({balance:a.data.user.balance||0,points:a.data.user.points||0,username:a.data.user.username||""})}catch(r){throw console.error("Error fetching user data:",r),r}},[s]);Object(l.useEffect)(()=>{localStorage.getItem("userId")?(async()=>{try{i(null),(await Promise.allSettled([_(),f(),y(),w()])).forEach((e,a)=>{"rejected"===e.status&&console.error(`Error in Promise ${a}:`,e.reason)}),u(k())}catch(e){console.error("Dashboard initialization error:",e),i("Failed to load dashboard data. Please try again later.")}})():e("/user/login")},[e,_]);const f=async()=>{try{console.log("Fetching user bets...");const e=localStorage.getItem("userId"),a=await o.a.get(`${Ie}/handlers/get_user_bets.php?userId=${e}`);if(console.log("User bets response:",a.data),!a.data.success)throw console.error("Failed to fetch user bets:",a.data.message),new Error(a.data.message||"Failed to fetch user bets");g(a.data.bets||[])}catch(r){throw console.error("Error fetching user bets:",r),r}},y=async()=>{try{console.log("Fetching recent challenges...");const e=await o.a.get(`${Ie}/handlers/recent_challenges.php`);if(console.log("Recent challenges response:",e.data),!e.data.success)throw console.error("Failed to fetch recent challenges:",e.data.message),new Error(e.data.message||"Failed to fetch recent challenges");{const a=(e.data.challenges||[]).map(e=>({...e,end_time:new Date(e.end_time)}));v(a)}}catch(r){throw console.error("Error fetching recent challenges:",r),r}},w=async()=>{try{console.log("Fetching teams...");const a=await o.a.get(`${Ie}/handlers/team_management.php`);if(console.log("Teams response:",a.data),200!==a.data.status)throw console.error("Failed to fetch teams:",a.data.message),new Error(a.data.message||"Failed to fetch teams");N(a.data.data||[])}catch(e){throw console.error("Error fetching teams:",e),e}},C=e=>{const a=h.find(a=>a.name===e);return a?`${Ie}/${a.logo}`:""},k=()=>{const e=(new Date).getHours();return e<12?"Good morning":e<18?"Good afternoon":"Good evening"},O=e=>{let{days:a,hours:t,minutes:l,seconds:s,completed:r}=e;return r?n.a.createElement("span",{className:"status-badge expired"},"Expired"):n.a.createElement("span",null,a,"d:",t,"h:",l,"m:",s,"s")};return r?n.a.createElement("div",{className:"user-dashboard"},n.a.createElement("div",{className:"error-message"},r)):n.a.createElement("div",{className:"user-dashboard"},n.a.createElement("h1",null,d,", ",a.username||"User","!"),n.a.createElement("div",{className:"dashboard-grid"},n.a.createElement("div",{className:"account-balance"},n.a.createElement("h2",null,"Account Balance: ",a.balance||0," FanCoins"),n.a.createElement("div",{className:"action-buttons"},n.a.createElement(m.b,{to:"/deposit",className:"primary-button"},"Deposit FanCoins"),n.a.createElement(m.b,{to:"/withdraw",className:"secondary-button"},"Withdraw FanCoins")))),n.a.createElement("div",{className:"recent-bets-container"},n.a.createElement("div",{className:"section-header"},n.a.createElement("h2",null,"Recent Bets"),n.a.createElement(m.b,{to:"/user/bets",className:"view-all-button"},"View All ",n.a.createElement(S.b,{className:"view-all-icon"}))),n.a.createElement("div",{className:"bets-list"},0===E.length?n.a.createElement("div",{className:"no-data"},"No recent bets found"):E.map(e=>{var a;return n.a.createElement("div",{key:e.bet_id||`bet-${e.unique_code}`,className:"bet-card"},n.a.createElement("div",{className:"bet-header"},n.a.createElement("span",{className:`bet-status ${e.bet_status}`},e.bet_status),n.a.createElement("span",{className:"bet-reference"},"REF: ",(null===(a=e.unique_code)||void 0===a?void 0:a.toUpperCase())||`${e.bet_id}DNRBKCC`)),n.a.createElement("div",{className:"potential-returns"},n.a.createElement("div",{key:"win",className:"return-item win"},"Win: ",e.potential_return_win_user1," FC"),n.a.createElement("div",{key:"draw",className:"return-item draw"},"Draw: ",e.potential_return_draw_user1," FC"),n.a.createElement("div",{key:"loss",className:"return-item loss"},"Loss: ",e.potential_return_loss_user1," FC")),n.a.createElement("div",{className:"bet-matchup"},n.a.createElement("div",{className:"bet-team"},n.a.createElement("img",{src:C(e.team_a),alt:e.team_a,className:"team-logo-medium"}),n.a.createElement("div",{className:"team-details"},n.a.createElement("span",{className:"team-name"},e.team_a),n.a.createElement("span",{className:"username"},"team_a_win"===e.bet_choice_user1?e.user1_name:e.user2_name),n.a.createElement("span",{className:"bet-amount"},"team_a_win"===e.bet_choice_user1?e.amount_user1:e.amount_user2," FC"))),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"bet-team"},n.a.createElement("img",{src:C(e.team_b),alt:e.team_b,className:"team-logo-medium"}),n.a.createElement("div",{className:"team-details"},n.a.createElement("span",{className:"team-name"},e.team_b),n.a.createElement("span",{className:"username"},"team_b_win"===e.bet_choice_user1?e.user1_name:e.user2_name),n.a.createElement("span",{className:"bet-amount"},"team_b_win"===e.bet_choice_user1?e.amount_user1:e.amount_user2," FC")))))}))),n.a.createElement("div",{className:"dashboard-recent-challenges"},n.a.createElement("div",{className:"dashboard-section-header"},n.a.createElement("h2",null,"Recent Challenges"),n.a.createElement(m.b,{to:"/user/challenges",className:"dashboard-view-all-button"},"View All ",n.a.createElement(S.l,{className:"view-all-icon"}))),n.a.createElement("div",{className:"dashboard-challenges-list"},0===b.length?n.a.createElement("div",{className:"dashboard-no-data"},"No recent challenges found"):n.a.createElement("div",{className:"dashboard-challenges-grid"},b.slice(0,3).map(e=>n.a.createElement("div",{key:e.challenge_id||`challenge-${e.id}`,className:"dashboard-challenge-card"},n.a.createElement("div",{className:"dashboard-challenge-teams"},n.a.createElement("div",{key:"team-a",className:"dashboard-challenge-team"},n.a.createElement("div",{className:"dashboard-team-logo-wrapper"},n.a.createElement("img",{src:C(e.team_a),alt:e.team_a,className:"dashboard-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}})),n.a.createElement("span",{className:"dashboard-team-name"},e.team_a)),n.a.createElement("div",{className:"dashboard-vs-badge"},"VS"),n.a.createElement("div",{key:"team-b",className:"dashboard-challenge-team"},n.a.createElement("div",{className:"dashboard-team-logo-wrapper"},n.a.createElement("img",{src:C(e.team_b),alt:e.team_b,className:"dashboard-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}})),n.a.createElement("span",{className:"dashboard-team-name"},e.team_b))),n.a.createElement("div",{className:"dashboard-challenge-details"},n.a.createElement("div",{className:"dashboard-challenge-info"},n.a.createElement("div",{className:"dashboard-challenge-stats"},n.a.createElement("div",{className:"dashboard-stat-row"},n.a.createElement("span",{className:"dashboard-stat-label"},"Status:"),n.a.createElement("span",{className:`dashboard-challenge-status dashboard-status-${e.status.toLowerCase()}`},e.status)),n.a.createElement("div",{className:"dashboard-stat-row"},n.a.createElement("span",{className:"dashboard-stat-label"},"Goal Advantage:"),n.a.createElement("span",{className:"dashboard-stat-value"},"+",e.team_a_goal_advantage," / +",e.team_b_goal_advantage)),n.a.createElement("div",{className:"dashboard-stat-row"},n.a.createElement("span",{className:"dashboard-stat-label"},"Odds:"),n.a.createElement("span",{className:"dashboard-stat-value"},e.odds_team_a," - ",e.odds_team_b))),n.a.createElement("span",{className:"dashboard-challenge-date"},new Date(e.match_date).toLocaleDateString())),n.a.createElement("div",{className:"dashboard-challenge-timer"},n.a.createElement(T.a,{date:e.end_time,renderer:O})),n.a.createElement(m.b,{to:`/user/join-challenge/${e.challenge_id}`,className:"dashboard-place-bet-button"},"Place Bet"))))))))};t(69),t(20);const Pe="/backend";var Re=function(){const{challengeId:e}=Object(c.s)(),a=Object(c.q)(),[t,s]=Object(l.useState)(null),[r,m]=Object(l.useState)(""),[o,d]=Object(l.useState)(""),[u,E]=Object(l.useState)(null),[p,g]=Object(l.useState)(null),[b,v]=Object(l.useState)([]),[h,N]=Object(l.useState)({win:0,draw:(null===t||void 0===t?void 0:t.odds_draw)||0,loss:(null===t||void 0===t?void 0:t.odds_lost)||0}),_=Object(l.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{const t=await i.get(`${Pe}/handlers/team_management.php`,{headers:{Authorization:`Bearer ${localStorage.getItem("userToken")}`,"Content-Type":"application/json"}});200===t.data.status&&v(t.data.data)}catch(a){console.error("Error fetching teams:",a),e<2&&setTimeout(()=>_(e+1),1e3)}},[]),f=Object(l.useCallback)(async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;try{const r=await i.get(`${Pe}/handlers/get_challenges.php`,{params:{id:e},headers:{Authorization:`Bearer ${localStorage.getItem("userToken")}`,"Content-Type":"application/json"}});if(r.data.success&&r.data.challenge)return s(r.data.challenge),void E(null);a<2?setTimeout(()=>f(a+1),1e3):E("Challenge not found. Please try again.")}catch(n){var t,l;console.error("Error fetching challenge details:",n);const e=(null===(t=n.response)||void 0===t?void 0:null===(l=t.data)||void 0===l?void 0:l.message)||"Failed to load challenge details. Please refresh the page.";E(e),a<2&&setTimeout(()=>f(a+1),1e3)}},[e]),y=Object(l.useCallback)(()=>{if(!o||!t)return;const e=parseFloat(o);let a=0;try{if("team_a_win"===r)a=parseFloat(t.odds_team_a)||0;else if("team_b_win"===r)a=parseFloat(t.odds_team_b)||0;else{if("draw"!==r)return;a=parseFloat(t.odds_draw)||0}const n=parseFloat(t.odds_draw)||.9,s=parseFloat(t.odds_lost)||.2;N({win:(e*a).toFixed(2),draw:(e*n).toFixed(2),loss:(e*s).toFixed(2)})}catch(l){console.error("Error calculating returns:",l)}},[o,r,t]);Object(l.useEffect)(()=>{e&&(f(),_())},[f,_,e]),Object(l.useEffect)(()=>{y()},[y]);const w=Object(l.useCallback)(e=>{const a=b.find(a=>a.name===e);return a?`${Pe}/${a.logo}`:""},[b]);return t||u?n.a.createElement("div",{className:"join-challenge"},n.a.createElement("h1",null,"Join Challenge"),u&&n.a.createElement("div",{className:"error-message"},u),p&&n.a.createElement("div",{className:"success-message"},p),t&&n.a.createElement("div",{className:"centered-layout"},n.a.createElement("div",{className:"challenge-details"},n.a.createElement("div",{className:"team-info"},n.a.createElement("div",{className:"challenge-team-wrapper"},n.a.createElement("img",{src:w(t.team_a),alt:t.team_a,className:"challenge-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenge-team-name"},t.team_a))),n.a.createElement("div",{className:"vs-container"},n.a.createElement("span",{className:"vs"},"VS")),n.a.createElement("div",{className:"team-info"},n.a.createElement("div",{className:"challenge-team-wrapper"},n.a.createElement("img",{src:w(t.team_b),alt:t.team_b,className:"challenge-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenge-team-name"},t.team_b)))),n.a.createElement("div",{className:"match-details-grid"},n.a.createElement("div",{className:"detail-item match-type"},n.a.createElement("div",{className:"label"},"Match Type"),n.a.createElement("div",{className:"value"},"full_time"===t.match_type?"Full Time":"Half Time")),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("div",{className:"label"},t.team_a," Odds"),n.a.createElement("div",{className:"value"},t.odds_team_a)),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("div",{className:"label"},t.team_b," Odds"),n.a.createElement("div",{className:"value"},t.odds_team_b)),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("div",{className:"label"},"Draw Odds"),n.a.createElement("div",{className:"value"},t.odds_draw)),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("div",{className:"label"},"Loss Odds"),n.a.createElement("div",{className:"value"},t.odds_lost)),n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"label"},"Goal Advantage"),n.a.createElement("div",{className:"value"},t.team_a_goal_advantage," - ",t.team_b_goal_advantage)),n.a.createElement("div",{className:"detail-item date"},n.a.createElement("div",{className:"label"},"Match Date"),n.a.createElement("div",{className:"value"},new Date(t.match_date).toLocaleString()))),n.a.createElement("form",{className:"bet-form",onSubmit:async l=>{if(l.preventDefault(),E(null),r&&o)try{const l=localStorage.getItem("userId");let u;"team_a_win"===r?u=t.odds_team_a:"team_b_win"===r?u=t.odds_team_b:"draw"===r&&(u=t.odds_draw);const p=parseFloat(o),b=p*u,v=p*(t.odds_draw||.9),h=p*(t.odds_lost||.2),N=await i.post(`${Pe}/handlers/place_bet.php`,{challengeId:e,userId:l,outcome:r,amount:o,odds:u,potential_return_user1:b,potential_return_win_user1:b,potential_return_draw_user1:v,potential_return_loss_user1:h},{headers:{Authorization:`Bearer ${localStorage.getItem("userToken")}`,"Content-Type":"application/json"}});if(N.data.success){const e=N.data.bet_id;g("Bet placed successfully! Redirecting to your bets..."),m(""),d("");const t=setTimeout(()=>{a("/user/bets/outgoing",{state:{newBetId:e},replace:!0})},4e3);return()=>clearTimeout(t)}throw new Error(N.data.message||"Failed to place bet")}catch(c){var n,s;console.error("Error placing bet:",c);const e=(null===(n=c.response)||void 0===n?void 0:null===(s=n.data)||void 0===s?void 0:s.message)||"An error occurred while placing your bet.";E(e)}else E("Please select an outcome and enter a bet amount.")}},n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"outcome"},"Select Outcome:"),n.a.createElement("select",{id:"outcome",value:r,onChange:e=>m(e.target.value)},n.a.createElement("option",{value:""},"-- Select --"),n.a.createElement("option",{value:"team_a_win"},"Home Win (",t.team_a,")"),n.a.createElement("option",{value:"team_b_win"},"Away Win (",t.team_b,")"),n.a.createElement("option",{value:"draw"},"Draw"))),n.a.createElement("div",null,n.a.createElement("label",{htmlFor:"amount"},"Bet Amount (FanCoins):"),n.a.createElement("input",{type:"number",id:"amount",value:o,onChange:e=>d(e.target.value)})),o&&r&&n.a.createElement("div",{className:"potential-returns"},n.a.createElement("h3",null,"Expected Returns"),n.a.createElement("div",{className:"returns-grid"},n.a.createElement("div",{className:"return-item win"},n.a.createElement("div",{className:"label"},"Win"),n.a.createElement("div",{className:"value-container"},n.a.createElement("div",{className:"value"},Number(h.win).toFixed(2)),n.a.createElement("div",{className:"currency"},"FanCoins"))),n.a.createElement("div",{className:"return-item draw"},n.a.createElement("div",{className:"label"},"Draw"),n.a.createElement("div",{className:"value-container"},n.a.createElement("div",{className:"value"},Number(h.draw).toFixed(2)),n.a.createElement("div",{className:"currency"},"FanCoins"))),n.a.createElement("div",{className:"return-item loss"},n.a.createElement("div",{className:"label"},"Loss Return"),n.a.createElement("div",{className:"value-container"},n.a.createElement("div",{className:"value"},Number(h.loss).toFixed(2)),n.a.createElement("div",{className:"currency"},"FanCoins"))))),n.a.createElement("button",{type:"submit"},"Place Bet")))):n.a.createElement("div",{className:"join-challenge"},n.a.createElement("p",null,"Loading challenge details..."))};t(70);var qe=()=>{const{challengeId:e,betId:a,uniqueCode:t}=Object(c.s)(),s=Object(c.q)(),[r,m]=Object(l.useState)(null),[o,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(""),[p,g]=Object(l.useState)(null),[b,v]=Object(l.useState)(null),[h,N]=Object(l.useState)(""),[_,f]=Object(l.useState)(!0),[y,w]=Object(l.useState)({win:0,draw:0,loss:0}),[S,C]=Object(l.useState)([]),k=localStorage.getItem("userId"),O=null===o||void 0===o?void 0:o.user1_id,F=Object(l.useCallback)(async()=>{try{console.log("Fetching challenge with ID:",e);const a=await i.get("/backend/handlers/get_challenges.php",{params:{id:e}});if(console.log("Challenge response:",a.data),!a.data.success)throw new Error(a.data.message||"Failed to fetch challenge");m(a.data.challenge)}catch(p){console.error("Error fetching challenge:",p),g("Failed to load challenge details. Please try again.")}},[e]),j=Object(l.useCallback)(async()=>{try{console.log("Fetching bet details:",{betId:a,uniqueCode:t});const e=await i.get("/backend/handlers/get_bet_details.php",{params:{betId:a,uniqueCode:t}});if(console.log("Bet details response:",e.data),!e.data.success)throw new Error(e.data.message||"Failed to fetch bet details");d(e.data.bet),E(e.data.bet.amount_user1)}catch(p){console.error("Error fetching bet details:",p),g("Error loading bet details. Please try again.")}},[a,t]),L=Object(l.useCallback)(async()=>{try{const e=await i.get("/backend/handlers/team_management.php");200===e.data.status?C(e.data.data):console.warn("Team data not in expected format:",e.data)}catch(p){console.error("Error fetching teams:",p)}},[]),T=Object(l.useCallback)(async()=>{try{if(!k)throw new Error("User ID not found");const e=await i.get("/backend/handlers/user_data.php",{params:{userId:k}});if(!e.data.success)throw new Error(e.data.message||"Failed to fetch user data");N(e.data.user.username)}catch(p){console.error("Error fetching current username:",p),g("Error loading user data")}},[k]),x=Object(l.useCallback)(e=>{const a=S.find(a=>a.name===e);return a?`/backend/${a.logo}`:"/default-team-logo.png"},[S]),$=Object(l.useCallback)(()=>{if(u&&r&&o)try{const e=parseFloat(u);if(isNaN(e))return;let a;a="team_a_win"===o.bet_choice_user1?parseFloat(r.odds_team_b):"team_b_win"===o.bet_choice_user1?parseFloat(r.odds_team_a):parseFloat(r.odds_draw);const t=parseFloat(r.odds_draw)||.9,l=parseFloat(r.odds_lost)||.2;w({win:(e*a).toFixed(2),draw:(e*t).toFixed(2),loss:(e*l).toFixed(2)})}catch(p){console.error("Error calculating returns:",p)}},[u,r,o]),D=Object(l.useCallback)(()=>{if(!r||!o)return null;const e="team_b_win"===o.bet_choice_user1?r.team_b:r.team_a,a="team_b_win"===o.bet_choice_user1?x(r.team_b):x(r.team_a),t="team_b_win"===o.bet_choice_user1?r.team_a:r.team_b,l="team_b_win"===o.bet_choice_user1?x(r.team_a):x(r.team_b);return n.a.createElement("div",{className:"challenge-details"},n.a.createElement("div",{className:"team-info"},n.a.createElement("div",{className:"challenge-team-wrapper"},n.a.createElement("img",{src:a,alt:e,className:"challenge-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenge-team-name"},e),n.a.createElement("span",{className:"player-name"},o.user1_name),n.a.createElement("span",{className:"opponent-pick"},"Opponent's Pick"))),n.a.createElement("div",{className:"vs-container"},n.a.createElement("span",{className:"vs"},"VS")),n.a.createElement("div",{className:"team-info"},n.a.createElement("div",{className:"challenge-team-wrapper"},n.a.createElement("img",{src:l,alt:t,className:"challenge-team-logo",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenge-team-name"},t),n.a.createElement("span",{className:"player-name"},h))))},[r,o,x,h]);Object(l.useEffect)(()=>{(async()=>{f(!0),g(null);try{if(!k)return void s("/user/login");await Promise.all([F(),j(),L(),T()]),f(!1)}catch(p){console.error("Error initializing component:",p),g("Failed to load bet details. Please try again."),f(!1)}})()},[e,a,t,k,s,F,j,L,T]),Object(l.useEffect)(()=>{o&&parseInt(O)===parseInt(k)&&(g("You cannot bet against yourself!"),setTimeout(()=>{s("/user/bets/outgoing")},3e3))},[o,O,k,s]),Object(l.useEffect)(()=>{$()},[$]);return _?n.a.createElement("div",{className:"join-challenge"},n.a.createElement("p",null,"Loading bet details...")):p?n.a.createElement("div",{className:"join-challenge"},n.a.createElement("div",{className:"error-message"},p),n.a.createElement("button",{onClick:()=>s("/user/bets"),className:"back-button"},"Back to Bets")):r&&o?n.a.createElement("div",{className:"join-challenge"},n.a.createElement("h1",null,"Accept Bet"),p&&n.a.createElement("div",{className:"error-message"},p),b&&n.a.createElement("div",{className:"success-message"},b),n.a.createElement("div",{className:"centered-layout"},D()),n.a.createElement("div",{className:"match-details-grid"},n.a.createElement("div",{className:"detail-item match-type"},n.a.createElement("span",{className:"label"},"Match Type"),n.a.createElement("span",{className:"value"},"full_time"===r.match_type?"Full Time":"Half Time")),n.a.createElement("div",{className:"detail-item date"},n.a.createElement("span",{className:"label"},"Match Date"),n.a.createElement("span",{className:"value"},new Date(r.match_date).toLocaleString())),n.a.createElement("div",{className:"detail-item odds"},n.a.createElement("span",{className:"label"},"Your Odds"),n.a.createElement("span",{className:"value"},"team_a_win"===o.bet_choice_user1?r.odds_team_b:r.odds_team_a,"x")),n.a.createElement("div",{className:"detail-item amount"},n.a.createElement("span",{className:"label"},"Bet Amount"),n.a.createElement("span",{className:"value"},u," FanCoins"))),n.a.createElement("div",{className:"returns-grid"},n.a.createElement("div",{className:"return-item win"},n.a.createElement("span",{className:"label"},"Win Return"),n.a.createElement("span",{className:"value"},y.win," FanCoins")),n.a.createElement("div",{className:"return-item draw"},n.a.createElement("span",{className:"label"},"Draw Return"),n.a.createElement("span",{className:"value"},y.draw," FanCoins")),n.a.createElement("div",{className:"return-item loss"},n.a.createElement("span",{className:"label"},"Loss Return"),n.a.createElement("span",{className:"value"},y.loss," FanCoins"))),n.a.createElement("form",{className:"bet-form",onSubmit:async e=>{e.preventDefault(),g(""),v("");try{if(!k)throw new Error("Please log in to place a bet");if(!u||isNaN(parseFloat(u)))throw new Error("Please enter a valid bet amount");const e=parseFloat(u);let c,m;if("team_a_win"===o.bet_choice_user1?(c=parseFloat(r.odds_team_b),m="team_b_win"):"team_b_win"===o.bet_choice_user1?(c=parseFloat(r.odds_team_a),m="team_a_win"):(c=parseFloat(r.odds_draw),m="draw"),isNaN(c))throw new Error("Invalid odds calculation");const d={betId:parseInt(a),challengeId:parseInt(r.challenge_id),userId:parseInt(k),uniqueCode:t,amount:e.toFixed(2),amount_user2:e.toFixed(2),bet_choice_user2:m,odds_user2:c.toFixed(2),potential_return_user2:(e*c).toFixed(2),potential_return_win_user2:(e*c).toFixed(2),potential_return_draw_user2:(e*(r.odds_draw||.9)).toFixed(2),potential_return_loss_user2:(e*(r.odds_lost||.2)).toFixed(2),team1_id:parseInt(r.team_a_id),team2_id:parseInt(r.team_b_id)};console.log("Sending bet data:",d);const b=await i.post("/backend/handlers/accept_bet.php",d);if(console.log("Server response:",b.data),!b.data.success)throw new Error(b.data.message||"Failed to join bet");v("Successfully joined the bet! Redirecting..."),E(""),setTimeout(()=>{s("/user/bets/accepted",{replace:!0})},2e3)}catch(p){var l,n;console.error("Error joining bet:",p),g((null===(l=p.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message)||p.message||"An error occurred while joining the bet.")}}},n.a.createElement("button",{type:"submit",className:"submit-button",disabled:!!p},"Accept Bet"))):n.a.createElement("div",{className:"join-challenge"},n.a.createElement("div",{className:"error-message"},"Bet details not found"),n.a.createElement("button",{onClick:()=>s("/user/bets"),className:"back-button"},"Back to Bets"))};t(15);const Be="/backend";var Me=function(){var e;const[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)([]),[m,i]=Object(l.useState)(!0),[d,u]=Object(l.useState)(null),[E,p]=Object(l.useState)(1),[g,b]=Object(l.useState)(1),[v]=Object(l.useState)(20),[h,N]=Object(l.useState)(""),_=localStorage.getItem("userId"),f=Object(c.q)(),y=Object(c.o)(),w=Object(l.useRef)(null),[S,C]=Object(l.useState)(!1),[k,O]=Object(l.useState)(""),[F,j]=Object(l.useState)(!1),[L,T]=Object(l.useState)(null),[x,$]=Object(l.useState)((null===(e=y.state)||void 0===e?void 0:e.newBetId)||null),D=Object(l.useCallback)(async()=>{try{console.log("Fetching bets...");const l=await o.a.get(`${Be}/handlers/get_bets.php`,{params:{userId:_,page:E,limit:v,search:h}});console.log("Bets response:",l.data),l.data.success?(t(l.data.bets||[]),b(l.data.pagination.totalPages)):(console.error("Failed to fetch bets:",l.data.message),u(l.data.message||"Failed to fetch bets"))}catch(d){var e,a;console.error("Error fetching bets:",d),u((null===(e=d.response)||void 0===e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)||"An error occurred while fetching bets.")}finally{i(!1)}},[_,E,v,h]),I=Object(l.useCallback)(async()=>{try{console.log("Fetching teams...");const t=await o.a.get(`${Be}/handlers/team_management.php`);console.log("Teams response:",t.data),200===t.data.status?r(t.data.data||[]):(console.error("Failed to fetch teams:",t.data.message),u(t.data.message||"Failed to fetch teams"))}catch(d){var e,a;console.error("Error fetching teams:",d),u((null===(e=d.response)||void 0===e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)||"An error occurred while fetching teams.")}},[]);Object(l.useEffect)(()=>{_?(console.log("Initializing ViewBets with userId:",_),(async()=>{try{u(null),i(!0),await Promise.all([D(),I()])}catch(e){console.error("ViewBets initialization error:",e),u("Failed to initialize bets view. Please try again later.")}})()):f("/login")},[_,D,I,f]),Object(l.useEffect)(()=>{if(x&&w.current){w.current.scrollIntoView({behavior:"smooth",block:"center"});const e=setTimeout(()=>{$(null)},5e3);return()=>clearTimeout(e)}},[a,x]);const A=(e,a)=>"joined"===e.bet_status.toLowerCase()||"completed"===e.bet_status.toLowerCase()?a?"Creator":"Opponent":a?"Creator":"Waiting for Opponent";if(m)return n.a.createElement("div",{className:"loading"},"Loading bets...");if(d)return n.a.createElement("div",{className:"error"},d);const P=e=>{const a=s.find(a=>a.name===e);return a?`${Be}/${a.logo}`:""},R=e=>{const a=`${window.location.origin}/user/join-challenge2/${e.challenge_id}/${e.bet_id}/${e.unique_code}/${e.user1_id}`;O(a),C(!0)},q=()=>{C(!1)},B=e=>(e.unique_code||`${e.bet_id}DNRBKCC`).toUpperCase(),M=e=>{T(e),j(!0)};return n.a.createElement("div",{className:"view-bets-container"},n.a.createElement("div",{className:"title-section"},n.a.createElement("h2",null,"Outgoing Bets"),n.a.createElement("div",{className:"title-line"})),n.a.createElement("div",{className:"search-container"},n.a.createElement("input",{type:"text",placeholder:"Search by reference number...",value:h,onChange:e=>{N(e.target.value),p(1)},className:"search-input"})),n.a.createElement("div",{className:"table-responsive"},n.a.createElement("table",{className:"bets-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"#"),n.a.createElement("th",null,"Ref"),n.a.createElement("th",{colSpan:"3",className:"teams-header compact"},"Teams"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Return"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Match Date"),n.a.createElement("th",null,"Actions"))),n.a.createElement("tbody",null,a.map((e,a)=>n.a.createElement("tr",{key:e.bet_id,ref:e.bet_id===x?w:null,className:e.bet_id===x?"highlight-new-bet":""},n.a.createElement("td",null,(E-1)*v+a+1),n.a.createElement("td",null,n.a.createElement("span",{className:"reference",onClick:()=>M(e)},B(e))),n.a.createElement("td",{className:"team-cell compact"},n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:P(e.team_a),alt:e.team_a,className:"team-logo"}),n.a.createElement("span",null,e.team_a),"team_a_win"===e.bet_choice_user1&&n.a.createElement("span",{className:"pick-badge"},"Your Pick"))),n.a.createElement("td",{className:"vs-cell compact"},n.a.createElement("div",{className:"vs-indicator"},"VS")),n.a.createElement("td",{className:"team-cell compact"},n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:P(e.team_b),alt:e.team_b,className:"team-logo"}),n.a.createElement("span",null,e.team_b),"team_a_win"!==e.bet_choice_user1&&n.a.createElement("span",{className:"pick-badge"},"Your Pick"))),n.a.createElement("td",{className:"amount-cell"},e.amount_user1," ",n.a.createElement("span",{className:"currency"},"FanCoins")),n.a.createElement("td",{className:"return-cell"},e.potential_return_win_user1," ",n.a.createElement("span",{className:"currency"},"FanCoins")),n.a.createElement("td",null,n.a.createElement("div",{className:"status-container"},n.a.createElement("div",{className:`status-badge ${e.bet_status}`},n.a.createElement("span",{className:"status-dot"}),n.a.createElement("span",{className:"status-text"},"open"===e.bet_status&&"Open","joined"===e.bet_status&&"Joined","completed"===e.bet_status&&"Completed")))),n.a.createElement("td",{className:"date-cell"},n.a.createElement("div",{className:"date-display"},n.a.createElement("div",{className:"date-line"},new Date(e.match_date).toLocaleDateString()),n.a.createElement("div",{className:"time-line"},new Date(e.match_date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})))),n.a.createElement("td",null,"open"===e.bet_status&&n.a.createElement("button",{onClick:()=>R(e),className:"generate-link-btn"},"Generate Link"))))))),n.a.createElement("div",{className:"mobile-bets-grid"},a.map((e,a)=>n.a.createElement("div",{key:e.bet_id,className:"mobile-bet-card",ref:e.bet_id===x?w:null,"data-status":e.bet_status.toLowerCase()},n.a.createElement("div",{className:"mobile-bet-header"},n.a.createElement("span",{className:"mobile-bet-ref",onClick:()=>M(e)},B(e)),n.a.createElement("div",{className:`status-badge ${e.bet_status.toLowerCase()}`},n.a.createElement("span",{className:"status-dot"}),e.bet_status)),n.a.createElement("div",{className:"mobile-users-section"},n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},e.user1_username),n.a.createElement("span",{className:"mobile-user-status creator"},A(e,!0))),n.a.createElement("div",{className:"mobile-vs-divider"},"VS"),n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},e.user2_username||"---"),n.a.createElement("span",{className:`mobile-user-status ${e.user2_username?"opponent":"waiting"}`},A(e,!1)))),n.a.createElement("div",{className:"mobile-teams-container"},n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:P(e.team_a),alt:e.team_a,className:"mobile-team-logo"}),n.a.createElement("span",null,e.team_a)),n.a.createElement("div",{className:"mobile-vs"},"VS"),n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:P(e.team_b),alt:e.team_b,className:"mobile-team-logo"}),n.a.createElement("span",null,e.team_b))),n.a.createElement("div",{className:"mobile-bet-details"},n.a.createElement("div",{className:"mobile-detail-item"},n.a.createElement("span",{className:"mobile-detail-label"},"Amount"),n.a.createElement("span",{className:"mobile-detail-value"},e.amount_user1," FanCoins")),n.a.createElement("div",{className:"mobile-detail-item"},n.a.createElement("span",{className:"mobile-detail-label"},"Potential Return"),n.a.createElement("span",{className:"mobile-detail-value"},e.potential_return_win_user1," FanCoins")),n.a.createElement("div",{className:"mobile-detail-item full-width"},n.a.createElement("span",{className:"mobile-detail-label"},"Match Date"),n.a.createElement("span",{className:"mobile-detail-value"},new Date(e.match_date).toLocaleString()))),"open"===e.bet_status&&n.a.createElement("div",{className:"mobile-bet-actions"},n.a.createElement("button",{className:"mobile-action-button",onClick:()=>R(e)},"Generate Link"))))),window.innerWidth>768&&F&&L&&n.a.createElement("div",{className:"modal-overlay",onClick:()=>j(!1)},n.a.createElement("div",{className:"bet-details-modal",onClick:e=>e.stopPropagation()},n.a.createElement("button",{className:"close-button",onClick:()=>j(!1)},"\xd7"),n.a.createElement("div",{className:"modal-left"},n.a.createElement("div",{className:"modal-header"},n.a.createElement("h3",{className:"reference-title"},"Bet Reference: ",B(L)),n.a.createElement("div",{className:"status-badges"},n.a.createElement("div",{className:`status-badge-large ${L.bet_status}`},"open"===L.bet_status&&"OPEN FOR BETS","joined"===L.bet_status&&"BET MATCHED","completed"===L.bet_status&&"COMPLETED"),n.a.createElement("div",{className:`match-type-badge-large ${L.match_type}`},"half_time"===L.match_type?"HALF TIME":"FULL TIME"))),n.a.createElement("div",{className:"teams-match"},n.a.createElement("div",{className:"team-card"},"team_a_win"===L.bet_choice_user1&&n.a.createElement("div",{className:"selected-badge"},"Selected"),n.a.createElement("img",{src:P(L.team_a),alt:L.team_a}),n.a.createElement("div",{className:"team-name"},L.team_a),n.a.createElement("div",{className:"team-username"},L.user1_username),n.a.createElement("div",{className:"team-odds"},L.odds_team_a,"x")),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"team-card"},"team_b_win"===L.bet_choice_user1&&n.a.createElement("div",{className:"selected-badge"},"Selected"),n.a.createElement("img",{src:P(L.team_b),alt:L.team_b}),n.a.createElement("div",{className:"team-name"},L.team_b),n.a.createElement("div",{className:"team-username"},L.user2_username||"Waiting for opponent"),n.a.createElement("div",{className:"team-odds"},L.odds_team_b,"x"))),n.a.createElement("div",{className:"match-details-grid"},n.a.createElement("div",{className:"details-section schedule-section"},n.a.createElement("div",{className:"section-title"},"MATCH SCHEDULE"),n.a.createElement("div",{className:"schedule-grid"},n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"MATCH DATE"),n.a.createElement("span",{className:"schedule-value"},new Date(L.match_date).toLocaleString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"START TIME"),n.a.createElement("span",{className:"schedule-value"},new Date(L.start_time).toLocaleTimeString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"END TIME"),n.a.createElement("span",{className:"schedule-value"},new Date(L.end_time).toLocaleTimeString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"CHALLENGE CREATED"),n.a.createElement("span",{className:"schedule-value"},new Date(L.challenge_date).toLocaleString())))),n.a.createElement("div",{className:"details-section odds-section"},n.a.createElement("div",{className:"section-title"},"ODDS INFORMATION"),n.a.createElement("div",{className:"odds-grid"},n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},L.team_a," WIN"),n.a.createElement("span",{className:"odds-value"},L.odds_team_a,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},L.team_b," WIN"),n.a.createElement("span",{className:"odds-value"},L.odds_team_b,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},"DRAW"),n.a.createElement("span",{className:"odds-value"},L.odds_draw,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},"LOSS MULTIPLIER"),n.a.createElement("span",{className:"odds-value"},L.odds_lost,"x")))))),n.a.createElement("div",{className:"modal-right"},n.a.createElement("div",{className:"details-section"},n.a.createElement("div",{className:"section-title"},"BET STATUS"),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"CREATED BY"),n.a.createElement("span",{className:"detail-value created-by"},L.user1_username)),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"STATUS"),n.a.createElement("span",{className:"detail-value status-value"},L.user2_username?`Joined by ${L.user2_username}`:"Waiting for opponent"))),n.a.createElement("div",{className:"details-section"},n.a.createElement("div",{className:"section-title"},"FINANCIAL DETAILS"),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"YOUR BET"),n.a.createElement("span",{className:"detail-value amount"},L.amount_user1," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"POTENTIAL WIN"),n.a.createElement("span",{className:"detail-value return"},L.potential_return_win_user1," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"POTENTIAL LOSS"),n.a.createElement("span",{className:"detail-value amount"},L.potential_loss_user1," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"DRAW OUTCOME"),n.a.createElement("span",{className:"detail-value"},L.potential_draw_win_user1," FanCoins")),L.user2_username&&n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"OPPONENT BET"),n.a.createElement("span",{className:"detail-value amount"},L.amount_user2," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"OPPONENT WIN"),n.a.createElement("span",{className:"detail-value return"},L.potential_return_win_user2," FanCoins"))))))),window.innerWidth<=768&&F&&L&&n.a.createElement("div",{className:"modal-overlay",onClick:()=>j(!1)},n.a.createElement("div",{className:"mobile-modal-content",onClick:e=>e.stopPropagation()},n.a.createElement("div",{className:"mobile-modal-header"},n.a.createElement("h3",null,"Bet Details"),n.a.createElement("button",{className:"mobile-modal-close",onClick:()=>j(!1)},"\xd7")),n.a.createElement("div",{className:"mobile-modal-body"},n.a.createElement("div",{className:"mobile-ref-section"},n.a.createElement("div",{className:"mobile-ref-number"},n.a.createElement("span",{className:"mobile-ref-label"},"Reference Number"),n.a.createElement("span",{className:"mobile-ref-value"},B(L))),n.a.createElement("div",{className:"mobile-status-badges"},n.a.createElement("span",{className:`mobile-status-badge ${L.bet_status.toLowerCase()}`},"open"===L.bet_status&&"OPEN FOR BETS","joined"===L.bet_status&&"BET MATCHED","completed"===L.bet_status&&"COMPLETED"),n.a.createElement("span",{className:"mobile-status-badge mobile-match-type"},"half_time"===L.match_type?"HALF TIME":"FULL TIME"))),n.a.createElement("div",{className:"mobile-users-section"},n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},L.user1_username),n.a.createElement("span",{className:"mobile-user-status creator"},A(L,!0))),n.a.createElement("div",{className:"mobile-vs-divider"},"VS"),n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},L.user2_username||"---"),n.a.createElement("span",{className:`mobile-user-status ${L.user2_username?"opponent":"waiting"}`},A(L,!1)))),n.a.createElement("div",{className:"mobile-teams-container"},n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:P(L.team_a),alt:L.team_a,className:"mobile-team-logo"}),n.a.createElement("span",null,L.team_a)),n.a.createElement("div",{className:"mobile-vs"},"VS"),n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:P(L.team_b),alt:L.team_b,className:"mobile-team-logo"}),n.a.createElement("span",null,L.team_b))),n.a.createElement("div",{className:"mobile-section-title"},"ODDS INFORMATION"),n.a.createElement("div",{className:"mobile-odds-grid"},n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},L.team_a," WIN"),n.a.createElement("span",{className:"mobile-odds-value"},L.odds_team_a,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},L.team_b," WIN"),n.a.createElement("span",{className:"mobile-odds-value"},L.odds_team_b,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},"DRAW"),n.a.createElement("span",{className:"mobile-odds-value"},L.odds_draw,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},"LOSS"),n.a.createElement("span",{className:"mobile-odds-value"},L.odds_lost,"x"))),n.a.createElement("div",{className:"mobile-section-title"},"FINANCIAL DETAILS"),n.a.createElement("div",{className:"mobile-financial-grid"},n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Your Bet"),n.a.createElement("span",{className:"mobile-financial-value"},L.amount_user1," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Potential Win"),n.a.createElement("span",{className:"mobile-financial-value win"},"+",L.potential_return_win_user1," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Potential Loss"),n.a.createElement("span",{className:"mobile-financial-value loss"},"-",L.potential_return_loss_user1," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Draw Return"),n.a.createElement("span",{className:"mobile-financial-value draw"},L.potential_return_draw_user1," FC")))),"open"===L.bet_status&&n.a.createElement("div",{className:"mobile-modal-footer"},n.a.createElement("button",{className:"mobile-modal-action-button",onClick:()=>R(L)},"Generate Link")))),S&&n.a.createElement("div",{className:"modal-overlay",onClick:q},n.a.createElement("div",{className:"link-modal",onClick:e=>e.stopPropagation()},n.a.createElement("span",{className:"close",onClick:q},"\xd7"),n.a.createElement("h3",null,"Share this link with your friend"),n.a.createElement("div",{className:"link-container"},n.a.createElement("input",{type:"text",value:k,readOnly:!0}),n.a.createElement("button",{onClick:()=>{navigator.clipboard.writeText(k),alert("Link copied to clipboard!")}},"Copy")))),n.a.createElement("style",{jsx:!0},"\n        .teams-header.compact {\n          width: 20%;  /* Reduced from 30% */\n        }\n        \n        .team-cell.compact {\n          max-width: 80px;  /* Reduced from 100px */\n          padding: 4px;  /* Reduced padding */\n        }\n        \n        .vs-cell.compact {\n          padding: 0 4px;  /* Reduced padding */\n          width: 30px;  /* Fixed width */\n        }\n        \n        .team-info {\n          display: flex;\n          align-items: center;\n          gap: 4px;  /* Reduced gap between elements */\n          max-width: 100%;\n        }\n        \n        .team-info span {\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n          font-size: 0.9rem;\n        }\n\n        .date-cell {\n          padding: 4px 8px;\n          min-width: 100px;\n        }\n\n        .date-display {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          font-size: 0.85rem;\n        }\n\n        .date-line {\n          color: #333;\n        }\n\n        .time-line {\n          color: #666;\n          font-size: 0.8rem;\n        }\n      "))};const Ue="/backend";var We=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]),r=localStorage.getItem("userId"),m=Object(c.q)(),[i,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(null),p=Object(l.useCallback)(async()=>{try{const e=await o.a.get(`${Ue}/handlers/get_bets.php?userId=${r}`);e.data.success?a(e.data.incomingBets):console.error("Error fetching bets:",e.data.message||"Unknown error")}catch(i){console.error("Error fetching bets:",i)}},[r]);Object(l.useEffect)(()=>{r?(p(),g()):m("/user/login")},[r,p,m]);const g=async()=>{try{const e=await o.a.get(`${Ue}/handlers/team_management.php`);200===e.data.status&&s(e.data.data)}catch(i){console.error("Error fetching teams:",i)}},b=e=>{const a=t.find(a=>a.name===e);return a?`${Ue}/${a.logo}`:""};return n.a.createElement("div",{className:"view-bets-container"},n.a.createElement("h2",null,"Incoming Bets"),i&&n.a.createElement("div",{className:"error-message"},i),u&&n.a.createElement("div",{className:"success-message"},u),n.a.createElement("table",{className:"bets-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"From"),n.a.createElement("th",null,"Team A"),n.a.createElement("th",null,"Team B"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Action"))),n.a.createElement("tbody",null,e&&e.length>0?e.map(t=>n.a.createElement("tr",{key:t.bet_id},n.a.createElement("td",null,t.user1_name),n.a.createElement("td",null,n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:b(t.team_a),alt:t.team_a,className:"team-logo"}),n.a.createElement("span",null,t.team_a))),n.a.createElement("td",null,n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:b(t.team_b),alt:t.team_b,className:"team-logo"}),n.a.createElement("span",null,t.team_b))),n.a.createElement("td",null,t.amount_user1," FanCoins"),n.a.createElement("td",null,n.a.createElement("span",{className:`status ${t.bet_status}`},t.bet_status)),n.a.createElement("td",null,n.a.createElement("button",{className:"accept-button",onClick:()=>(async t=>{try{if(!r)return d("Please log in to accept a bet"),void m("/user/login");const s=e.find(e=>e.bet_id===t);if(!s)return void d("Bet not found.");const c=s.amount_user1,u=await o.a.get(`${Ue}/handlers/user_data.php?id=${s.user1_id}`);if(!u.data.success)return void d("Failed to fetch user data.");if(parseFloat(u.data.user.balance)<c)return void d("The bet creator no longer has sufficient funds.");const p=await o.a.post(`${Ue}/handlers/accept_bet.php`,{betId:t,userId:r,amount:c});p.data.success?(E("Bet accepted successfully!"),a(e=>e.filter(e=>e.bet_id!==t)),setTimeout(()=>{m("/accepted-bets")},2e3)):d(p.data.message||"Failed to accept bet")}catch(i){var l,n;d((null===(l=i.response)||void 0===l?void 0:null===(n=l.data)||void 0===n?void 0:n.message)||"Error accepting bet.")}})(t.bet_id)},"Accept")))):n.a.createElement("tr",null,n.a.createElement("td",{colSpan:"6"},"No incoming bets found.")))))};t(71);const He="/backend";var Ve=function(){var e,a,t,s;const{username:r}=Object(c.s)(),[i,d]=Object(l.useState)({username:"",email:"",favorite_team:"",balance:0,total_bets:0,win_rate:0,leaderboard_score:0}),[u,E]=Object(l.useState)(""),[p,g]=Object(l.useState)([]),[b,v]=Object(l.useState)(""),[h,N]=Object(l.useState)([]),[_,f]=Object(l.useState)(!1),[y,w]=Object(l.useState)(null),[C,k]=Object(l.useState)(!1),[O,F]=Object(l.useState)(null),[j,L]=Object(l.useState)(""),[T,x]=Object(l.useState)({show:!1,type:"",message:""});Object(l.useEffect)(()=>{const e=r||localStorage.getItem("username");e&&($(e),D())},[r]);const $=async e=>{try{const t=await o.a.get(`${He}/handlers/profile.php?username=${e}`);t.data.success&&d(t.data.profile)}catch(a){console.error("Error fetching profile:",a)}},D=async()=>{try{const a=await o.a.get(`${He}/handlers/team_management.php`);200===a.data.status&&N(a.data.data)}catch(e){console.error("Error fetching teams:",e)}},I=e=>{const a=h.find(a=>a.name===e);return a?`${He}/${a.logo}`:""},A=async()=>{try{if(v(""),!u.trim())return void v("Please enter a username to search");const a=await o.a.get(`${He}/handlers/search_users.php?query=${u}`);if(a.data.success&&a.data.users.length>0){const e=localStorage.getItem("userId"),t=a.data.users.filter(a=>a.user_id!==e);g(t),0===t.length&&v("No users found matching your search")}else g([]),v("No users found matching your search")}catch(e){g([]),v("Error searching for users")}},P=e=>{e.username!==localStorage.getItem("username")&&(F(e),k(!0))};return n.a.createElement("div",{className:"profile-container"},T.show&&n.a.createElement("div",{className:`message-status ${T.type}`},T.message),n.a.createElement("div",{className:"profile-header"},n.a.createElement("div",{className:"profile-banner"},n.a.createElement("div",{className:"profile-avatar"},n.a.createElement("img",{src:I(i.favorite_team),alt:i.favorite_team,className:"team-avatar"})),n.a.createElement("div",{className:"profile-info-header"},n.a.createElement("h1",{className:"welcome-text"},(()=>{const e=(new Date).getHours();return e<12?"Good Morning":e<18?"Good Afternoon":"Good Evening"})(),", ",i.username),n.a.createElement("span",{className:"member-since"},"Member since 2024"),r&&r!==localStorage.getItem("username")&&n.a.createElement("button",{className:"banner-message-btn",onClick:()=>P(i)},n.a.createElement(S.s,{className:"msg-icon"})," Send Message"))),n.a.createElement("div",{className:"stats-container"},n.a.createElement("div",{className:"stats-grid"},n.a.createElement("div",{className:"stat-card bets"},n.a.createElement(S.r,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Total Bets"),n.a.createElement("span",{className:"stat-value"},i.total_bets||0))),n.a.createElement("div",{className:"stat-card winrate"},n.a.createElement(S.L,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Win Rate"),n.a.createElement("span",{className:"stat-value"},i.win_rate||0,"%"))),n.a.createElement("div",{className:"stat-card score"},n.a.createElement(S.i,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Rank"),n.a.createElement("span",{className:"stat-value"},"#",i.leaderboard_position||0))),n.a.createElement("div",{className:"stat-card points"},n.a.createElement(S.I,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Points"),n.a.createElement("span",{className:"stat-value"},i.leaderboard_score||0))),n.a.createElement("div",{className:"stat-card friends"},n.a.createElement(S.T,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Friends"),n.a.createElement("span",{className:"stat-value"},(null===(e=i.friends)||void 0===e?void 0:e.length)||0))),n.a.createElement("div",{className:"stat-card requests"},n.a.createElement(S.R,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("h3",null,"Requests"),n.a.createElement("span",{className:"stat-value"},i.friend_requests||0)))))),n.a.createElement("div",{className:"search-section card"},n.a.createElement("h2",null,"Find Friends"),n.a.createElement("div",{className:"search-box"},n.a.createElement("input",{type:"text",placeholder:"Search users...",value:u,onChange:e=>E(e.target.value)}),n.a.createElement("button",{onClick:A},"Search")),b&&n.a.createElement("div",{className:"search-error"},b),p.length>0&&n.a.createElement("div",{className:"search-results"},p.map(e=>n.a.createElement("div",{key:e.user_id,className:"user-search-card"},n.a.createElement("div",{className:"user-search-info"},n.a.createElement("div",{className:"user-primary-info"},n.a.createElement("span",{className:"username"},e.username),n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:I(e.favorite_team),alt:e.favorite_team,className:"team-logo-tiny"}),n.a.createElement("span",{className:"favorite-team"},e.favorite_team))),n.a.createElement("div",{className:"user-stats"},n.a.createElement("span",null,"Total Bets: ",e.total_bets||0),n.a.createElement("span",null,"Win Rate: ",e.win_rate||0,"%"),n.a.createElement("span",null,"Score: ",e.leaderboard_score||0))),n.a.createElement("button",{onClick:()=>(async e=>{try{const t=localStorage.getItem("userId"),l=await o.a.post(`${He}/handlers/add_friend.php`,{user_id:t,friend_id:e,action:"request"});l.data.success?(g([]),E(""),x({show:!0,type:"success",message:"Friend request sent successfully!"}),setTimeout(()=>{x({show:!1,type:"",message:""})},3e3)):v(l.data.message)}catch(a){v("Failed to send friend request")}})(e.user_id),className:"add-friend-btn"},n.a.createElement(S.R,null)," Send Friend Request"))))),n.a.createElement("div",{className:"profile-recent-bets-section card"},n.a.createElement("h2",null,"Recent Bets"),n.a.createElement("div",{className:"profile-bets-table-container"},n.a.createElement("table",{className:"profile-recent-bets-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"Reference"),n.a.createElement("th",null,"Teams"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Returns"))),n.a.createElement("tbody",null,i.recent_bets&&i.recent_bets.map((e,a)=>{var t;return n.a.createElement("tr",{key:e.bet_id,onClick:()=>(e=>{w(e),f(!0)})(e)},n.a.createElement("td",null,n.a.createElement("span",{className:"profile-recent-bet-ref"},(null===(t=e.unique_code)||void 0===t?void 0:t.toUpperCase())||`${e.bet_id}DNRBKCC`)),n.a.createElement("td",null,n.a.createElement("div",{className:"profile-recent-team-cell"},n.a.createElement("div",{className:"profile-recent-team-selector"},n.a.createElement("div",{className:"profile-recent-team-logo"},n.a.createElement("img",{src:I(e.team_a),alt:e.team_a,onError:e=>{e.target.src="/default-team-logo.png"}})),n.a.createElement("span",{className:"profile-recent-team-name"},e.team_a),n.a.createElement("span",{className:"profile-recent-username"},e.user1_name)),n.a.createElement("span",{className:"profile-recent-vs"},"VS"),n.a.createElement("div",{className:"profile-recent-team-selector"},n.a.createElement("div",{className:"profile-recent-team-logo"},n.a.createElement("img",{src:I(e.team_b),alt:e.team_b,onError:e=>{e.target.src="/default-team-logo.png"}})),n.a.createElement("span",{className:"profile-recent-team-name"},e.team_b),n.a.createElement("span",{className:"profile-recent-username"},e.user2_name)))),n.a.createElement("td",null,n.a.createElement("span",{className:"profile-recent-bet-amount"},e.amount_user1)),n.a.createElement("td",null,n.a.createElement("span",{className:`profile-recent-bet-status ${e.bet_status.toLowerCase()}`},e.bet_status)),n.a.createElement("td",null,n.a.createElement("div",{className:"profile-recent-returns"},n.a.createElement("div",{className:"profile-recent-return-item"},n.a.createElement("span",{className:"profile-recent-return-label"},"Win:"),n.a.createElement("span",{className:"profile-recent-return-value"},e.potential_return_win_user1," FC")),n.a.createElement("div",{className:"profile-recent-return-item"},n.a.createElement("span",{className:"profile-recent-return-label"},"Draw:"),n.a.createElement("span",{className:"profile-recent-return-value"},e.potential_return_draw_user1," FC")))))}))))),n.a.createElement("div",{className:"friends-section"},n.a.createElement("div",{className:"section-header"},n.a.createElement("h2",null,"Friends (",(null===(a=i.friends)||void 0===a?void 0:a.length)||0,")"),n.a.createElement("div",{className:"search-box"},n.a.createElement("input",{type:"text",placeholder:"Search users...",value:u,onChange:e=>E(e.target.value)}),n.a.createElement("button",{onClick:A},"Search"))),n.a.createElement("div",{className:"friends-grid"},null===(t=i.friends)||void 0===t?void 0:t.map(e=>n.a.createElement("div",{key:e.user_id,className:"friend-card"},n.a.createElement("div",{className:"friend-header"},n.a.createElement("div",{className:"friend-avatar"},n.a.createElement("img",{src:I(e.favorite_team),alt:e.favorite_team})),n.a.createElement("div",{className:"friend-basic-info"},n.a.createElement(m.b,{to:`/user/profile/${e.username}`},n.a.createElement("h3",null,e.username)),n.a.createElement("span",{className:"favorite-team"},e.favorite_team))),n.a.createElement("div",{className:"friend-stats-grid"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Total Bets"),n.a.createElement("span",null,e.total_bets)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Win Rate"),n.a.createElement("span",null,e.win_rate,"%")),n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Score"),n.a.createElement("span",null,e.leaderboard_score))),n.a.createElement("button",{className:"message-btn",onClick:()=>P(e)},"Send Message"))))),_&&y&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"bet-details-modal"},n.a.createElement("button",{className:"close-button",onClick:()=>f(!1)},"\xd7"),n.a.createElement("div",{className:"modal-left"},n.a.createElement("h3",{className:"reference-title"},"Bet Reference: ",(null===(s=y.unique_code)||void 0===s?void 0:s.toUpperCase())||`${y.bet_id}DNRBKCC`),n.a.createElement("div",{className:"teams-match"},n.a.createElement("div",{className:"team-card user1"},n.a.createElement("img",{src:I(y.team_a),alt:y.team_a}),n.a.createElement("div",{className:"team-name"},y.team_a),n.a.createElement("div",{className:"team-odds user1"},"Odds: ",y.odds_team_a),n.a.createElement("div",{className:"team-user"},y.user1_name)),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"team-card user2"},n.a.createElement("img",{src:I(y.team_b),alt:y.team_b}),n.a.createElement("div",{className:"team-name"},y.team_b),n.a.createElement("div",{className:"team-odds user2"},"Odds: ",y.odds_team_b),n.a.createElement("div",{className:"team-user"},y.user2_name)))),n.a.createElement("div",{className:"modal-right"},n.a.createElement("div",{className:"status-badge-large","data-status":y.bet_status},y.bet_status.toUpperCase()),n.a.createElement("div",{className:"bet-details-grid"},n.a.createElement("div",{className:"detail-item amount"},n.a.createElement("div",{className:"detail-label"},"Your Bet Amount"),n.a.createElement("div",{className:"detail-value amount"},y.amount_user2," FanCoins")),n.a.createElement("div",{className:"detail-item return"},n.a.createElement("div",{className:"detail-label"},"Your Potential Return"),n.a.createElement("div",{className:"detail-value return"},y.potential_return_win_user2," FanCoins")),n.a.createElement("div",{className:"detail-item"},n.a.createElement("div",{className:"detail-label"},"Created At"),n.a.createElement("div",{className:"detail-value"},new Date(y.created_at).toLocaleString())))))),C&&O&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"message-modal"},n.a.createElement("button",{className:"close-button",onClick:()=>k(!1)},"\xd7"),n.a.createElement("div",{className:"modal-user-preview"},n.a.createElement("div",{className:"modal-user-avatar"},n.a.createElement("img",{src:I(O.favorite_team),alt:O.favorite_team})),n.a.createElement("div",{className:"modal-user-info"},n.a.createElement("h3",null,O.username),n.a.createElement("span",{className:"modal-team-name"},O.favorite_team)),n.a.createElement("div",{className:"modal-user-stats"},n.a.createElement("div",{className:"modal-stat"},n.a.createElement(S.L,{className:"modal-stat-icon"}),n.a.createElement("div",{className:"modal-stat-details"},n.a.createElement("label",null,"Total Wins"),n.a.createElement("span",null,O.wins||0))),n.a.createElement("div",{className:"modal-stat"},n.a.createElement(S.I,{className:"modal-stat-icon"}),n.a.createElement("div",{className:"modal-stat-details"},n.a.createElement("label",null,"Points"),n.a.createElement("span",null,O.leaderboard_score||0)))),n.a.createElement("div",{className:"modal-user-stats-grid"},n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Wins"),n.a.createElement("span",null,O.wins||0)),n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Draws"),n.a.createElement("span",null,O.draws||0)),n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Losses"),n.a.createElement("span",null,O.losses||0)))),n.a.createElement("div",{className:"message-input-section"},n.a.createElement("div",{className:"message-input-header"},n.a.createElement("label",null,"Send Message to ",O.username)),n.a.createElement("div",{className:"message-textarea-container"},n.a.createElement("textarea",{className:"message-textarea",value:j,onChange:e=>L(e.target.value),placeholder:"Type your message here...",rows:"6"})),n.a.createElement("div",{className:"message-modal-footer"},n.a.createElement("button",{className:"modal-btn cancel-btn",onClick:()=>{k(!1),L("")}},"Cancel"),n.a.createElement("button",{className:"modal-btn send-btn",onClick:async()=>{try{const a=localStorage.getItem("userId"),t=await o.a.post(`${He}/handlers/send_message.php`,{sender_id:a,receiver_id:O.user_id,message:j});t.data.success?(L(""),k(!1),x({show:!0,type:"success",message:"Message sent successfully!"}),setTimeout(()=>{x({show:!1,type:"",message:""})},3e3)):x({show:!0,type:"error",message:"Failed to send message: "+t.data.message})}catch(e){console.error("Error sending message:",e),x({show:!0,type:"error",message:"Failed to send message. Please try again."})}},disabled:!j.trim()},n.a.createElement(S.s,{className:"send-icon"}),"Send Message"))))))};const ze="/backend";var Ye=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)([]),[r,m]=Object(l.useState)(!1),[i,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(!0),[p,g]=Object(l.useState)(null),[b,v]=Object(l.useState)(1),[h,N]=Object(l.useState)(1),[_]=Object(l.useState)(20),f=localStorage.getItem("userId"),y=Object(c.q)(),w=Object(l.useCallback)(async()=>{try{const e=await o.a.get(`${ze}/handlers/get_accepted_bets.php?userId=${f}&page=${b}&limit=${_}`);e.data.success?(a(e.data.bets||[]),N(e.data.pagination.totalPages)):(g("Failed to fetch accepted bets"),console.error("Error fetching bets:",e.data.message||"Unknown error"))}catch(p){console.error("Error fetching bets:",p),g("An error occurred while fetching accepted bets.")}finally{E(!1)}},[f,b,_]),S=Object(l.useCallback)(async()=>{try{const e=await o.a.get(`${ze}/handlers/team_management.php`);200===e.data.status?s(e.data.data||[]):(g("Failed to fetch teams"),console.error("Failed to fetch teams:",e.data.message))}catch(p){console.error("Error fetching teams:",p),g("An error occurred while fetching teams.")}},[]);Object(l.useEffect)(()=>{f?(w(),S()):y("/user/login")},[f,w,S,y]);const C=e=>{v(e)},k=e=>{const a=t.find(a=>a.name===e);return a?`${ze}/${a.logo}`:""},O=e=>(e.unique_code||`${e.bet_id}DNRBKCC`).toUpperCase(),F=e=>{d(e),m(!0)};return n.a.createElement("div",{className:"view-bets-container"},n.a.createElement("div",{className:"title-section"},n.a.createElement("h2",null,"Accepted Bets"),n.a.createElement("div",{className:"title-line"})),n.a.createElement("div",{className:"table-responsive"},n.a.createElement("table",{className:"bets-table rounded-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"#"),n.a.createElement("th",null,"Ref"),n.a.createElement("th",{colSpan:"3",className:"teams-header compact"},"Teams"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Return"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Match\xa0Date"))),n.a.createElement("tbody",null,e.map((e,a)=>n.a.createElement("tr",{key:e.bet_id},n.a.createElement("td",null,(b-1)*_+a+1),n.a.createElement("td",null,n.a.createElement("span",{className:"reference",onClick:()=>F(e)},O(e))),n.a.createElement("td",{className:"team-cell compact"},n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:k(e.team_a),alt:e.team_a,className:"team-logo"}),n.a.createElement("span",null,e.team_a),"team_a_win"===e.bet_choice_user2&&n.a.createElement("span",{className:"pick-badge"},"Your Pick"))),n.a.createElement("td",{className:"vs-cell compact"},n.a.createElement("div",{className:"vs-indicator"},"VS")),n.a.createElement("td",{className:"team-cell compact"},n.a.createElement("div",{className:"team-info"},n.a.createElement("img",{src:k(e.team_b),alt:e.team_b,className:"team-logo"}),n.a.createElement("span",null,e.team_b),"team_b_win"===e.bet_choice_user2&&n.a.createElement("span",{className:"pick-badge"},"Your Pick"))),n.a.createElement("td",{className:"amount-cell"},e.amount_user2," ",n.a.createElement("span",{className:"currency"},"FanCoins")),n.a.createElement("td",{className:"return-cell"},e.potential_return_win_user2," ",n.a.createElement("span",{className:"currency"},"FanCoins")),n.a.createElement("td",null,n.a.createElement("div",{className:"status-container"},n.a.createElement("div",{className:`status-badge ${e.bet_status}`},n.a.createElement("span",{className:"status-dot"}),n.a.createElement("span",{className:"status-text"},"joined"===e.bet_status&&"Joined","completed"===e.bet_status&&"Completed")))),n.a.createElement("td",{className:"date-cell"},n.a.createElement("div",{className:"date-display"},n.a.createElement("div",{className:"date-line"},new Date(e.match_date).toLocaleDateString()),n.a.createElement("div",{className:"time-line"},new Date(e.match_date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))))))))),n.a.createElement("div",{className:"mobile-bets-grid"},e.map(e=>n.a.createElement("div",{key:e.bet_id,className:"mobile-bet-card"},n.a.createElement("div",{className:"mobile-bet-header"},n.a.createElement("span",{className:"mobile-bet-ref",onClick:()=>F(e)},O(e)),n.a.createElement("div",{className:`status-badge ${e.bet_status.toLowerCase()}`},n.a.createElement("span",{className:"status-dot"}),"joined"===e.bet_status?"BET MATCHED":e.bet_status.toUpperCase())),n.a.createElement("div",{className:"mobile-users-section"},n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},e.username),n.a.createElement("span",{className:"mobile-user-status creator"},"Creator")),n.a.createElement("div",{className:"mobile-vs-divider"},"VS"),n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},e.username2),n.a.createElement("span",{className:"mobile-user-status opponent"},"Opponent"))),n.a.createElement("div",{className:"mobile-teams-container"},n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:k(e.team_a),alt:e.team_a,className:"mobile-team-logo"}),n.a.createElement("span",{className:"mobile-team-name"},e.team_a),"team_a_win"===e.bet_choice_user2&&n.a.createElement("span",{className:"mobile-pick-badge"},"Your Pick")),n.a.createElement("div",{className:"mobile-vs"},"VS"),n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:k(e.team_b),alt:e.team_b,className:"mobile-team-logo"}),n.a.createElement("span",{className:"mobile-team-name"},e.team_b),"team_b_win"===e.bet_choice_user2&&n.a.createElement("span",{className:"mobile-pick-badge"},"Your Pick"))),n.a.createElement("div",{className:"mobile-bet-details"},n.a.createElement("div",{className:"mobile-detail-item"},n.a.createElement("span",{className:"mobile-detail-label"},"Amount"),n.a.createElement("span",{className:"mobile-detail-value"},e.amount_user2," FanCoins")),n.a.createElement("div",{className:"mobile-detail-item"},n.a.createElement("span",{className:"mobile-detail-label"},"Potential Return"),n.a.createElement("span",{className:"mobile-detail-value win"},"+",e.potential_return_win_user2," FanCoins")),n.a.createElement("div",{className:"mobile-detail-item full-width"},n.a.createElement("span",{className:"mobile-detail-label"},"Match Date"),n.a.createElement("span",{className:"mobile-detail-value"},n.a.createElement("div",{className:"mobile-date-display"},n.a.createElement("div",{className:"mobile-date-line"},new Date(e.match_date).toLocaleDateString()),n.a.createElement("div",{className:"mobile-time-line"},new Date(e.match_date).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}))))))))),window.innerWidth>768&&r&&i&&n.a.createElement("div",{className:"modal-overlay",onClick:()=>m(!1)},n.a.createElement("div",{className:"bet-details-modal",onClick:e=>e.stopPropagation()},n.a.createElement("button",{className:"close-button",onClick:()=>m(!1)},"\xd7"),n.a.createElement("div",{className:"modal-left"},n.a.createElement("div",{className:"modal-header"},n.a.createElement("h3",{className:"reference-title"},"Bet Reference: ",O(i)),n.a.createElement("div",{className:"status-badges"},n.a.createElement("div",{className:`status-badge-large ${i.bet_status}`},"joined"===i.bet_status&&"BET MATCHED","completed"===i.bet_status&&"COMPLETED"),n.a.createElement("div",{className:`match-type-badge-large ${i.match_type}`},"half_time"===i.match_type?"HALF TIME":"FULL TIME"))),n.a.createElement("div",{className:"teams-match"},n.a.createElement("div",{className:"team-card"},"team_a_win"===i.bet_choice_user2&&n.a.createElement("div",{className:"selected-badge"},"Selected"),n.a.createElement("img",{src:k(i.team_a),alt:i.team_a}),n.a.createElement("div",{className:"team-name"},i.team_a),n.a.createElement("div",{className:"team-username"},i.username),n.a.createElement("div",{className:"team-odds"},i.odds_team_a,"x")),n.a.createElement("div",{className:"vs-badge"},"VS"),n.a.createElement("div",{className:"team-card"},"team_b_win"===i.bet_choice_user2&&n.a.createElement("div",{className:"selected-badge"},"Selected"),n.a.createElement("img",{src:k(i.team_b),alt:i.team_b}),n.a.createElement("div",{className:"team-name"},i.team_b),n.a.createElement("div",{className:"team-username"},i.username2),n.a.createElement("div",{className:"team-odds"},i.odds_team_b,"x"))),n.a.createElement("div",{className:"match-details-grid"},n.a.createElement("div",{className:"details-section schedule-section"},n.a.createElement("div",{className:"section-title"},"MATCH SCHEDULE"),n.a.createElement("div",{className:"schedule-grid"},n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"MATCH DATE"),n.a.createElement("span",{className:"schedule-value"},new Date(i.match_date).toLocaleString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"START TIME"),n.a.createElement("span",{className:"schedule-value"},new Date(i.start_time).toLocaleTimeString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"END TIME"),n.a.createElement("span",{className:"schedule-value"},new Date(i.end_time).toLocaleTimeString())),n.a.createElement("div",{className:"schedule-item"},n.a.createElement("span",{className:"schedule-label"},"CHALLENGE CREATED"),n.a.createElement("span",{className:"schedule-value"},new Date(i.challenge_date).toLocaleString())))),n.a.createElement("div",{className:"details-section odds-section"},n.a.createElement("div",{className:"section-title"},"ODDS INFORMATION"),n.a.createElement("div",{className:"odds-grid"},n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},i.team_a," WIN"),n.a.createElement("span",{className:"odds-value"},i.odds_team_a,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},i.team_b," WIN"),n.a.createElement("span",{className:"odds-value"},i.odds_team_b,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},"YOUR ODDS"),n.a.createElement("span",{className:"odds-value"},"team_a_win"===i.bet_choice_user2?i.odds_team_a:i.odds_team_b,"x")),n.a.createElement("div",{className:"odds-item"},n.a.createElement("span",{className:"odds-label"},"POTENTIAL MULTIPLIER"),n.a.createElement("span",{className:"odds-value"},(i.potential_return_win_user2/i.amount_user2).toFixed(2),"x")))))),n.a.createElement("div",{className:"modal-right"},n.a.createElement("div",{className:"details-section"},n.a.createElement("div",{className:"section-title"},"BET STATUS"),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"CREATOR"),n.a.createElement("span",{className:"detail-value created-by"},i.username)),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"OPPONENT"),n.a.createElement("span",{className:"detail-value status-value"},i.username2))),n.a.createElement("div",{className:"details-section"},n.a.createElement("div",{className:"section-title"},"FINANCIAL DETAILS"),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"YOUR BET"),n.a.createElement("span",{className:"detail-value amount"},i.amount_user2," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"POTENTIAL WIN"),n.a.createElement("span",{className:"detail-value return"},i.potential_return_win_user2," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"POTENTIAL LOSS"),n.a.createElement("span",{className:"detail-value amount"},i.potential_return_loss_user2," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"DRAW OUTCOME"),n.a.createElement("span",{className:"detail-value"},i.potential_return_draw_user2," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"CREATOR BET"),n.a.createElement("span",{className:"detail-value amount"},i.amount_user1," FanCoins")),n.a.createElement("div",{className:"detail-row"},n.a.createElement("span",{className:"detail-label"},"CREATOR WIN"),n.a.createElement("span",{className:"detail-value return"},i.potential_return_win_user1," FanCoins")))))),window.innerWidth<=768&&r&&i&&n.a.createElement("div",{className:"modal-overlay",onClick:()=>m(!1)},n.a.createElement("div",{className:"mobile-modal-content",onClick:e=>e.stopPropagation()},n.a.createElement("div",{className:"mobile-modal-header"},n.a.createElement("h3",null,"Bet Details"),n.a.createElement("button",{className:"mobile-modal-close",onClick:()=>m(!1)},"\xd7")),n.a.createElement("div",{className:"mobile-modal-body"},n.a.createElement("div",{className:"mobile-ref-section"},n.a.createElement("div",{className:"mobile-ref-number"},n.a.createElement("span",{className:"mobile-ref-label"},"Reference Number"),n.a.createElement("span",{className:"mobile-ref-value"},O(i))),n.a.createElement("div",{className:"mobile-status-badges"},n.a.createElement("span",{className:`mobile-status-badge ${i.bet_status.toLowerCase()}`},"joined"===i.bet_status&&"BET MATCHED","completed"===i.bet_status&&"COMPLETED"),n.a.createElement("span",{className:"mobile-status-badge mobile-match-type"},"half_time"===i.match_type?"HALF TIME":"FULL TIME"))),n.a.createElement("div",{className:"mobile-users-section"},n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},i.username),n.a.createElement("span",{className:"mobile-user-status creator"},"Creator")),n.a.createElement("div",{className:"mobile-vs-divider"},"VS"),n.a.createElement("div",{className:"mobile-user-info"},n.a.createElement("span",{className:"mobile-user-name"},i.username2),n.a.createElement("span",{className:"mobile-user-status opponent"},"Opponent"))),n.a.createElement("div",{className:"mobile-teams-container"},n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:k(i.team_a),alt:i.team_a,className:"mobile-team-logo"}),n.a.createElement("span",null,i.team_a)),n.a.createElement("div",{className:"mobile-vs"},"VS"),n.a.createElement("div",{className:"mobile-team"},n.a.createElement("img",{src:k(i.team_b),alt:i.team_b,className:"mobile-team-logo"}),n.a.createElement("span",null,i.team_b))),n.a.createElement("div",{className:"mobile-section-title"},"ODDS INFORMATION"),n.a.createElement("div",{className:"mobile-odds-grid"},n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},i.team_a," WIN"),n.a.createElement("span",{className:"mobile-odds-value"},i.odds_team_a,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},i.team_b," WIN"),n.a.createElement("span",{className:"mobile-odds-value"},i.odds_team_b,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},"YOUR ODDS"),n.a.createElement("span",{className:"mobile-odds-value"},"team_a_win"===i.bet_choice_user2?i.odds_team_a:i.odds_team_b,"x")),n.a.createElement("div",{className:"mobile-odds-item"},n.a.createElement("span",{className:"mobile-odds-label"},"MULTIPLIER"),n.a.createElement("span",{className:"mobile-odds-value"},(i.potential_return_win_user2/i.amount_user2).toFixed(2),"x"))),n.a.createElement("div",{className:"mobile-section-title"},"FINANCIAL DETAILS"),n.a.createElement("div",{className:"mobile-financial-grid"},n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Your Bet"),n.a.createElement("span",{className:"mobile-financial-value"},i.amount_user2," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Potential Win"),n.a.createElement("span",{className:"mobile-financial-value win"},"+",i.potential_return_win_user2," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Potential Loss"),n.a.createElement("span",{className:"mobile-financial-value loss"},"-",i.potential_return_loss_user2," FC")),n.a.createElement("div",{className:"mobile-financial-item"},n.a.createElement("span",{className:"mobile-financial-label"},"Draw Return"),n.a.createElement("span",{className:"mobile-financial-value draw"},i.potential_return_draw_user2," FC")))))),(()=>{const e=[];for(let a=1;a<=h;a++)e.push(n.a.createElement("button",{key:a,onClick:()=>C(a),className:`pagination-button ${b===a?"active":""}`},a));return n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>C(b-1),disabled:1===b,className:"pagination-button"},"Previous"),e,n.a.createElement("button",{onClick:()=>C(b+1),disabled:b===h,className:"pagination-button"},"Next"))})(),n.a.createElement("style",{jsx:!0},"\n        .rounded-table {\n          border-radius: 12px;\n          overflow: hidden;\n          border: 1px solid #e0e0e0;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n        }\n        .rounded-table thead {\n          background-color: #f8f9fa;\n        }\n        .rounded-table th {\n          padding: 12px 8px;\n          font-weight: 600;\n          color: #444;\n          border-bottom: 2px solid #e0e0e0;\n        }\n        .rounded-table td {\n          padding: 10px 8px;\n          border-bottom: 1px solid #e0e0e0;\n        }\n        .rounded-table tr:last-child td {\n          border-bottom: none;\n        }\n        .teams-header.compact {\n          width: 20%;\n        }\n        .team-cell.compact {\n          max-width: 80px;\n          padding: 4px;\n        }\n        .vs-cell.compact {\n          padding: 0 4px;\n          width: 30px;\n        }\n        .team-info {\n          gap: 4px;\n        }\n        .team-logo {\n          width: 18px;\n          height: 18px;\n          min-width: 18px;\n        }\n        .pick-badge {\n          font-size: 0.7rem;\n          padding: 2px 4px;\n          background: #e3f2fd;\n          color: #1976d2;\n          border-radius: 4px;\n          white-space: nowrap;\n        }\n        .reference {\n          color: #1976d2;\n          text-decoration: underline;\n          cursor: pointer;\n        }\n        .reference:hover {\n          color: #0d47a1;\n        }\n        .table-responsive {\n          margin: 16px;\n          background: white;\n          border-radius: 12px;\n          overflow-x: auto;\n        }\n        .bets-table {\n          width: 100%;\n          border-collapse: collapse;\n          min-width: 800px;\n        }\n        .bets-table tr:hover {\n          background-color: #f5f5f5;\n        }\n        .username-cell {\n          font-weight: 500;\n          color: #2196f3;\n        }\n        \n        .username {\n          display: inline-block;\n          padding: 2px 6px;\n          border-radius: 4px;\n          background: #e3f2fd;\n          font-size: 0.9rem;\n        }\n\n        /* Mobile Styles */\n        .mobile-bets-grid {\n          display: none;\n          padding: 16px;\n          gap: 16px;\n          flex-direction: column;\n        }\n\n        .mobile-bet-card {\n          background: white;\n          border-radius: 12px;\n          padding: 16px;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n          border: 1px solid #e0e0e0;\n        }\n\n        .mobile-bet-header {\n          display: flex;\n          justify-content: space-between;\n          align-items: center;\n          margin-bottom: 16px;\n        }\n\n        .mobile-bet-ref {\n          color: #1976d2;\n          font-weight: 500;\n          text-decoration: underline;\n          cursor: pointer;\n        }\n\n        .mobile-users-section {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 16px;\n          padding: 8px 0;\n          border-bottom: 1px solid #e0e0e0;\n        }\n\n        .mobile-user-info {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 4px;\n        }\n\n        .mobile-user-name {\n          font-weight: 500;\n          color: #333;\n        }\n\n        .mobile-user-status {\n          font-size: 0.8rem;\n          padding: 2px 8px;\n          border-radius: 12px;\n        }\n\n        .mobile-user-status.creator {\n          background: #e3f2fd;\n          color: #1976d2;\n        }\n\n        .mobile-user-status.opponent {\n          background: #f3e5f5;\n          color: #7b1fa2;\n        }\n\n        .mobile-vs-divider {\n          color: #757575;\n          font-weight: 500;\n        }\n\n        .mobile-teams-container {\n          display: flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 16px;\n          padding: 12px;\n          background: #f8f9fa;\n          border-radius: 8px;\n        }\n\n        .mobile-team {\n          display: flex;\n          flex-direction: column;\n          align-items: center;\n          gap: 8px;\n          flex: 1;\n        }\n\n        .mobile-team-logo {\n          width: 40px;\n          height: 40px;\n          border-radius: 8px;\n          object-fit: cover;\n        }\n\n        .mobile-team-name {\n          font-weight: 500;\n          text-align: center;\n          font-size: 0.9rem;\n        }\n\n        .mobile-pick-badge {\n          font-size: 0.7rem;\n          padding: 2px 6px;\n          background: #e3f2fd;\n          color: #1976d2;\n          border-radius: 4px;\n          white-space: nowrap;\n        }\n\n        .mobile-vs {\n          color: #757575;\n          font-weight: 500;\n          margin: 0 12px;\n        }\n\n        .mobile-bet-details {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 12px;\n        }\n\n        .mobile-detail-item {\n          display: flex;\n          flex-direction: column;\n          gap: 4px;\n        }\n\n        .mobile-detail-item.full-width {\n          grid-column: 1 / -1;\n        }\n\n        .mobile-detail-label {\n          font-size: 0.8rem;\n          color: #757575;\n        }\n\n        .mobile-detail-value {\n          font-weight: 500;\n          color: #333;\n        }\n\n        .mobile-detail-value.win {\n          color: #2e7d32;\n        }\n\n        @media (max-width: 768px) {\n          .table-responsive {\n            display: none;\n          }\n          .mobile-bets-grid {\n            display: flex;\n          }\n        }\n\n        .date-cell {\n          padding: 4px 8px;\n          min-width: 100px;\n        }\n\n        .date-display {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          font-size: 0.85rem;\n        }\n\n        .date-line {\n          color: #333;\n        }\n\n        .time-line {\n          color: #666;\n          font-size: 0.8rem;\n        }\n\n        .mobile-date-display {\n          display: flex;\n          flex-direction: column;\n          gap: 2px;\n          font-size: 0.85rem;\n        }\n\n        .mobile-date-line {\n          color: #333;\n        }\n\n        .mobile-time-line {\n          color: #666;\n          font-size: 0.8rem;\n        }\n      "))};var Ge=function(){return n.a.createElement("div",null,n.a.createElement("h1",null,"Payment History"))};t(72);const Je="/backend",Ke=e=>{switch(e){case 1:return"\ud83d\udc51";case 2:return"\ud83e\udd48";case 3:return"\ud83e\udd49";case 4:case 5:return"\ud83c\udfc6";case 6:case 7:case 8:return"\u2b50";case 9:case 10:return"\ud83c\udf1f";default:return null}};var Qe=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,c]=Object(l.useState)(null),[m,i]=Object(l.useState)(1),[d,u]=Object(l.useState)({current_page:1,total_pages:0,total_records:0,records_per_page:10});Object(l.useEffect)(()=>{E()},[m]);const E=async()=>{try{s(!0),c(null),console.log("Fetching leaderboard data for page:",m);const g=await o.a.get(`${Je}/handlers/leaderboard.php`,{params:{page:m,limit:10}});if(console.log("Leaderboard API response:",g.data),!g.data.success)throw console.error("API returned error:",g.data.message),new Error(g.data.message||"Failed to load leaderboard");if(0===g.data.data.leaderboard.length&&m>1)return console.log("No data on current page, going back to previous page"),void i(e=>Math.max(1,e-1));a(g.data.data.leaderboard),u(g.data.data.pagination)}catch(p){var e,t,l,n,r,d,E;console.error("Error details:",{message:p.message,response:null===(e=p.response)||void 0===e?void 0:e.data,status:null===(t=p.response)||void 0===t?void 0:t.status,statusText:null===(l=p.response)||void 0===l?void 0:l.statusText});let a="Failed to load leaderboard data. ";(null===(n=p.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message)?a+=p.response.data.message:404===(null===(d=p.response)||void 0===d?void 0:d.status)?a+="API endpoint not found. Please check the server configuration.":500===(null===(E=p.response)||void 0===E?void 0:E.status)?a+="Server error occurred. Please try again later.":"Network Error"===p.message?a+="Network error. Please check your internet connection.":a+=p.message||"An unexpected error occurred",c(a)}finally{s(!1)}},p=e=>{e>=1&&e<=d.total_pages&&(console.log("Changing to page:",e),i(e))},g=()=>{console.log("Retrying leaderboard fetch..."),E()};return t?n.a.createElement("div",{className:"leaderboard-loading"},n.a.createElement("div",{className:"loading-spinner"}),n.a.createElement("p",null,"Loading leaderboard data...")):r?n.a.createElement("div",{className:"leaderboard-error"},n.a.createElement("p",null,r),n.a.createElement("button",{onClick:g,className:"retry-button"},"Retry")):n.a.createElement("div",{className:"leaderboard-container"},n.a.createElement("h2",null,"Top User Leaderboard"),0===e.length?n.a.createElement("div",{className:"no-data"},"No players found in the leaderboard"):n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"leaderboard-table"},n.a.createElement("div",{className:"leaderboard-header"},n.a.createElement("div",{className:"rank-column"},"RANK"),n.a.createElement("div",{className:"player-column"},"PLAYER"),n.a.createElement("div",{className:"points-column"},"POINTS"),n.a.createElement("div",{className:"stats-column"},"W/L"),n.a.createElement("div",{className:"bets-column"},"BETS"),n.a.createElement("div",{className:"challenges-column"},"CHALLENGES"),n.a.createElement("div",{className:"streak-column"},"STREAK")),e.map((e,a)=>{const t=(d.current_page-1)*d.records_per_page+a+1,l=Ke(t),s=e.total_bets>0?Math.round(e.wins/e.total_bets*100):0;return n.a.createElement("div",{key:e.user_id,className:"leaderboard-row"},n.a.createElement("div",{className:"rank-column"},l&&n.a.createElement("span",{className:"rank-icon"},l),n.a.createElement("span",{className:"rank-number"},"#",t)),n.a.createElement("div",{className:"player-column"},n.a.createElement("span",{className:"player-name"},e.username)),n.a.createElement("div",{className:"points-column"},e.points),n.a.createElement("div",{className:"stats-column"},n.a.createElement("span",{className:"wins"},e.wins),n.a.createElement("span",{className:"separator"},"/"),n.a.createElement("span",{className:"losses"},e.losses),n.a.createElement("span",{className:"win-rate"},"(",s,"%)")),n.a.createElement("div",{className:"bets-column"},e.total_bets),n.a.createElement("div",{className:"challenges-column"},e.total_challenges),n.a.createElement("div",{className:"streak-column"},n.a.createElement("span",{className:"current-streak"},e.current_streak),n.a.createElement("span",{className:"highest-streak"},"(",e.highest_streak,")")))})),d.total_pages>1&&n.a.createElement("div",{className:"pagination-controls"},n.a.createElement("button",{onClick:()=>p(m-1),disabled:1===m,className:"pagination-button"},"Previous"),n.a.createElement("span",{className:"page-info"},"Page ",m," of ",d.total_pages),n.a.createElement("button",{onClick:()=>p(m+1),disabled:m===d.total_pages,className:"pagination-button"},"Next"))))};var Xe=function(){const[e,a]=Object(l.useState)(""),[t,s]=Object(l.useState)(""),[r,c]=Object(l.useState)("");return n.a.createElement("div",null,n.a.createElement("h1",null,"Change Password"),n.a.createElement("form",{onSubmit:e=>{e.preventDefault()}},n.a.createElement("div",null,n.a.createElement("label",null,"Current Password:"),n.a.createElement("input",{type:"password",value:e,onChange:e=>a(e.target.value)})),n.a.createElement("div",null,n.a.createElement("label",null,"New Password:"),n.a.createElement("input",{type:"password",value:t,onChange:e=>s(e.target.value)})),n.a.createElement("div",null,n.a.createElement("label",null,"Confirm New Password:"),n.a.createElement("input",{type:"password",value:r,onChange:e=>c(e.target.value)})),n.a.createElement("button",{type:"submit"},"Change Password")))};var Ze=function(){const[e,a]=Object(l.useState)(""),[t,s]=Object(l.useState)("");return n.a.createElement("div",null,n.a.createElement("h1",null,"Deposit Funds"),n.a.createElement("form",{onSubmit:e=>{e.preventDefault()}},n.a.createElement("div",null,n.a.createElement("label",null,"Amount:"),n.a.createElement("input",{type:"number",value:e,onChange:e=>a(e.target.value),min:"0"})),n.a.createElement("div",null,n.a.createElement("label",null,"Payment Method:"),n.a.createElement("select",{value:t,onChange:e=>s(e.target.value)},n.a.createElement("option",{value:""},"Select a payment method"),n.a.createElement("option",{value:"credit_card"},"Credit Card"),n.a.createElement("option",{value:"bank_transfer"},"Bank Transfer"),n.a.createElement("option",{value:"crypto"},"Cryptocurrency"))),n.a.createElement("button",{type:"submit"},"Deposit")))};var ea=function(){const[e,a]=Object(l.useState)(""),[t,s]=Object(l.useState)("");return n.a.createElement("div",null,n.a.createElement("h1",null,"Withdraw Funds"),n.a.createElement("form",{onSubmit:e=>{e.preventDefault()}},n.a.createElement("div",null,n.a.createElement("label",null,"Amount:"),n.a.createElement("input",{type:"number",value:e,onChange:e=>a(e.target.value),min:"0"})),n.a.createElement("div",null,n.a.createElement("label",null,"Withdrawal Method:"),n.a.createElement("select",{value:t,onChange:e=>s(e.target.value)},n.a.createElement("option",{value:""},"Select a withdrawal method"),n.a.createElement("option",{value:"bank_transfer"},"Bank Transfer"),n.a.createElement("option",{value:"crypto"},"Cryptocurrency"))),n.a.createElement("button",{type:"submit"},"Withdraw")))};t(73);const aa="/backend";var ta=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,m]=Object(l.useState)(""),[i,d]=Object(l.useState)(!1),[u,E]=Object(l.useState)(null),[p,g]=Object(l.useState)(""),[b,v]=Object(l.useState)({show:!1,type:"",message:""}),h=localStorage.getItem("userId"),[N,_]=Object(l.useState)(""),[f,y]=Object(l.useState)("friends"),[w,C]=Object(l.useState)([]),[k,O]=Object(l.useState)([]),[F,j]=Object(l.useState)(!1),[L,T]=Object(l.useState)(null),x=Object(c.q)();Object(l.useEffect)(()=>{$(),D()},[]);const $=async()=>{try{s(!0),console.log("Fetching friends for user:",h);const l=await o.a.get(`${aa}/handlers/friends.php?user_id=${h}`);console.log("Friends response:",l.data),l.data.success?a(l.data.friends||[]):m(l.data.message||"Failed to fetch friends")}catch(r){var e,t;console.error("Error fetching friends:",r),m((null===(e=r.response)||void 0===e?void 0:null===(t=e.data)||void 0===t?void 0:t.message)||"An error occurred while fetching friends")}finally{s(!1)}},D=async()=>{try{const e=await o.a.get(`${aa}/handlers/team_management.php`);200===e.data.status&&O(e.data.data)}catch(r){console.error("Error fetching teams:",r)}},I=e=>{const a=k.find(a=>a.name===e);return a?`${aa}/${a.logo}`:""},A=()=>{if(!N.trim())return void v({show:!0,type:"error",message:"Please enter a username to search your friends list"});const t=e.filter(e=>e.username.toLowerCase().includes(N.toLowerCase()));0===t.length?(v({show:!0,type:"info",message:`No friends found with username "${N}". Try using "Find New Friends" to search for new users.`}),a(e)):(a(t),v({show:!0,type:"success",message:`Found ${t.length} friend${t.length>1?"s":""}`}))},P=async()=>{if(N.trim())try{const a=await o.a.get(`${aa}/handlers/search_users.php?query=${N}`);if(a.data.success){const t=localStorage.getItem("userId"),l=a.data.users.filter(a=>a.user_id!==t&&!e.some(e=>e.id===a.user_id));0===l.length?(v({show:!0,type:"info",message:`No new users found with username "${N}". Try a different username or check your spelling.`}),C([])):(C(l),v({show:!0,type:"success",message:`Found ${l.length} potential new friend${l.length>1?"s":""}`}))}}catch(r){v({show:!0,type:"error",message:"Error searching for users. Please try again."}),console.error("Error searching users:",r)}else v({show:!0,type:"error",message:"Please enter a username to search for new friends"})};return n.a.createElement("div",{className:"friends-container"},n.a.createElement("div",{className:"friends-header"},n.a.createElement("div",{className:"friends-title-section"},n.a.createElement("h1",null,"My Friends"),n.a.createElement("span",{className:"friends-count"},e.length," Friends")),n.a.createElement("div",{className:"friends-search-section"},n.a.createElement("div",{className:"search-type-toggle"},n.a.createElement("button",{className:`toggle-btn ${"friends"===f?"active":""}`,onClick:()=>{y("friends"),_(""),C([]),v({show:!1})}},"Search My Friends"),n.a.createElement("button",{className:`toggle-btn ${"new"===f?"active":""}`,onClick:()=>{y("new"),_(""),v({show:!1})}},"Find New Friends")),n.a.createElement("div",{className:"search-instructions"},"friends"===f?n.a.createElement("p",null,"Search through your existing friends list"):n.a.createElement("p",null,"Search for new users to add as friends")),n.a.createElement("div",{className:"search-wrapper"},n.a.createElement("input",{type:"text",placeholder:"friends"===f?"Enter friend's username...":"Search for new friends by username...",value:N,onChange:e=>_(e.target.value),onKeyPress:e=>{"Enter"===e.key&&("friends"===f?A():P())}}),n.a.createElement("button",{onClick:"friends"===f?A:P},n.a.createElement(S.H,null)," Search")))),b.show&&n.a.createElement("div",{className:`alert alert-${b.type}`},b.message),t?n.a.createElement("div",null,"Loading..."):r?n.a.createElement("div",{className:"message-status error"},r):0===e.length?n.a.createElement("div",{className:"message-status info"},"No friends found."):n.a.createElement("div",{className:"friends-grid"},e.map(e=>n.a.createElement("div",{key:e.id,className:"friend-card"},n.a.createElement("div",{className:"friend-banner"},n.a.createElement("div",{className:"friend-avatar"},e.favorite_team?n.a.createElement("img",{src:I(e.favorite_team),alt:e.favorite_team,className:"team-avatar"}):n.a.createElement("div",{className:"avatar-placeholder"},e.username.charAt(0).toUpperCase())),n.a.createElement("div",{className:"friend-info"},n.a.createElement("h3",{onClick:()=>(e=>{x(`/user/profile/${e}`)})(e.username),style:{cursor:"pointer"}},e.username),e.favorite_team&&n.a.createElement("span",null,e.favorite_team))),n.a.createElement("div",{className:"friend-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Total Bets"),n.a.createElement("span",{className:"stat-value"},e.total_bets)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Win Rate"),n.a.createElement("span",{className:"stat-value"},e.win_rate,"%")),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Score"),n.a.createElement("span",{className:"stat-value"},e.leaderboard_score||0))),n.a.createElement("div",{className:"friend-actions"},n.a.createElement("button",{className:"message-btn",onClick:()=>{E(e),d(!0)}},"Message"),n.a.createElement("button",{className:"unfriend-btn",onClick:()=>(async e=>{T(e),j(!0)})(e)},n.a.createElement(S.Q,null)," Unfriend"))))),i&&u&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"message-modal"},n.a.createElement("button",{className:"close-button",onClick:()=>d(!1)},"\xd7"),n.a.createElement("div",{className:"modal-user-preview"},n.a.createElement("div",{className:"modal-user-avatar"},n.a.createElement("img",{src:I(u.favorite_team),alt:u.favorite_team})),n.a.createElement("div",{className:"modal-user-info"},n.a.createElement("h3",null,u.username),n.a.createElement("span",{className:"modal-team-name"},u.favorite_team)),n.a.createElement("div",{className:"modal-user-stats"},n.a.createElement("div",{className:"modal-stat"},n.a.createElement(S.L,{className:"modal-stat-icon"}),n.a.createElement("div",{className:"modal-stat-details"},n.a.createElement("label",null,"Total Wins"),n.a.createElement("span",null,u.wins||0))),n.a.createElement("div",{className:"modal-stat"},n.a.createElement(S.I,{className:"modal-stat-icon"}),n.a.createElement("div",{className:"modal-stat-details"},n.a.createElement("label",null,"Points"),n.a.createElement("span",null,u.leaderboard_score||0)))),n.a.createElement("div",{className:"modal-user-stats-grid"},n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Wins"),n.a.createElement("span",null,u.wins||0)),n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Draws"),n.a.createElement("span",null,u.draws||0)),n.a.createElement("div",{className:"modal-stat-grid-item"},n.a.createElement("label",null,"Losses"),n.a.createElement("span",null,u.losses||0)))),n.a.createElement("div",{className:"message-input-section"},n.a.createElement("div",{className:"message-input-header"},n.a.createElement("label",null,"Send Message to ",u.username)),n.a.createElement("div",{className:"message-textarea-container"},n.a.createElement("textarea",{className:"message-textarea",value:p,onChange:e=>g(e.target.value),placeholder:"Type your message here...",rows:"6"})),n.a.createElement("div",{className:"message-modal-footer"},n.a.createElement("button",{className:"modal-btn cancel-btn",onClick:()=>{d(!1),g("")}},"Cancel"),n.a.createElement("button",{className:"modal-btn send-btn",onClick:async()=>{if(p.trim())try{const t=await o.a.post(`${aa}/handlers/send_message.php`,{sender_id:h,receiver_id:u.id,message:p});t.data.success?(v({show:!0,type:"success",message:"Message sent successfully"}),g(""),d(!1)):v({show:!0,type:"error",message:t.data.message||"Failed to send message"})}catch(r){var e,a;console.error("Error sending message:",r),v({show:!0,type:"error",message:(null===(e=r.response)||void 0===e?void 0:null===(a=e.data)||void 0===a?void 0:a.message)||"An error occurred while sending the message"})}else v({show:!0,type:"error",message:"Please enter a message"})},disabled:!p.trim()},n.a.createElement(S.s,{className:"send-icon"}),"Send Message"))))),"new"===f&&w.length>0&&n.a.createElement("div",{className:"new-friends-results"},n.a.createElement("h3",null,"Search Results"),n.a.createElement("div",{className:"search-results-grid"},w.map(e=>n.a.createElement("div",{key:e.user_id,className:"friend-card"},n.a.createElement("div",{className:"friend-header"},n.a.createElement("div",{className:"friend-avatar"},n.a.createElement("img",{src:I(e.favorite_team),alt:e.favorite_team})),n.a.createElement("div",{className:"friend-basic-info"},n.a.createElement("h3",null,e.username),n.a.createElement("span",{className:"favorite-team"},e.favorite_team))),n.a.createElement("div",{className:"friend-stats-grid"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Total Bets"),n.a.createElement("span",null,e.total_bets||0)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Win Rate"),n.a.createElement("span",null,e.win_rate||0,"%")),n.a.createElement("div",{className:"stat-item"},n.a.createElement("label",null,"Score"),n.a.createElement("span",null,e.leaderboard_score||0))),n.a.createElement("button",{className:"add-friend-btn",onClick:()=>(async e=>{try{(await o.a.post(`${aa}/handlers/add_friend.php`,{user_id:localStorage.getItem("userId"),friend_id:e,action:"request"})).data.success&&(v({show:!0,type:"success",message:"Friend request sent successfully!"}),C(a=>a.filter(a=>a.user_id!==e)))}catch(r){v({show:!0,type:"error",message:"Failed to send friend request"})}})(e.user_id)},n.a.createElement(S.R,null)," Send Friend Request"))))),F&&L&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"confirm-modal"},n.a.createElement("div",{className:"confirm-content"},n.a.createElement("div",{className:"confirm-icon"},n.a.createElement(S.Q,null)),n.a.createElement("h3",null,"Unfriend Confirmation"),n.a.createElement("p",null,"Are you sure you want to unfriend ",n.a.createElement("span",{className:"highlight"},L.username),"?"),n.a.createElement("div",{className:"confirm-actions"},n.a.createElement("button",{className:"cancel-btn",onClick:()=>{j(!1),T(null)}},"Cancel"),n.a.createElement("button",{className:"confirm-btn",onClick:async()=>{try{(await o.a.post(`${aa}/handlers/friends.php`,{action:"unfriend",user_id:h,friend_id:L.id})).data.success?(a(e.filter(e=>e.id!==L.id)),v({show:!0,type:"success",message:"Friend removed successfully"})):v({show:!0,type:"error",message:"Failed to remove friend"})}catch(r){v({show:!0,type:"error",message:"An error occurred"}),console.error("Error:",r)}finally{j(!1),T(null)}}},"Confirm Unfriend"))))))};t(74);const la="/backend";var na=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,c]=Object(l.useState)(""),[m,i]=Object(l.useState)([]),[d,u]=Object(l.useState)({show:!1,type:"",message:""}),E=localStorage.getItem("userId");Object(l.useEffect)(()=>{p(),g()},[]);const p=async()=>{try{console.log("Fetching friend requests for user:",E);const e=await o.a.get(`${la}/handlers/friendrequest.php?user_id=${E}`);console.log("Friend requests response:",e.data),e.data.success?a(e.data.requests||[]):c(e.data.message||"Failed to fetch friend requests")}catch(r){console.error("Error fetching friend requests:",r),c("Failed to fetch friend requests")}finally{s(!1)}},g=async()=>{try{const e=await o.a.get(`${la}/handlers/team_management.php`);200===e.data.status&&i(e.data.data)}catch(r){console.error("Error fetching teams:",r)}},b=e=>{const a=m.find(a=>a.name===e);return a?`${la}/${a.logo}`:""},v=async(e,t)=>{try{(await o.a.post(`${la}/handlers/friendrequest.php`,{action:t,user_id:E,friend_id:e})).data.success&&(a(a=>a.filter(a=>a.id!==e)),u({show:!0,type:"success",message:"accept"===t?"Friend request accepted!":"Friend request rejected"}),setTimeout(()=>{u({show:!1,type:"",message:""})},3e3))}catch(r){u({show:!0,type:"error",message:"Failed to process request"})}};if(t)return n.a.createElement("div",{className:"requests-loading"},"Loading requests...");if(r)return n.a.createElement("div",{className:"requests-error"},r);const h=e.filter(e=>"received"===e.request_type),N=e.filter(e=>"sent"===e.request_type);return n.a.createElement("div",{className:"friend-requests-container"},d.show&&n.a.createElement("div",{className:`alert alert-${d.type}`},d.message),n.a.createElement("div",{className:"requests-section"},n.a.createElement("div",{className:"requests-header"},n.a.createElement("h1",null,"Received Requests"),n.a.createElement("span",{className:"requests-count"},h.length," Pending")),0===h.length?n.a.createElement("div",{className:"no-requests"},n.a.createElement("div",{className:"no-requests-content"},n.a.createElement("h3",null,"No Received Requests"),n.a.createElement("p",null,"You don't have any incoming friend requests at the moment."))):n.a.createElement("div",{className:"requests-grid"},h.map(e=>n.a.createElement("div",{key:e.id,className:"request-card"},n.a.createElement("div",{className:"request-header"},n.a.createElement("div",{className:"request-avatar"},n.a.createElement("img",{src:b(e.favorite_team),alt:e.favorite_team})),n.a.createElement("div",{className:"request-user-info"},n.a.createElement("h3",null,e.username),n.a.createElement("span",{className:"favorite-team"},e.favorite_team))),n.a.createElement("div",{className:"request-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.L,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-details"},n.a.createElement("label",null,"Win Rate"),n.a.createElement("span",null,e.win_rate||0,"%"))),n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.I,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-details"},n.a.createElement("label",null,"Score"),n.a.createElement("span",null,e.leaderboard_score||0)))),n.a.createElement("div",{className:"request-actions"},n.a.createElement("button",{className:"accept-btn",onClick:()=>v(e.id,"accept")},n.a.createElement(S.N,null)," Accept"),n.a.createElement("button",{className:"reject-btn",onClick:()=>v(e.id,"reject")},n.a.createElement(S.S,null)," Reject")))))),n.a.createElement("div",{className:"requests-section"},n.a.createElement("div",{className:"requests-header"},n.a.createElement("h1",null,"Sent Requests"),n.a.createElement("span",{className:"requests-count"},N.length," Pending")),0===N.length?n.a.createElement("div",{className:"no-requests"},n.a.createElement("div",{className:"no-requests-content"},n.a.createElement("h3",null,"No Sent Requests"),n.a.createElement("p",null,"You haven't sent any friend requests yet."))):n.a.createElement("div",{className:"requests-grid"},N.map(e=>n.a.createElement("div",{key:e.id,className:"request-card sent-request"},n.a.createElement("div",{className:"request-header"},n.a.createElement("div",{className:"request-avatar"},n.a.createElement("img",{src:b(e.favorite_team),alt:e.favorite_team})),n.a.createElement("div",{className:"request-user-info"},n.a.createElement("h3",null,e.username),n.a.createElement("span",{className:"favorite-team"},e.favorite_team))),n.a.createElement("div",{className:"request-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.L,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-details"},n.a.createElement("label",null,"Win Rate"),n.a.createElement("span",null,e.win_rate||0,"%"))),n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.I,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-details"},n.a.createElement("label",null,"Score"),n.a.createElement("span",null,e.leaderboard_score||0)))),n.a.createElement("div",{className:"request-actions single-action"},n.a.createElement("button",{className:"cancel-btn",onClick:()=>(async e=>{try{(await o.a.post(`${la}/handlers/friendrequest.php`,{action:"cancel",user_id:E,friend_id:e})).data.success&&(a(a=>a.filter(a=>a.id!==e)),u({show:!0,type:"success",message:"Friend request cancelled"}),setTimeout(()=>{u({show:!1,type:"",message:""})},3e3))}catch(r){u({show:!0,type:"error",message:"Failed to cancel request"})}})(e.id)},n.a.createElement(S.O,null)," Cancel Request")))))))};t(75);var sa=()=>{const e=Object(c.q)(),[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)(null),[o,d]=Object(l.useState)(!0),[u,E]=Object(l.useState)(""),[p,g]=Object(l.useState)(""),[b,v]=Object(l.useState)(!1),[h,N]=Object(l.useState)(1),[_,f]=Object(l.useState)(""),[y,w]=Object(l.useState)(null),[C,k]=Object(l.useState)(""),[O,F]=Object(l.useState)(!1),[j,L]=Object(l.useState)(null),[T,x]=Object(l.useState)(0),$=async()=>{try{const e=await i.get("/backend/handlers/get_user_balance.php");200===e.data.status&&x(e.data.data.balance)}catch(u){console.error("Error fetching user balance:",u),E("Failed to fetch user balance")}};Object(l.useEffect)(()=>{(async()=>{try{d(!0),E("");const n=await i.get("/backend/handlers/league_home.php");if(console.log("League response:",n.data),200===n.data.status){const{leagues:e,user_stats:a,current_season:l}=n.data.data;e&&Array.isArray(e)?(t(e),console.log("Leagues loaded:",e.length)):(t([]),console.log("No leagues found or invalid data")),r(a||null),w(l||null),localStorage.getItem("leagueGuideShown")||(v(!0),localStorage.setItem("leagueGuideShown","true"))}else E(n.data.message||"Failed to load leagues")}catch(l){var a;console.error("Error fetching data:",l),401===(null===(a=l.response)||void 0===a?void 0:a.status)?e("/login"):E("Failed to load leagues. Please try again.")}finally{d(!1)}})(),$()},[e]);const D=async a=>{try{const n=localStorage.getItem("userId");if(!n)return E("Please log in to join a league"),void e("/login");const s=await i.get(`/backend/handlers/user_leagues.php?user_id=${n}`);if(console.log("User leagues response:",s.data),200!==s.data.status)return E(s.data.message||"Failed to verify league membership"),F(!1),void L(null);{const e=s.data.data.filter(e=>"active"===e.status);if(e.length>0)return E(`You are already a member of an active league: ${e[0].name}. You can only join one league at a time.`),F(!1),L(null),void setTimeout(()=>{E("")},5e3)}L(a),F(!0),k(a.min_bet_amount.toString()),await $()}catch(u){var t,l;console.error("Error checking user leagues:",u);const a=(null===(t=u.response)||void 0===t?void 0:null===(l=t.data)||void 0===l?void 0:l.message)||u.message||"Failed to verify league membership status";E(`Error: ${a}`),F(!1),L(null),setTimeout(()=>{E("")},5e3)}},I=a.filter(e=>e.name.toLowerCase().includes(_.toLowerCase())||e.description&&e.description.toLowerCase().includes(_.toLowerCase())),A=12*h,P=A-12,R=I.slice(P,A),q=Math.ceil(I.length/12),B=(S.L,S.i,S.c,S.y,e=>{let{league:a,userStats:t,onJoin:l,navigate:s}=e;return n.a.createElement("div",{className:"league-list-item"},n.a.createElement("div",{className:"league-main-info"},n.a.createElement("div",{className:"league-icon"},a.league_icon?n.a.createElement("img",{src:a.league_icon,alt:`${a.name} icon`}):n.a.createElement(S.L,null)),n.a.createElement("div",{className:"league-details"},n.a.createElement("div",{className:"league-header"},n.a.createElement("h3",{className:"league-title"},a.name),n.a.createElement("div",{className:"league-members"},n.a.createElement(S.T,{className:"info-icon"}),n.a.createElement("span",{className:"info-value"},a.member_count," Members"))),n.a.createElement("p",{className:"league-description"},a.description))),n.a.createElement("div",{className:"league-betting"},n.a.createElement("div",{className:"betting-limits"},n.a.createElement("div",{className:"limit-item"},n.a.createElement("span",{className:"limit-label"},"Min"),n.a.createElement("span",{className:"limit-value"},a.min_bet_formatted," FC")),n.a.createElement("div",{className:"limit-item"},n.a.createElement("span",{className:"limit-label"},"Max"),n.a.createElement("span",{className:"limit-value"},a.max_bet_formatted," FC")))),n.a.createElement("div",{className:"league-actions"},a.is_member?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"status-badge active",style:{backgroundColor:"#228B22"}},"Active"),n.a.createElement("button",{className:"view-league-btn",onClick:()=>s(`/user/leagues/${a.league_id}`)},n.a.createElement(S.i,null)," View")):a.has_active_membership?n.a.createElement("div",{className:"status-badge inactive",style:{backgroundColor:"#228B22"}},"In League"):n.a.createElement("button",{onClick:()=>l(a),className:"join-league-btn",disabled:(null===t||void 0===t?void 0:t.balance)<a.min_bet_amount},(null===t||void 0===t?void 0:t.balance)<a.min_bet_amount?n.a.createElement(n.a.Fragment,null,n.a.createElement(S.n,null)," Low Balance"):n.a.createElement(n.a.Fragment,null,n.a.createElement(S.G,null)," Join"))))});return n.a.createElement("div",{className:"league-home-container"},s&&n.a.createElement(e=>{var a;let{stats:t}=e;return n.a.createElement("div",{className:"user-stats-container"},n.a.createElement("div",{className:"user-stats-bar"},n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.L,{className:"stat-icon",style:{color:"#228B22"}}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("label",null,"Total Points"),n.a.createElement("span",null,(null===t||void 0===t?void 0:null===(a=t.total_points)||void 0===a?void 0:a.toLocaleString())||"0"))),n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.e,{className:"stat-icon",style:{color:"#228B22"}}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("label",null,"Current Streak"),n.a.createElement("span",null,(null===t||void 0===t?void 0:t.current_streak)||"0"))),n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.n,{className:"stat-icon",style:{color:"#228B22"}}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("label",null,"Balance"),n.a.createElement("span",null,parseFloat((null===t||void 0===t?void 0:t.balance)||0).toLocaleString()," FC"))),n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.h,{className:"stat-icon",style:{color:"#228B22"}}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("label",null,"Leagues Joined"),n.a.createElement("span",null,(null===t||void 0===t?void 0:t.leagues_joined)||"0")))),n.a.createElement("div",{className:"progress-container"},n.a.createElement("div",{className:"progress-info"},n.a.createElement("span",null,"Progress to Next Level"),n.a.createElement("span",null,50-(null===t||void 0===t?void 0:t.points)%50," points needed")),n.a.createElement("div",{className:"progress-bar"},n.a.createElement("div",{className:"progress-fill",style:{width:`${Math.min((null===t||void 0===t?void 0:t.points)%50/50*100,100)}%`}}))))},{stats:s}),y&&n.a.createElement((S.L,S.i,S.c,S.y,e=>{let{season:a}=e;return n.a.createElement("div",{className:"season-info"},n.a.createElement("div",{className:"season-header"},n.a.createElement(S.i,{className:"season-icon"}),n.a.createElement("div",{className:"season-title"},n.a.createElement("h3",null,a.name),n.a.createElement("div",{className:"season-dates"},n.a.createElement("span",null,"Started: ",new Date(a.start_date).toLocaleDateString()),n.a.createElement("span",null,"Ends: ",new Date(a.end_date).toLocaleDateString())))),a.days_remaining>0&&n.a.createElement("div",{className:"season-countdown"},n.a.createElement(S.G,{className:"countdown-icon"}),n.a.createElement("span",null,a.days_remaining," days remaining")))}),{season:y}),u&&n.a.createElement("div",{className:"error-message",style:{position:"fixed",top:"20px",right:"20px",background:"#ff4444",color:"white",padding:"15px 25px",borderRadius:"5px",zIndex:1e3,animation:"slideIn 0.3s ease-out"}},u),n.a.createElement("div",{className:"league-controls"},n.a.createElement("input",{type:"text",placeholder:"Search leagues...",value:_,onChange:e=>f(e.target.value),className:"league-search"}),n.a.createElement(m.b,{to:"/user/my-leagues",className:"view-my-leagues-btn"},n.a.createElement(S.T,null)," My Leagues")),n.a.createElement("div",{className:"leagues-list"},o?n.a.createElement("div",{className:"loading"},"Loading leagues..."):u?n.a.createElement("div",{className:"error-message"},u):0===R.length?n.a.createElement(()=>n.a.createElement("div",{className:"empty-leagues"},n.a.createElement(S.L,{className:"empty-state-icon"}),n.a.createElement("p",null,"No leagues available at the moment"),n.a.createElement("span",null,"Check back soon for new leagues!")),null):R.map(a=>n.a.createElement(B,{key:a.league_id,league:a,userStats:s,onJoin:D,navigate:e}))),n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>N(e=>Math.max(e-1,1)),disabled:1===h},n.a.createElement(S.i,{className:"rotate-180"})," Previous"),n.a.createElement("span",null,"Page ",h," of ",Math.max(q,1)),n.a.createElement("button",{onClick:()=>N(e=>Math.min(e+1,Math.max(q,1))),disabled:h===Math.max(q,1)},"Next ",n.a.createElement(S.i,null))),b&&n.a.createElement(e=>{let{onClose:a}=e;return n.a.createElement("div",{className:"welcome-guide"},n.a.createElement("div",{className:"guide-content"},n.a.createElement("h2",null,n.a.createElement(S.q,null)," Welcome to 247 League!"),n.a.createElement("div",{className:"guide-features"},n.a.createElement("div",{className:"feature-item"},n.a.createElement(S.L,{className:"feature-icon"}),n.a.createElement("div",{className:"feature-text"},n.a.createElement("h3",null,"Compete in Leagues"),n.a.createElement("p",null,"Join leagues that match your betting range and compete with others"))),n.a.createElement("div",{className:"feature-item"},n.a.createElement(S.i,{className:"feature-icon"}),n.a.createElement("div",{className:"feature-text"},n.a.createElement("h3",null,"Earn Points"),n.a.createElement("p",null,"Win: 3 points | Draw: 1 point | Climb the leaderboard"))),n.a.createElement("div",{className:"feature-item"},n.a.createElement(S.C,{className:"feature-icon"}),n.a.createElement("div",{className:"feature-text"},n.a.createElement("h3",null,"Win Rewards"),n.a.createElement("p",null,"Unlock badges and earn rewards as you progress"))),n.a.createElement("div",{className:"feature-item"},n.a.createElement(S.n,{className:"feature-icon"}),n.a.createElement("div",{className:"feature-text"},n.a.createElement("h3",null,"Secure Investment"),n.a.createElement("p",null,"Get your minimum deposit back after the season (90 days)")))),n.a.createElement("button",{className:"start-button",onClick:a},"Get Started ",n.a.createElement(S.G,null))))},{onClose:()=>v(!1)}),p&&n.a.createElement("div",{className:"success-message"},p),O&&j&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"modal-content"},n.a.createElement("h2",null,"Join ",j.name),n.a.createElement("p",null,"Your Balance: ",T.toLocaleString()," FC"),n.a.createElement("p",null,"Min Bet: ",j.min_bet_amount.toLocaleString()," FC"),n.a.createElement("p",null,"Max Bet: ",j.max_bet_amount.toLocaleString()," FC"),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Enter Amount (FC):"),n.a.createElement("input",{type:"number",value:C,onChange:e=>k(e.target.value),min:j.min_bet_amount,max:Math.min(j.max_bet_amount,T)})),u&&n.a.createElement("div",{className:"error"},u),n.a.createElement("div",{className:"modal-buttons"},n.a.createElement("button",{onClick:async()=>{try{E(""),g("");const r=parseFloat(C);if(isNaN(r))return void E("Please enter a valid amount");if(r<j.min_bet_amount||r>j.max_bet_amount)return void E(`Amount must be between ${j.min_bet_amount} and ${j.max_bet_amount} FC`);if(r>T)return void E(`Insufficient balance. Your current balance is ${T} FC`);const c=localStorage.getItem("userId");if(!c)return E("User ID not found. Please log in again."),void e("/login");const m=await i.post("/backend/handlers/join_league.php",{league_id:j.league_id,user_id:c,amount:r});200===m.data.status&&(t(e=>e.map(e=>e.league_id===j.league_id?{...e,...m.data.data.league,is_member:!0}:e)),g("Successfully joined the league!"),setTimeout(()=>{F(!1),k(""),L(null),g(""),$()},2e3))}catch(s){var a,l,n;console.error("Error joining league:",s);const t=(null===(a=s.response)||void 0===a?void 0:null===(l=a.data)||void 0===l?void 0:l.message)||"Failed to join league";E(t),setTimeout(()=>{F(!1),k(""),L(null),E("")},3e3),401===(null===(n=s.response)||void 0===n?void 0:n.status)&&e("/login")}},disabled:parseFloat(C)>T,className:"primary-button"},"Join League"),n.a.createElement("button",{onClick:()=>{F(!1),E(""),k("")},className:"secondary-button"},"Cancel")))))};t(12);var ra=()=>{const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(null),[r,c]=Object(l.useState)(!0);return Object(l.useEffect)(()=>{(async()=>{try{const[t,l]=await Promise.all([o.a.get("/backend/handlers/user_achievements.php"),o.a.get("/backend/handlers/user_stats.php")]);a(t.data.data),s(l.data.data)}catch(e){console.error("Error fetching achievements:",e)}finally{c(!1)}})()},[]),r?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"achievements-page"},n.a.createElement("header",{className:"achievements-header"},n.a.createElement("h1",null,"My Achievements"),t&&n.a.createElement("div",{className:"achievement-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Total Badges"),n.a.createElement("span",{className:"stat-value"},t.total_badges)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Total Points"),n.a.createElement("span",{className:"stat-value"},t.total_points)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"stat-label"},"Ranking"),n.a.createElement("span",{className:"stat-value"},t.global_rank)))),n.a.createElement("div",{className:"achievements-grid"},e.map(e=>n.a.createElement("div",{key:e.id,className:`achievement-card ${e.unlocked?"unlocked":"locked"}`},n.a.createElement("div",{className:"achievement-icon"},n.a.createElement("span",{className:"icon"},e.icon_class)),n.a.createElement("div",{className:"achievement-info"},n.a.createElement("h3",null,e.title),n.a.createElement("p",null,e.description),e.unlocked&&n.a.createElement("div",{className:"achievement-date"},"Unlocked: ",new Date(e.unlock_date).toLocaleDateString()),!e.unlocked&&e.progress&&n.a.createElement("div",{className:"achievement-progress"},n.a.createElement("div",{className:"progress-bar",style:{width:`${e.current_value/e.target_value*100}%`}}),n.a.createElement("span",{className:"progress-text"},e.current_value," / ",e.target_value))),e.reward&&n.a.createElement("div",{className:"achievement-reward"},n.a.createElement("span",{className:"reward-label"},"Reward:"),n.a.createElement("span",{className:"reward-value"},e.reward))))))};var ca=()=>{const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(null),[r,c]=Object(l.useState)(null),[m,i]=Object(l.useState)(!0);return Object(l.useEffect)(()=>{(async()=>{try{const t=await o.a.get("/backend/handlers/season_history.php");a(t.data.data),t.data.data.length>0&&s(t.data.data[0].season_id)}catch(e){console.error("Error fetching season history:",e)}finally{i(!1)}})()},[]),Object(l.useEffect)(()=>{(async()=>{if(t)try{const a=await o.a.get(`/backend/handlers/season_stats.php?season_id=${t}`);c(a.data.data)}catch(e){console.error("Error fetching season stats:",e)}})()},[t]),m?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"season-history"},n.a.createElement("header",null,n.a.createElement("h1",null,"Season History"),n.a.createElement("select",{value:t||"",onChange:e=>s(e.target.value),className:"season-selector"},e.map(e=>n.a.createElement("option",{key:e.season_id,value:e.season_id},e.season_name," (",new Date(e.start_date).toLocaleDateString()," -",new Date(e.end_date).toLocaleDateString(),")")))),r&&n.a.createElement("div",{className:"season-details"},n.a.createElement("div",{className:"season-summary"},n.a.createElement("div",{className:"summary-card"},n.a.createElement("h3",null,"Your Performance"),n.a.createElement("div",{className:"stats-grid"},n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"label"},"Final Rank"),n.a.createElement("span",{className:"value"},r.final_rank)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"label"},"Total Points"),n.a.createElement("span",{className:"value"},r.total_points)),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"label"},"Win Rate"),n.a.createElement("span",{className:"value"},r.win_rate,"%")),n.a.createElement("div",{className:"stat-item"},n.a.createElement("span",{className:"label"},"Rewards Earned"),n.a.createElement("span",{className:"value"},r.rewards_earned))))),n.a.createElement("div",{className:"season-highlights"},n.a.createElement("h3",null,"Season Highlights"),n.a.createElement("div",{className:"highlights-grid"},r.highlights.map((e,a)=>n.a.createElement("div",{key:a,className:"highlight-card"},n.a.createElement("span",{className:"highlight-date"},new Date(e.date).toLocaleDateString()),n.a.createElement("p",{className:"highlight-description"},e.description))))),n.a.createElement("div",{className:"season-matches"},n.a.createElement("h3",null,"Match History"),n.a.createElement("div",{className:"matches-list"},r.matches.map(e=>n.a.createElement("div",{key:e.match_id,className:"match-item"},n.a.createElement("div",{className:"match-date"},new Date(e.date).toLocaleDateString()),n.a.createElement("div",{className:"match-details"},n.a.createElement("span",{className:`result ${e.result.toLowerCase()}`},e.result),n.a.createElement("span",{className:"points"},"+",e.points," pts"))))))))};var ma=()=>{const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,c]=Object(l.useState)("");return Object(l.useEffect)(()=>{(async()=>{try{s(!0);const t=localStorage.getItem("userId");if(!t)return void c("User not found");const l=await i.get(`/backend/handlers/user_leagues.php?user_id=${t}`);console.log("User leagues response:",l.data),200===l.data.status?a(l.data.data):c(l.data.message||"Failed to load leagues")}catch(e){console.error("Error fetching user leagues:",e),c("Failed to load your leagues. Please try again.")}finally{s(!1)}})()},[]),t?n.a.createElement("div",{className:"loading-container"},n.a.createElement("div",{className:"loading-spinner"},"Loading your leagues...")):r?n.a.createElement("div",{className:"error-container"},n.a.createElement("div",{className:"error-message"},r),n.a.createElement(m.b,{to:"/user/leagues",className:"return-link"},"Return to Leagues")):0===e.length?n.a.createElement("div",{className:"empty-leagues-container"},n.a.createElement("div",{className:"no-leagues-message"},n.a.createElement(S.L,{className:"large-icon"}),n.a.createElement("h2",null,"You haven't joined any leagues yet!"),n.a.createElement("p",null,"Join a league to start competing and earning rewards."),n.a.createElement(m.b,{to:"/user/leagues",className:"join-league-button"},"Browse Leagues"))):n.a.createElement("div",{className:"my-leagues-container"},n.a.createElement("div",{className:"my-leagues-header"},n.a.createElement("h1",null,"My Leagues"),n.a.createElement("div",{className:"leagues-stats"},n.a.createElement("div",{className:"stat-item"},n.a.createElement(S.L,{className:"stat-icon"}),n.a.createElement("div",{className:"stat-info"},n.a.createElement("label",null,"Active Leagues"),n.a.createElement("span",null,e.length))))),n.a.createElement("div",{className:"leagues-grid"},e.map(e=>n.a.createElement("div",{key:e.league_id,className:"league-card my-league"},n.a.createElement("div",{className:"league-banner"},e.league_banner?n.a.createElement("img",{src:e.league_banner,alt:`${e.name} banner`}):n.a.createElement("div",{className:"default-banner"})),n.a.createElement("div",{className:"league-icon"},e.league_icon?n.a.createElement("img",{src:e.league_icon,alt:`${e.name} icon`}):n.a.createElement(S.L,null)),n.a.createElement("h3",null,e.name),n.a.createElement("p",null,e.description),n.a.createElement("div",{className:"league-stats"},n.a.createElement("div",{className:"stat-row"},n.a.createElement("span",null,n.a.createElement(S.T,null)," ",e.member_count," Members"),n.a.createElement("span",null,n.a.createElement(S.i,null)," Rank #",e.user_rank)),n.a.createElement("div",{className:"stat-row"},n.a.createElement("span",null,n.a.createElement(S.L,null)," ",e.points," Points"),n.a.createElement("span",null,n.a.createElement(S.v,null)," ",e.streak," Streak")),n.a.createElement("div",{className:"stat-row"},n.a.createElement("span",null,"Wins: ",e.wins),n.a.createElement("span",null,"Losses: ",e.losses))),n.a.createElement("div",{className:"betting-limits"},n.a.createElement("span",null,"Min: ",e.min_bet_formatted," FC"),n.a.createElement("span",null,"Max: ",e.max_bet_formatted," FC")),n.a.createElement("div",{className:"league-footer"},n.a.createElement("span",{className:"join-date"},n.a.createElement(S.f,null)," Joined: ",new Date(e.join_date).toLocaleDateString()),n.a.createElement(m.b,{to:`/user/leagues/${e.league_id}`,className:"view-league-btn"},"View League"))))))};t(76);var oa=function(){var e;const[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)(""),[m,o]=Object(l.useState)(""),[d,u]=Object(l.useState)(0),[E,p]=Object(l.useState)(""),[g,b]=Object(l.useState)(""),[v,h]=Object(l.useState)(!1),[N,_]=Object(l.useState)(!0),f=Object(c.q)(),y=localStorage.getItem("userId");Object(l.useEffect)(()=>{y?w():f("/login")},[f,y]);const w=async()=>{try{_(!0),p("");const[a,l]=await Promise.all([i.get("/backend/handlers/get_friends.php",{params:{user_id:y,status:"accepted"}}),i.get("/backend/handlers/user_data.php",{params:{id:y}})]);if(!a.data.success)throw new Error(a.data.message||"Failed to fetch friends list");if(t(a.data.friends),!l.data.success)throw new Error(l.data.message||"Failed to fetch user balance");u(l.data.user.balance)}catch(e){console.error("Error fetching data:",e),p(e.message||"Failed to load data. Please try again.")}finally{_(!1)}};return N?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"transfer-container"},n.a.createElement("h1",null,"Transfer FanCoins"),n.a.createElement("div",{className:"balance-display"},"Your Balance: ",d," FC"),E&&n.a.createElement("div",{className:"error-message"},E),g&&n.a.createElement("div",{className:"success-message"},g),n.a.createElement("div",{className:"transfer-form"},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Select Friend:"),n.a.createElement("select",{value:s,onChange:e=>r(e.target.value),required:!0},n.a.createElement("option",{value:""},"Select a friend"),a.map(e=>n.a.createElement("option",{key:e.user_id,value:e.user_id},e.username)))),n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Amount (FC):"),n.a.createElement("input",{type:"number",value:m,onChange:e=>o(e.target.value),min:"1",max:d,required:!0,placeholder:"Enter amount"})),n.a.createElement("button",{onClick:async()=>{s&&m?parseFloat(m)<=0?p("Amount must be greater than 0"):parseFloat(m)>d?p("Insufficient balance"):h(!0):p("Please fill in all fields")},className:"transfer-btn"},"Transfer FanCoins")),v&&n.a.createElement("div",{className:"confirmation-modal"},n.a.createElement("div",{className:"modal-content"},n.a.createElement("h2",null,"Confirm Transfer"),n.a.createElement("p",null,"Are you sure you want to transfer ",m," FC to ",null===(e=a.find(e=>e.user_id===parseInt(s)))||void 0===e?void 0:e.username,"?"),n.a.createElement("div",{className:"modal-actions"},n.a.createElement("button",{onClick:()=>h(!1),className:"cancel-btn"},"Cancel"),n.a.createElement("button",{onClick:async()=>{try{p("");const a=new FormData;a.append("from_user_id",y),a.append("to_user_id",s),a.append("amount",m);const t=await i.post("/backend/handlers/transfer.php",a);if(!t.data.success)throw new Error(t.data.message||"Transfer failed");b("Transfer completed successfully!"),o(""),r(""),await w()}catch(e){console.error("Error processing transfer:",e),p(e.message||"Failed to process transfer. Please try again.")}h(!1)},className:"confirm-btn"},"Confirm Transfer")))))};t(77);var ia=function(){const[e,a]=Object(l.useState)(""),[t,s]=Object(l.useState)(1),[r,m]=Object(l.useState)([]),[o,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(null),[p,g]=Object(l.useState)(""),[b,v]=Object(l.useState)(""),[h,N]=Object(l.useState)(86400),[_,f]=Object(l.useState)(!0),y=Object(c.q)(),w=localStorage.getItem("userId");Object(l.useEffect)(()=>{w?S():y("/login")},[y,w]),Object(l.useEffect)(()=>{if(3===t&&h>0){const e=setInterval(()=>{N(e=>e-1)},1e3);return()=>clearInterval(e)}},[t,h]);const S=async()=>{try{f(!0),g("");const a=await i.get("/backend/handlers/payment_methods.php");if(200!==a.data.status)throw new Error("Failed to fetch payment methods");m(a.data.data||[])}catch(e){console.error("Error fetching payment methods:",e),g(e.message||"Failed to load payment methods. Please try again.")}finally{f(!1)}};return _?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"credit-wallet-container"},n.a.createElement("h1",null,"Credit Wallet"),p&&n.a.createElement("div",{className:"error-message"},p),b&&n.a.createElement("div",{className:"success-message"},b),1===t&&n.a.createElement("div",{className:"step-container"},n.a.createElement("h2",null,"Enter Amount"),n.a.createElement("form",{onSubmit:a=>{a.preventDefault(),!e||e<=0?g("Please enter a valid amount"):(g(""),s(2))}},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Amount (\u20a6)"),n.a.createElement("input",{type:"number",value:e,onChange:e=>a(e.target.value),min:"1",required:!0,placeholder:"Enter amount"})),n.a.createElement("button",{type:"submit",className:"next-btn"},"Next"))),2===t&&n.a.createElement("div",{className:"step-container"},n.a.createElement("h2",null,"Select Payment Method"),n.a.createElement("div",{className:"payment-methods-grid"},r.map(e=>n.a.createElement("div",{key:e.id,className:"payment-method-card",onClick:()=>(e=>{d(e),s(3)})(e)},n.a.createElement("div",{className:"method-type"},"bank"===e.type?"\ud83c\udfe6":"\ud83d\udcb0"),n.a.createElement("div",{className:"method-details"},"bank"===e.type?n.a.createElement(n.a.Fragment,null,n.a.createElement("h3",null,e.bank_name),n.a.createElement("p",null,e.account_number),n.a.createElement("p",null,e.account_name)):n.a.createElement(n.a.Fragment,null,n.a.createElement("h3",null,e.wallet_name),n.a.createElement("p",null,e.wallet_address))))))),3===t&&n.a.createElement("div",{className:"step-container"},n.a.createElement("h2",null,"Make Payment"),n.a.createElement("div",{className:"timer"},"Time Remaining: ",(e=>{const a=Math.floor(e/3600),t=Math.floor(e%3600/60),l=e%60;return`${a.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`})(h)),n.a.createElement("div",{className:"payment-details"},n.a.createElement("h3",null,"Payment Details:"),n.a.createElement("p",null,"Amount: \u20a6",e),"bank"===o.type?n.a.createElement(n.a.Fragment,null,n.a.createElement("p",null,"Bank Name: ",o.bank_name),n.a.createElement("p",null,"Account Number: ",o.account_number),n.a.createElement("p",null,"Account Name: ",o.account_name)):n.a.createElement(n.a.Fragment,null,n.a.createElement("p",null,"Wallet Name: ",o.wallet_name),n.a.createElement("p",null,"Wallet Address: ",o.wallet_address))),n.a.createElement("form",{onSubmit:async a=>{if(a.preventDefault(),u)try{g("");const a=new FormData;a.append("amount",e),a.append("payment_method_id",o.id),a.append("proof_image",u),a.append("user_id",w);const l=await i.post("/backend/handlers/credit_request.php",a,{headers:{"Content-Type":"multipart/form-data"}});if(!l.data.success)throw new Error(l.data.message||"Failed to submit payment proof");v("Payment proof submitted successfully. Please wait for admin approval."),s(4)}catch(t){console.error("Error submitting payment proof:",t),g(t.message||"Failed to submit payment proof. Please try again.")}else g("Please upload proof of payment")}},n.a.createElement("div",{className:"form-group"},n.a.createElement("label",null,"Upload Payment Proof"),n.a.createElement("input",{type:"file",accept:"image/*",onChange:e=>{const a=e.target.files[0];if(a){if(a.size>5242880)return void g("File size should be less than 5MB");E(a),g("")}},required:!0}),n.a.createElement("small",null,"Maximum file size: 5MB")),n.a.createElement("button",{type:"submit",className:"submit-btn"},"Submit Payment Proof"))),4===t&&n.a.createElement("div",{className:"step-container success-container"},n.a.createElement("div",{className:"success-icon"},"\u2705"),n.a.createElement("h2",null,"Payment Submitted"),n.a.createElement("p",null,"Your payment proof has been submitted successfully."),n.a.createElement("p",null,"Please wait for admin approval. This usually takes 5-10 minutes."),n.a.createElement("p",null,"You will be notified once your payment is approved.")))};t(78);var da=function(){const[e,a]=Object(l.useState)([]),[t,s]=Object(l.useState)(!0),[r,m]=Object(l.useState)(""),[o,d]=Object(l.useState)(!1),[u,E]=Object(l.useState)(null),[p,g]=Object(l.useState)(1),[b]=Object(l.useState)(10),v=Object(c.q)(),h=localStorage.getItem("userId");Object(l.useEffect)(()=>{h?N():v("/login")},[v,h]);const N=async()=>{try{s(!0),m("");const t=await i.get("/backend/handlers/get_credit_requests.php",{params:{user_id:h}});if(!t.data.success)throw new Error(t.data.message||"Failed to fetch credit requests");a(t.data.requests||[])}catch(e){console.error("Error fetching credit requests:",e),m(e.message||"Failed to load credit requests. Please try again.")}finally{s(!1)}},_=p*b,f=_-b,y=e.slice(f,_),w=Math.ceil(e.length/b),S=e=>g(e),C=e=>{switch(e){case"approved":return"status-badge success";case"rejected":return"status-badge danger";case"expired":return"status-badge warning";default:return"status-badge pending"}},k=e=>new Date(e).toLocaleString();return t?n.a.createElement("div",{className:"loading"},"Loading..."):n.a.createElement("div",{className:"credit-history-container"},n.a.createElement("h1",null,"Credit Request History"),r&&n.a.createElement("div",{className:"error-message"},r),e.length>0?n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"total-amount"},n.a.createElement("h2",null,"Total Approved Credits: ",n.a.createElement("span",{className:"amount"},"\u20a6",e.filter(e=>"approved"===e.status).reduce((e,a)=>e+parseFloat(a.amount),0).toLocaleString()))),n.a.createElement("div",{className:"credit-requests-table"},n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",null,"#"),n.a.createElement("th",null,"Date"),n.a.createElement("th",null,"Amount"),n.a.createElement("th",null,"Payment Method"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Expires"),n.a.createElement("th",null,"Proof"))),n.a.createElement("tbody",null,y.map((e,a)=>n.a.createElement("tr",{key:e.request_id},n.a.createElement("td",null,f+a+1),n.a.createElement("td",null,k(e.created_at)),n.a.createElement("td",null,"\u20a6",parseFloat(e.amount).toLocaleString()),n.a.createElement("td",null,"bank"===e.payment_method_type?n.a.createElement(n.a.Fragment,null,e.bank_name," - ",e.account_number):n.a.createElement(n.a.Fragment,null,e.wallet_name," - ",e.wallet_address)),n.a.createElement("td",null,n.a.createElement("span",{className:C(e.status)},e.status.charAt(0).toUpperCase()+e.status.slice(1))),n.a.createElement("td",null,e.expires_at?k(e.expires_at):"N/A"),n.a.createElement("td",null,n.a.createElement("button",{className:"view-proof-btn",onClick:()=>(e=>{E({url:`/backend/handlers/get_proof_image.php?request_id=${e.request_id}&user_id=${h}`,amount:e.amount,date:e.created_at,status:e.status}),d(!0)})(e)},"View Proof"))))))),w>1&&n.a.createElement(()=>{const e=[];for(let a=1;a<=w;a++)e.push(a);return n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>S(p-1),disabled:1===p,className:"pagination-btn"},"Previous"),n.a.createElement("span",{className:"page-info"},"Page ",p," of ",w),n.a.createElement("button",{onClick:()=>S(p+1),disabled:p===w,className:"pagination-btn"},"Next"))},null)):n.a.createElement("div",{className:"no-requests"},n.a.createElement("p",null,"No credit requests found."),n.a.createElement("button",{onClick:()=>v("/user/wallet"),className:"new-request-btn"},"Make a New Request")),o&&n.a.createElement(e=>{let{proof:a,onClose:t}=e;return a?n.a.createElement("div",{className:"modal-overlay",onClick:t},n.a.createElement("div",{className:"proof-modal",onClick:e=>e.stopPropagation()},n.a.createElement("button",{className:"close-modal",onClick:t},"\xd7"),n.a.createElement("div",{className:"proof-details"},n.a.createElement("h3",null,"Payment Proof"),n.a.createElement("p",null,"Amount: \u20a6",parseFloat(a.amount).toLocaleString()),n.a.createElement("p",null,"Date: ",k(a.date)),n.a.createElement("p",null,"Status: ",n.a.createElement("span",{className:C(a.status)},a.status.charAt(0).toUpperCase()+a.status.slice(1)))),n.a.createElement("div",{className:"proof-image-container"},n.a.createElement("img",{src:a.url,alt:"Payment Proof"})))):null},{proof:u,onClose:()=>{d(!1),E(null)}}))};t(79);const ua="/backend",Ea=20;var pa=function(){const e=Object(c.q)(),[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)([]),[i,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(!0),[p,g]=Object(l.useState)("all"),[b,v]=Object(l.useState)(""),[h,N]=Object(l.useState)(1);Object(l.useEffect)(()=>{localStorage.getItem("userId")?(async()=>{try{d(null),await Promise.all([_(),f()])}catch(e){console.error("Page initialization error:",e),d("Failed to load challenges. Please try again later.")}finally{E(!1)}})():e("/user/login")},[e]);const _=async()=>{try{console.log("Fetching challenges...");const e=await o.a.get(`${ua}/handlers/challenge_management.php`);if(console.log("Challenges response:",e.data),!e.data.success)throw console.error("Failed to fetch challenges:",e.data.message),new Error(e.data.message||"Failed to fetch challenges");{const a=(e.data.challenges||[]).sort((e,a)=>{const t={Open:1,Closed:2,Settled:3,Expired:4};return t[e.status]!==t[a.status]?t[e.status]-t[a.status]:new Date(a.match_date)-new Date(e.match_date)}).map(e=>({...e,end_time:new Date(e.end_time)}));t(a)}}catch(i){throw console.error("Error fetching challenges:",i),i}},f=async()=>{try{console.log("Fetching teams...");const a=await o.a.get(`${ua}/handlers/team_management.php`);if(console.log("Teams response:",a.data),200!==a.data.status)throw console.error("Failed to fetch teams:",a.data.message),new Error(a.data.message||"Failed to fetch teams");r(a.data.data||[])}catch(e){throw console.error("Error fetching teams:",e),e}},y=e=>{const a=s.find(a=>a.name===e);return a?`${ua}/${a.logo}`:""},w=e=>{let{days:a,hours:t,minutes:l,seconds:s,completed:r}=e;if(r)return n.a.createElement("span",{className:"challenges-countdown-compact"},"Expired");let c="";return a>0&&(c+=`${a}d `),t>0&&(c+=`${t}h `),l>0&&(c+=`${l}m `),c+=`${s}s`,n.a.createElement("span",{className:"challenges-countdown-compact"},c)},S=a.filter(e=>{if(b){const a=b.toLowerCase();return e.team_a.toLowerCase().includes(a)||e.team_b.toLowerCase().includes(a)}return!0}).filter(e=>{const a=new Date,t=(new Date(e.match_date),new Date(e.end_time));switch(p){case"closed":return"Closed"===e.status;case"expired":return a>t&&"Settled"!==e.status;case"settled":return"Settled"===e.status;default:return!0}}).sort((e,a)=>{new Date;const t=new Date(e.match_date),l=new Date(a.match_date),n=new Date(e.end_time),s=new Date(a.end_time);if("closed"===p)return l-t;if("expired"===p)return s-n;if("settled"===p)return l-t;const r={Open:1,Closed:2,Expired:3,Settled:4};return r[e.status]!==r[a.status]?r[e.status]-r[a.status]:"Expired"===e.status?s-n:t-l}),C=S.length,k=Math.ceil(C/Ea),O=(h-1)*Ea,F=S.slice(O,O+Ea),j=e=>{N(e),window.scrollTo(0,0)};return i?n.a.createElement("div",{className:"challenges-page"},n.a.createElement("div",{className:"error-message"},i)):u?n.a.createElement("div",{className:"challenges-page"},n.a.createElement("div",{className:"loading"},"Loading challenges...")):n.a.createElement("div",{className:"challenges-page"},n.a.createElement("div",{className:"challenges-header"},n.a.createElement("h1",null,"All Challenges"),n.a.createElement("div",{className:"challenges-controls"},n.a.createElement("div",{className:"search-box"},n.a.createElement("input",{type:"text",placeholder:"Search teams...",value:b,onChange:e=>v(e.target.value)})),n.a.createElement("div",{className:"filter-buttons"},n.a.createElement("button",{className:`filter-button ${"all"===p?"active":""}`,onClick:()=>g("all")},"All"),n.a.createElement("button",{className:`filter-button ${"closed"===p?"active":""}`,onClick:()=>g("closed")},"Closed"),n.a.createElement("button",{className:`filter-button ${"expired"===p?"active":""}`,onClick:()=>g("expired")},"Expired"),n.a.createElement("button",{className:`filter-button ${"settled"===p?"active":""}`,onClick:()=>g("settled")},"Settled")))),n.a.createElement("div",{className:"challenges-list"},0===S.length?n.a.createElement("div",{className:"no-data"},"No challenges found"):n.a.createElement(n.a.Fragment,null,n.a.createElement("div",{className:"table-container"},n.a.createElement("table",null,n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",{className:"number-column"},"#"),n.a.createElement("th",null,"Teams"),n.a.createElement("th",null,"Odds"),n.a.createElement("th",null,"Goals"),n.a.createElement("th",null,"Date"),n.a.createElement("th",null,"Time Left"),n.a.createElement("th",null,"Status"),n.a.createElement("th",null,"Action"))),n.a.createElement("tbody",null,F.map((e,a)=>n.a.createElement("tr",{key:e.challenge_id},n.a.createElement("td",{className:"number-column"},O+a+1),n.a.createElement("td",null,n.a.createElement("div",{className:"challenges-teams-compact"},n.a.createElement("div",{className:"challenges-team-info"},n.a.createElement("img",{src:y(e.team_a),alt:e.team_a,className:"challenges-team-logo-small",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenges-team-name-compact"},e.team_a)),n.a.createElement("span",{className:"challenges-vs-small"},"vs"),n.a.createElement("div",{className:"challenges-team-info"},n.a.createElement("img",{src:y(e.team_b),alt:e.team_b,className:"challenges-team-logo-small",onError:e=>{e.target.src="/default-team-logo.png"}}),n.a.createElement("span",{className:"challenges-team-name-compact"},e.team_b)))),n.a.createElement("td",{className:"challenges-odds-compact"},e.odds_team_a," - ",e.odds_team_b),n.a.createElement("td",{className:"challenges-goals-compact"},"+",e.team_a_goal_advantage," / +",e.team_b_goal_advantage),n.a.createElement("td",{className:"challenges-date-compact"},new Date(e.match_date).toLocaleDateString()),n.a.createElement("td",null,n.a.createElement(T.a,{date:new Date(e.end_time),renderer:w,onComplete:()=>{}})),n.a.createElement("td",null,n.a.createElement("span",{className:`status-badge ${e.status.toLowerCase()}`},e.status)),n.a.createElement("td",null,"Open"===e.status&&n.a.createElement(m.b,{to:`/user/join-challenge/${e.challenge_id}`,className:"challenges-place-bet-button-small"},"Bet"))))))),k>1&&(()=>{const e=[];let a=Math.max(1,h-Math.floor(2.5)),t=Math.min(k,a+5-1);t-a+1<5&&(a=Math.max(1,t-5+1)),a>1&&(e.push(n.a.createElement("button",{key:"first",onClick:()=>j(1),className:"pagination-button"},"1")),a>2&&e.push(n.a.createElement("span",{key:"ellipsis1",className:"pagination-ellipsis"},"...")));for(let l=a;l<=t;l++)e.push(n.a.createElement("button",{key:l,onClick:()=>j(l),className:`pagination-button ${h===l?"active":""}`},l));return t<k&&(t<k-1&&e.push(n.a.createElement("span",{key:"ellipsis2",className:"pagination-ellipsis"},"...")),e.push(n.a.createElement("button",{key:"last",onClick:()=>j(k),className:"pagination-button"},k))),n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{onClick:()=>j(h-1),disabled:1===h,className:"pagination-button nav"},"\u2190"),e,n.a.createElement("button",{onClick:()=>j(h+1),disabled:h===k,className:"pagination-button nav"},"\u2192"))})())))};t(80);const ga="/backend",ba=100;var va=function(){const e=Object(c.q)(),[a,t]=Object(l.useState)([]),[s,r]=Object(l.useState)([]),[i,d]=Object(l.useState)(null),[u,E]=Object(l.useState)(!0),[p,g]=Object(l.useState)("all"),[b,v]=Object(l.useState)(""),[h,N]=Object(l.useState)(1),[_,f]=Object(l.useState)(!1),[y,w]=Object(l.useState)(null);Object(l.useEffect)(()=>{localStorage.getItem("userId")?(async()=>{try{d(null),await Promise.all([O(),C()])}catch(e){console.error("Page initialization error:",e),d("Failed to load bets. Please try again later.")}finally{E(!1)}})():e("/user/login")},[e]);const C=async()=>{try{console.log("Fetching teams...");const a=await o.a.get(`${ga}/handlers/team_management.php`);if(console.log("Teams response:",a.data),200!==a.data.status)throw console.error("Failed to fetch teams:",a.data.message),new Error(a.data.message||"Failed to fetch teams");r(a.data.data||[])}catch(e){throw console.error("Error fetching teams:",e),e}},k=e=>{const a=s.find(a=>a.name===e);return a?`${ga}/${a.logo}`:""},O=async()=>{try{console.log("Fetching user bets...");const e=localStorage.getItem("userId"),a=await o.a.get(`${ga}/handlers/get_all_user_bets.php?userId=${e}`);if(console.log("User bets response:",a.data),!a.data.success)throw console.error("Failed to fetch user bets:",a.data.message),new Error(a.data.message||"Failed to fetch user bets");t(a.data.bets||[])}catch(i){throw console.error("Error fetching user bets:",i),i}},F=e=>e?new Date(e).toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit",hour12:!0}):"N/A",j=a.filter(e=>{if(b){var a,t;const l=b.toLowerCase();return(null===(a=e.team_a)||void 0===a?void 0:a.toLowerCase().includes(l))||(null===(t=e.team_b)||void 0===t?void 0:t.toLowerCase().includes(l))}return!0}).filter(e=>{switch(p){case"won":return"won"===e.bet_status;case"lost":return"lost"===e.bet_status;case"pending":return"pending"===e.bet_status;default:return!0}}).sort((e,a)=>new Date(a.created_at)-new Date(e.created_at)),L=(h-1)*ba,T=j.slice(L,L+ba),x=Math.ceil(j.length/ba),$=e=>{N(e),window.scrollTo(0,0)};return u?n.a.createElement("div",{className:"loading"},"Loading..."):i?n.a.createElement("div",{className:"error-message"},i):n.a.createElement("div",{className:"recent-bets-page"},n.a.createElement("div",{className:"recent-bets-header"},n.a.createElement("h1",null,"Recent Bets"),n.a.createElement("div",{className:"recent-bets-controls"},n.a.createElement("div",{className:"search-box"},n.a.createElement("input",{type:"text",placeholder:"Search teams...",value:b,onChange:e=>v(e.target.value)})),n.a.createElement("div",{className:"filter-buttons"},n.a.createElement("button",{className:`filter-button ${"all"===p?"active":""}`,onClick:()=>g("all")},"All"),n.a.createElement("button",{className:`filter-button ${"won"===p?"active":""}`,onClick:()=>g("won")},"Won"),n.a.createElement("button",{className:`filter-button ${"lost"===p?"active":""}`,onClick:()=>g("lost")},"Lost"),n.a.createElement("button",{className:`filter-button ${"pending"===p?"active":""}`,onClick:()=>g("pending")},"Pending")))),n.a.createElement("div",{className:"table-container"},n.a.createElement("table",{className:"recent-bets-table"},n.a.createElement("thead",null,n.a.createElement("tr",null,n.a.createElement("th",{className:"number-column"},"#"),n.a.createElement("th",null,"Teams"),n.a.createElement("th",null,"Details"),n.a.createElement("th",null,"Returns"),n.a.createElement("th",{className:"actions-column"},"Status & Actions"))),n.a.createElement("tbody",null,0===T.length?n.a.createElement("tr",null,n.a.createElement("td",{colSpan:5,className:"no-data"},"No bets found")):T.map((e,a)=>{var t,l;return n.a.createElement("tr",{key:e.bet_id},n.a.createElement("td",{className:"number-column"},L+a+1),n.a.createElement("td",{className:"teams-cell"},n.a.createElement("div",{className:"match-date"},F(e.created_at)),n.a.createElement("div",{className:"recent-bets-teams-row"},n.a.createElement("div",{className:"recent-bets-team"},n.a.createElement("div",{className:"recent-bets-team-details"},n.a.createElement("img",{src:k(e.team_a),alt:e.team_a,className:"recent-bets-team-logo"}),n.a.createElement("span",{className:"recent-bets-team-name"},e.team_a)),n.a.createElement("div",{className:"recent-bets-user-info"},n.a.createElement(m.b,{to:`/user/profile/${e.user1_id}`,className:"bet-username"},e.user1_name),n.a.createElement("span",{className:"bet-amount"},e.amount_user1))),n.a.createElement("span",{className:"recent-bets-vs"},"VS"),n.a.createElement("div",{className:"recent-bets-team"},n.a.createElement("div",{className:"recent-bets-team-details"},n.a.createElement("img",{src:k(e.team_b),alt:e.team_b,className:"recent-bets-team-logo"}),n.a.createElement("span",{className:"recent-bets-team-name"},e.team_b)),n.a.createElement("div",{className:"recent-bets-user-info"},e.user2_name?n.a.createElement(m.b,{to:`/user/profile/${e.user2_id}`,className:"bet-username"},e.user2_name):n.a.createElement("span",{className:"bet-username"},"Waiting for opponent"),n.a.createElement("span",{className:"bet-amount"},e.amount_user2||"-"))))),n.a.createElement("td",{className:"bet-details-compact"},n.a.createElement("div",{className:"bet-details-stack"},n.a.createElement("span",{className:"reference-code clickable"},(null===(t=e.unique_code)||void 0===t?void 0:t.toUpperCase())||`${e.bet_id}DNRBKCC`),n.a.createElement("span",{className:"bet-choice"},"Choice: ",(e=>{const a=e.bet_choice_user1;return"team_a_win"===a?e.team_a:"team_b_win"===a?e.team_b:"Draw"})(e)))),n.a.createElement("td",{className:"returns-compact"},n.a.createElement("div",null,"Win: ",e.potential_return_win_user1),n.a.createElement("div",null,"Draw: ",e.potential_return_draw_user1),n.a.createElement("div",null,"Loss: ",e.potential_return_loss_user1)),n.a.createElement("td",{className:"actions-column"},n.a.createElement("div",{className:"status-actions"},n.a.createElement("span",{className:`status-badge ${null===(l=e.bet_status)||void 0===l?void 0:l.toLowerCase()}`},e.bet_status),n.a.createElement("button",{className:"view-details-btn",onClick:()=>(e=>{w(e),f(!0)})(e),title:"View Details"},n.a.createElement(S.u,null)))))})))),x>1&&n.a.createElement("div",{className:"pagination"},n.a.createElement("button",{className:"pagination-button nav",onClick:()=>$(1),disabled:1===h},"<<"),n.a.createElement("button",{className:"pagination-button nav",onClick:()=>$(h-1),disabled:1===h},"<"),Array.from({length:x},(e,a)=>a+1).filter(e=>{const a=Math.abs(e-h);return 0===a||1===a||1===e||e===x}).map((e,a,t)=>a>0&&t[a-1]!==e-1?[n.a.createElement("span",{key:`ellipsis-${e}`,className:"pagination-ellipsis"},"..."),n.a.createElement("button",{key:e,className:`pagination-button ${h===e?"active":""}`,onClick:()=>$(e)},e)]:n.a.createElement("button",{key:e,className:`pagination-button ${h===e?"active":""}`,onClick:()=>$(e)},e)),n.a.createElement("button",{className:"pagination-button nav",onClick:()=>$(h+1),disabled:h===x},">"),n.a.createElement("button",{className:"pagination-button nav",onClick:()=>$(x),disabled:h===x},">>")),_&&y&&n.a.createElement("div",{className:"modal-overlay"},n.a.createElement("div",{className:"modal-content"},n.a.createElement("div",{className:"modal-header"},n.a.createElement("h3",null,"Bet Details"),n.a.createElement("button",{className:"close-btn",onClick:()=>f(!1)},"\xd7")),n.a.createElement("div",{className:"modal-body"},n.a.createElement("div",{className:"bet-section"},n.a.createElement("div",{className:"section-title"},"MATCH DETAILS"),n.a.createElement("div",{className:"match-grid"},n.a.createElement("div",{className:"team-card"},n.a.createElement("div",{className:"team-header"},n.a.createElement("img",{src:k(y.team_a),alt:y.team_a,className:"team-logo"}),n.a.createElement("span",{className:"team-name"},y.team_a),"team_a_win"===y.bet_choice_user1&&n.a.createElement("span",{className:"pick-badge"},"Your Pick")),n.a.createElement("div",{className:"team-odds"},n.a.createElement("span",{className:"odds-label"},"Win Odds"),n.a.createElement("span",{className:"odds-value"},y.odds_team_a,"x")),n.a.createElement("div",{className:"user-bet-info"},n.a.createElement("span",{className:"username"},y.user1_name),n.a.createElement("span",{className:"bet-amount"},y.amount_user1))),n.a.createElement("div",{className:"vs-container"},n.a.createElement("div",{className:"vs"},"VS"),n.a.createElement("div",{className:"draw-odds"},n.a.createElement("span",{className:"odds-label"},"Draw"),n.a.createElement("span",{className:"odds-value"},"2.0x")),n.a.createElement("div",{className:"match-date"},F(y.created_at))),n.a.createElement("div",{className:"team-card"},n.a.createElement("div",{className:"team-header"},n.a.createElement("img",{src:k(y.team_b),alt:y.team_b,className:"team-logo"}),n.a.createElement("span",{className:"team-name"},y.team_b),"team_b_win"===y.bet_choice_user1&&n.a.createElement("span",{className:"pick-badge"},"Your Pick")),n.a.createElement("div",{className:"team-odds"},n.a.createElement("span",{className:"odds-label"},"Win Odds"),n.a.createElement("span",{className:"odds-value"},y.odds_team_b,"x")),n.a.createElement("div",{className:"user-bet-info"},n.a.createElement("span",{className:"username"},y.user2_name||"Waiting for opponent"),n.a.createElement("span",{className:"bet-amount"},y.amount_user2||"-")))),n.a.createElement("div",{className:"section-title"},"FINANCIAL DETAILS"),n.a.createElement("div",{className:"financial-grid"},n.a.createElement("div",{className:"financial-item"},n.a.createElement("span",{className:"financial-label"},"Your Bet"),n.a.createElement("span",{className:"financial-value"},y.amount_user1)),n.a.createElement("div",{className:"financial-item"},n.a.createElement("span",{className:"financial-label"},"Potential Win"),n.a.createElement("span",{className:"financial-value win"},"+",y.potential_return_win_user1)),n.a.createElement("div",{className:"financial-item"},n.a.createElement("span",{className:"financial-label"},"Potential Loss"),n.a.createElement("span",{className:"financial-value loss"},"-",y.potential_return_loss_user1)),n.a.createElement("div",{className:"financial-item"},n.a.createElement("span",{className:"financial-label"},"Draw Return"),n.a.createElement("span",{className:"financial-value draw"},y.potential_return_draw_user1))))))))};const ha=e=>{let{children:a}=e;if(!localStorage.getItem("userId")){const e=window.location.pathname;return"/login"!==e&&sessionStorage.setItem("redirectAfterLogin",e),n.a.createElement(c.a,{to:"/login",replace:!0})}return a};var Na=function(){return n.a.createElement(E,null,n.a.createElement(w,null,n.a.createElement("div",{className:"App"},n.a.createElement(m.a,null,n.a.createElement(c.e,null,n.a.createElement(c.c,{path:"/",element:n.a.createElement(M,null)}),n.a.createElement(c.c,{path:"/login",element:n.a.createElement(H,null)}),n.a.createElement(c.c,{path:"/register",element:n.a.createElement(z,null)}),n.a.createElement(c.c,{path:"/admin/login",element:n.a.createElement(U,null)}),n.a.createElement(c.c,{path:"/admin",element:n.a.createElement(F,null)},n.a.createElement(c.c,{index:!0,element:n.a.createElement(G,null)}),n.a.createElement(c.c,{path:"dashboard",element:n.a.createElement(G,null)}),n.a.createElement(c.c,{path:"challenge-system",element:n.a.createElement(K,null)}),n.a.createElement(c.c,{path:"challenge-management",element:n.a.createElement(Ne,null)}),n.a.createElement(c.c,{path:"credit-challenge",element:n.a.createElement(Se,null)}),n.a.createElement(c.c,{path:"team-management",element:n.a.createElement(Ee,null)}),n.a.createElement(c.c,{path:"league-management",element:n.a.createElement(Fe,null)}),n.a.createElement(c.c,{path:"league-management/create",element:n.a.createElement(xe,null)}),n.a.createElement(c.c,{path:"league-seasons",element:n.a.createElement(Te,null)}),n.a.createElement(c.c,{path:"league-divisions",element:n.a.createElement(Fe,null)}),n.a.createElement(c.c,{path:"league-rewards",element:n.a.createElement(Fe,null)}),n.a.createElement(c.c,{path:"league-management/:leagueId/seasons",element:n.a.createElement(Te,null)}),n.a.createElement(c.c,{path:"league-users",element:n.a.createElement(De,null)}),n.a.createElement(c.c,{path:"users",element:n.a.createElement(X,null)}),n.a.createElement(c.c,{path:"add-user",element:n.a.createElement(re,null)}),n.a.createElement(c.c,{path:"credit-user",element:n.a.createElement(ie,null)}),n.a.createElement(c.c,{path:"debit-user",element:n.a.createElement(de,null)}),n.a.createElement(c.c,{path:"payment-methods",element:n.a.createElement(me,null)}),n.a.createElement(c.c,{path:"bets",element:n.a.createElement(ee,null)}),n.a.createElement(c.c,{path:"transactions",element:n.a.createElement(ae,null)}),n.a.createElement(c.c,{path:"leaderboard",element:n.a.createElement(te,null)}),n.a.createElement(c.c,{path:"settings",element:n.a.createElement(le,null)}),n.a.createElement(c.c,{path:"reports",element:n.a.createElement(ne,null)})),n.a.createElement(c.c,{path:"/user",element:n.a.createElement(ha,null,n.a.createElement(j,null))},n.a.createElement(c.c,{index:!0,element:n.a.createElement(Ae,null)}),n.a.createElement(c.c,{path:"dashboard",element:n.a.createElement(Ae,null)}),n.a.createElement(c.c,{path:"bets",element:n.a.createElement(Me,null)}),n.a.createElement(c.c,{path:"bets/outgoing",element:n.a.createElement(Me,null)}),n.a.createElement(c.c,{path:"bets/incoming",element:n.a.createElement(We,null)}),n.a.createElement(c.c,{path:"bets/accepted",element:n.a.createElement(Ye,null)}),n.a.createElement(c.c,{path:"payment-history",element:n.a.createElement(Ge,null)}),n.a.createElement(c.c,{path:"leaderboard",element:n.a.createElement(Qe,null)}),n.a.createElement(c.c,{path:"profile",element:n.a.createElement(Ve,null)}),n.a.createElement(c.c,{path:"profile/:username",element:n.a.createElement(Ve,null)}),n.a.createElement(c.c,{path:"change-password",element:n.a.createElement(Xe,null)}),n.a.createElement(c.c,{path:"deposit",element:n.a.createElement(Ze,null)}),n.a.createElement(c.c,{path:"withdraw",element:n.a.createElement(ea,null)}),n.a.createElement(c.c,{path:"friends",element:n.a.createElement(ta,null)}),n.a.createElement(c.c,{path:"friend-requests",element:n.a.createElement(na,null)}),n.a.createElement(c.c,{path:"join-challenge/:challengeId",element:n.a.createElement(Re,null)}),n.a.createElement(c.c,{path:"join-challenge2/:challengeId/:betId/:uniqueCode/:user1Id",element:n.a.createElement(qe,null)}),n.a.createElement(c.c,{path:"messages",element:n.a.createElement(L,null)}),n.a.createElement(c.c,{path:"challenges",element:n.a.createElement(pa,null)}),n.a.createElement(c.c,{path:"recent-bets",element:n.a.createElement(va,null)}),n.a.createElement(c.c,{path:"leagues",element:n.a.createElement(sa,null)}),n.a.createElement(c.c,{path:"my-leagues",element:n.a.createElement(ma,null)}),n.a.createElement(c.c,{path:"leagues/:leagueId",element:n.a.createElement($e,null)}),n.a.createElement(c.c,{path:"leagues/achievements",element:n.a.createElement(ra,null)}),n.a.createElement(c.c,{path:"leagues/seasons",element:n.a.createElement(ca,null)}),n.a.createElement(c.c,{path:"league",element:n.a.createElement(c.a,{to:"/user/leagues",replace:!0})}),n.a.createElement(c.c,{path:"league/:leagueId/selection",element:n.a.createElement(c.a,{to:"/user/leagues/:leagueId",replace:!0})}),n.a.createElement(c.c,{path:"league/:leagueId/leaderboard",element:n.a.createElement(c.a,{to:"/user/leagues/:leagueId/leaderboard",replace:!0})}),n.a.createElement(c.c,{path:"achievements",element:n.a.createElement(c.a,{to:"/user/leagues/achievements",replace:!0})}),n.a.createElement(c.c,{path:"season-history",element:n.a.createElement(c.a,{to:"/user/leagues/seasons",replace:!0})}),n.a.createElement(c.c,{path:"transfer",element:n.a.createElement(oa,null)}),n.a.createElement(c.c,{path:"wallet",element:n.a.createElement(ia,null)}),n.a.createElement(c.c,{path:"credit-history",element:n.a.createElement(da,null)})),n.a.createElement(c.c,{path:"*",element:localStorage.getItem("userId")&&localStorage.getItem("userToken")?n.a.createElement(c.a,{to:"/user/dashboard",replace:!0}):n.a.createElement(c.a,{to:"/login",replace:!0})}))))))};var _a=e=>{e&&e instanceof Function&&t.e(3).then(t.bind(null,83)).then(a=>{let{getCLS:t,getFID:l,getFCP:n,getLCP:s,getTTFB:r}=a;t(e),l(e),n(e),s(e),r(e)})};document.addEventListener("DOMContentLoaded",()=>{if(!document.getElementById("root")){console.error("Root element not found. Creating root element...");const e=document.createElement("div");e.id="root",document.body.appendChild(e)}try{r.a.createRoot(document.getElementById("root")).render(n.a.createElement(n.a.StrictMode,null,n.a.createElement(Na,null)))}catch(e){console.error("Error rendering React app:",e);const a=document.getElementById("root");a&&(a.innerHTML='\n        <div style="padding: 20px; text-align: center;">\n          <h1>Something went wrong</h1>\n          <p>Please try refreshing the page. If the problem persists, contact support.</p>\n          <button onclick="window.location.reload()">Refresh Page</button>\n        </div>\n      ')}}),_a()}],[[22,1,2]]]);
//# sourceMappingURL=main.f385f739.chunk.js.map