{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction CurrencyManagement() {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stats\n  const [stats, setStats] = useState({\n    totalCurrencies: 0,\n    activeCurrencies: 0,\n    inactiveCurrencies: 0,\n    lastUpdated: null\n  });\n\n  // Modal states\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [editingRate, setEditingRate] = useState(null);\n  const [editingCurrency, setEditingCurrency] = useState(null);\n\n  // Form states\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n\n  // Load currencies and exchange rates\n  const loadData = useCallback(async () => {\n    try {\n      const [currenciesResponse, ratesResponse] = await Promise.all([execute(() => currencyService.getCurrencies(false)),\n      // Get all currencies including inactive\n      execute(() => currencyService.getExchangeRates())]);\n      if (currenciesResponse.success) {\n        setCurrencies(currenciesResponse.data.currencies || []);\n      }\n      if (ratesResponse.success) {\n        setExchangeRates(ratesResponse.data.exchange_rates || []);\n      }\n    } catch (error) {\n      console.error('Failed to load currency data:', error);\n    }\n  }, [execute]);\n\n  // Update exchange rate\n  const handleUpdateRate = useCallback(async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      alert('Please enter a valid exchange rate');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        alert('Exchange rate updated successfully!');\n      } else {\n        alert(response.message || 'Failed to update exchange rate');\n      }\n    } catch (error) {\n      alert('Error updating exchange rate: ' + error.message);\n    }\n  }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n  // Toggle currency active status\n  const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('toggle', {\n        currency_id: currencyId\n      }));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n      } else {\n        alert(response.message || 'Failed to toggle currency status');\n      }\n    } catch (error) {\n      alert('Error toggling currency: ' + error.message);\n    }\n  }, [execute, loadData, refreshCurrencyData]);\n\n  // Add new currency\n  const handleAddCurrency = useCallback(async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('create', newCurrency));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        alert('Currency added successfully!');\n      } else {\n        alert(response.message || 'Failed to add currency');\n      }\n    } catch (error) {\n      alert('Error adding currency: ' + error.message);\n    }\n  }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n  // Get exchange rate for currency\n  const getExchangeRate = useCallback(currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  }, [exchangeRates]);\n\n  // Format date\n  const formatDate = useCallback(dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }, []);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"currency-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Currency Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage supported currencies and exchange rates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddCurrency(true),\n        children: \"Add New Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\u26A0\\uFE0F \", error.message || 'An error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-secondary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 17\n    }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"currencies-grid\",\n      children: currencies.map(currency => {\n        const rate = getExchangeRate(currency.id);\n        const isEditing = editingRate === currency.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `currency-card ${!currency.is_active ? 'inactive' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: currency.currency_code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: currency.currency_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"currency-symbol\",\n                children: currency.currency_symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 198,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${currency.is_active ? 'active' : 'inactive'}`,\n                children: currency.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exchange-rate-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Exchange Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 37\n            }, this), rate ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rate-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-rate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rate-value\",\n                  children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Updated: \", formatDate(rate.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 45\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rate-edit-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.0001\",\n                  value: newRate,\n                  onChange: e => setNewRate(e.target.value),\n                  placeholder: \"New rate\",\n                  className: \"rate-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: notes,\n                  onChange: e => setNotes(e.target.value),\n                  placeholder: \"Update notes (optional)\",\n                  className: \"notes-input\",\n                  rows: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"edit-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleUpdateRate(currency.id),\n                    className: \"btn btn-success btn-sm\",\n                    disabled: loading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRate(null);\n                      setNewRate('');\n                      setNotes('');\n                    },\n                    className: \"btn btn-secondary btn-sm\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate(rate.rate_to_fancoin.toString());\n                },\n                className: \"btn btn-outline btn-sm\",\n                children: \"Update Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-rate\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No exchange rate set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate('');\n                },\n                className: \"btn btn-primary btn-sm\",\n                children: \"Set Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 273,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n              className: `btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`,\n              disabled: loading,\n              children: currency.is_active ? 'Deactivate' : 'Activate'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 33\n          }, this)]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 17\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 305,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 163,\n    columnNumber: 9\n  }, this);\n}\n_s(CurrencyManagement, \"J9tRpo9D4JygUqQfVoQtJcZE2tc=\");\n_c = CurrencyManagement;\n;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "editingRate", "setEditingRate", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadData", "useCallback", "currenciesResponse", "ratesResponse", "Promise", "all", "execute", "currencyService", "getCurrencies", "getExchangeRates", "data", "exchange_rates", "console", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "alert", "response", "updateExchangeRate", "refreshCurrencyData", "message", "handleToggleCurrency", "currentStatus", "manageCurrencies", "currency_id", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "currency", "id", "isEditing", "rate_to_fancoin", "updated_at", "type", "step", "value", "onChange", "e", "target", "placeholder", "rows", "disabled", "toString", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [editingRate, setEditingRate] = useState(null);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    // Load currencies and exchange rates\n    const loadData = useCallback(async () => {\n        try {\n            const [currenciesResponse, ratesResponse] = await Promise.all([\n                execute(() => currencyService.getCurrencies(false)), // Get all currencies including inactive\n                execute(() => currencyService.getExchangeRates())\n            ]);\n\n            if (currenciesResponse.success) {\n                setCurrencies(currenciesResponse.data.currencies || []);\n            }\n\n            if (ratesResponse.success) {\n                setExchangeRates(ratesResponse.data.exchange_rates || []);\n            }\n        } catch (error) {\n            console.error('Failed to load currency data:', error);\n        }\n    }, [execute]);\n\n    // Update exchange rate\n    const handleUpdateRate = useCallback(async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            alert('Please enter a valid exchange rate');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                alert('Exchange rate updated successfully!');\n            } else {\n                alert(response.message || 'Failed to update exchange rate');\n            }\n        } catch (error) {\n            alert('Error updating exchange rate: ' + error.message);\n        }\n    }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n    // Toggle currency active status\n    const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('toggle', { currency_id: currencyId })\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n            } else {\n                alert(response.message || 'Failed to toggle currency status');\n            }\n        } catch (error) {\n            alert('Error toggling currency: ' + error.message);\n        }\n    }, [execute, loadData, refreshCurrencyData]);\n\n    // Add new currency\n    const handleAddCurrency = useCallback(async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            alert('Please fill in all required fields');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('create', newCurrency)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                alert('Currency added successfully!');\n            } else {\n                alert(response.message || 'Failed to add currency');\n            }\n        } catch (error) {\n            alert('Error adding currency: ' + error.message);\n        }\n    }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n    // Get exchange rate for currency\n    const getExchangeRate = useCallback((currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    }, [exchangeRates]);\n\n    // Format date\n    const formatDate = useCallback((dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }, []);\n\n    // Load data on component mount\n    useEffect(() => {\n        loadData();\n    }, [loadData]);\n\n    return (\n        <div className=\"currency-management\">\n            <div className=\"page-header\">\n                <h1>Currency Management</h1>\n                <p>Manage supported currencies and exchange rates</p>\n                <button \n                    className=\"btn btn-primary\"\n                    onClick={() => setShowAddCurrency(true)}\n                >\n                    Add New Currency\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <p>⚠️ {error.message || 'An error occurred'}</p>\n                    <button onClick={loadData} className=\"btn btn-secondary\">\n                        Retry\n                    </button>\n                </div>\n            )}\n\n            {loading && currencies.length === 0 ? (\n                <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                    <p>Loading currencies...</p>\n                </div>\n            ) : (\n                <div className=\"currencies-grid\">\n                    {currencies.map((currency) => {\n                        const rate = getExchangeRate(currency.id);\n                        const isEditing = editingRate === currency.id;\n\n                        return (\n                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>\n                                <div className=\"currency-header\">\n                                    <div className=\"currency-info\">\n                                        <h3>{currency.currency_code}</h3>\n                                        <p>{currency.currency_name}</p>\n                                        <span className=\"currency-symbol\">{currency.currency_symbol}</span>\n                                    </div>\n                                    <div className=\"currency-status\">\n                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>\n                                            {currency.is_active ? 'Active' : 'Inactive'}\n                                        </span>\n                                    </div>\n                                </div>\n\n                                <div className=\"exchange-rate-section\">\n                                    <h4>Exchange Rate</h4>\n                                    {rate ? (\n                                        <div className=\"rate-info\">\n                                            <div className=\"current-rate\">\n                                                <span className=\"rate-value\">\n                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                </span>\n                                                <small>Updated: {formatDate(rate.updated_at)}</small>\n                                            </div>\n\n                                            {isEditing ? (\n                                                <div className=\"rate-edit-form\">\n                                                    <input\n                                                        type=\"number\"\n                                                        step=\"0.0001\"\n                                                        value={newRate}\n                                                        onChange={(e) => setNewRate(e.target.value)}\n                                                        placeholder=\"New rate\"\n                                                        className=\"rate-input\"\n                                                    />\n                                                    <textarea\n                                                        value={notes}\n                                                        onChange={(e) => setNotes(e.target.value)}\n                                                        placeholder=\"Update notes (optional)\"\n                                                        className=\"notes-input\"\n                                                        rows=\"2\"\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button \n                                                            onClick={() => handleUpdateRate(currency.id)}\n                                                            className=\"btn btn-success btn-sm\"\n                                                            disabled={loading}\n                                                        >\n                                                            Save\n                                                        </button>\n                                                        <button \n                                                            onClick={() => {\n                                                                setEditingRate(null);\n                                                                setNewRate('');\n                                                                setNotes('');\n                                                            }}\n                                                            className=\"btn btn-secondary btn-sm\"\n                                                        >\n                                                            Cancel\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <button \n                                                    onClick={() => {\n                                                        setEditingRate(currency.id);\n                                                        setNewRate(rate.rate_to_fancoin.toString());\n                                                    }}\n                                                    className=\"btn btn-outline btn-sm\"\n                                                >\n                                                    Update Rate\n                                                </button>\n                                            )}\n                                        </div>\n                                    ) : (\n                                        <div className=\"no-rate\">\n                                            <p>No exchange rate set</p>\n                                            <button \n                                                onClick={() => {\n                                                    setEditingRate(currency.id);\n                                                    setNewRate('');\n                                                }}\n                                                className=\"btn btn-primary btn-sm\"\n                                            >\n                                                Set Rate\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"currency-actions\">\n                                    <button \n                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}\n                                        disabled={loading}\n                                    >\n                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                    </button>\n                                </div>\n                            </div>\n                        );\n                    })}\n                </div>\n            )}\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC;IAC3CwC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;;EAEtC;EACA,MAAMgD,QAAQ,GAAGC,WAAW,CAAC,YAAY;IACrC,IAAI;MACA,MAAM,CAACC,kBAAkB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DC,OAAO,CAAC,MAAMC,eAAe,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MAAE;MACrDF,OAAO,CAAC,MAAMC,eAAe,CAACE,gBAAgB,CAAC,CAAC,CAAC,CACpD,CAAC;MAEF,IAAIP,kBAAkB,CAAC1B,OAAO,EAAE;QAC5BP,aAAa,CAACiC,kBAAkB,CAACQ,IAAI,CAAC1C,UAAU,IAAI,EAAE,CAAC;MAC3D;MAEA,IAAImC,aAAa,CAAC3B,OAAO,EAAE;QACvBL,gBAAgB,CAACgC,aAAa,CAACO,IAAI,CAACC,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACZsC,OAAO,CAACtC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC,EAAE,CAACgC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMO,gBAAgB,GAAGZ,WAAW,CAAC,MAAOa,UAAU,IAAK;IACvD,IAAI,CAAClB,OAAO,IAAImB,KAAK,CAACC,UAAU,CAACpB,OAAO,CAAC,CAAC,EAAE;MACxCqB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAAC,MAC3BC,eAAe,CAACY,kBAAkB,CAACL,UAAU,EAAEE,UAAU,CAACpB,OAAO,CAAC,EAAEE,KAAK,CAC7E,CAAC;MAED,IAAIoB,QAAQ,CAAC1C,OAAO,EAAE;QAClB,MAAMwB,QAAQ,CAAC,CAAC;QAChB,MAAMoB,mBAAmB,CAAC,CAAC;QAC3BjC,cAAc,CAAC,IAAI,CAAC;QACpBU,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZkB,KAAK,CAAC,qCAAqC,CAAC;MAChD,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACG,OAAO,IAAI,gCAAgC,CAAC;MAC/D;IACJ,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACZ2C,KAAK,CAAC,gCAAgC,GAAG3C,KAAK,CAAC+C,OAAO,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACzB,OAAO,EAAEE,KAAK,EAAEQ,OAAO,EAAEN,QAAQ,EAAEoB,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAME,oBAAoB,GAAGrB,WAAW,CAAC,OAAOa,UAAU,EAAES,aAAa,KAAK;IAC1E,IAAI;MACA,MAAML,QAAQ,GAAG,MAAMZ,OAAO,CAAC,MAC3BC,eAAe,CAACiB,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,WAAW,EAAEX;MAAW,CAAC,CAC1E,CAAC;MAED,IAAII,QAAQ,CAAC1C,OAAO,EAAE;QAClB,MAAMwB,QAAQ,CAAC,CAAC;QAChB,MAAMoB,mBAAmB,CAAC,CAAC;QAC3BH,KAAK,CAAC,YAAYM,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;MAClF,CAAC,MAAM;QACHN,KAAK,CAACC,QAAQ,CAACG,OAAO,IAAI,kCAAkC,CAAC;MACjE;IACJ,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACZ2C,KAAK,CAAC,2BAA2B,GAAG3C,KAAK,CAAC+C,OAAO,CAAC;IACtD;EACJ,CAAC,EAAE,CAACf,OAAO,EAAEN,QAAQ,EAAEoB,mBAAmB,CAAC,CAAC;;EAE5C;EACA,MAAMM,iBAAiB,GAAGzB,WAAW,CAAC,YAAY;IAC9C,IAAI,CAACX,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1FuB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMZ,OAAO,CAAC,MAC3BC,eAAe,CAACiB,gBAAgB,CAAC,QAAQ,EAAElC,WAAW,CAC1D,CAAC;MAED,IAAI4B,QAAQ,CAAC1C,OAAO,EAAE;QAClB,MAAMwB,QAAQ,CAAC,CAAC;QAChB,MAAMoB,mBAAmB,CAAC,CAAC;QAC3BnC,kBAAkB,CAAC,KAAK,CAAC;QACzBM,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACFsB,KAAK,CAAC,8BAA8B,CAAC;MACzC,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACG,OAAO,IAAI,wBAAwB,CAAC;MACvD;IACJ,CAAC,CAAC,OAAO/C,KAAK,EAAE;MACZ2C,KAAK,CAAC,yBAAyB,GAAG3C,KAAK,CAAC+C,OAAO,CAAC;IACpD;EACJ,CAAC,EAAE,CAAC/B,WAAW,EAAEgB,OAAO,EAAEN,QAAQ,EAAEoB,mBAAmB,CAAC,CAAC;;EAEzD;EACA,MAAMO,eAAe,GAAG1B,WAAW,CAAEa,UAAU,IAAK;IAChD,OAAO5C,aAAa,CAAC0D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,WAAW,KAAKX,UAAU,CAAC;EACtE,CAAC,EAAE,CAAC5C,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAM4D,UAAU,GAAG7B,WAAW,CAAE8B,UAAU,IAAK;IAC3C,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArF,SAAS,CAAC,MAAM;IACZ+C,QAAQ,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACIpC,OAAA;IAAK2E,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChC5E,OAAA;MAAK2E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB5E,OAAA;QAAA4E,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BhF,OAAA;QAAA4E,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDhF,OAAA;QACI2E,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAM5D,kBAAkB,CAAC,IAAI,CAAE;QAAAuD,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELtE,KAAK,iBACFV,OAAA;MAAK2E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B5E,OAAA;QAAA4E,QAAA,GAAG,eAAG,EAAClE,KAAK,CAAC+C,OAAO,IAAI,mBAAmB;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDhF,OAAA;QAAQiF,OAAO,EAAE7C,QAAS;QAACuC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEAxE,OAAO,IAAIJ,UAAU,CAAC8E,MAAM,KAAK,CAAC,gBAC/BlF,OAAA;MAAK2E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B5E,OAAA;QAAK2E,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BhF,OAAA;QAAA4E,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,gBAENhF,OAAA;MAAK2E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3BxE,UAAU,CAAC+E,GAAG,CAAEC,QAAQ,IAAK;QAC1B,MAAMnB,IAAI,GAAGF,eAAe,CAACqB,QAAQ,CAACC,EAAE,CAAC;QACzC,MAAMC,SAAS,GAAGhE,WAAW,KAAK8D,QAAQ,CAACC,EAAE;QAE7C,oBACIrF,OAAA;UAAuB2E,SAAS,EAAE,iBAAiB,CAACS,QAAQ,CAACrD,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;UAAA6C,QAAA,gBACvF5E,OAAA;YAAK2E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B5E,OAAA;cAAK2E,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1B5E,OAAA;gBAAA4E,QAAA,EAAKQ,QAAQ,CAACxD;cAAa;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjChF,OAAA;gBAAA4E,QAAA,EAAIQ,QAAQ,CAACvD;cAAa;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BhF,OAAA;gBAAM2E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEQ,QAAQ,CAACtD;cAAe;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNhF,OAAA;cAAK2E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B5E,OAAA;gBAAM2E,SAAS,EAAE,gBAAgBS,QAAQ,CAACrD,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA6C,QAAA,EACzEQ,QAAQ,CAACrD,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClC5E,OAAA;cAAA4E,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBf,IAAI,gBACDjE,OAAA;cAAK2E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB5E,OAAA;gBAAK2E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB5E,OAAA;kBAAM2E,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,SAClB,EAACQ,QAAQ,CAACtD,eAAe,EAAEmC,IAAI,CAACsB,eAAe;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACPhF,OAAA;kBAAA4E,QAAA,GAAO,WAAS,EAACV,UAAU,CAACD,IAAI,CAACuB,UAAU,CAAC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAELM,SAAS,gBACNtF,OAAA;gBAAK2E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3B5E,OAAA;kBACIyF,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE3D,OAAQ;kBACf4D,QAAQ,EAAGC,CAAC,IAAK5D,UAAU,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5CI,WAAW,EAAC,UAAU;kBACtBpB,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFhF,OAAA;kBACI2F,KAAK,EAAEzD,KAAM;kBACb0D,QAAQ,EAAGC,CAAC,IAAK1D,QAAQ,CAAC0D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1CI,WAAW,EAAC,yBAAyB;kBACrCpB,SAAS,EAAC,aAAa;kBACvBqB,IAAI,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFhF,OAAA;kBAAK2E,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB5E,OAAA;oBACIiF,OAAO,EAAEA,CAAA,KAAMhC,gBAAgB,CAACmC,QAAQ,CAACC,EAAE,CAAE;oBAC7CV,SAAS,EAAC,wBAAwB;oBAClCsB,QAAQ,EAAEzF,OAAQ;oBAAAoE,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACThF,OAAA;oBACIiF,OAAO,EAAEA,CAAA,KAAM;sBACX1D,cAAc,CAAC,IAAI,CAAC;sBACpBU,UAAU,CAAC,EAAE,CAAC;sBACdE,QAAQ,CAAC,EAAE,CAAC;oBAChB,CAAE;oBACFwC,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENhF,OAAA;gBACIiF,OAAO,EAAEA,CAAA,KAAM;kBACX1D,cAAc,CAAC6D,QAAQ,CAACC,EAAE,CAAC;kBAC3BpD,UAAU,CAACgC,IAAI,CAACsB,eAAe,CAACW,QAAQ,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFvB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENhF,OAAA;cAAK2E,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACpB5E,OAAA;gBAAA4E,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3BhF,OAAA;gBACIiF,OAAO,EAAEA,CAAA,KAAM;kBACX1D,cAAc,CAAC6D,QAAQ,CAACC,EAAE,CAAC;kBAC3BpD,UAAU,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACF0C,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENhF,OAAA;YAAK2E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7B5E,OAAA;cACIiF,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC0B,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAACrD,SAAS,CAAE;cACrE4C,SAAS,EAAE,cAAcS,QAAQ,CAACrD,SAAS,GAAG,aAAa,GAAG,aAAa,EAAG;cAC9EkE,QAAQ,EAAEzF,OAAQ;cAAAoE,QAAA,EAEjBQ,QAAQ,CAACrD,SAAS,GAAG,YAAY,GAAG;YAAU;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlGAI,QAAQ,CAACC,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGhB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA5D,eAAe,iBACZpB,OAAA;MAAK2E,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAM5D,kBAAkB,CAAC,KAAK,CAAE;MAAAuD,QAAA,eACpE5E,OAAA;QAAK2E,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGY,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;QAAAvB,QAAA,gBAC/D5E,OAAA;UAAA4E,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBhF,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB5E,OAAA;YAAA4E,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7ChF,OAAA;YACIyF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEjE,WAAW,CAACE,aAAc;YACjCgE,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAEiE,CAAC,CAACC,MAAM,CAACH,KAAK,CAACS,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB5E,OAAA;YAAA4E,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BhF,OAAA;YACIyF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEjE,WAAW,CAACG,aAAc;YACjC+D,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAEgE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACjFI,WAAW,EAAC;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB5E,OAAA;YAAA4E,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BhF,OAAA;YACIyF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEjE,WAAW,CAACI,eAAgB;YACnC8D,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAE+D,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnFI,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB5E,OAAA;YAAA4E,QAAA,gBACI5E,OAAA;cACIyF,IAAI,EAAC,UAAU;cACfa,OAAO,EAAE5E,WAAW,CAACK,SAAU;cAC/B6D,QAAQ,EAAGC,CAAC,IAAKlE,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAE8D,CAAC,CAACC,MAAM,CAACQ;cAAO,CAAC;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNhF,OAAA;UAAK2E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B5E,OAAA;YACIiF,OAAO,EAAEnB,iBAAkB;YAC3Ba,SAAS,EAAC,iBAAiB;YAC3BsB,QAAQ,EAAEzF,OAAQ;YAAAoE,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThF,OAAA;YACIiF,OAAO,EAAEA,CAAA,KAAM5D,kBAAkB,CAAC,KAAK,CAAE;YACzCsD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAC7E,EAAA,CA/VQD,kBAAkB;AAAAqG,EAAA,GAAlBrG,kBAAkB;AA+V1B;AAED,eAAeA,kBAAkB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}