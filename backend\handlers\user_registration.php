<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
include_once '../includes/currency_utils.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode([
        'status' => $status,
        'success' => $status >= 200 && $status < 300,
        'message' => $message,
        'data' => $data
    ]);
    exit();
}

try {
    $conn = getDBConnection();

    if (!$conn) {
        jsonResponse(500, "Database connection failed");
    }

    $data = json_decode(file_get_contents("php://input"));

    if (!$data) {
        jsonResponse(400, "Invalid JSON data");
    }

    // Validate required fields
    if (
        empty($data->username) ||
        empty($data->full_name) ||
        empty($data->email) ||
        empty($data->password) ||
        empty($data->favorite_team)
    ) {
        jsonResponse(400, "All required fields must be provided");
    }

    $username = htmlspecialchars(strip_tags($data->username));
    $full_name = htmlspecialchars(strip_tags($data->full_name));
    $email = htmlspecialchars(strip_tags($data->email));
    $password = password_hash($data->password, PASSWORD_DEFAULT);
    $favorite_team = htmlspecialchars(strip_tags($data->favorite_team));

    // Handle preferred currency (now mandatory)
    if (!isset($data->preferred_currency_id) || empty($data->preferred_currency_id)) {
        jsonResponse(400, "Preferred currency selection is required");
    }

    $preferred_currency_id = (int)$data->preferred_currency_id;

    // Validate that the currency exists and is active
    if (!isValidCurrency($conn, $preferred_currency_id)) {
        jsonResponse(400, "Invalid currency selection");
    }

    // Check if username or email already exists
    $check_query = "SELECT * FROM users WHERE username = :username OR email = :email";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(":username", $username);
    $check_stmt->bindParam(":email", $email);
    $check_stmt->execute();

    if ($check_stmt->rowCount() > 0) {
        jsonResponse(400, "Username or email already exists");
    }

    // Insert new user with currency preference
    $query = "INSERT INTO users (username, full_name, email, password_hash, favorite_team, preferred_currency_id)
              VALUES (:username, :full_name, :email, :password, :favorite_team, :preferred_currency_id)";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(":username", $username);
    $stmt->bindParam(":full_name", $full_name);
    $stmt->bindParam(":email", $email);
    $stmt->bindParam(":password", $password);
    $stmt->bindParam(":favorite_team", $favorite_team);
    $stmt->bindParam(":preferred_currency_id", $preferred_currency_id);

    if ($stmt->execute()) {
        $userId = $conn->lastInsertId();

        // Get the selected currency information for response
        $currencyInfo = getCurrencyByCode($conn, 'USD'); // Default fallback
        if ($preferred_currency_id !== 1) {
            $stmt = $conn->prepare("SELECT currency_code, currency_name, currency_symbol FROM currencies WHERE id = :id");
            $stmt->execute(['id' => $preferred_currency_id]);
            $selectedCurrency = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($selectedCurrency) {
                $currencyInfo = [
                    'currency_code' => $selectedCurrency['currency_code'],
                    'currency_name' => $selectedCurrency['currency_name'],
                    'currency_symbol' => $selectedCurrency['currency_symbol']
                ];
            }
        }

        jsonResponse(201, "User registered successfully", [
            'user_id' => (int)$userId,
            'username' => $username,
            'email' => $email,
            'preferred_currency' => $currencyInfo,
            'registration_date' => date('Y-m-d H:i:s')
        ]);
    } else {
        jsonResponse(503, "Unable to register user");
    }

} catch (PDOException $e) {
    error_log("Database error in user_registration.php: " . $e->getMessage());
    jsonResponse(500, "Database error occurred");
} catch (Exception $e) {
    error_log("General error in user_registration.php: " . $e->getMessage());
    jsonResponse(500, "An error occurred during registration");
}
?>
