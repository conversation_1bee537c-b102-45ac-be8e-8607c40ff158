{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\contexts\\\\CurrencyContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/**\n * Currency Context - Fixed Version\n * \n * Provides currency data and conversion utilities throughout the application\n * with proper error handling, caching, and infinite loop prevention.\n */\n\nimport React, { createContext, useState, useContext, useEffect, useCallback, useMemo } from 'react';\nimport { currencyService } from '../services';\nimport { useUser } from '../context/UserContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencyContext = /*#__PURE__*/createContext();\nexport const CurrencyProvider = ({\n  children\n}) => {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [userCurrency, setUserCurrency] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [initialized, setInitialized] = useState(false);\n  const {\n    userData\n  } = useUser();\n\n  // Cache configuration\n  const CACHE_KEY = 'fanbet247_currencies';\n  const CACHE_EXPIRY_KEY = 'fanbet247_currencies_expiry';\n  const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes\n\n  // Cache utilities\n  const getCachedData = useCallback(() => {\n    try {\n      const cached = localStorage.getItem(CACHE_KEY);\n      const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);\n      if (cached && expiry && Date.now() < parseInt(expiry)) {\n        return JSON.parse(cached);\n      }\n    } catch (error) {\n      console.error('Error reading cached currencies:', error);\n    }\n    return null;\n  }, []);\n  const setCachedData = useCallback(data => {\n    try {\n      localStorage.setItem(CACHE_KEY, JSON.stringify(data));\n      localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n    } catch (error) {\n      console.error('Error caching currencies:', error);\n    }\n  }, []);\n\n  // Load currencies and exchange rates\n  const loadCurrencyData = useCallback(async (forceRefresh = false) => {\n    if (loading) return; // Prevent multiple simultaneous loads\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Try cache first unless force refresh\n      if (!forceRefresh) {\n        const cached = getCachedData();\n        if (cached && cached.currencies && cached.exchangeRates) {\n          setCurrencies(cached.currencies);\n          setExchangeRates(cached.exchangeRates);\n          setInitialized(true);\n          return;\n        }\n      }\n\n      // Fetch fresh data\n      const [currenciesResponse, ratesResponse] = await Promise.all([currencyService.getCurrencies(true), currencyService.getExchangeRates()]);\n      if (currenciesResponse.success && ratesResponse.success) {\n        const currenciesData = currenciesResponse.data.currencies || [];\n        const ratesData = ratesResponse.data.exchange_rates || [];\n        setCurrencies(currenciesData);\n        setExchangeRates(ratesData);\n\n        // Cache the data\n        setCachedData({\n          currencies: currenciesData,\n          exchangeRates: ratesData\n        });\n        setInitialized(true);\n      } else {\n        throw new Error(currenciesResponse.message || ratesResponse.message || 'Failed to load currency data');\n      }\n    } catch (err) {\n      console.error('Error loading currency data:', err);\n      setError(err.message || 'Failed to load currency data');\n\n      // Try to use cached data as fallback\n      const cached = getCachedData();\n      if (cached && cached.currencies) {\n        setCurrencies(cached.currencies);\n        setExchangeRates(cached.exchangeRates || []);\n        setInitialized(true);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [loading, getCachedData, setCachedData]);\n\n  // Load user currency preference\n  const loadUserCurrency = useCallback(async userId => {\n    if (!userId || !currencies.length) return;\n    try {\n      const response = await currencyService.getUserCurrencyPreference(userId);\n      if (response.success && response.data) {\n        setUserCurrency(response.data);\n      } else {\n        // Default to USD if no preference found\n        const usdCurrency = currencies.find(c => c.currency_code === 'USD');\n        if (usdCurrency) {\n          setUserCurrency(usdCurrency);\n        }\n      }\n    } catch (err) {\n      console.error('Error loading user currency preference:', err);\n      // Default to USD on error\n      const usdCurrency = currencies.find(c => c.currency_code === 'USD');\n      if (usdCurrency) {\n        setUserCurrency(usdCurrency);\n      }\n    }\n  }, [currencies]);\n\n  // Convert FanCoin to user's preferred currency\n  const convertToUserCurrency = useCallback(fanCoinAmount => {\n    if (!userCurrency || !exchangeRates.length || !fanCoinAmount) {\n      return {\n        amount: fanCoinAmount,\n        currency: 'FC',\n        symbol: 'FC',\n        formatted: `${fanCoinAmount} FC`\n      };\n    }\n    const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);\n    if (!rate) {\n      return {\n        amount: fanCoinAmount,\n        currency: 'FC',\n        symbol: 'FC',\n        formatted: `${fanCoinAmount} FC`\n      };\n    }\n    const convertedAmount = fanCoinAmount * rate.rate_to_fancoin;\n    return {\n      amount: convertedAmount,\n      currency: userCurrency.currency_code,\n      symbol: userCurrency.currency_symbol,\n      formatted: `${userCurrency.currency_symbol}${convertedAmount.toFixed(2)}`\n    };\n  }, [userCurrency, exchangeRates]);\n\n  // Format amount for display with both currencies\n  const formatAmountForDisplay = useCallback((fanCoinAmount, showBoth = true) => {\n    const conversion = convertToUserCurrency(fanCoinAmount);\n    if (!showBoth || conversion.currency === 'FC') {\n      return conversion.formatted;\n    }\n    return `${conversion.formatted} (${fanCoinAmount} FC)`;\n  }, [convertToUserCurrency]);\n\n  // Get currency by ID\n  const getCurrencyById = useCallback(id => {\n    return currencies.find(c => c.id === id);\n  }, [currencies]);\n\n  // Get currency by code\n  const getCurrencyByCode = useCallback(code => {\n    return currencies.find(c => c.currency_code === code);\n  }, [currencies]);\n\n  // Update user currency preference\n  const updateUserCurrency = useCallback(async newCurrency => {\n    if (!(userData !== null && userData !== void 0 && userData.user_id)) return;\n    try {\n      const response = await currencyService.updateUserCurrencyPreference(userData.user_id, newCurrency.id);\n      if (response.success) {\n        setUserCurrency(newCurrency);\n        return true;\n      } else {\n        throw new Error(response.message || 'Failed to update currency preference');\n      }\n    } catch (err) {\n      console.error('Error updating user currency:', err);\n      setError(err.message);\n      return false;\n    }\n  }, [userData]);\n\n  // Initialize currency data on mount\n  useEffect(() => {\n    if (!initialized) {\n      loadCurrencyData();\n    }\n  }, [initialized, loadCurrencyData]);\n\n  // Load user currency when user data or currencies change\n  useEffect(() => {\n    if (userData !== null && userData !== void 0 && userData.user_id && currencies.length > 0 && !userCurrency) {\n      loadUserCurrency(userData.user_id);\n    }\n  }, [userData === null || userData === void 0 ? void 0 : userData.user_id, currencies.length, userCurrency, loadUserCurrency]);\n\n  // Memoized context value to prevent unnecessary re-renders\n  const contextValue = useMemo(() => ({\n    // Data\n    currencies,\n    exchangeRates,\n    userCurrency,\n    loading,\n    error,\n    initialized,\n    // Functions\n    convertToUserCurrency,\n    formatAmountForDisplay,\n    getCurrencyById,\n    getCurrencyByCode,\n    updateUserCurrency,\n    refreshCurrencyData: () => loadCurrencyData(true),\n    // State setters (for external updates)\n    setCurrencies,\n    setExchangeRates,\n    setUserCurrency\n  }), [currencies, exchangeRates, userCurrency, loading, error, initialized, convertToUserCurrency, formatAmountForDisplay, getCurrencyById, getCurrencyByCode, updateUserCurrency, loadCurrencyData]);\n  return /*#__PURE__*/_jsxDEV(CurrencyContext.Provider, {\n    value: contextValue,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 268,\n    columnNumber: 9\n  }, this);\n};\n_s(CurrencyProvider, \"XHS1BaPBxCcLFqkxNY01XYNN6bc=\", false, function () {\n  return [useUser];\n});\n_c = CurrencyProvider;\nexport const useCurrency = () => {\n  _s2();\n  const context = useContext(CurrencyContext);\n  if (!context) {\n    throw new Error('useCurrency must be used within a CurrencyProvider');\n  }\n  return context;\n};\n_s2(useCurrency, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport default CurrencyContext;\nvar _c;\n$RefreshReg$(_c, \"CurrencyProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useContext", "useEffect", "useCallback", "useMemo", "currencyService", "useUser", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>cyContext", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "userCurrency", "setUserCurrency", "loading", "setLoading", "error", "setError", "initialized", "setInitialized", "userData", "CACHE_KEY", "CACHE_EXPIRY_KEY", "CACHE_DURATION", "getCachedData", "cached", "localStorage", "getItem", "expiry", "Date", "now", "parseInt", "JSON", "parse", "console", "setCachedData", "data", "setItem", "stringify", "toString", "loadCurrencyData", "forceRefresh", "currenciesResponse", "ratesResponse", "Promise", "all", "getCurrencies", "getExchangeRates", "success", "currenciesData", "ratesData", "exchange_rates", "Error", "message", "err", "loadUserCurrency", "userId", "length", "response", "getUserCurrencyPreference", "usdCurrency", "find", "c", "currency_code", "convertToUserCurrency", "fanCoinAmount", "amount", "currency", "symbol", "formatted", "rate", "r", "currency_id", "id", "convertedAmount", "rate_to_fancoin", "currency_symbol", "toFixed", "formatAmountForDisplay", "showBoth", "conversion", "getCurrencyById", "getCurrencyByCode", "code", "updateUserCurrency", "newCurrency", "user_id", "updateUserCurrencyPreference", "contextValue", "refreshCurrencyData", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useCurrency", "_s2", "context", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/contexts/CurrencyContext.js"], "sourcesContent": ["/**\n * Currency Context - Fixed Version\n * \n * Provides currency data and conversion utilities throughout the application\n * with proper error handling, caching, and infinite loop prevention.\n */\n\nimport React, { createContext, useState, useContext, useEffect, useCallback, useMemo } from 'react';\nimport { currencyService } from '../services';\nimport { useUser } from '../context/UserContext';\n\nconst CurrencyContext = createContext();\n\nexport const CurrencyProvider = ({ children }) => {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [userCurrency, setUserCurrency] = useState(null);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState(null);\n    const [initialized, setInitialized] = useState(false);\n    \n    const { userData } = useUser();\n\n    // Cache configuration\n    const CACHE_KEY = 'fanbet247_currencies';\n    const CACHE_EXPIRY_KEY = 'fanbet247_currencies_expiry';\n    const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes\n\n    // Cache utilities\n    const getCachedData = useCallback(() => {\n        try {\n            const cached = localStorage.getItem(CACHE_KEY);\n            const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);\n            \n            if (cached && expiry && Date.now() < parseInt(expiry)) {\n                return JSON.parse(cached);\n            }\n        } catch (error) {\n            console.error('Error reading cached currencies:', error);\n        }\n        return null;\n    }, []);\n\n    const setCachedData = useCallback((data) => {\n        try {\n            localStorage.setItem(CACHE_KEY, JSON.stringify(data));\n            localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());\n        } catch (error) {\n            console.error('Error caching currencies:', error);\n        }\n    }, []);\n\n    // Load currencies and exchange rates\n    const loadCurrencyData = useCallback(async (forceRefresh = false) => {\n        if (loading) return; // Prevent multiple simultaneous loads\n        \n        try {\n            setLoading(true);\n            setError(null);\n\n            // Try cache first unless force refresh\n            if (!forceRefresh) {\n                const cached = getCachedData();\n                if (cached && cached.currencies && cached.exchangeRates) {\n                    setCurrencies(cached.currencies);\n                    setExchangeRates(cached.exchangeRates);\n                    setInitialized(true);\n                    return;\n                }\n            }\n\n            // Fetch fresh data\n            const [currenciesResponse, ratesResponse] = await Promise.all([\n                currencyService.getCurrencies(true),\n                currencyService.getExchangeRates()\n            ]);\n\n            if (currenciesResponse.success && ratesResponse.success) {\n                const currenciesData = currenciesResponse.data.currencies || [];\n                const ratesData = ratesResponse.data.exchange_rates || [];\n\n                setCurrencies(currenciesData);\n                setExchangeRates(ratesData);\n\n                // Cache the data\n                setCachedData({\n                    currencies: currenciesData,\n                    exchangeRates: ratesData\n                });\n\n                setInitialized(true);\n            } else {\n                throw new Error(\n                    currenciesResponse.message || \n                    ratesResponse.message || \n                    'Failed to load currency data'\n                );\n            }\n        } catch (err) {\n            console.error('Error loading currency data:', err);\n            setError(err.message || 'Failed to load currency data');\n            \n            // Try to use cached data as fallback\n            const cached = getCachedData();\n            if (cached && cached.currencies) {\n                setCurrencies(cached.currencies);\n                setExchangeRates(cached.exchangeRates || []);\n                setInitialized(true);\n            }\n        } finally {\n            setLoading(false);\n        }\n    }, [loading, getCachedData, setCachedData]);\n\n    // Load user currency preference\n    const loadUserCurrency = useCallback(async (userId) => {\n        if (!userId || !currencies.length) return;\n\n        try {\n            const response = await currencyService.getUserCurrencyPreference(userId);\n            \n            if (response.success && response.data) {\n                setUserCurrency(response.data);\n            } else {\n                // Default to USD if no preference found\n                const usdCurrency = currencies.find(c => c.currency_code === 'USD');\n                if (usdCurrency) {\n                    setUserCurrency(usdCurrency);\n                }\n            }\n        } catch (err) {\n            console.error('Error loading user currency preference:', err);\n            // Default to USD on error\n            const usdCurrency = currencies.find(c => c.currency_code === 'USD');\n            if (usdCurrency) {\n                setUserCurrency(usdCurrency);\n            }\n        }\n    }, [currencies]);\n\n    // Convert FanCoin to user's preferred currency\n    const convertToUserCurrency = useCallback((fanCoinAmount) => {\n        if (!userCurrency || !exchangeRates.length || !fanCoinAmount) {\n            return {\n                amount: fanCoinAmount,\n                currency: 'FC',\n                symbol: 'FC',\n                formatted: `${fanCoinAmount} FC`\n            };\n        }\n\n        const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);\n        if (!rate) {\n            return {\n                amount: fanCoinAmount,\n                currency: 'FC',\n                symbol: 'FC',\n                formatted: `${fanCoinAmount} FC`\n            };\n        }\n\n        const convertedAmount = fanCoinAmount * rate.rate_to_fancoin;\n        \n        return {\n            amount: convertedAmount,\n            currency: userCurrency.currency_code,\n            symbol: userCurrency.currency_symbol,\n            formatted: `${userCurrency.currency_symbol}${convertedAmount.toFixed(2)}`\n        };\n    }, [userCurrency, exchangeRates]);\n\n    // Format amount for display with both currencies\n    const formatAmountForDisplay = useCallback((fanCoinAmount, showBoth = true) => {\n        const conversion = convertToUserCurrency(fanCoinAmount);\n        \n        if (!showBoth || conversion.currency === 'FC') {\n            return conversion.formatted;\n        }\n\n        return `${conversion.formatted} (${fanCoinAmount} FC)`;\n    }, [convertToUserCurrency]);\n\n    // Get currency by ID\n    const getCurrencyById = useCallback((id) => {\n        return currencies.find(c => c.id === id);\n    }, [currencies]);\n\n    // Get currency by code\n    const getCurrencyByCode = useCallback((code) => {\n        return currencies.find(c => c.currency_code === code);\n    }, [currencies]);\n\n    // Update user currency preference\n    const updateUserCurrency = useCallback(async (newCurrency) => {\n        if (!userData?.user_id) return;\n\n        try {\n            const response = await currencyService.updateUserCurrencyPreference(\n                userData.user_id, \n                newCurrency.id\n            );\n\n            if (response.success) {\n                setUserCurrency(newCurrency);\n                return true;\n            } else {\n                throw new Error(response.message || 'Failed to update currency preference');\n            }\n        } catch (err) {\n            console.error('Error updating user currency:', err);\n            setError(err.message);\n            return false;\n        }\n    }, [userData]);\n\n    // Initialize currency data on mount\n    useEffect(() => {\n        if (!initialized) {\n            loadCurrencyData();\n        }\n    }, [initialized, loadCurrencyData]);\n\n    // Load user currency when user data or currencies change\n    useEffect(() => {\n        if (userData?.user_id && currencies.length > 0 && !userCurrency) {\n            loadUserCurrency(userData.user_id);\n        }\n    }, [userData?.user_id, currencies.length, userCurrency, loadUserCurrency]);\n\n    // Memoized context value to prevent unnecessary re-renders\n    const contextValue = useMemo(() => ({\n        // Data\n        currencies,\n        exchangeRates,\n        userCurrency,\n        loading,\n        error,\n        initialized,\n\n        // Functions\n        convertToUserCurrency,\n        formatAmountForDisplay,\n        getCurrencyById,\n        getCurrencyByCode,\n        updateUserCurrency,\n        refreshCurrencyData: () => loadCurrencyData(true),\n\n        // State setters (for external updates)\n        setCurrencies,\n        setExchangeRates,\n        setUserCurrency\n    }), [\n        currencies,\n        exchangeRates,\n        userCurrency,\n        loading,\n        error,\n        initialized,\n        convertToUserCurrency,\n        formatAmountForDisplay,\n        getCurrencyById,\n        getCurrencyByCode,\n        updateUserCurrency,\n        loadCurrencyData\n    ]);\n\n    return (\n        <CurrencyContext.Provider value={contextValue}>\n            {children}\n        </CurrencyContext.Provider>\n    );\n};\n\nexport const useCurrency = () => {\n    const context = useContext(CurrencyContext);\n    if (!context) {\n        throw new Error('useCurrency must be used within a CurrencyProvider');\n    }\n    return context;\n};\n\nexport default CurrencyContext;\n"], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACnG,SAASC,eAAe,QAAQ,aAAa;AAC7C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,eAAe,gBAAGV,aAAa,CAAC,CAAC;AAEvC,OAAO,MAAMW,gBAAgB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9C,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACe,aAAa,EAAEC,gBAAgB,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACiB,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,KAAK,EAAEC,QAAQ,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACuB,WAAW,EAAEC,cAAc,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAErD,MAAM;IAAEyB;EAAS,CAAC,GAAGnB,OAAO,CAAC,CAAC;;EAE9B;EACA,MAAMoB,SAAS,GAAG,sBAAsB;EACxC,MAAMC,gBAAgB,GAAG,6BAA6B;EACtD,MAAMC,cAAc,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;;EAEvC;EACA,MAAMC,aAAa,GAAG1B,WAAW,CAAC,MAAM;IACpC,IAAI;MACA,MAAM2B,MAAM,GAAGC,YAAY,CAACC,OAAO,CAACN,SAAS,CAAC;MAC9C,MAAMO,MAAM,GAAGF,YAAY,CAACC,OAAO,CAACL,gBAAgB,CAAC;MAErD,IAAIG,MAAM,IAAIG,MAAM,IAAIC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACH,MAAM,CAAC,EAAE;QACnD,OAAOI,IAAI,CAACC,KAAK,CAACR,MAAM,CAAC;MAC7B;IACJ,CAAC,CAAC,OAAOT,KAAK,EAAE;MACZkB,OAAO,CAAClB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAC5D;IACA,OAAO,IAAI;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMmB,aAAa,GAAGrC,WAAW,CAAEsC,IAAI,IAAK;IACxC,IAAI;MACAV,YAAY,CAACW,OAAO,CAAChB,SAAS,EAAEW,IAAI,CAACM,SAAS,CAACF,IAAI,CAAC,CAAC;MACrDV,YAAY,CAACW,OAAO,CAACf,gBAAgB,EAAE,CAACO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,cAAc,EAAEgB,QAAQ,CAAC,CAAC,CAAC;IACpF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZkB,OAAO,CAAClB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACrD;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwB,gBAAgB,GAAG1C,WAAW,CAAC,OAAO2C,YAAY,GAAG,KAAK,KAAK;IACjE,IAAI3B,OAAO,EAAE,OAAO,CAAC;;IAErB,IAAI;MACAC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAI,CAACwB,YAAY,EAAE;QACf,MAAMhB,MAAM,GAAGD,aAAa,CAAC,CAAC;QAC9B,IAAIC,MAAM,IAAIA,MAAM,CAACjB,UAAU,IAAIiB,MAAM,CAACf,aAAa,EAAE;UACrDD,aAAa,CAACgB,MAAM,CAACjB,UAAU,CAAC;UAChCG,gBAAgB,CAACc,MAAM,CAACf,aAAa,CAAC;UACtCS,cAAc,CAAC,IAAI,CAAC;UACpB;QACJ;MACJ;;MAEA;MACA,MAAM,CAACuB,kBAAkB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1D7C,eAAe,CAAC8C,aAAa,CAAC,IAAI,CAAC,EACnC9C,eAAe,CAAC+C,gBAAgB,CAAC,CAAC,CACrC,CAAC;MAEF,IAAIL,kBAAkB,CAACM,OAAO,IAAIL,aAAa,CAACK,OAAO,EAAE;QACrD,MAAMC,cAAc,GAAGP,kBAAkB,CAACN,IAAI,CAAC5B,UAAU,IAAI,EAAE;QAC/D,MAAM0C,SAAS,GAAGP,aAAa,CAACP,IAAI,CAACe,cAAc,IAAI,EAAE;QAEzD1C,aAAa,CAACwC,cAAc,CAAC;QAC7BtC,gBAAgB,CAACuC,SAAS,CAAC;;QAE3B;QACAf,aAAa,CAAC;UACV3B,UAAU,EAAEyC,cAAc;UAC1BvC,aAAa,EAAEwC;QACnB,CAAC,CAAC;QAEF/B,cAAc,CAAC,IAAI,CAAC;MACxB,CAAC,MAAM;QACH,MAAM,IAAIiC,KAAK,CACXV,kBAAkB,CAACW,OAAO,IAC1BV,aAAa,CAACU,OAAO,IACrB,8BACJ,CAAC;MACL;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVpB,OAAO,CAAClB,KAAK,CAAC,8BAA8B,EAAEsC,GAAG,CAAC;MAClDrC,QAAQ,CAACqC,GAAG,CAACD,OAAO,IAAI,8BAA8B,CAAC;;MAEvD;MACA,MAAM5B,MAAM,GAAGD,aAAa,CAAC,CAAC;MAC9B,IAAIC,MAAM,IAAIA,MAAM,CAACjB,UAAU,EAAE;QAC7BC,aAAa,CAACgB,MAAM,CAACjB,UAAU,CAAC;QAChCG,gBAAgB,CAACc,MAAM,CAACf,aAAa,IAAI,EAAE,CAAC;QAC5CS,cAAc,CAAC,IAAI,CAAC;MACxB;IACJ,CAAC,SAAS;MACNJ,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACD,OAAO,EAAEU,aAAa,EAAEW,aAAa,CAAC,CAAC;;EAE3C;EACA,MAAMoB,gBAAgB,GAAGzD,WAAW,CAAC,MAAO0D,MAAM,IAAK;IACnD,IAAI,CAACA,MAAM,IAAI,CAAChD,UAAU,CAACiD,MAAM,EAAE;IAEnC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAM1D,eAAe,CAAC2D,yBAAyB,CAACH,MAAM,CAAC;MAExE,IAAIE,QAAQ,CAACV,OAAO,IAAIU,QAAQ,CAACtB,IAAI,EAAE;QACnCvB,eAAe,CAAC6C,QAAQ,CAACtB,IAAI,CAAC;MAClC,CAAC,MAAM;QACH;QACA,MAAMwB,WAAW,GAAGpD,UAAU,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,KAAK,CAAC;QACnE,IAAIH,WAAW,EAAE;UACb/C,eAAe,CAAC+C,WAAW,CAAC;QAChC;MACJ;IACJ,CAAC,CAAC,OAAON,GAAG,EAAE;MACVpB,OAAO,CAAClB,KAAK,CAAC,yCAAyC,EAAEsC,GAAG,CAAC;MAC7D;MACA,MAAMM,WAAW,GAAGpD,UAAU,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAK,KAAK,CAAC;MACnE,IAAIH,WAAW,EAAE;QACb/C,eAAe,CAAC+C,WAAW,CAAC;MAChC;IACJ;EACJ,CAAC,EAAE,CAACpD,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMwD,qBAAqB,GAAGlE,WAAW,CAAEmE,aAAa,IAAK;IACzD,IAAI,CAACrD,YAAY,IAAI,CAACF,aAAa,CAAC+C,MAAM,IAAI,CAACQ,aAAa,EAAE;MAC1D,OAAO;QACHC,MAAM,EAAED,aAAa;QACrBE,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,GAAGJ,aAAa;MAC/B,CAAC;IACL;IAEA,MAAMK,IAAI,GAAG5D,aAAa,CAACmD,IAAI,CAACU,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAK5D,YAAY,CAAC6D,EAAE,CAAC;IACvE,IAAI,CAACH,IAAI,EAAE;MACP,OAAO;QACHJ,MAAM,EAAED,aAAa;QACrBE,QAAQ,EAAE,IAAI;QACdC,MAAM,EAAE,IAAI;QACZC,SAAS,EAAE,GAAGJ,aAAa;MAC/B,CAAC;IACL;IAEA,MAAMS,eAAe,GAAGT,aAAa,GAAGK,IAAI,CAACK,eAAe;IAE5D,OAAO;MACHT,MAAM,EAAEQ,eAAe;MACvBP,QAAQ,EAAEvD,YAAY,CAACmD,aAAa;MACpCK,MAAM,EAAExD,YAAY,CAACgE,eAAe;MACpCP,SAAS,EAAE,GAAGzD,YAAY,CAACgE,eAAe,GAAGF,eAAe,CAACG,OAAO,CAAC,CAAC,CAAC;IAC3E,CAAC;EACL,CAAC,EAAE,CAACjE,YAAY,EAAEF,aAAa,CAAC,CAAC;;EAEjC;EACA,MAAMoE,sBAAsB,GAAGhF,WAAW,CAAC,CAACmE,aAAa,EAAEc,QAAQ,GAAG,IAAI,KAAK;IAC3E,MAAMC,UAAU,GAAGhB,qBAAqB,CAACC,aAAa,CAAC;IAEvD,IAAI,CAACc,QAAQ,IAAIC,UAAU,CAACb,QAAQ,KAAK,IAAI,EAAE;MAC3C,OAAOa,UAAU,CAACX,SAAS;IAC/B;IAEA,OAAO,GAAGW,UAAU,CAACX,SAAS,KAAKJ,aAAa,MAAM;EAC1D,CAAC,EAAE,CAACD,qBAAqB,CAAC,CAAC;;EAE3B;EACA,MAAMiB,eAAe,GAAGnF,WAAW,CAAE2E,EAAE,IAAK;IACxC,OAAOjE,UAAU,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACW,EAAE,KAAKA,EAAE,CAAC;EAC5C,CAAC,EAAE,CAACjE,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM0E,iBAAiB,GAAGpF,WAAW,CAAEqF,IAAI,IAAK;IAC5C,OAAO3E,UAAU,CAACqD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa,KAAKoB,IAAI,CAAC;EACzD,CAAC,EAAE,CAAC3E,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAM4E,kBAAkB,GAAGtF,WAAW,CAAC,MAAOuF,WAAW,IAAK;IAC1D,IAAI,EAACjE,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEkE,OAAO,GAAE;IAExB,IAAI;MACA,MAAM5B,QAAQ,GAAG,MAAM1D,eAAe,CAACuF,4BAA4B,CAC/DnE,QAAQ,CAACkE,OAAO,EAChBD,WAAW,CAACZ,EAChB,CAAC;MAED,IAAIf,QAAQ,CAACV,OAAO,EAAE;QAClBnC,eAAe,CAACwE,WAAW,CAAC;QAC5B,OAAO,IAAI;MACf,CAAC,MAAM;QACH,MAAM,IAAIjC,KAAK,CAACM,QAAQ,CAACL,OAAO,IAAI,sCAAsC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVpB,OAAO,CAAClB,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;MACnDrC,QAAQ,CAACqC,GAAG,CAACD,OAAO,CAAC;MACrB,OAAO,KAAK;IAChB;EACJ,CAAC,EAAE,CAACjC,QAAQ,CAAC,CAAC;;EAEd;EACAvB,SAAS,CAAC,MAAM;IACZ,IAAI,CAACqB,WAAW,EAAE;MACdsB,gBAAgB,CAAC,CAAC;IACtB;EACJ,CAAC,EAAE,CAACtB,WAAW,EAAEsB,gBAAgB,CAAC,CAAC;;EAEnC;EACA3C,SAAS,CAAC,MAAM;IACZ,IAAIuB,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEkE,OAAO,IAAI9E,UAAU,CAACiD,MAAM,GAAG,CAAC,IAAI,CAAC7C,YAAY,EAAE;MAC7D2C,gBAAgB,CAACnC,QAAQ,CAACkE,OAAO,CAAC;IACtC;EACJ,CAAC,EAAE,CAAClE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkE,OAAO,EAAE9E,UAAU,CAACiD,MAAM,EAAE7C,YAAY,EAAE2C,gBAAgB,CAAC,CAAC;;EAE1E;EACA,MAAMiC,YAAY,GAAGzF,OAAO,CAAC,OAAO;IAChC;IACAS,UAAU;IACVE,aAAa;IACbE,YAAY;IACZE,OAAO;IACPE,KAAK;IACLE,WAAW;IAEX;IACA8C,qBAAqB;IACrBc,sBAAsB;IACtBG,eAAe;IACfC,iBAAiB;IACjBE,kBAAkB;IAClBK,mBAAmB,EAAEA,CAAA,KAAMjD,gBAAgB,CAAC,IAAI,CAAC;IAEjD;IACA/B,aAAa;IACbE,gBAAgB;IAChBE;EACJ,CAAC,CAAC,EAAE,CACAL,UAAU,EACVE,aAAa,EACbE,YAAY,EACZE,OAAO,EACPE,KAAK,EACLE,WAAW,EACX8C,qBAAqB,EACrBc,sBAAsB,EACtBG,eAAe,EACfC,iBAAiB,EACjBE,kBAAkB,EAClB5C,gBAAgB,CACnB,CAAC;EAEF,oBACIrC,OAAA,CAACC,eAAe,CAACsF,QAAQ;IAACC,KAAK,EAAEH,YAAa;IAAAlF,QAAA,EACzCA;EAAQ;IAAAsF,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAEnC,CAAC;AAACxF,EAAA,CAlQWF,gBAAgB;EAAA,QAQJJ,OAAO;AAAA;AAAA+F,EAAA,GARnB3F,gBAAgB;AAoQ7B,OAAO,MAAM4F,WAAW,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC7B,MAAMC,OAAO,GAAGvG,UAAU,CAACQ,eAAe,CAAC;EAC3C,IAAI,CAAC+F,OAAO,EAAE;IACV,MAAM,IAAI/C,KAAK,CAAC,oDAAoD,CAAC;EACzE;EACA,OAAO+C,OAAO;AAClB,CAAC;AAACD,GAAA,CANWD,WAAW;AAQxB,eAAe7F,eAAe;AAAC,IAAA4F,EAAA;AAAAI,YAAA,CAAAJ,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}