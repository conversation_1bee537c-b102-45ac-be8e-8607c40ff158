import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaEnvelope, FaCheck, FaTimes, FaSave, FaEye, FaEyeSlash } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function SMTPSettings() {
    const [settings, setSettings] = useState({
        host: 'smtp.gmail.com',
        port: '587',
        username: '',
        password: '',
        encryption: 'tls',
        from_email: '<EMAIL>',
        from_name: 'FanBet247',
        is_active: 'false'
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [testEmail, setTestEmail] = useState('<EMAIL>');
    const [sendingTestEmail, setSendingTestEmail] = useState(false);

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${API_BASE_URL}/handlers/get_smtp_settings.php`);
            
            if (response.data.success && response.data.settings) {
                setSettings(response.data.settings);
            }
        } catch (err) {
            setError('Failed to load SMTP settings. Please try again.');
            console.error('Error fetching settings:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            setSaving(true);
            setError('');
            setSuccess('');

            const response = await axios.post(`${API_BASE_URL}/handlers/update_smtp_settings.php`, settings);

            if (response.data.success) {
                setSuccess('SMTP settings saved successfully!');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                throw new Error(response.data.message || 'Failed to save SMTP settings');
            }
        } catch (err) {
            setError(err.message || 'Failed to save SMTP settings. Please try again.');
            console.error('Error saving settings:', err);
        } finally {
            setSaving(false);
        }
    };

    const testConnection = async () => {
        try {
            setError('');
            setSuccess('');

            const response = await axios.post(`${API_BASE_URL}/handlers/test_smtp.php`, settings);

            if (response.data.success) {
                setSuccess('SMTP connection test successful!');
            } else {
                setError(response.data.message || 'SMTP connection test failed');
            }
        } catch (err) {
            setError('Failed to test SMTP connection. Please check your settings.');
            console.error('Error testing SMTP:', err);
        }
    };

    const sendTestEmail = async () => {
        try {
            setSendingTestEmail(true);
            setError('');
            setSuccess('');

            const response = await axios.post(`${API_BASE_URL}/handlers/send_test_email.php`, {
                email: testEmail,
                smtp_settings: settings
            });

            if (response.data.success) {
                setSuccess(`Test email sent successfully to ${testEmail}!`);
            } else {
                setError(response.data.message || 'Failed to send test email');
            }
        } catch (err) {
            setError('Failed to send test email. Please check your settings.');
            console.error('Error sending test email:', err);
        } finally {
            setSendingTestEmail(false);
        }
    };

    if (loading) {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading SMTP settings...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                    <FaEnvelope className="text-blue-500" />
                    SMTP Settings
                </h1>
                <p className="text-gray-600 mt-2">
                    Configure email server settings for sending notifications and OTP codes.
                </p>
            </div>



            {/* Alerts */}
            {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <FaTimes className="text-red-500" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                    <FaCheck className="text-green-500" />
                    <span className="text-green-700">{success}</span>
                </div>
            )}

            {/* Settings Form */}
            <div className="bg-white rounded-lg shadow-sm border">
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    {/* Server Configuration */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Server Configuration</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    SMTP Host
                                </label>
                                <input
                                    type="text"
                                    name="host"
                                    value={settings.host}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="smtp.gmail.com"
                                    required
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Port
                                </label>
                                <input
                                    type="number"
                                    name="port"
                                    value={settings.port}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="587"
                                    required
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Encryption
                                </label>
                                <select
                                    name="encryption"
                                    value={settings.encryption}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="none">None</option>
                                    <option value="ssl">SSL</option>
                                    <option value="tls">TLS</option>
                                </select>
                            </div>
                            
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="is_active"
                                    name="is_active"
                                    checked={settings.is_active === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="is_active" className="ml-2 block text-sm text-gray-900">
                                    Enable SMTP
                                </label>
                            </div>
                        </div>
                    </div>

                    {/* Authentication */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Authentication</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Username
                                </label>
                                <input
                                    type="text"
                                    name="username"
                                    value={settings.username}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="<EMAIL>"
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Password
                                </label>
                                <div className="relative">
                                    <input
                                        type={showPassword ? "text" : "password"}
                                        name="password"
                                        value={settings.password}
                                        onChange={handleInputChange}
                                        className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Your app password"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    >
                                        {showPassword ? (
                                            <FaEyeSlash className="h-4 w-4 text-gray-400" />
                                        ) : (
                                            <FaEye className="h-4 w-4 text-gray-400" />
                                        )}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Email Settings */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Email Settings</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    From Email
                                </label>
                                <input
                                    type="email"
                                    name="from_email"
                                    value={settings.from_email}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="<EMAIL>"
                                    required
                                />
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    From Name
                                </label>
                                <input
                                    type="text"
                                    name="from_name"
                                    value={settings.from_name}
                                    onChange={handleInputChange}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="FanBet247"
                                    required
                                />
                            </div>
                        </div>
                    </div>

                    {/* Test Email Section */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Test Email</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Test Email Address
                                </label>
                                <input
                                    type="email"
                                    value={testEmail}
                                    onChange={(e) => setTestEmail(e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="<EMAIL>"
                                    required
                                />
                            </div>
                            <div className="flex items-end">
                                <button
                                    type="button"
                                    onClick={sendTestEmail}
                                    disabled={sendingTestEmail || !testEmail}
                                    className="flex items-center gap-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                                >
                                    <FaEnvelope />
                                    {sendingTestEmail ? 'Sending...' : 'Send Test Email'}
                                </button>
                            </div>
                        </div>
                        <p className="text-sm text-gray-600 mt-2">
                            Send a test email to verify your SMTP configuration is working correctly.
                        </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-between pt-6 border-t">
                        <button
                            type="button"
                            onClick={testConnection}
                            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                        >
                            Test Connection
                        </button>

                        <button
                            type="submit"
                            disabled={saving}
                            className="flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            <FaSave />
                            {saving ? 'Saving...' : 'Save SMTP Settings'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default SMTPSettings;
