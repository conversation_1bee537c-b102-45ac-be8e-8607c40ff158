import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { FaUsers, FaUserPlus, FaUserCheck, FaUserClock, FaEdit, FaSearch, FaEye, FaUserSlash, FaBan, FaTrophy, FaCoins, FaChartLine, FaCalendarAlt } from 'react-icons/fa';
import './UserManagement.css';
// import CustomModal from '../components/CustomModal';

const API_BASE_URL = '/backend';

function UserManagement() {
    const navigate = useNavigate();
    const [users, setUsers] = useState([]);
    const [teams, setTeams] = useState([]);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [editingUserId, setEditingUserId] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [usersPerPage] = useState(10);
    const [stats, setStats] = useState({
        totalUsers: 0,
        activeUsers: 0,
        newUsers: 0,
        pendingUsers: 0
    });
    const [editingUser, setEditingUser] = useState({
        username: '',
        full_name: '',
        email: '',
        favorite_team: '',
        balance: 0
    });
    const [showAddUserModal, setShowAddUserModal] = useState(false);
    const [showUserDetailsModal, setShowUserDetailsModal] = useState(false);
    const [selectedUserDetails, setSelectedUserDetails] = useState(null);
    const [loadingUserDetails, setLoadingUserDetails] = useState(false);
    const [newUser, setNewUser] = useState({
        username: '',
        full_name: '',
        email: '',
        password: '',
        favorite_team: '',
        balance: 0
    });

    // Modal states - temporarily disabled
    // const [modalState, setModalState] = useState({
    //     isOpen: false,
    //     type: 'confirm',
    //     title: '',
    //     message: '',
    //     onConfirm: null,
    //     confirmText: 'Confirm',
    //     confirmButtonColor: 'blue'
    // });

    useEffect(() => {
        fetchUsers();
        fetchTeams();
    }, []);

    const fetchUsers = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php`);
            if (response.data.success) {
                const userData = response.data.data || [];
                setUsers(userData);

                // Calculate stats
                const now = new Date();
                const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

                // For demo purposes, we'll simulate some stats
                const totalUsers = userData.length;
                const activeUsers = userData.filter(user => user.last_login && new Date(user.last_login) > oneWeekAgo).length || Math.floor(totalUsers * 0.7);
                const newUsers = userData.filter(user => user.created_at && new Date(user.created_at) > oneWeekAgo).length || Math.floor(totalUsers * 0.2);
                const pendingUsers = userData.filter(user => user.status === 'pending').length || Math.floor(totalUsers * 0.1);

                setStats({
                    totalUsers,
                    activeUsers,
                    newUsers,
                    pendingUsers
                });
            } else {
                setError(response.data.message || 'Failed to fetch users');
            }
        } catch (err) {
            setError('Failed to fetch users. Please check your network connection and try again.');
            console.error('Error fetching users:', err);
        }
    };

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            setTeams(response.data.data || []);
        } catch (err) {
            setError('Failed to fetch teams');
        }
    };

    const getTeamLogo = (teamName) => {
        const team = teams.find(team => team.name === teamName);
        return team ? `${API_BASE_URL}/${team.logo}` : null;
    };

    const getDefaultAvatar = () => {
        return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='40' height='40' viewBox='0 0 40 40'%3E%3Ccircle cx='20' cy='20' r='20' fill='%23e5e7eb'/%3E%3Cpath d='M20 20c3.3 0 6-2.7 6-6s-2.7-6-6-6-6 2.7-6 6 2.7 6 6 6zm0 3c-4 0-12 2-12 6v3h24v-3c0-4-8-6-12-6z' fill='%23374151'/%3E%3C/svg%3E";
    };

    const handleEdit = (user) => {
        setEditingUserId(user.user_id);
        setEditingUser(user);
    };

    const handleUpdate = async (e) => {
        e.preventDefault();
        try {
            const response = await axios.put(`${API_BASE_URL}/handlers/user_management.php?id=${editingUserId}`, editingUser);
            if (response.data.success) {
                setSuccess('User updated successfully!');
                fetchUsers();
                setEditingUserId(null);
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to update user');
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            setError('Failed to update user');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setEditingUser(prev => ({ ...prev, [name]: value }));
    };

    const handleNewUserInputChange = (e) => {
        const { name, value } = e.target;
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    const handleAddUser = async (e) => {
        e.preventDefault();
        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);
            if (response.data.success) {
                setSuccess('User added successfully!');
                fetchUsers();
                setShowAddUserModal(false);
                setNewUser({
                    username: '',
                    full_name: '',
                    email: '',
                    password: '',
                    favorite_team: '',
                    balance: 0
                });
                // Clear success message after 3 seconds
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to add user');
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            setError('Failed to add user');
            setTimeout(() => setError(''), 3000);
        }
    };

    const handleViewUserDetails = async (user) => {
        setLoadingUserDetails(true);
        setShowUserDetailsModal(true);
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/user_management.php?id=${user.user_id}`);
            if (response.data.success) {
                setSelectedUserDetails(response.data.data);
            } else {
                setError(response.data.message || 'Failed to fetch user details');
                setShowUserDetailsModal(false);
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            setError('Failed to fetch user details');
            setShowUserDetailsModal(false);
            setTimeout(() => setError(''), 3000);
            console.error('Error fetching user details:', err);
        } finally {
            setLoadingUserDetails(false);
        }
    };

    const handleSuspendUser = async (user) => {
        if (window.confirm(`Are you sure you want to suspend user "${user.username}"? This will prevent them from accessing their account.`)) {
            try {
                console.log('Suspending user:', user.user_id); // Debug log
                const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {
                    user_id: user.user_id,
                    action: 'suspend'
                });
                console.log('Suspend response:', response.data); // Debug log
                if (response.data.success) {
                    setSuccess(`User "${user.username}" suspended successfully!`);
                    fetchUsers();
                    setTimeout(() => setSuccess(''), 3000);
                } else {
                    setError(response.data.message || 'Failed to suspend user');
                    setTimeout(() => setError(''), 3000);
                }
            } catch (err) {
                console.error('Suspend error:', err); // Debug log
                setError('Failed to suspend user: ' + (err.response?.data?.message || err.message));
                setTimeout(() => setError(''), 3000);
            }
        }
    };

    const handleBanUser = async (user) => {
        if (window.confirm(`Are you sure you want to ban user "${user.username}"? This action cannot be undone and will permanently prevent them from accessing their account.`)) {
            try {
                console.log('Banning user:', user.user_id); // Debug log
                const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {
                    user_id: user.user_id,
                    action: 'ban'
                });
                console.log('Ban response:', response.data); // Debug log
                if (response.data.success) {
                    setSuccess(`User "${user.username}" banned successfully!`);
                    fetchUsers();
                    setTimeout(() => setSuccess(''), 3000);
                } else {
                    setError(response.data.message || 'Failed to ban user');
                    setTimeout(() => setError(''), 3000);
                }
            } catch (err) {
                console.error('Ban error:', err); // Debug log
                setError('Failed to ban user: ' + (err.response?.data?.message || err.message));
                setTimeout(() => setError(''), 3000);
            }
        }
    };

    // Search functionality
    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1); // Reset to first page when searching
    };

    // Filter users based on search term
    const filteredUsers = users.filter(user =>
        user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Pagination
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsers = filteredUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);

    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    // Generate page numbers
    const pageNumbers = [];
    for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Page Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800">User Management</h1>
                <p className="text-gray-600">Manage all users in the system</p>
            </div>

            {/* Notification Messages */}
            {error && (
                <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}
            {success && (
                <div className="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{success}</span>
                </div>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Total Users */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-blue-100 p-3 mr-4">
                        <FaUsers className="text-blue-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Total Users</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.totalUsers}</h3>
                    </div>
                </div>

                {/* Active Users */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-green-100 p-3 mr-4">
                        <FaUserCheck className="text-green-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Active Users</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.activeUsers}</h3>
                    </div>
                </div>

                {/* New Users */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-yellow-100 p-3 mr-4">
                        <FaUserPlus className="text-yellow-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">New Users (7d)</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.newUsers}</h3>
                    </div>
                </div>

                {/* Pending Users */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-purple-100 p-3 mr-4">
                        <FaUserClock className="text-purple-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Pending Users</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.pendingUsers}</h3>
                    </div>
                </div>
            </div>

            {/* Search and Filter */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <h2 className="text-lg font-semibold text-white bg-blue-600 px-4 py-2 rounded mb-4 md:mb-0">User List</h2>
                    <div className="flex flex-col md:flex-row md:items-center space-y-2 md:space-y-0 md:space-x-4">
                        <button
                            onClick={() => setShowAddUserModal(true)}
                            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                        >
                            <FaUserPlus className="mr-2" />
                            Add User
                        </button>
                        <div className="relative w-full md:w-64">
                            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaSearch className="h-4 w-4 text-gray-400" />
                            </div>
                            <input
                                type="text"
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                                placeholder="Search users..."
                                value={searchTerm}
                                onChange={handleSearch}
                            />
                        </div>
                    </div>
                </div>

                {/* Users Table */}
                <div className="user-table-container">
                    <table className="user-table">
                        <thead className="bg-blue-600">
                            <tr>
                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    #
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small">
                                    Avatar
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    Username
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium">
                                    Full Name
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-medium">
                                    Email
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-small">
                                    Favorite Team
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-hide-very-small">
                                    Balance
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider user-table-actions">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {currentUsers.map((user, index) => (
                                <tr key={user.user_id} className="hover:bg-gray-50">
                                    <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {indexOfFirstUser + index + 1}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap user-table-hide-small">
                                        <div className="flex items-center">
                                            <img
                                                src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}
                                                alt={user.favorite_team || 'Default Avatar'}
                                                className="w-10 h-10 rounded-full object-contain border border-gray-200"
                                                onError={(e) => {
                                                    e.target.src = getDefaultAvatar();
                                                }}
                                            />
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">{user.username}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                            user.status === 'banned' ? 'bg-red-100 text-red-800' :
                                            user.status === 'suspended' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-green-100 text-green-800'
                                        }`}>
                                            {user.status === 'banned' ? 'BANNED' :
                                             user.status === 'suspended' ? 'SUSPENDED' :
                                             'ACTIVE'}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap user-table-hide-medium">
                                        <div className="text-sm text-gray-500">{user.full_name}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap user-table-hide-medium">
                                        <div className="text-sm text-gray-500">{user.email}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap user-table-hide-small">
                                        <div className="flex items-center space-x-2">
                                            {user.favorite_team && getTeamLogo(user.favorite_team) && (
                                                <img
                                                    src={getTeamLogo(user.favorite_team)}
                                                    alt={user.favorite_team}
                                                    className="w-5 h-5 rounded-full object-contain"
                                                />
                                            )}
                                            <div className="text-sm text-gray-500">{user.favorite_team || 'No team selected'}</div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap user-table-hide-very-small">
                                        <div className="text-sm font-medium text-gray-900">{user.balance} FC</div>
                                    </td>
                                    <td className="px-4 py-4 whitespace-nowrap text-sm font-medium user-table-actions">
                                        <div className="flex space-x-1">
                                            <button
                                                onClick={() => handleViewUserDetails(user)}
                                                className="text-green-600 hover:text-green-900 hover:bg-green-50 p-1 rounded transition-colors"
                                                title="View Details"
                                            >
                                                <FaEye />
                                            </button>
                                            <button
                                                onClick={() => handleEdit(user)}
                                                className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-1 rounded transition-colors"
                                                title="Edit User"
                                            >
                                                <FaEdit />
                                            </button>
                                            <button
                                                onClick={() => handleSuspendUser(user)}
                                                className="text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 p-1 rounded transition-colors"
                                                title="Suspend User"
                                            >
                                                <FaUserSlash />
                                            </button>
                                            <button
                                                onClick={() => handleBanUser(user)}
                                                className="text-red-600 hover:text-red-900 hover:bg-red-50 p-1 rounded transition-colors"
                                                title="Ban User"
                                            >
                                                <FaBan />
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className="flex items-center justify-between border-t border-gray-200 px-4 py-3 sm:px-6 mt-4">
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing <span className="font-medium">{indexOfFirstUser + 1}</span> to{' '}
                                    <span className="font-medium">
                                        {indexOfLastUser > filteredUsers.length ? filteredUsers.length : indexOfLastUser}
                                    </span>{' '}
                                    of <span className="font-medium">{filteredUsers.length}</span> results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => paginate(currentPage > 1 ? currentPage - 1 : 1)}
                                        disabled={currentPage === 1}
                                        className={`relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium ${
                                            currentPage === 1 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                        }`}
                                    >
                                        <span className="sr-only">Previous</span>
                                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </button>

                                    {pageNumbers.map(number => (
                                        <button
                                            key={number}
                                            onClick={() => paginate(number)}
                                            className={`relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium ${
                                                currentPage === number
                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                    : 'text-gray-500 hover:bg-gray-50'
                                            }`}
                                        >
                                            {number}
                                        </button>
                                    ))}

                                    <button
                                        onClick={() => paginate(currentPage < totalPages ? currentPage + 1 : totalPages)}
                                        disabled={currentPage === totalPages}
                                        className={`relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium ${
                                            currentPage === totalPages ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                                        }`}
                                    >
                                        <span className="sr-only">Next</span>
                                        <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                        </svg>
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Edit User Modal */}
            {editingUserId && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
                        <div className="flex justify-between items-center p-4 border-b">
                            <h3 className="text-lg font-semibold text-gray-800">Edit User</h3>
                            <button
                                className="text-gray-500 hover:text-gray-700 text-2xl focus:outline-none"
                                onClick={() => setEditingUserId(null)}
                            >
                                ×
                            </button>
                        </div>

                        <form onSubmit={handleUpdate} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                    <input
                                        type="text"
                                        name="username"
                                        value={editingUser.username}
                                        onChange={handleInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                    <input
                                        type="text"
                                        name="full_name"
                                        value={editingUser.full_name}
                                        onChange={handleInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={editingUser.email}
                                        onChange={handleInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Favorite Team</label>
                                    <select
                                        name="favorite_team"
                                        value={editingUser.favorite_team}
                                        onChange={handleInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        required
                                    >
                                        <option value="">Select Favorite Team</option>
                                        {teams.map(team => (
                                            <option key={team.id} value={team.name}>{team.name}</option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Balance (FanCoins)</label>
                                    <input
                                        type="number"
                                        name="balance"
                                        value={editingUser.balance}
                                        onChange={handleInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                        required
                                    />
                                </div>
                            </div>

                            <div className="mt-6 flex justify-end space-x-3">
                                <button
                                    type="button"
                                    onClick={() => setEditingUserId(null)}
                                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                >
                                    Update User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Add User Modal */}
            {showAddUserModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
                        <div className="flex justify-between items-center p-4 border-b">
                            <h3 className="text-lg font-semibold text-gray-800">Add New User</h3>
                            <button
                                className="text-gray-500 hover:text-gray-700 text-2xl focus:outline-none"
                                onClick={() => setShowAddUserModal(false)}
                            >
                                ×
                            </button>
                        </div>

                        <form onSubmit={handleAddUser} className="p-6">
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                                    <input
                                        type="text"
                                        name="username"
                                        value={newUser.username}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                                    <input
                                        type="text"
                                        name="full_name"
                                        value={newUser.full_name}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                                    <input
                                        type="email"
                                        name="email"
                                        value={newUser.email}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                                    <input
                                        type="password"
                                        name="password"
                                        value={newUser.password}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        required
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Favorite Team</label>
                                    <select
                                        name="favorite_team"
                                        value={newUser.favorite_team}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    >
                                        <option value="">Select Favorite Team</option>
                                        {teams.map(team => (
                                            <option key={team.id} value={team.name}>{team.name}</option>
                                        ))}
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">Initial Balance (FanCoins)</label>
                                    <input
                                        type="number"
                                        name="balance"
                                        value={newUser.balance}
                                        onChange={handleNewUserInputChange}
                                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                        min="0"
                                    />
                                </div>
                            </div>

                            <div className="mt-6 flex justify-end space-x-3">
                                <button
                                    type="button"
                                    onClick={() => setShowAddUserModal(false)}
                                    className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                >
                                    Cancel
                                </button>
                                <button
                                    type="submit"
                                    className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                                >
                                    Add User
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            )}

            {/* Sports Card User Details Modal */}
            {showUserDetailsModal && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                    <div className="sports-card-modal w-full max-w-4xl">
                        {/* Sports Card Header */}
                        <div className="sports-card-header">
                            <button
                                className="absolute top-4 right-4 text-white hover:text-gray-200 text-2xl focus:outline-none z-20 w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-colors"
                                onClick={() => {
                                    setShowUserDetailsModal(false);
                                    setSelectedUserDetails(null);
                                }}
                            >
                                ×
                            </button>

                            {loadingUserDetails ? (
                                <div className="flex flex-col items-center">
                                    <div className="loading-spinner mb-4"></div>
                                    <span className="text-lg">Loading player stats...</span>
                                </div>
                            ) : selectedUserDetails ? (
                                <div className="sports-card-header-content">
                                    <div className="sports-card-avatar">
                                        <img
                                            src={getTeamLogo(selectedUserDetails.favorite_team) || getDefaultAvatar()}
                                            alt={selectedUserDetails.favorite_team || 'Player Avatar'}
                                            onError={(e) => {
                                                e.target.src = getDefaultAvatar();
                                            }}
                                        />
                                    </div>
                                    <div className="sports-card-info">
                                        <div className="sports-card-name">{selectedUserDetails.full_name}</div>
                                        <div className="sports-card-username">@{selectedUserDetails.username}</div>
                                        <div className={`sports-card-status ${
                                            selectedUserDetails.status === 'banned' ? 'status-banned' :
                                            selectedUserDetails.status === 'suspended' ? 'status-suspended' :
                                            'status-active'
                                        }`}>
                                            {selectedUserDetails.status?.toUpperCase() || 'ACTIVE'} PLAYER
                                        </div>
                                        {selectedUserDetails.favorite_team && (
                                            <div className="sports-card-team">
                                                <span className="team-label">Team:</span> {selectedUserDetails.favorite_team}
                                            </div>
                                        )}
                                    </div>
                                </div>
                            ) : null}
                        </div>

                        {/* Sports Card Body */}
                        <div className="sports-card-body">
                            {!loadingUserDetails && selectedUserDetails && (
                                <div className="space-y-4">
                                    {/* Performance Stats Grid */}
                                    <div className="performance-grid">
                                        <div className="performance-stat">
                                            <div className="performance-number text-blue-600">{selectedUserDetails.total_bets || 0}</div>
                                            <div className="performance-label">Total Bets</div>
                                        </div>
                                        <div className="performance-stat">
                                            <div className="performance-number text-green-600">{selectedUserDetails.wins || 0}</div>
                                            <div className="performance-label">Wins</div>
                                        </div>
                                        <div className="performance-stat">
                                            <div className="performance-number text-yellow-600">{selectedUserDetails.draws || 0}</div>
                                            <div className="performance-label">Draws</div>
                                        </div>
                                        <div className="performance-stat">
                                            <div className="performance-number text-red-600">{selectedUserDetails.losses || 0}</div>
                                            <div className="performance-label">Losses</div>
                                        </div>
                                    </div>

                                    {/* User Information Summary */}
                                    <div className="user-info-summary">
                                        <div className="user-info-row">
                                            <span className="user-info-label">Player ID</span>
                                            <span className="user-info-value">#{selectedUserDetails.user_id}</span>
                                        </div>
                                        <div className="user-info-row">
                                            <span className="user-info-label">Email</span>
                                            <span className="user-info-value">{selectedUserDetails.email}</span>
                                        </div>
                                        <div className="user-info-row">
                                            <span className="user-info-label">Role</span>
                                            <span className="user-info-value capitalize">{selectedUserDetails.role}</span>
                                        </div>
                                        <div className="user-info-row">
                                            <span className="user-info-label">Favorite Team</span>
                                            <span className="user-info-value">{selectedUserDetails.favorite_team || 'None'}</span>
                                        </div>
                                        <div className="user-info-row">
                                            <span className="user-info-label">League Status</span>
                                            <span className="user-info-value">
                                                {selectedUserDetails.active_league_memberships > 0
                                                    ? `Member of ${selectedUserDetails.active_league_memberships} league${selectedUserDetails.active_league_memberships > 1 ? 's' : ''}`
                                                    : 'No active leagues'
                                                }
                                            </span>
                                        </div>
                                        {selectedUserDetails.league_names && (
                                            <div className="user-info-row">
                                                <span className="user-info-label">Active Leagues</span>
                                                <span className="user-info-value">{selectedUserDetails.league_names}</span>
                                            </div>
                                        )}
                                    </div>

                                    {/* Compact Stats Grid */}
                                    <div className="compact-stats-grid">
                                        {/* Financial & Points */}
                                        <div className="compact-stat-card">
                                            <div className="compact-stat-header">
                                                <div className="compact-stat-title">Financial & Points</div>
                                                <FaCoins className="compact-stat-icon" />
                                            </div>
                                            <div className="compact-stats-row">
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Balance</div>
                                                    <div className="compact-stat-value positive">{selectedUserDetails.balance} FC</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Current Points</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.points || 0}</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Total Points</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.total_points || 0}</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Active Leagues</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.active_league_memberships || 0}</div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Performance & Streaks */}
                                        <div className="compact-stat-card">
                                            <div className="compact-stat-header">
                                                <div className="compact-stat-title">Performance</div>
                                                <FaTrophy className="compact-stat-icon" />
                                            </div>
                                            <div className="compact-stats-row">
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Win Rate</div>
                                                    <div className="compact-stat-value">
                                                        {selectedUserDetails.total_bets > 0
                                                            ? `${Math.round((selectedUserDetails.wins / selectedUserDetails.total_bets) * 100)}%`
                                                            : '0%'
                                                        }
                                                    </div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Current Streak</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.current_streak || 0}</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Best Streak</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.highest_streak || 0}</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Bets Created</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.total_bets_created || 0}</div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Activity & Dates */}
                                        <div className="compact-stat-card">
                                            <div className="compact-stat-header">
                                                <div className="compact-stat-title">Activity</div>
                                                <FaCalendarAlt className="compact-stat-icon" />
                                            </div>
                                            <div className="compact-stats-row">
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Joined</div>
                                                    <div className="compact-stat-value">{new Date(selectedUserDetails.created_at).toLocaleDateString()}</div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Last Active</div>
                                                    <div className="compact-stat-value">
                                                        {selectedUserDetails.last_active ?
                                                            new Date(selectedUserDetails.last_active).toLocaleDateString() :
                                                            'Never'
                                                        }
                                                    </div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Last Bet</div>
                                                    <div className="compact-stat-value">
                                                        {selectedUserDetails.last_bet_date ?
                                                            new Date(selectedUserDetails.last_bet_date).toLocaleDateString() :
                                                            'Never'
                                                        }
                                                    </div>
                                                </div>
                                                <div className="compact-stat-item">
                                                    <div className="compact-stat-label">Bets Joined</div>
                                                    <div className="compact-stat-value">{selectedUserDetails.total_bets_joined || 0}</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            )}
                            {/* Modal Footer */}
                            <div className="flex justify-center p-6 bg-gray-50 border-t border-gray-200 mt-4">
                                <button
                                    onClick={() => {
                                        setShowUserDetailsModal(false);
                                        setSelectedUserDetails(null);
                                    }}
                                    className="px-8 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium shadow-lg"
                                    style={{ backgroundColor: '#166534' }}
                                >
                                    Close Player Card
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Custom Modal - Temporarily disabled */}
            {/*
            <CustomModal
                isOpen={modalState.isOpen}
                onClose={() => setModalState({ ...modalState, isOpen: false })}
                onConfirm={modalState.onConfirm}
                title={modalState.title}
                message={modalState.message}
                type={modalState.type}
                confirmText={modalState.confirmText}
                confirmButtonColor={modalState.confirmButtonColor}
            />
            */}
        </div>
    );
}
export default UserManagement;