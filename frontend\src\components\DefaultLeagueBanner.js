import React from 'react';
import { FaFootballBall, FaTrophy } from 'react-icons/fa';

const DefaultLeagueBanner = ({ name, color = '#0369a1', height = 120 }) => {
  // Generate a lighter shade of the base color for the pattern
  const lighterColor = color === '#0369a1' 
    ? '#0284c7' 
    : color.startsWith('#') 
      ? `${color}99` // Add 60% opacity
      : color;

  return (
    <svg 
      width="100%" 
      height={height} 
      viewBox="0 0 800 200" 
      xmlns="http://www.w3.org/2000/svg"
      style={{ backgroundColor: color }}
    >
      {/* Background Pattern */}
      <defs>
        <pattern id="footballPattern" patternUnits="userSpaceOnUse" width="60" height="60" patternTransform="rotate(45)">
          <circle cx="10" cy="10" r="8" fill={lighterColor} opacity="0.2" />
          <path d="M30 30 L40 20 L50 30 L40 40 Z" fill={lighterColor} opacity="0.15" />
        </pattern>
      </defs>
      
      <rect width="100%" height="100%" fill={`url(#footballPattern)`} />
      
      {/* League Name */}
      <text 
        x="50%" 
        y="50%" 
        fontFamily="Arial, sans-serif" 
        fontSize="28" 
        fontWeight="bold" 
        fill="white" 
        textAnchor="middle" 
        dominantBaseline="middle"
      >
        {name || 'Football League'}
      </text>
      
      {/* Decorative Elements */}
      <g transform="translate(150, 100) scale(0.8)" opacity="0.2" fill="white">
        <path d="M25,2.5C12.2,2.5,1.9,12.8,1.9,25.6s10.3,23.1,23.1,23.1s23.1-10.3,23.1-23.1S37.8,2.5,25,2.5z M40.7,26.9 c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.2l-10.2,5.8c-0.2,0.1-0.4,0.1-0.6,0.1c-0.4,0-0.8-0.2-1-0.6l-5.8-10.2 c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.4c0-0.1,0-0.3,0-0.4c0-0.1,0.1-0.3,0.1-0.4l5.8-10.2c0.3-0.5,0.9-0.7,1.4-0.5 l10.2,5.8c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.2,0.2,0.3c0.1,0.1,0.1,0.3,0.1,0.4c0,0.1,0,0.3,0,0.4l-0.1,0.4 c0,0.1-0.1,0.3-0.1,0.4L40.7,26.9z" />
      </g>
      
      <g transform="translate(600, 100) scale(0.8)" opacity="0.2" fill="white">
        <path d="M25,2.5C12.2,2.5,1.9,12.8,1.9,25.6s10.3,23.1,23.1,23.1s23.1-10.3,23.1-23.1S37.8,2.5,25,2.5z M40.7,26.9 c-0.1,0.1-0.1,0.2-0.2,0.3c-0.1,0.1-0.2,0.1-0.3,0.2l-10.2,5.8c-0.2,0.1-0.4,0.1-0.6,0.1c-0.4,0-0.8-0.2-1-0.6l-5.8-10.2 c-0.1-0.1-0.1-0.2-0.2-0.3c-0.1-0.1-0.1-0.2-0.1-0.4c0-0.1,0-0.3,0-0.4c0-0.1,0.1-0.3,0.1-0.4l5.8-10.2c0.3-0.5,0.9-0.7,1.4-0.5 l10.2,5.8c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0.1,0.2,0.2,0.2,0.3c0.1,0.1,0.1,0.3,0.1,0.4c0,0.1,0,0.3,0,0.4l-0.1,0.4 c0,0.1-0.1,0.3-0.1,0.4L40.7,26.9z" />
      </g>
      
      {/* Trophy Icon */}
      <g transform="translate(400, 160) scale(0.6)" opacity="0.3" fill="white">
        <path d="M12.5,1.9c0-1,0.8-1.9,1.9-1.9h21.2c1,0,1.9,0.8,1.9,1.9v3.8c0,1-0.8,1.9-1.9,1.9h-1.9v5.8c3.8,1.9,6.7,5.8,6.7,10.6 c0,6.7-5.8,12.5-12.5,12.5h-3.8c-6.7,0-12.5-5.8-12.5-12.5c0-4.8,2.9-8.7,6.7-10.6V7.7h-1.9c-1,0-1.9-0.8-1.9-1.9V1.9z M17.3,7.7 h15.4v5.8c0,1-0.8,1.9-1.9,1.9c-1,0-1.9-0.8-1.9-1.9V9.6h-1.9v3.8c0,1-0.8,1.9-1.9,1.9c-1,0-1.9-0.8-1.9-1.9V9.6h-1.9v3.8 c0,1-0.8,1.9-1.9,1.9c-1,0-1.9-0.8-1.9-1.9V7.7z M25,32.7c3.8,0,6.7-2.9,6.7-6.7c0-3.8-2.9-6.7-6.7-6.7c-3.8,0-6.7,2.9-6.7,6.7 C18.3,29.8,21.2,32.7,25,32.7z" />
      </g>
    </svg>
  );
};

export default DefaultLeagueBanner;
