{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\TestLogin.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TestLogin = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    usernameOrEmail: '',\n    password: ''\n  });\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    setError(''); // Clear error when user types\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      console.log('🔄 Attempting login with:', formData.usernameOrEmail);\n      const response = await axios.post('simple_user_login.php', formData);\n      console.log('✅ Login response:', response.data);\n      if (response.data.success) {\n        // Store user data in localStorage\n        localStorage.setItem('user', JSON.stringify(response.data.user));\n        localStorage.setItem('isLoggedIn', 'true');\n        console.log('🎯 Redirecting to UserDashboard...');\n\n        // Redirect to UserDashboard\n        navigate('/user-dashboard');\n      } else {\n        setError(response.data.message || 'Login failed');\n      }\n    } catch (error) {\n      console.error('❌ Login error:', error);\n      setError('Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testCredentials = [{\n    username: 'demohomexx',\n    password: 'loving12'\n  }, {\n    username: 'testuser',\n    password: 'testpass123'\n  }, {\n    username: 'lilwayne',\n    password: 'loving12'\n  }, {\n    username: 'jameslink01',\n    password: 'loving12'\n  }, {\n    username: 'Bobyanka01',\n    password: 'loving12'\n  }];\n  const fillTestCredentials = (username, password) => {\n    setFormData({\n      usernameOrEmail: username,\n      password: password\n    });\n    setError('');\n  };\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Test Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Simple login system for testing UserDashboard redirect\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-form-container\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSubmit,\n          className: \"auth-form\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Email or Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"usernameOrEmail\",\n                value: formData.usernameOrEmail,\n                onChange: handleChange,\n                placeholder: \"Enter your email or username\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"input-container\",\n              children: /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleChange,\n                placeholder: \"Enter your password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 102,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"auth-button\",\n            disabled: loading,\n            children: loading ? 'Signing In...' : 'Sign In'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 84,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"test-credentials\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"Test Credentials (Click to Fill)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"credentials-grid\",\n            children: testCredentials.map((cred, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"button\",\n              className: \"credential-button\",\n              onClick: () => fillTestCredentials(cred.username, cred.password),\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: cred.username\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                children: cred.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 134,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 33\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"auth-links\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Want to test the original login?\", /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/login\",\n              children: \" Go to Original Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n                .test-credentials {\n                    margin-top: 2rem;\n                    padding: 1rem;\n                    background: #f8f9fa;\n                    border-radius: 8px;\n                    border: 1px solid #e9ecef;\n                }\n\n                .test-credentials h3 {\n                    margin: 0 0 1rem 0;\n                    color: #495057;\n                    font-size: 1rem;\n                }\n\n                .credentials-grid {\n                    display: grid;\n                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n                    gap: 0.5rem;\n                }\n\n                .credential-button {\n                    padding: 0.75rem;\n                    background: white;\n                    border: 1px solid #dee2e6;\n                    border-radius: 4px;\n                    cursor: pointer;\n                    transition: all 0.2s;\n                    text-align: center;\n                }\n\n                .credential-button:hover {\n                    background: #e9ecef;\n                    border-color: #adb5bd;\n                }\n\n                .credential-button strong {\n                    color: #495057;\n                }\n\n                .credential-button small {\n                    color: #6c757d;\n                }\n            `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 149,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 9\n  }, this);\n};\n_s(TestLogin, \"MndtFvY1b4FRe9yEkR0w22N9puU=\", false, function () {\n  return [useNavigate];\n});\n_c = TestLogin;\nexport default TestLogin;\nvar _c;\n$RefreshReg$(_c, \"TestLogin\");", "map": {"version": 3, "names": ["React", "useState", "axios", "useNavigate", "UserAuthLayout", "jsxDEV", "_jsxDEV", "TestLogin", "_s", "formData", "setFormData", "usernameOrEmail", "password", "error", "setError", "loading", "setLoading", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "console", "log", "response", "post", "data", "success", "localStorage", "setItem", "JSON", "stringify", "user", "message", "testCredentials", "username", "fillTestCredentials", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "type", "onChange", "placeholder", "required", "disabled", "map", "cred", "index", "onClick", "href", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/TestLogin.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport axios from '../utils/axiosConfig';\nimport { useNavigate } from 'react-router-dom';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport '../styles/UserAuth.css';\n\nconst TestLogin = () => {\n    const [formData, setFormData] = useState({\n        usernameOrEmail: '',\n        password: ''\n    });\n    const [error, setError] = useState('');\n    const [loading, setLoading] = useState(false);\n    const navigate = useNavigate();\n\n    const handleChange = (e) => {\n        setFormData({\n            ...formData,\n            [e.target.name]: e.target.value\n        });\n        setError(''); // Clear error when user types\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setLoading(true);\n        setError('');\n\n        try {\n            console.log('🔄 Attempting login with:', formData.usernameOrEmail);\n            \n            const response = await axios.post('simple_user_login.php', formData);\n            \n            console.log('✅ Login response:', response.data);\n\n            if (response.data.success) {\n                // Store user data in localStorage\n                localStorage.setItem('user', JSON.stringify(response.data.user));\n                localStorage.setItem('isLoggedIn', 'true');\n                \n                console.log('🎯 Redirecting to UserDashboard...');\n                \n                // Redirect to UserDashboard\n                navigate('/user-dashboard');\n            } else {\n                setError(response.data.message || 'Login failed');\n            }\n        } catch (error) {\n            console.error('❌ Login error:', error);\n            setError('Login failed. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const testCredentials = [\n        { username: 'demohomexx', password: 'loving12' },\n        { username: 'testuser', password: 'testpass123' },\n        { username: 'lilwayne', password: 'loving12' },\n        { username: 'jameslink01', password: 'loving12' },\n        { username: 'Bobyanka01', password: 'loving12' }\n    ];\n\n    const fillTestCredentials = (username, password) => {\n        setFormData({ usernameOrEmail: username, password: password });\n        setError('');\n    };\n\n    return (\n        <UserAuthLayout>\n            <div className=\"auth-container\">\n                <div className=\"auth-header\">\n                    <h1>Test Login</h1>\n                    <p>Simple login system for testing UserDashboard redirect</p>\n                </div>\n\n                <div className=\"auth-form-container\">\n                    {error && (\n                        <div className=\"error-message\">\n                            {error}\n                        </div>\n                    )}\n\n                    <form onSubmit={handleSubmit} className=\"auth-form\">\n                        <div className=\"form-group\">\n                            <label>Email or Username</label>\n                            <div className=\"input-container\">\n                                <input\n                                    type=\"text\"\n                                    name=\"usernameOrEmail\"\n                                    value={formData.usernameOrEmail}\n                                    onChange={handleChange}\n                                    placeholder=\"Enter your email or username\"\n                                    required\n                                />\n                            </div>\n                        </div>\n\n                        <div className=\"form-group\">\n                            <label>Password</label>\n                            <div className=\"input-container\">\n                                <input\n                                    type=\"password\"\n                                    name=\"password\"\n                                    value={formData.password}\n                                    onChange={handleChange}\n                                    placeholder=\"Enter your password\"\n                                    required\n                                />\n                            </div>\n                        </div>\n\n                        <button \n                            type=\"submit\" \n                            className=\"auth-button\"\n                            disabled={loading}\n                        >\n                            {loading ? 'Signing In...' : 'Sign In'}\n                        </button>\n                    </form>\n\n                    <div className=\"test-credentials\">\n                        <h3>Test Credentials (Click to Fill)</h3>\n                        <div className=\"credentials-grid\">\n                            {testCredentials.map((cred, index) => (\n                                <button\n                                    key={index}\n                                    type=\"button\"\n                                    className=\"credential-button\"\n                                    onClick={() => fillTestCredentials(cred.username, cred.password)}\n                                >\n                                    <strong>{cred.username}</strong>\n                                    <br />\n                                    <small>{cred.password}</small>\n                                </button>\n                            ))}\n                        </div>\n                    </div>\n\n                    <div className=\"auth-links\">\n                        <p>\n                            Want to test the original login? \n                            <a href=\"/login\"> Go to Original Login</a>\n                        </p>\n                    </div>\n                </div>\n            </div>\n\n            <style jsx>{`\n                .test-credentials {\n                    margin-top: 2rem;\n                    padding: 1rem;\n                    background: #f8f9fa;\n                    border-radius: 8px;\n                    border: 1px solid #e9ecef;\n                }\n\n                .test-credentials h3 {\n                    margin: 0 0 1rem 0;\n                    color: #495057;\n                    font-size: 1rem;\n                }\n\n                .credentials-grid {\n                    display: grid;\n                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n                    gap: 0.5rem;\n                }\n\n                .credential-button {\n                    padding: 0.75rem;\n                    background: white;\n                    border: 1px solid #dee2e6;\n                    border-radius: 4px;\n                    cursor: pointer;\n                    transition: all 0.2s;\n                    text-align: center;\n                }\n\n                .credential-button:hover {\n                    background: #e9ecef;\n                    border-color: #adb5bd;\n                }\n\n                .credential-button strong {\n                    color: #495057;\n                }\n\n                .credential-button small {\n                    color: #6c757d;\n                }\n            `}</style>\n        </UserAuthLayout>\n    );\n};\n\nexport default TestLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,KAAK,MAAM,sBAAsB;AACxC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,cAAc,MAAM,8BAA8B;AACzD,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAC;IACrCU,eAAe,EAAE,EAAE;IACnBC,QAAQ,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgB,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAE9B,MAAMe,YAAY,GAAIC,CAAC,IAAK;IACxBT,WAAW,CAAC;MACR,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC9B,CAAC,CAAC;IACFR,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;EAClB,CAAC;EAED,MAAMS,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAC9BA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBR,UAAU,CAAC,IAAI,CAAC;IAChBF,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACAW,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEjB,QAAQ,CAACE,eAAe,CAAC;MAElE,MAAMgB,QAAQ,GAAG,MAAMzB,KAAK,CAAC0B,IAAI,CAAC,uBAAuB,EAAEnB,QAAQ,CAAC;MAEpEgB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEC,QAAQ,CAACE,IAAI,CAAC;MAE/C,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB;QACAC,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACP,QAAQ,CAACE,IAAI,CAACM,IAAI,CAAC,CAAC;QAChEJ,YAAY,CAACC,OAAO,CAAC,YAAY,EAAE,MAAM,CAAC;QAE1CP,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;;QAEjD;QACAT,QAAQ,CAAC,iBAAiB,CAAC;MAC/B,CAAC,MAAM;QACHH,QAAQ,CAACa,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,cAAc,CAAC;MACrD;IACJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACZY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACtCC,QAAQ,CAAC,iCAAiC,CAAC;IAC/C,CAAC,SAAS;MACNE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqB,eAAe,GAAG,CACpB;IAAEC,QAAQ,EAAE,YAAY;IAAE1B,QAAQ,EAAE;EAAW,CAAC,EAChD;IAAE0B,QAAQ,EAAE,UAAU;IAAE1B,QAAQ,EAAE;EAAc,CAAC,EACjD;IAAE0B,QAAQ,EAAE,UAAU;IAAE1B,QAAQ,EAAE;EAAW,CAAC,EAC9C;IAAE0B,QAAQ,EAAE,aAAa;IAAE1B,QAAQ,EAAE;EAAW,CAAC,EACjD;IAAE0B,QAAQ,EAAE,YAAY;IAAE1B,QAAQ,EAAE;EAAW,CAAC,CACnD;EAED,MAAM2B,mBAAmB,GAAGA,CAACD,QAAQ,EAAE1B,QAAQ,KAAK;IAChDF,WAAW,CAAC;MAAEC,eAAe,EAAE2B,QAAQ;MAAE1B,QAAQ,EAAEA;IAAS,CAAC,CAAC;IAC9DE,QAAQ,CAAC,EAAE,CAAC;EAChB,CAAC;EAED,oBACIR,OAAA,CAACF,cAAc;IAAAoC,QAAA,gBACXlC,OAAA;MAAKmC,SAAS,EAAC,gBAAgB;MAAAD,QAAA,gBAC3BlC,OAAA;QAAKmC,SAAS,EAAC,aAAa;QAAAD,QAAA,gBACxBlC,OAAA;UAAAkC,QAAA,EAAI;QAAU;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnBvC,OAAA;UAAAkC,QAAA,EAAG;QAAsD;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eAENvC,OAAA;QAAKmC,SAAS,EAAC,qBAAqB;QAAAD,QAAA,GAC/B3B,KAAK,iBACFP,OAAA;UAAKmC,SAAS,EAAC,eAAe;UAAAD,QAAA,EACzB3B;QAAK;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CACR,eAEDvC,OAAA;UAAMwC,QAAQ,EAAEvB,YAAa;UAACkB,SAAS,EAAC,WAAW;UAAAD,QAAA,gBAC/ClC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBlC,OAAA;cAAAkC,QAAA,EAAO;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChCvC,OAAA;cAAKmC,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC5BlC,OAAA;gBACIyC,IAAI,EAAC,MAAM;gBACX1B,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEb,QAAQ,CAACE,eAAgB;gBAChCqC,QAAQ,EAAE9B,YAAa;gBACvB+B,WAAW,EAAC,8BAA8B;gBAC1CC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENvC,OAAA;YAAKmC,SAAS,EAAC,YAAY;YAAAD,QAAA,gBACvBlC,OAAA;cAAAkC,QAAA,EAAO;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvBvC,OAAA;cAAKmC,SAAS,EAAC,iBAAiB;cAAAD,QAAA,eAC5BlC,OAAA;gBACIyC,IAAI,EAAC,UAAU;gBACf1B,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;gBACzBoC,QAAQ,EAAE9B,YAAa;gBACvB+B,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;cAAA;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENvC,OAAA;YACIyC,IAAI,EAAC,QAAQ;YACbN,SAAS,EAAC,aAAa;YACvBU,QAAQ,EAAEpC,OAAQ;YAAAyB,QAAA,EAEjBzB,OAAO,GAAG,eAAe,GAAG;UAAS;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAEPvC,OAAA;UAAKmC,SAAS,EAAC,kBAAkB;UAAAD,QAAA,gBAC7BlC,OAAA;YAAAkC,QAAA,EAAI;UAAgC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCvC,OAAA;YAAKmC,SAAS,EAAC,kBAAkB;YAAAD,QAAA,EAC5BH,eAAe,CAACe,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BhD,OAAA;cAEIyC,IAAI,EAAC,QAAQ;cACbN,SAAS,EAAC,mBAAmB;cAC7Bc,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAACc,IAAI,CAACf,QAAQ,EAAEe,IAAI,CAACzC,QAAQ,CAAE;cAAA4B,QAAA,gBAEjElC,OAAA;gBAAAkC,QAAA,EAASa,IAAI,CAACf;cAAQ;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAChCvC,OAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNvC,OAAA;gBAAAkC,QAAA,EAAQa,IAAI,CAACzC;cAAQ;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA,GAPzBS,KAAK;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQN,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENvC,OAAA;UAAKmC,SAAS,EAAC,YAAY;UAAAD,QAAA,eACvBlC,OAAA;YAAAkC,QAAA,GAAG,kCAEC,eAAAlC,OAAA;cAAGkD,IAAI,EAAC,QAAQ;cAAAhB,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAENvC,OAAA;MAAOmD,GAAG;MAAAjB,QAAA,EAAE;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAa;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEzB,CAAC;AAACrC,EAAA,CA5LID,SAAS;EAAA,QAOMJ,WAAW;AAAA;AAAAuD,EAAA,GAP1BnD,SAAS;AA8Lf,eAAeA,SAAS;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}