import React from 'react';
import { useNavigate } from 'react-router-dom';
import Countdown from 'react-countdown';

// Countdown renderer component - more compact version
const CountdownRenderer = ({ days, hours, minutes, seconds, completed }) => {
  if (completed) {
    return <span className="countdown-completed">Completed</span>;
  }

  return (
    <div className="countdown">
      {days > 0 && <span>{days}d</span>}
      {hours > 0 && <span>{hours}h</span>}
      <span>{minutes}m</span>
      <span>{seconds}s</span>
    </div>
  );
};

const getMatchStatus = (startTime, endTime) => {
  try {
    const now = new Date();
    const start = new Date(startTime);
    const end = new Date(endTime);

    if (now < start) {
      return 'upcoming';
    } else if (now >= start && now <= end) {
      return 'live';
    } else {
      return 'expired';
    }
  } catch (error) {
    console.error('Error calculating match status:', error);
    return 'unknown';
  }
};

const EmptyState = ({ message }) => (
  <div className="empty-state">
    <img
      src="/empty-challenges.png"
      alt="No data"
      className="empty-state__image"
      onError={(e) => e.target.style.display = 'none'}
    />
    <p className="empty-state__message">{message}</p>
  </div>
);

const ChallengesList = ({
  recentChallenges,
  loading,
  isLoggedIn,
  API_BASE_URL
}) => {
  const navigate = useNavigate();

  const handleBetClick = (challengeId) => {
    if (!isLoggedIn) {
      sessionStorage.setItem('redirectAfterLogin', `/user/join-challenge/${challengeId}`);
      navigate('/login');
    } else {
      navigate(`/user/join-challenge/${challengeId}`);
    }
  };

  if (loading) {
    return <div className="loading">Loading challenges...</div>;
  }

  if (recentChallenges.length === 0) {
    return <EmptyState message="No active challenges at the moment. Check back later!" />;
  }

  return (
    <div className="challenges-list">
      <div className="challenge-list-header">
        <div className="header-match">MATCH</div>
        <div className="header-odds">ODDS</div>
        <div className="header-action">ACTION</div>
      </div>

      {recentChallenges.length > 0 ? (
        <div className="sport-list">
          {recentChallenges.map(challenge => {
            const matchStatus = getMatchStatus(challenge.start_time, challenge.end_time);
            const isLive = matchStatus === 'live';
            const isCompleted = matchStatus === 'expired';

            return (
              <div key={challenge.challenge_id} className={`challenge-card ${isLive ? 'live-match' : ''}`}>
                <div className="match-container">
                  {isLive && <div className="live-indicator"><span className="live-dot"></span>LIVE</div>}

                  <div className="teams-container">
                    <div className="team team-a">
                      <img
                        src={`${API_BASE_URL}/${challenge.team_a_logo}`}
                        alt={challenge.team_a}
                        className="team-logo"
                        onError={(e) => {
                          e.target.src = '/default-team-logo.png';
                        }}
                      />
                      <span className="team-name">{challenge.team_a}</span>
                    </div>

                    <div className="vs-container">
                      <span className="vs-text">VS</span>
                    </div>

                    <div className="team team-b">
                      <img
                        src={`${API_BASE_URL}/${challenge.team_b_logo}`}
                        alt={challenge.team_b}
                        className="team-logo"
                        onError={(e) => {
                          e.target.src = '/default-team-logo.png';
                        }}
                      />
                      <span className="team-name">{challenge.team_b}</span>
                    </div>
                  </div>

                  <div className="match-details">
                    <span className={`match-type ${challenge.match_type}`}>
                      {challenge.match_type === 'full_time' || challenge.match_type === 'FT' ? 'Full Time' :
                       challenge.match_type === 'half_time' || challenge.match_type === 'HT' ? 'Half Time' : 'Full Time'}
                    </span>

                    <div className="match-time">
                      {isLive ? (
                        <span className="in-progress">In Progress</span>
                      ) : isCompleted ? (
                        <span className="ended">Ended</span>
                      ) : (
                        <Countdown
                          date={new Date(challenge.start_time)}
                          renderer={CountdownRenderer}
                        />
                      )}
                    </div>
                  </div>
                </div>

                <div className="odds-container">
                  <div className="odds-header">
                    <span className="odds-label">Home</span>
                    <span className="odds-label">Draw</span>
                    <span className="odds-label">Away</span>
                  </div>
                  <div className="odds-values">
                    <div className="odds-item home">
                      <span className="odds-value">{Number(challenge.odds_team_a).toFixed(2)}</span>
                    </div>
                    <div className="odds-item draw">
                      <span className="odds-value">{Number(challenge.odds_draw).toFixed(2)}</span>
                    </div>
                    <div className="odds-item away">
                      <span className="odds-value">{Number(challenge.odds_team_b).toFixed(2)}</span>
                    </div>
                  </div>
                </div>

                <div className="action-container">
                  <button
                    className={`action-button ${isLoggedIn ? 'place-bet' : 'login'}`}
                    onClick={() => handleBetClick(challenge.challenge_id)}
                  >
                    <i className="fas fa-sign-in-alt"></i> {isLoggedIn ? 'BET' : 'LOGIN'}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <EmptyState message="No active challenges at the moment. Check back later!" />
      )}
    </div>
  );
};

export default ChallengesList;
