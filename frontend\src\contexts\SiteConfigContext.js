import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import { API_BASE_URL } from '../config';

const SiteConfigContext = createContext();

export const useSiteConfig = () => {
    const context = useContext(SiteConfigContext);
    if (!context) {
        throw new Error('useSiteConfig must be used within a SiteConfigProvider');
    }
    return context;
};

export const SiteConfigProvider = ({ children }) => {
    const [config, setConfig] = useState({
        site_name: 'FanBet247',
        site_logo: 'uploads/logo/fanbet247_logo.svg',
        contact_email: '<EMAIL>',
        footer_text: '© 2024 FanBet247. All rights reserved.'
    });
    const [loading, setLoading] = useState(true);

    const fetchConfig = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/get_site_config.php`);
            if (response.data.success) {
                setConfig(response.data.config);
            }
        } catch (error) {
            console.error('Failed to fetch site config:', error);
            // Keep default values if fetch fails
        } finally {
            setLoading(false);
        }
    };

    const updateConfig = (newConfig) => {
        setConfig(prev => ({ ...prev, ...newConfig }));
    };

    useEffect(() => {
        fetchConfig();
    }, []);

    const value = {
        config,
        loading,
        updateConfig,
        refreshConfig: fetchConfig
    };

    return (
        <SiteConfigContext.Provider value={value}>
            {children}
        </SiteConfigContext.Provider>
    );
};

export default SiteConfigContext;
