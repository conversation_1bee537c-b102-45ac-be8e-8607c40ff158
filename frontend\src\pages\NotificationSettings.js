import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaBell, FaCheck, FaTimes, FaSave } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function NotificationSettings() {
    const [settings, setSettings] = useState({
        email_notifications_enabled: 'true',
        bet_notifications: 'true',
        challenge_notifications: 'true',
        league_notifications: 'true',
        admin_notifications: 'true',
        notification_frequency: 'immediate'
    });
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    useEffect(() => {
        fetchSettings();
    }, []);

    const fetchSettings = async () => {
        try {
            setLoading(true);
            const response = await axios.get(`${API_BASE_URL}/handlers/get_notification_settings.php`);
            
            if (response.data.success && response.data.settings) {
                const settingsData = {};
                Object.keys(response.data.settings).forEach(key => {
                    settingsData[key] = response.data.settings[key].value;
                });
                setSettings(settingsData);
            }
        } catch (err) {
            setError('Failed to load notification settings. Please try again.');
            console.error('Error fetching settings:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setSettings(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? (checked ? 'true' : 'false') : value
        }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        try {
            setSaving(true);
            setError('');
            setSuccess('');

            const response = await axios.post(`${API_BASE_URL}/handlers/update_notification_settings.php`, {
                settings: settings
            });

            if (response.data.success) {
                setSuccess('Notification settings saved successfully!');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                throw new Error(response.data.message || 'Failed to save notification settings');
            }
        } catch (err) {
            setError(err.message || 'Failed to save notification settings. Please try again.');
            console.error('Error saving settings:', err);
        } finally {
            setSaving(false);
        }
    };

    if (loading) {
        return (
            <div className="p-6">
                <div className="flex items-center justify-center h-64">
                    <div className="text-lg text-gray-600">Loading notification settings...</div>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6">
            {/* Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800 flex items-center gap-3">
                    <FaBell className="text-blue-500" />
                    Notification Settings
                </h1>
                <p className="text-gray-600 mt-2">
                    Configure email and push notification templates and settings.
                </p>
            </div>

            {/* Alerts */}
            {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                    <FaTimes className="text-red-500" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {success && (
                <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                    <FaCheck className="text-green-500" />
                    <span className="text-green-700">{success}</span>
                </div>
            )}

            {/* Settings Form */}
            <div className="bg-white rounded-lg shadow-sm border">
                <form onSubmit={handleSubmit} className="p-6 space-y-6">
                    {/* General Settings */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">General Settings</h2>
                        <div className="space-y-4">
                            <div className="flex items-center">
                                <input
                                    type="checkbox"
                                    id="email_notifications_enabled"
                                    name="email_notifications_enabled"
                                    checked={settings.email_notifications_enabled === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <label htmlFor="email_notifications_enabled" className="ml-2 block text-sm text-gray-900">
                                    Enable Email Notifications (Master Switch)
                                </label>
                            </div>
                            
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    Notification Frequency
                                </label>
                                <select
                                    name="notification_frequency"
                                    value={settings.notification_frequency}
                                    onChange={handleInputChange}
                                    className="w-full md:w-1/3 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                    <option value="immediate">Immediate</option>
                                    <option value="hourly">Hourly</option>
                                    <option value="daily">Daily</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    {/* Notification Types */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Notification Types</h2>
                        <div className="space-y-4">
                            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h3 className="font-medium text-gray-900">Bet Notifications</h3>
                                    <p className="text-sm text-gray-600">Notifications for bet activities, wins, losses, and settlements</p>
                                </div>
                                <input
                                    type="checkbox"
                                    id="bet_notifications"
                                    name="bet_notifications"
                                    checked={settings.bet_notifications === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                            </div>
                            
                            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h3 className="font-medium text-gray-900">Challenge Notifications</h3>
                                    <p className="text-sm text-gray-600">Notifications for new challenges, challenge updates, and results</p>
                                </div>
                                <input
                                    type="checkbox"
                                    id="challenge_notifications"
                                    name="challenge_notifications"
                                    checked={settings.challenge_notifications === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                            </div>
                            
                            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h3 className="font-medium text-gray-900">League Notifications</h3>
                                    <p className="text-sm text-gray-600">Notifications for league activities, rankings, and season updates</p>
                                </div>
                                <input
                                    type="checkbox"
                                    id="league_notifications"
                                    name="league_notifications"
                                    checked={settings.league_notifications === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                            </div>
                            
                            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                                <div>
                                    <h3 className="font-medium text-gray-900">Admin Notifications</h3>
                                    <p className="text-sm text-gray-600">Notifications for admin activities and system alerts</p>
                                </div>
                                <input
                                    type="checkbox"
                                    id="admin_notifications"
                                    name="admin_notifications"
                                    checked={settings.admin_notifications === 'true'}
                                    onChange={handleInputChange}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                            </div>
                        </div>
                    </div>

                    {/* Email Templates */}
                    <div>
                        <h2 className="text-lg font-semibold text-gray-800 mb-4">Email Templates</h2>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <div className="flex items-center gap-2 mb-2">
                                <FaBell className="text-blue-500" />
                                <span className="font-medium text-blue-800">Template Management</span>
                            </div>
                            <p className="text-blue-700 text-sm">
                                Email templates can be customized for different notification types. 
                                This feature will be available in a future update.
                            </p>
                        </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end pt-6 border-t">
                        <button
                            type="submit"
                            disabled={saving}
                            className="flex items-center gap-2 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                            <FaSave />
                            {saving ? 'Saving...' : 'Save Notification Settings'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default NotificationSettings;
