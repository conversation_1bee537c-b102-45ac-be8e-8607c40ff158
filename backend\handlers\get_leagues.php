<?php
header("Access-Control-Allow-Origin: *"); 
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';
$conn = getDBConnection();

try {
    // Fetch all active leagues
    $query = "SELECT 
        l.*,
        COALESCE(m.member_count, 0) as member_count
    FROM leagues l
    LEFT JOIN (
        SELECT league_id, COUNT(*) as member_count 
        FROM league_memberships 
        WHERE status = 'active'
        GROUP BY league_id
    ) m ON m.league_id = l.league_id
    WHERE l.status IN ('active', 'upcoming')
    ORDER BY l.created_at DESC";

    $stmt = $conn->prepare($query);
    $stmt->execute();
    $leagues = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Add full URLs for media files
    foreach ($leagues as &$league) {
        if ($league['icon_path']) {
            $league['icon_url'] = '/backend/uploads/leagues/icons/' . $league['icon_path'];
        }
        if ($league['banner_path']) {
            $league['banner_url'] = '/backend/uploads/leagues/banners/' . $league['banner_path'];
        }
    }
    
    echo json_encode([
        'success' => true,
        'leagues' => $leagues
    ]);
} catch (PDOException $e) {
    error_log("Database error in get_leagues.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
} 