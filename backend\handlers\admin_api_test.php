<?php
// Save as admin_api_test.php in your backend/handlers directory
header("Content-Type: text/html; charset=UTF-8");

class APITester {
    private $baseUrl;
    private $adminCredentials;
    private $token;
    private $results = [];

    public function __construct($baseUrl) {
        $this->baseUrl = $baseUrl;
        $this->adminCredentials = [
            'identifier' => 'superadmin',  // Update with your admin credentials
            'password' => 'loving12'
        ];
    }

    public function runTests() {
        echo "<html><head><title>Admin API Tests</title>";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .success { color: green; }
            .error { color: red; }
            .warning { color: orange; }
            .test-group { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
            .endpoint { background: #f4f4f4; padding: 5px; margin: 5px 0; }
            pre { background: #f8f8f8; padding: 10px; overflow-x: auto; }
        </style></head><body>";
        
        echo "<h1>Admin API Test Results</h1>";
        
        // Test Groups
        $this->testDatabaseConnection();
        $this->testAdminLogin();
        $this->testDashboardData();
        $this->testUserManagement();
        $this->testChallengeManagement();
        
        $this->displaySummary();
        
        echo "</body></html>";
    }

    private function makeRequest($endpoint, $method = 'GET', $data = null) {
        $ch = curl_init();
        $url = $this->baseUrl . $endpoint;
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_TIMEOUT => 30
        ];

        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            if ($data) {
                $options[CURLOPT_POSTFIELDS] = json_encode($data);
                $options[CURLOPT_HTTPHEADER] = ['Content-Type: application/json'];
            }
        }

        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        return [
            'success' => ($httpCode >= 200 && $httpCode < 300),
            'status_code' => $httpCode,
            'response' => json_decode($response, true),
            'error' => $error
        ];
    }

    private function testDatabaseConnection() {
        echo "<div class='test-group'>";
        echo "<h2>Database Connection Test</h2>";
        
        try {
            require_once '../includes/db_connect.php';
            $conn = getDBConnection();
            
            if ($conn) {
                $this->logSuccess("Database connection", "Connection successful");
                
                // Test admin table
                $stmt = $conn->query("SELECT COUNT(*) FROM admins");
                $adminCount = $stmt->fetchColumn();
                $this->logSuccess("Admin table check", "Found $adminCount admin records");
                
                // Test users table
                $stmt = $conn->query("SELECT COUNT(*) FROM users");
                $userCount = $stmt->fetchColumn();
                $this->logSuccess("Users table check", "Found $userCount user records");
            } else {
                $this->logError("Database connection", "Connection failed");
            }
        } catch (PDOException $e) {
            $this->logError("Database connection", $e->getMessage());
        }
        echo "</div>";
    }

    private function testAdminLogin() {
        echo "<div class='test-group'>";
        echo "<h2>Admin Login Test</h2>";
        
        // Test login endpoint
        $loginResult = $this->makeRequest('/handlers/admin_login_handler.php', 'POST', $this->adminCredentials);
        
        if ($loginResult['success']) {
            $this->logSuccess("Admin Login", "Login successful");
            $this->token = $loginResult['response']['admin_id'] ?? null;
        } else {
            $this->logError("Admin Login", "Login failed: " . json_encode($loginResult['response']));
        }
        echo "</div>";
    }

    private function testDashboardData() {
        echo "<div class='test-group'>";
        echo "<h2>Dashboard Data Test</h2>";
        
        if (!$this->token) {
            $this->logError("Dashboard Data", "Skipped - No authentication token");
            echo "</div>";
            return;
        }

        $dashboardResult = $this->makeRequest('/handlers/admin_dashboard_data.php?adminId=' . $this->token);
        
        if ($dashboardResult['success']) {
            $this->logSuccess("Dashboard Data", "Data retrieved successfully");
            $this->validateDashboardData($dashboardResult['response']);
        } else {
            $this->logError("Dashboard Data", "Failed to retrieve data: " . json_encode($dashboardResult['response']));
        }
        echo "</div>";
    }

    private function validateDashboardData($data) {
        $requiredFields = ['totalUsers', 'activeChallenges', 'totalBets', 'recentActivity', 'recentBets'];
        
        if (!$data || !is_array($data)) {
            $this->logError("Dashboard Data", "Invalid data structure");
            return;
        }
    
        foreach ($requiredFields as $field) {
            if (array_key_exists($field, $data)) {
                $this->logSuccess("Dashboard Field: $field", "Present - " . json_encode($data[$field]));
            } else {
                $this->logError("Dashboard Field: $field", "Missing");
            }
        }
    }
      private function testUserManagement() {
          echo "<div class='test-group'>";
          echo "<h2>User Management Tests</h2>";
        
          // Test user listing
          $usersResult = $this->makeRequest('/handlers/user_management.php');
          $this->validateEndpoint("User Listing", $usersResult);

          // Test user creation
          $userData = [
              'username' => 'testuser_' . time(),
              'email' => 'test' . time() . '@test.com',
              'password' => 'test123'
          ];
          $createResult = $this->makeRequest('/handlers/add_user.php', 'POST', $userData);
          $this->validateEndpoint("User Creation", $createResult);
      }

      private function testChallengeManagement() {
          echo "<div class='test-group'>";
          echo "<h2>Challenge Management Tests</h2>";
        
          // Test GET challenges
          $challengesResult = $this->makeRequest('/handlers/challenge_management.php');
          if ($challengesResult['success']) {
              $this->logSuccess("Fetch Challenges", "Retrieved challenges successfully");
              $this->validateChallengeData($challengesResult['response']);
          } else {
              $this->logError("Fetch Challenges", "Failed to retrieve challenges");
          }

          // Test challenge update endpoint
          $updateData = [
              'challenge_id' => 1,
              'status' => 'Open',
              'team_a' => 'Test Team A',
              'team_b' => 'Test Team B'
          ];
          $updateResult = $this->makeRequest('/handlers/update_challenge.php', 'POST', $updateData);
          $this->validateEndpoint("Challenge Update", $updateResult);
      }

      private function testTeamManagement() {
          echo "<div class='test-group'>";
          echo "<h2>Team Management Tests</h2>";
        
          $teamsResult = $this->makeRequest('/handlers/team_management.php');
          $this->validateEndpoint("Team Listing", $teamsResult);
      }

      private function testBetSystem() {
          echo "<div class='test-group'>";
          echo "<h2>Betting System Tests</h2>";
        
          // Test active bets
          $betsResult = $this->makeRequest('/handlers/get_active_bets.php');
          $this->validateEndpoint("Active Bets", $betsResult);

          // Test bet creation
          $betData = [
              'user_id' => 1,
              'challenge_id' => 1,
              'amount' => 100
          ];
          $createBetResult = $this->makeRequest('/handlers/create_bet.php', 'POST', $betData);
          $this->validateEndpoint("Bet Creation", $createBetResult);
      }

      private function validateEndpoint($name, $result) {
          if ($result['success']) {
              $this->logSuccess($name, "Endpoint working correctly");
              echo "<pre>" . json_encode($result['response'], JSON_PRETTY_PRINT) . "</pre>";
          } else {
              $this->logError($name, "Endpoint failed: " . $result['status_code']);
          }
      }

      private function validateChallengeData($data) {
          $requiredFields = ['challenge_id', 'team_a', 'team_b', 'status', 'match_date'];
          foreach ($requiredFields as $field) {
              if (isset($data[0][$field])) {
                  $this->logSuccess("Challenge Field: $field", "Present");
              } else {
                  $this->logError("Challenge Field: $field", "Missing");
              }
          }
      }

      private function logSuccess($test, $message) {
          echo "<div class='success'>";
          echo "✓ $test: $message";
          echo "</div>";
          $this->results[$test] = true;
      }

      private function logError($test, $message) {
          echo "<div class='error'>";
          echo "✗ $test: $message";
          echo "</div>";
          $this->results[$test] = false;
      }

      private function displaySummary() {
          $total = count($this->results);
          $passed = count(array_filter($this->results));
        
          echo "<div class='test-group'>";
          echo "<h2>Test Summary</h2>";
          echo "<p>Total Tests: $total</p>";
          echo "<p>Passed: $passed</p>";
          echo "<p>Failed: " . ($total - $passed) . "</p>";
          echo "</div>";
      }
  }

  // Run the tests
  $tester = new APITester('https://fanbet247.xyz/backend');
  $tester->runTests();
  ?>