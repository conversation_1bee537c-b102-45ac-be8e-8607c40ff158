<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

// Handle DELETE request
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    $challenge_id = isset($_GET['challenge_id']) ? intval($_GET['challenge_id']) : 0;
    
    if (!$challenge_id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Challenge ID is required'
        ]);
        exit;
    }

    try {
        $conn = getDBConnection();
        
        // First check if challenge is expired
        $checkStmt = $conn->prepare("
            SELECT * FROM challenges 
            WHERE challenge_id = :challenge_id 
            AND (end_time < NOW() OR status = 'Expired')
        ");
        $checkStmt->execute([':challenge_id' => $challenge_id]);
        $challenge = $checkStmt->fetch(PDO::FETCH_ASSOC);

        if (!$challenge) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Challenge not found or not expired'
            ]);
            exit;
        }

        // Delete the challenge
        $deleteStmt = $conn->prepare("DELETE FROM challenges WHERE challenge_id = :challenge_id");
        $deleteStmt->execute([':challenge_id' => $challenge_id]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Challenge deleted successfully'
        ]);
        
    } catch(PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to delete challenge'
        ]);
    }
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents("php://input"), true);
    
    try {
        $conn = getDBConnection();
        
        $stmt = $conn->prepare("
            UPDATE challenges 
            SET odds_team_a = :odds_team_a,
                odds_team_b = :odds_team_b,
                start_time = :start_time,
                end_time = :end_time,
                match_date = :match_date,
                status = :status
            WHERE challenge_id = :challenge_id
        ");
        
        $stmt->execute([
            ':odds_team_a' => $data['odds_team_a'],
            ':odds_team_b' => $data['odds_team_b'],
            ':start_time' => $data['start_time'],
            ':end_time' => $data['end_time'],
            ':match_date' => $data['match_date'],
            ':status' => $data['status'],
            ':challenge_id' => $data['challenge_id']
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Challenge updated successfully'
        ]);
        
    } catch(PDOException $e) {
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => 'Failed to update challenge'
        ]);
    }
    exit;
}

// GET request handling for fetching challenges
try {
    $conn = getDBConnection();
    $stmt = $conn->query("
        SELECT 
            c.*,
            CASE 
                WHEN c.end_time < NOW() THEN 'Expired'
                ELSE c.status 
            END as display_status
        FROM challenges c
        ORDER BY 
            CASE 
                WHEN c.end_time < NOW() THEN 1
                ELSE 0
            END,
            c.challenge_date DESC
    ");
    
    $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'challenges' => $challenges
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database error occurred'
    ]);
}
