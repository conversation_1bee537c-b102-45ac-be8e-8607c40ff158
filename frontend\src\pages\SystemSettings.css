/* System Settings Styles */
.system-settings {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.system-settings h1 {
  color: #000000;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.75rem;
}

.system-description {
  margin-bottom: 2rem;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.6;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.settings-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.settings-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-color: #166534;
}

.settings-card h3 {
  color: #166534;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.settings-card p {
  color: #4b5563;
  font-size: 0.95rem;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.settings-card .btn {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: auto;
}

.settings-card .btn-primary {
  background-color: #166534;
  color: white;
}

.settings-card .btn-primary:hover {
  background-color: #15803d;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .system-settings {
    padding: 1.5rem;
    max-width: 100%;
  }
  
  .settings-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
  }
}

@media (max-width: 768px) {
  .system-settings {
    padding: 1rem;
  }
  
  .system-settings h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
  
  .system-description {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }
  
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .settings-card {
    padding: 1.25rem;
  }
}

@media (max-width: 480px) {
  .system-settings {
    padding: 0.75rem;
    border-radius: 4px;
  }
  
  .system-settings h1 {
    font-size: 1.3rem;
    padding-bottom: 0.5rem;
  }
  
  .settings-card {
    padding: 1rem;
    border-radius: 6px;
  }
  
  .settings-card h3 {
    font-size: 1.1rem;
  }
  
  .settings-card .btn {
    padding: 0.6rem 0.8rem;
    font-size: 0.9rem;
  }
}
