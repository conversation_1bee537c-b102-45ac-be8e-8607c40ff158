{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nconst CurrencyManagement = () => {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [editingRate, setEditingRate] = useState(null);\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const {\n    loading,\n    error,\n    execute\n  } = useApiService({\n    onError: error => {\n      console.error('Currency Management Error:', error);\n    }\n  });\n  const {\n    refreshCurrencyData\n  } = useCurrency();\n\n  // Load currencies and exchange rates\n  const loadData = useCallback(async () => {\n    try {\n      const [currenciesResponse, ratesResponse] = await Promise.all([execute(() => currencyService.getCurrencies(false)),\n      // Get all currencies including inactive\n      execute(() => currencyService.getExchangeRates())]);\n      if (currenciesResponse.success) {\n        setCurrencies(currenciesResponse.data.currencies || []);\n      }\n      if (ratesResponse.success) {\n        setExchangeRates(ratesResponse.data.exchange_rates || []);\n      }\n    } catch (error) {\n      console.error('Failed to load currency data:', error);\n    }\n  }, [execute]);\n\n  // Update exchange rate\n  const handleUpdateRate = useCallback(async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      alert('Please enter a valid exchange rate');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        alert('Exchange rate updated successfully!');\n      } else {\n        alert(response.message || 'Failed to update exchange rate');\n      }\n    } catch (error) {\n      alert('Error updating exchange rate: ' + error.message);\n    }\n  }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n  // Toggle currency active status\n  const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('toggle', {\n        currency_id: currencyId\n      }));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n      } else {\n        alert(response.message || 'Failed to toggle currency status');\n      }\n    } catch (error) {\n      alert('Error toggling currency: ' + error.message);\n    }\n  }, [execute, loadData, refreshCurrencyData]);\n\n  // Add new currency\n  const handleAddCurrency = useCallback(async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('create', newCurrency));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        alert('Currency added successfully!');\n      } else {\n        alert(response.message || 'Failed to add currency');\n      }\n    } catch (error) {\n      alert('Error adding currency: ' + error.message);\n    }\n  }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n  // Get exchange rate for currency\n  const getExchangeRate = useCallback(currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  }, [exchangeRates]);\n\n  // Format date\n  const formatDate = useCallback(dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }, []);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"currency-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Currency Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage supported currencies and exchange rates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddCurrency(true),\n        children: \"Add New Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\u26A0\\uFE0F \", error.message || 'An error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-secondary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 17\n    }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"currencies-grid\",\n      children: currencies.map(currency => {\n        const rate = getExchangeRate(currency.id);\n        const isEditing = editingRate === currency.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `currency-card ${!currency.is_active ? 'inactive' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: currency.currency_code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: currency.currency_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"currency-symbol\",\n                children: currency.currency_symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${currency.is_active ? 'active' : 'inactive'}`,\n                children: currency.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exchange-rate-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Exchange Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 37\n            }, this), rate ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rate-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-rate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rate-value\",\n                  children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Updated: \", formatDate(rate.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 210,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 45\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rate-edit-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.0001\",\n                  value: newRate,\n                  onChange: e => setNewRate(e.target.value),\n                  placeholder: \"New rate\",\n                  className: \"rate-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: notes,\n                  onChange: e => setNotes(e.target.value),\n                  placeholder: \"Update notes (optional)\",\n                  className: \"notes-input\",\n                  rows: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 223,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"edit-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleUpdateRate(currency.id),\n                    className: \"btn btn-success btn-sm\",\n                    disabled: loading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 231,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRate(null);\n                      setNewRate('');\n                      setNotes('');\n                    },\n                    className: \"btn btn-secondary btn-sm\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate(rate.rate_to_fancoin.toString());\n                },\n                className: \"btn btn-outline btn-sm\",\n                children: \"Update Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-rate\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No exchange rate set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate('');\n                },\n                className: \"btn btn-primary btn-sm\",\n                children: \"Set Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n              className: `btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`,\n              disabled: loading,\n              children: currency.is_active ? 'Deactivate' : 'Activate'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 33\n          }, this)]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 17\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 300,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 155,\n    columnNumber: 9\n  }, this);\n};\n_s(CurrencyManagement, \"R7YIZMxmWKkUgEBgHtABNmrqBZ8=\", true);\n_c = CurrencyManagement;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "editingRate", "setEditingRate", "newRate", "setNewRate", "notes", "setNotes", "showAddCurrency", "setShowAddCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "loading", "error", "execute", "useApiService", "onError", "console", "refreshCurrencyData", "useCurrency", "loadData", "useCallback", "currenciesResponse", "ratesResponse", "Promise", "all", "currencyService", "getCurrencies", "getExchangeRates", "success", "data", "exchange_rates", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "alert", "response", "updateExchangeRate", "message", "handleToggleCurrency", "currentStatus", "manageCurrencies", "currency_id", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "currency", "id", "isEditing", "rate_to_fancoin", "updated_at", "type", "step", "value", "onChange", "e", "target", "placeholder", "rows", "disabled", "toString", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nconst CurrencyManagement = () => {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [editingRate, setEditingRate] = useState(null);\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n\n    const { loading, error, execute } = useApiService({\n        onError: (error) => {\n            console.error('Currency Management Error:', error);\n        }\n    });\n\n    const { refreshCurrencyData } = useCurrency();\n\n    // Load currencies and exchange rates\n    const loadData = useCallback(async () => {\n        try {\n            const [currenciesResponse, ratesResponse] = await Promise.all([\n                execute(() => currencyService.getCurrencies(false)), // Get all currencies including inactive\n                execute(() => currencyService.getExchangeRates())\n            ]);\n\n            if (currenciesResponse.success) {\n                setCurrencies(currenciesResponse.data.currencies || []);\n            }\n\n            if (ratesResponse.success) {\n                setExchangeRates(ratesResponse.data.exchange_rates || []);\n            }\n        } catch (error) {\n            console.error('Failed to load currency data:', error);\n        }\n    }, [execute]);\n\n    // Update exchange rate\n    const handleUpdateRate = useCallback(async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            alert('Please enter a valid exchange rate');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                alert('Exchange rate updated successfully!');\n            } else {\n                alert(response.message || 'Failed to update exchange rate');\n            }\n        } catch (error) {\n            alert('Error updating exchange rate: ' + error.message);\n        }\n    }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n    // Toggle currency active status\n    const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('toggle', { currency_id: currencyId })\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n            } else {\n                alert(response.message || 'Failed to toggle currency status');\n            }\n        } catch (error) {\n            alert('Error toggling currency: ' + error.message);\n        }\n    }, [execute, loadData, refreshCurrencyData]);\n\n    // Add new currency\n    const handleAddCurrency = useCallback(async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            alert('Please fill in all required fields');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('create', newCurrency)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                alert('Currency added successfully!');\n            } else {\n                alert(response.message || 'Failed to add currency');\n            }\n        } catch (error) {\n            alert('Error adding currency: ' + error.message);\n        }\n    }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n    // Get exchange rate for currency\n    const getExchangeRate = useCallback((currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    }, [exchangeRates]);\n\n    // Format date\n    const formatDate = useCallback((dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }, []);\n\n    // Load data on component mount\n    useEffect(() => {\n        loadData();\n    }, [loadData]);\n\n    return (\n        <div className=\"currency-management\">\n            <div className=\"page-header\">\n                <h1>Currency Management</h1>\n                <p>Manage supported currencies and exchange rates</p>\n                <button \n                    className=\"btn btn-primary\"\n                    onClick={() => setShowAddCurrency(true)}\n                >\n                    Add New Currency\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <p>⚠️ {error.message || 'An error occurred'}</p>\n                    <button onClick={loadData} className=\"btn btn-secondary\">\n                        Retry\n                    </button>\n                </div>\n            )}\n\n            {loading && currencies.length === 0 ? (\n                <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                    <p>Loading currencies...</p>\n                </div>\n            ) : (\n                <div className=\"currencies-grid\">\n                    {currencies.map((currency) => {\n                        const rate = getExchangeRate(currency.id);\n                        const isEditing = editingRate === currency.id;\n\n                        return (\n                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>\n                                <div className=\"currency-header\">\n                                    <div className=\"currency-info\">\n                                        <h3>{currency.currency_code}</h3>\n                                        <p>{currency.currency_name}</p>\n                                        <span className=\"currency-symbol\">{currency.currency_symbol}</span>\n                                    </div>\n                                    <div className=\"currency-status\">\n                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>\n                                            {currency.is_active ? 'Active' : 'Inactive'}\n                                        </span>\n                                    </div>\n                                </div>\n\n                                <div className=\"exchange-rate-section\">\n                                    <h4>Exchange Rate</h4>\n                                    {rate ? (\n                                        <div className=\"rate-info\">\n                                            <div className=\"current-rate\">\n                                                <span className=\"rate-value\">\n                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                </span>\n                                                <small>Updated: {formatDate(rate.updated_at)}</small>\n                                            </div>\n\n                                            {isEditing ? (\n                                                <div className=\"rate-edit-form\">\n                                                    <input\n                                                        type=\"number\"\n                                                        step=\"0.0001\"\n                                                        value={newRate}\n                                                        onChange={(e) => setNewRate(e.target.value)}\n                                                        placeholder=\"New rate\"\n                                                        className=\"rate-input\"\n                                                    />\n                                                    <textarea\n                                                        value={notes}\n                                                        onChange={(e) => setNotes(e.target.value)}\n                                                        placeholder=\"Update notes (optional)\"\n                                                        className=\"notes-input\"\n                                                        rows=\"2\"\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button \n                                                            onClick={() => handleUpdateRate(currency.id)}\n                                                            className=\"btn btn-success btn-sm\"\n                                                            disabled={loading}\n                                                        >\n                                                            Save\n                                                        </button>\n                                                        <button \n                                                            onClick={() => {\n                                                                setEditingRate(null);\n                                                                setNewRate('');\n                                                                setNotes('');\n                                                            }}\n                                                            className=\"btn btn-secondary btn-sm\"\n                                                        >\n                                                            Cancel\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <button \n                                                    onClick={() => {\n                                                        setEditingRate(currency.id);\n                                                        setNewRate(rate.rate_to_fancoin.toString());\n                                                    }}\n                                                    className=\"btn btn-outline btn-sm\"\n                                                >\n                                                    Update Rate\n                                                </button>\n                                            )}\n                                        </div>\n                                    ) : (\n                                        <div className=\"no-rate\">\n                                            <p>No exchange rate set</p>\n                                            <button \n                                                onClick={() => {\n                                                    setEditingRate(currency.id);\n                                                    setNewRate('');\n                                                }}\n                                                className=\"btn btn-primary btn-sm\"\n                                            >\n                                                Set Rate\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"currency-actions\">\n                                    <button \n                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}\n                                        disabled={loading}\n                                    >\n                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                    </button>\n                                </div>\n                            </div>\n                        );\n                    })}\n                </div>\n            )}\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,YAAY,GAAG,UAAU;AAE/B,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACsB,OAAO,EAAEC,UAAU,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC0B,eAAe,EAAEC,kBAAkB,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4B,WAAW,EAAEC,cAAc,CAAC,GAAG7B,QAAQ,CAAC;IAC3C8B,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAGC,aAAa,CAAC;IAC9CC,OAAO,EAAGH,KAAK,IAAK;MAChBI,OAAO,CAACJ,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD;EACJ,CAAC,CAAC;EAEF,MAAM;IAAEK;EAAoB,CAAC,GAAGC,WAAW,CAAC,CAAC;;EAE7C;EACA,MAAMC,QAAQ,GAAGC,WAAW,CAAC,YAAY;IACrC,IAAI;MACA,MAAM,CAACC,kBAAkB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DX,OAAO,CAAC,MAAMY,eAAe,CAACC,aAAa,CAAC,KAAK,CAAC,CAAC;MAAE;MACrDb,OAAO,CAAC,MAAMY,eAAe,CAACE,gBAAgB,CAAC,CAAC,CAAC,CACpD,CAAC;MAEF,IAAIN,kBAAkB,CAACO,OAAO,EAAE;QAC5BlC,aAAa,CAAC2B,kBAAkB,CAACQ,IAAI,CAACpC,UAAU,IAAI,EAAE,CAAC;MAC3D;MAEA,IAAI6B,aAAa,CAACM,OAAO,EAAE;QACvBhC,gBAAgB,CAAC0B,aAAa,CAACO,IAAI,CAACC,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACZI,OAAO,CAACJ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMkB,gBAAgB,GAAGX,WAAW,CAAC,MAAOY,UAAU,IAAK;IACvD,IAAI,CAACjC,OAAO,IAAIkC,KAAK,CAACC,UAAU,CAACnC,OAAO,CAAC,CAAC,EAAE;MACxCoC,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAAC,MAC3BY,eAAe,CAACY,kBAAkB,CAACL,UAAU,EAAEE,UAAU,CAACnC,OAAO,CAAC,EAAEE,KAAK,CAC7E,CAAC;MAED,IAAImC,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMT,QAAQ,CAAC,CAAC;QAChB,MAAMF,mBAAmB,CAAC,CAAC;QAC3BnB,cAAc,CAAC,IAAI,CAAC;QACpBE,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZiC,KAAK,CAAC,qCAAqC,CAAC;MAChD,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,gCAAgC,CAAC;MAC/D;IACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACZuB,KAAK,CAAC,gCAAgC,GAAGvB,KAAK,CAAC0B,OAAO,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACvC,OAAO,EAAEE,KAAK,EAAEY,OAAO,EAAEM,QAAQ,EAAEF,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAMsB,oBAAoB,GAAGnB,WAAW,CAAC,OAAOY,UAAU,EAAEQ,aAAa,KAAK;IAC1E,IAAI;MACA,MAAMJ,QAAQ,GAAG,MAAMvB,OAAO,CAAC,MAC3BY,eAAe,CAACgB,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,WAAW,EAAEV;MAAW,CAAC,CAC1E,CAAC;MAED,IAAII,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMT,QAAQ,CAAC,CAAC;QAChB,MAAMF,mBAAmB,CAAC,CAAC;QAC3BkB,KAAK,CAAC,YAAYK,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;MAClF,CAAC,MAAM;QACHL,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,kCAAkC,CAAC;MACjE;IACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACZuB,KAAK,CAAC,2BAA2B,GAAGvB,KAAK,CAAC0B,OAAO,CAAC;IACtD;EACJ,CAAC,EAAE,CAACzB,OAAO,EAAEM,QAAQ,EAAEF,mBAAmB,CAAC,CAAC;;EAE5C;EACA,MAAM0B,iBAAiB,GAAGvB,WAAW,CAAC,YAAY;IAC9C,IAAI,CAACf,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1F0B,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMvB,OAAO,CAAC,MAC3BY,eAAe,CAACgB,gBAAgB,CAAC,QAAQ,EAAEpC,WAAW,CAC1D,CAAC;MAED,IAAI+B,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMT,QAAQ,CAAC,CAAC;QAChB,MAAMF,mBAAmB,CAAC,CAAC;QAC3Bb,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACFyB,KAAK,CAAC,8BAA8B,CAAC;MACzC,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,wBAAwB,CAAC;MACvD;IACJ,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACZuB,KAAK,CAAC,yBAAyB,GAAGvB,KAAK,CAAC0B,OAAO,CAAC;IACpD;EACJ,CAAC,EAAE,CAACjC,WAAW,EAAEQ,OAAO,EAAEM,QAAQ,EAAEF,mBAAmB,CAAC,CAAC;;EAEzD;EACA,MAAM2B,eAAe,GAAGxB,WAAW,CAAEY,UAAU,IAAK;IAChD,OAAOrC,aAAa,CAACkD,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,WAAW,KAAKV,UAAU,CAAC;EACtE,CAAC,EAAE,CAACrC,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMoD,UAAU,GAAG3B,WAAW,CAAE4B,UAAU,IAAK;IAC3C,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA7E,SAAS,CAAC,MAAM;IACZyC,QAAQ,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACI9B,OAAA;IAAKmE,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCpE,OAAA;MAAKmE,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBpE,OAAA;QAAAoE,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BxE,OAAA;QAAAoE,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDxE,OAAA;QACImE,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAM1D,kBAAkB,CAAC,IAAI,CAAE;QAAAqD,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELjD,KAAK,iBACFvB,OAAA;MAAKmE,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BpE,OAAA;QAAAoE,QAAA,GAAG,eAAG,EAAC7C,KAAK,CAAC0B,OAAO,IAAI,mBAAmB;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDxE,OAAA;QAAQyE,OAAO,EAAE3C,QAAS;QAACqC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEAlD,OAAO,IAAIlB,UAAU,CAACsE,MAAM,KAAK,CAAC,gBAC/B1E,OAAA;MAAKmE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BpE,OAAA;QAAKmE,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BxE,OAAA;QAAAoE,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,gBAENxE,OAAA;MAAKmE,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3BhE,UAAU,CAACuE,GAAG,CAAEC,QAAQ,IAAK;QAC1B,MAAMnB,IAAI,GAAGF,eAAe,CAACqB,QAAQ,CAACC,EAAE,CAAC;QACzC,MAAMC,SAAS,GAAGtE,WAAW,KAAKoE,QAAQ,CAACC,EAAE;QAE7C,oBACI7E,OAAA;UAAuBmE,SAAS,EAAE,iBAAiB,CAACS,QAAQ,CAACvD,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;UAAA+C,QAAA,gBACvFpE,OAAA;YAAKmE,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BpE,OAAA;cAAKmE,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BpE,OAAA;gBAAAoE,QAAA,EAAKQ,QAAQ,CAAC1D;cAAa;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCxE,OAAA;gBAAAoE,QAAA,EAAIQ,QAAQ,CAACzD;cAAa;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BxE,OAAA;gBAAMmE,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEQ,QAAQ,CAACxD;cAAe;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNxE,OAAA;cAAKmE,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BpE,OAAA;gBAAMmE,SAAS,EAAE,gBAAgBS,QAAQ,CAACvD,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA+C,QAAA,EACzEQ,QAAQ,CAACvD,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENxE,OAAA;YAAKmE,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCpE,OAAA;cAAAoE,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBf,IAAI,gBACDzD,OAAA;cAAKmE,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBpE,OAAA;gBAAKmE,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBpE,OAAA;kBAAMmE,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,SAClB,EAACQ,QAAQ,CAACxD,eAAe,EAAEqC,IAAI,CAACsB,eAAe;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACPxE,OAAA;kBAAAoE,QAAA,GAAO,WAAS,EAACV,UAAU,CAACD,IAAI,CAACuB,UAAU,CAAC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAELM,SAAS,gBACN9E,OAAA;gBAAKmE,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3BpE,OAAA;kBACIiF,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAEzE,OAAQ;kBACf0E,QAAQ,EAAGC,CAAC,IAAK1E,UAAU,CAAC0E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5CI,WAAW,EAAC,UAAU;kBACtBpB,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFxE,OAAA;kBACImF,KAAK,EAAEvE,KAAM;kBACbwE,QAAQ,EAAGC,CAAC,IAAKxE,QAAQ,CAACwE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1CI,WAAW,EAAC,yBAAyB;kBACrCpB,SAAS,EAAC,aAAa;kBACvBqB,IAAI,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFxE,OAAA;kBAAKmE,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBpE,OAAA;oBACIyE,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACkC,QAAQ,CAACC,EAAE,CAAE;oBAC7CV,SAAS,EAAC,wBAAwB;oBAClCsB,QAAQ,EAAEnE,OAAQ;oBAAA8C,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTxE,OAAA;oBACIyE,OAAO,EAAEA,CAAA,KAAM;sBACXhE,cAAc,CAAC,IAAI,CAAC;sBACpBE,UAAU,CAAC,EAAE,CAAC;sBACdE,QAAQ,CAAC,EAAE,CAAC;oBAChB,CAAE;oBACFsD,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENxE,OAAA;gBACIyE,OAAO,EAAEA,CAAA,KAAM;kBACXhE,cAAc,CAACmE,QAAQ,CAACC,EAAE,CAAC;kBAC3BlE,UAAU,CAAC8C,IAAI,CAACsB,eAAe,CAACW,QAAQ,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFvB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENxE,OAAA;cAAKmE,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACpBpE,OAAA;gBAAAoE,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3BxE,OAAA;gBACIyE,OAAO,EAAEA,CAAA,KAAM;kBACXhE,cAAc,CAACmE,QAAQ,CAACC,EAAE,CAAC;kBAC3BlE,UAAU,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFwD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENxE,OAAA;YAAKmE,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BpE,OAAA;cACIyE,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC0B,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAACvD,SAAS,CAAE;cACrE8C,SAAS,EAAE,cAAcS,QAAQ,CAACvD,SAAS,GAAG,aAAa,GAAG,aAAa,EAAG;cAC9EoE,QAAQ,EAAEnE,OAAQ;cAAA8C,QAAA,EAEjBQ,QAAQ,CAACvD,SAAS,GAAG,YAAY,GAAG;YAAU;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlGAI,QAAQ,CAACC,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGhB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA1D,eAAe,iBACZd,OAAA;MAAKmE,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAM1D,kBAAkB,CAAC,KAAK,CAAE;MAAAqD,QAAA,eACpEpE,OAAA;QAAKmE,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGY,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;QAAAvB,QAAA,gBAC/DpE,OAAA;UAAAoE,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBxE,OAAA;UAAKmE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBpE,OAAA;YAAAoE,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CxE,OAAA;YACIiF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACE,aAAc;YACjCkE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAEmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAACS,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBpE,OAAA;YAAAoE,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BxE,OAAA;YACIiF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACG,aAAc;YACjCiE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAEkE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACjFI,WAAW,EAAC;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBpE,OAAA;YAAAoE,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BxE,OAAA;YACIiF,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACI,eAAgB;YACnCgE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAEiE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnFI,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBpE,OAAA;YAAAoE,QAAA,gBACIpE,OAAA;cACIiF,IAAI,EAAC,UAAU;cACfa,OAAO,EAAE9E,WAAW,CAACK,SAAU;cAC/B+D,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAEgE,CAAC,CAACC,MAAM,CAACQ;cAAO,CAAC;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNxE,OAAA;UAAKmE,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BpE,OAAA;YACIyE,OAAO,EAAEnB,iBAAkB;YAC3Ba,SAAS,EAAC,iBAAiB;YAC3BsB,QAAQ,EAAEnE,OAAQ;YAAA8C,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTxE,OAAA;YACIyE,OAAO,EAAEA,CAAA,KAAM1D,kBAAkB,CAAC,KAAK,CAAE;YACzCoD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACrE,EAAA,CAvVID,kBAAkB;AAAA6F,EAAA,GAAlB7F,kBAAkB;AAyVxB,eAAeA,kBAAkB;AAAC,IAAA6F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}