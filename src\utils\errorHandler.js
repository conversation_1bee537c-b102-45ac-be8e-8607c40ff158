// Custom error types
export class APIError extends Error {
  constructor(message, status, code) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.code = code;
  }
}

export class ValidationError extends Error {
  constructor(message, field) {
    super(message);
    this.name = 'ValidationError';
    this.field = field;
  }
}

// Error logging utility
export const logError = (error, context = '') => {
  const timestamp = new Date().toISOString();
  const errorDetails = {
    timestamp,
    type: error.name,
    message: error.message,
    context,
    stack: error.stack
  };
  
  // Log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.error('🔥 Error:', errorDetails);
  }
  
  // Here you can add additional logging services
  // e.g., Sentry, LogRocket, etc.
  
  return errorDetails;
};

// Error handler middleware for API requests
export const apiErrorHandler = (error, req, res, next) => {
  const errorDetails = logError(error, `API: ${req.method} ${req.path}`);
  
  if (error instanceof APIError) {
    return res.status(error.status).json({
      error: true,
      message: error.message,
      code: error.code
    });
  }
  
  if (error instanceof ValidationError) {
    return res.status(400).json({
      error: true,
      message: error.message,
      field: error.field
    });
  }
  
  // Default error response
  return res.status(500).json({
    error: true,
    message: 'An unexpected error occurred',
    ...(process.env.NODE_ENV === 'development' && { details: errorDetails })
  });
}; 