{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\AddUser.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaUserPlus, FaUser, FaEnvelope, FaLock, FaShieldAlt, FaCoins, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction AddUser() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [currencies, setCurrencies] = useState([]);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    preferred_currency: '',\n    balance: 0\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  useEffect(() => {\n    fetchTeams();\n    fetchCurrencies();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n      setTeams(response.data.data || []);\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n  const fetchCurrencies = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/manage_currencies.php`);\n      if (response.data.success) {\n        // Only show active currencies\n        const activeCurrencies = response.data.data.filter(currency => currency.is_active);\n        setCurrencies(activeCurrencies);\n      }\n    } catch (err) {\n      console.error('Failed to fetch currencies:', err);\n      setError('Failed to fetch currencies');\n    }\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\n      if (response.data.success) {\n        setSuccess('User added successfully!');\n        setNewUser({\n          username: '',\n          full_name: '',\n          email: '',\n          password: '',\n          favorite_team: '',\n          preferred_currency: '',\n          balance: 0\n        });\n      } else {\n        setError(response.data.message || 'Failed to add user');\n      }\n    } catch (err) {\n      setError('Failed to add user');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-gray-800\",\n        children: \"Add New User\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Create a new user account in the system\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm p-6 w-full\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaUserPlus, {\n            className: \"text-green-700 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: \"User Information\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Username\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"username\",\n                value: newUser.username,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                placeholder: \"Enter username\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Full Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaUser, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"full_name\",\n                value: newUser.full_name,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                placeholder: \"Enter full name\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Email\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaEnvelope, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"email\",\n                name: \"email\",\n                value: newUser.email,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                placeholder: \"Enter email address\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Password\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaLock, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"password\",\n                name: \"password\",\n                value: newUser.password,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                placeholder: \"Enter password\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Favorite Team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaShieldAlt, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"favorite_team\",\n                value: newUser.favorite_team,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Favorite Team\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 207,\n                  columnNumber: 37\n                }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: team.name,\n                  children: team.name\n                }, team.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Preferred Currency\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaExchangeAlt, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                name: \"preferred_currency\",\n                value: newUser.preferred_currency,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select Preferred Currency\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 37\n                }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: currency.currency_code,\n                  children: [currency.currency_symbol, \" \", currency.currency_code, \" - \", currency.currency_name]\n                }, currency.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 232,\n                  columnNumber: 41\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Initial Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative rounded-md shadow-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\",\n                children: /*#__PURE__*/_jsxDEV(FaCoins, {\n                  className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 37\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"number\",\n                name: \"balance\",\n                value: newUser.balance,\n                onChange: handleInputChange,\n                className: \"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\",\n                placeholder: \"Enter initial balance\",\n                required: true,\n                style: {\n                  paddingLeft: '2.5rem'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pt-4\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\",\n            children: [/*#__PURE__*/_jsxDEV(FaUserPlus, {\n              className: \"mr-2 h-5 w-5\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 29\n            }, this), \" Add User\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 9\n  }, this);\n}\n_s(AddUser, \"yyoohll0pDBXWFvDgkwIBgA0eCY=\");\n_c = AddUser;\nexport default AddUser;\nvar _c;\n$RefreshReg$(_c, \"AddUser\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaUserPlus", "FaUser", "FaEnvelope", "FaLock", "FaShieldAlt", "FaCoins", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "AddUser", "_s", "teams", "setTeams", "currencies", "setCurrencies", "newUser", "setNewUser", "username", "full_name", "email", "password", "favorite_team", "preferred_currency", "balance", "error", "setError", "success", "setSuccess", "fetchTeams", "fetchCurrencies", "response", "get", "data", "err", "activeCurrencies", "filter", "currency", "is_active", "console", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "role", "onSubmit", "type", "onChange", "placeholder", "required", "style", "paddingLeft", "map", "team", "id", "currency_code", "currency_symbol", "currency_name", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/AddUser.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaUserPlus, FaUser, FaEnvelope, FaLock, FaShieldAlt, FaCoins, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction AddUser() {\n    const [teams, setTeams] = useState([]);\n    const [currencies, setCurrencies] = useState([]);\n    const [newUser, setNewUser] = useState({\n        username: '',\n        full_name: '',\n        email: '',\n        password: '',\n        favorite_team: '',\n        preferred_currency: '',\n        balance: 0\n    });\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    useEffect(() => {\n        fetchTeams();\n        fetchCurrencies();\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);\n            setTeams(response.data.data || []);\n        } catch (err) {\n            setError('Failed to fetch teams');\n        }\n    };\n\n    const fetchCurrencies = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/manage_currencies.php`);\n            if (response.data.success) {\n                // Only show active currencies\n                const activeCurrencies = response.data.data.filter(currency => currency.is_active);\n                setCurrencies(activeCurrencies);\n            }\n        } catch (err) {\n            console.error('Failed to fetch currencies:', err);\n            setError('Failed to fetch currencies');\n        }\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewUser(prev => ({ ...prev, [name]: value }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);\n            if (response.data.success) {\n                setSuccess('User added successfully!');\n                setNewUser({\n                    username: '',\n                    full_name: '',\n                    email: '',\n                    password: '',\n                    favorite_team: '',\n                    preferred_currency: '',\n                    balance: 0\n                });\n            } else {\n                setError(response.data.message || 'Failed to add user');\n            }\n        } catch (err) {\n            setError('Failed to add user');\n        }\n    };\n\n    return (\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\n            {/* Page Header */}\n            <div className=\"mb-8\">\n                <h1 className=\"text-2xl font-bold text-gray-800\">Add New User</h1>\n                <p className=\"text-gray-600\">Create a new user account in the system</p>\n            </div>\n\n            {/* Notification Messages */}\n            {error && (\n                <div className=\"mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{error}</span>\n                </div>\n            )}\n            {success && (\n                <div className=\"mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{success}</span>\n                </div>\n            )}\n\n            {/* Form Card */}\n            <div className=\"bg-white rounded-lg shadow-sm p-6 w-full\">\n                <div className=\"flex items-center mb-6\">\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\n                        <FaUserPlus className=\"text-green-700 text-xl\" />\n                    </div>\n                    <h2 className=\"text-lg font-semibold text-gray-800\">User Information</h2>\n                </div>\n\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                        {/* Username */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Username</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaUser className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <input\n                                    type=\"text\"\n                                    name=\"username\"\n                                    value={newUser.username}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    placeholder=\"Enter username\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                />\n                            </div>\n                        </div>\n\n                        {/* Full Name */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Full Name</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaUser className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <input\n                                    type=\"text\"\n                                    name=\"full_name\"\n                                    value={newUser.full_name}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    placeholder=\"Enter full name\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                />\n                            </div>\n                        </div>\n\n                        {/* Email */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaEnvelope className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <input\n                                    type=\"email\"\n                                    name=\"email\"\n                                    value={newUser.email}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    placeholder=\"Enter email address\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                />\n                            </div>\n                        </div>\n\n                        {/* Password */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Password</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaLock className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <input\n                                    type=\"password\"\n                                    name=\"password\"\n                                    value={newUser.password}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    placeholder=\"Enter password\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                />\n                            </div>\n                        </div>\n\n                        {/* Favorite Team */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Favorite Team</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaShieldAlt className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <select\n                                    name=\"favorite_team\"\n                                    value={newUser.favorite_team}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                >\n                                    <option value=\"\">Select Favorite Team</option>\n                                    {teams.map(team => (\n                                        <option key={team.id} value={team.name}>{team.name}</option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n\n                        {/* Preferred Currency */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Preferred Currency</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaExchangeAlt className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <select\n                                    name=\"preferred_currency\"\n                                    value={newUser.preferred_currency}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                >\n                                    <option value=\"\">Select Preferred Currency</option>\n                                    {currencies.map(currency => (\n                                        <option key={currency.id} value={currency.currency_code}>\n                                            {currency.currency_symbol} {currency.currency_code} - {currency.currency_name}\n                                        </option>\n                                    ))}\n                                </select>\n                            </div>\n                        </div>\n\n                        {/* Initial Balance */}\n                        <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">Initial Balance</label>\n                            <div className=\"relative rounded-md shadow-sm\">\n                                <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10\">\n                                    <FaCoins className=\"h-4 w-4 text-green-600\" />\n                                </div>\n                                <input\n                                    type=\"number\"\n                                    name=\"balance\"\n                                    value={newUser.balance}\n                                    onChange={handleInputChange}\n                                    className=\"block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm\"\n                                    placeholder=\"Enter initial balance\"\n                                    required\n                                    style={{paddingLeft: '2.5rem'}}\n                                />\n                            </div>\n                        </div>\n                    </div>\n\n                    <div className=\"pt-4\">\n                        <button\n                            type=\"submit\"\n                            className=\"w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500\"\n                        >\n                            <FaUserPlus className=\"mr-2 h-5 w-5\" /> Add User\n                        </button>\n                    </div>\n                </form>\n            </div>\n        </div>\n    );\n}\n\nexport default AddUser;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,MAAM,EAAEC,UAAU,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7G,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC;IACnCqB,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,kBAAkB,EAAE,EAAE;IACtBC,OAAO,EAAE;EACb,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,EAAE,CAAC;EAE1CC,SAAS,CAAC,MAAM;IACZ+B,UAAU,CAAC,CAAC;IACZC,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAME,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAGvB,YAAY,+BAA+B,CAAC;MAChFI,QAAQ,CAACkB,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;IACtC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVR,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;EAED,MAAMI,eAAe,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhC,KAAK,CAACiC,GAAG,CAAC,GAAGvB,YAAY,iCAAiC,CAAC;MAClF,IAAIsB,QAAQ,CAACE,IAAI,CAACN,OAAO,EAAE;QACvB;QACA,MAAMQ,gBAAgB,GAAGJ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACG,MAAM,CAACC,QAAQ,IAAIA,QAAQ,CAACC,SAAS,CAAC;QAClFvB,aAAa,CAACoB,gBAAgB,CAAC;MACnC;IACJ,CAAC,CAAC,OAAOD,GAAG,EAAE;MACVK,OAAO,CAACd,KAAK,CAAC,6BAA6B,EAAES,GAAG,CAAC;MACjDR,QAAQ,CAAC,4BAA4B,CAAC;IAC1C;EACJ,CAAC;EAED,MAAMc,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,UAAU,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBrB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IAEd,IAAI;MACA,MAAMG,QAAQ,GAAG,MAAMhC,KAAK,CAACiD,IAAI,CAAC,GAAGvC,YAAY,wBAAwB,EAAEO,OAAO,CAAC;MACnF,IAAIe,QAAQ,CAACE,IAAI,CAACN,OAAO,EAAE;QACvBC,UAAU,CAAC,0BAA0B,CAAC;QACtCX,UAAU,CAAC;UACPC,QAAQ,EAAE,EAAE;UACZC,SAAS,EAAE,EAAE;UACbC,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,aAAa,EAAE,EAAE;UACjBC,kBAAkB,EAAE,EAAE;UACtBC,OAAO,EAAE;QACb,CAAC,CAAC;MACN,CAAC,MAAM;QACHE,QAAQ,CAACK,QAAQ,CAACE,IAAI,CAACgB,OAAO,IAAI,oBAAoB,CAAC;MAC3D;IACJ,CAAC,CAAC,OAAOf,GAAG,EAAE;MACVR,QAAQ,CAAC,oBAAoB,CAAC;IAClC;EACJ,CAAC;EAED,oBACIlB,OAAA;IAAK0C,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExC3C,OAAA;MAAK0C,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACjB3C,OAAA;QAAI0C,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClE/C,OAAA;QAAG0C,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAuC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvE,CAAC,EAGL9B,KAAK,iBACFjB,OAAA;MAAK0C,SAAS,EAAC,+EAA+E;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eACvG3C,OAAA;QAAM0C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAE1B;MAAK;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACA5B,OAAO,iBACJnB,OAAA;MAAK0C,SAAS,EAAC,qFAAqF;MAACM,IAAI,EAAC,OAAO;MAAAL,QAAA,eAC7G3C,OAAA;QAAM0C,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAExB;MAAO;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGD/C,OAAA;MAAK0C,SAAS,EAAC,0CAA0C;MAAAC,QAAA,gBACrD3C,OAAA;QAAK0C,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACnC3C,OAAA;UAAK0C,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/C3C,OAAA,CAACR,UAAU;YAACkD,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACN/C,OAAA;UAAI0C,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxE,CAAC,eAEN/C,OAAA;QAAMiD,QAAQ,EAAEX,YAAa;QAACI,SAAS,EAAC,WAAW;QAAAC,QAAA,gBAC/C3C,OAAA;UAAK0C,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAElD3C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACP,MAAM;kBAACiD,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN/C,OAAA;gBACIkD,IAAI,EAAC,MAAM;gBACXhB,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE3B,OAAO,CAACE,QAAS;gBACxByC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKU,WAAW,EAAC,gBAAgB;gBAC5BC,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAS;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACjF/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACP,MAAM;kBAACiD,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN/C,OAAA;gBACIkD,IAAI,EAAC,MAAM;gBACXhB,IAAI,EAAC,WAAW;gBAChBC,KAAK,EAAE3B,OAAO,CAACG,SAAU;gBACzBwC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKU,WAAW,EAAC,iBAAiB;gBAC7BC,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7E/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACN,UAAU;kBAACgD,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChD,CAAC,eACN/C,OAAA;gBACIkD,IAAI,EAAC,OAAO;gBACZhB,IAAI,EAAC,OAAO;gBACZC,KAAK,EAAE3B,OAAO,CAACI,KAAM;gBACrBuC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKU,WAAW,EAAC,qBAAqB;gBACjCC,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChF/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACL,MAAM;kBAAC+C,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN/C,OAAA;gBACIkD,IAAI,EAAC,UAAU;gBACfhB,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAE3B,OAAO,CAACK,QAAS;gBACxBsC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKU,WAAW,EAAC,gBAAgB;gBAC5BC,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrF/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACJ,WAAW;kBAAC8C,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN/C,OAAA;gBACIkC,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAE3B,OAAO,CAACM,aAAc;gBAC7BqC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKW,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ,CAAE;gBAAAZ,QAAA,gBAE/B3C,OAAA;kBAAQmC,KAAK,EAAC,EAAE;kBAAAQ,QAAA,EAAC;gBAAoB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAC7C3C,KAAK,CAACoD,GAAG,CAACC,IAAI,iBACXzD,OAAA;kBAAsBmC,KAAK,EAAEsB,IAAI,CAACvB,IAAK;kBAAAS,QAAA,EAAEc,IAAI,CAACvB;gBAAI,GAArCuB,IAAI,CAACC,EAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAuC,CAC9D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC1F/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACF,aAAa;kBAAC4C,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC,eACN/C,OAAA;gBACIkC,IAAI,EAAC,oBAAoB;gBACzBC,KAAK,EAAE3B,OAAO,CAACO,kBAAmB;gBAClCoC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKW,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ,CAAE;gBAAAZ,QAAA,gBAE/B3C,OAAA;kBAAQmC,KAAK,EAAC,EAAE;kBAAAQ,QAAA,EAAC;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EAClDzC,UAAU,CAACkD,GAAG,CAAC3B,QAAQ,iBACpB7B,OAAA;kBAA0BmC,KAAK,EAAEN,QAAQ,CAAC8B,aAAc;kBAAAhB,QAAA,GACnDd,QAAQ,CAAC+B,eAAe,EAAC,GAAC,EAAC/B,QAAQ,CAAC8B,aAAa,EAAC,KAAG,EAAC9B,QAAQ,CAACgC,aAAa;gBAAA,GADpEhC,QAAQ,CAAC6B,EAAE;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACX,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAGN/C,OAAA;YAAA2C,QAAA,gBACI3C,OAAA;cAAO0C,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACvF/C,OAAA;cAAK0C,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC1C3C,OAAA;gBAAK0C,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eACtF3C,OAAA,CAACH,OAAO;kBAAC6C,SAAS,EAAC;gBAAwB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7C,CAAC,eACN/C,OAAA;gBACIkD,IAAI,EAAC,QAAQ;gBACbhB,IAAI,EAAC,SAAS;gBACdC,KAAK,EAAE3B,OAAO,CAACQ,OAAQ;gBACvBmC,QAAQ,EAAEnB,iBAAkB;gBAC5BU,SAAS,EAAC,sJAAsJ;gBAChKU,WAAW,EAAC,uBAAuB;gBACnCC,QAAQ;gBACRC,KAAK,EAAE;kBAACC,WAAW,EAAE;gBAAQ;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN/C,OAAA;UAAK0C,SAAS,EAAC,MAAM;UAAAC,QAAA,eACjB3C,OAAA;YACIkD,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,+NAA+N;YAAAC,QAAA,gBAEzO3C,OAAA,CAACR,UAAU;cAACkD,SAAS,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,aAC3C;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd;AAAC5C,EAAA,CA1QQD,OAAO;AAAA4D,EAAA,GAAP5D,OAAO;AA4QhB,eAAeA,OAAO;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}