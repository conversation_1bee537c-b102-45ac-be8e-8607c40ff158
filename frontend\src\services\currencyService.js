/**
 * Currency API Service
 * 
 * Handles all currency-related API calls with consistent error handling
 * and response formatting.
 */

import apiService from './apiService';

class CurrencyService {
    /**
     * Get all available currencies
     * @param {boolean} activeOnly - Whether to fetch only active currencies
     * @returns {Promise<ApiResponse>}
     */
    async getCurrencies(activeOnly = true) {
        return await apiService.get('get_currencies.php', { active_only: activeOnly });
    }

    /**
     * Get current exchange rates
     * @returns {Promise<ApiResponse>}
     */
    async getExchangeRates() {
        return await apiService.get('get_exchange_rates.php');
    }

    /**
     * Convert FanCoin amount to specific currency
     * @param {number} amount - FanCoin amount to convert
     * @param {number} currencyId - Target currency ID
     * @returns {Promise<ApiResponse>}
     */
    async convertCurrency(amount, currencyId) {
        return await apiService.get('convert_currency.php', {
            amount,
            currency_id: currencyId
        });
    }

    /**
     * Get user's currency preference
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserCurrencyPreference(userId) {
        return await apiService.get('get_user_currency_preference.php', {
            user_id: userId
        });
    }

    /**
     * Update user's currency preference
     * @param {number} userId - User ID
     * @param {number} currencyId - New currency ID
     * @returns {Promise<ApiResponse>}
     */
    async updateUserCurrencyPreference(userId, currencyId) {
        return await apiService.post('update_user_currency_preference.php', {
            user_id: userId,
            currency_id: currencyId
        });
    }

    /**
     * Update exchange rate (Admin only)
     * @param {number} currencyId - Currency ID
     * @param {number} rate - New exchange rate
     * @param {string} notes - Optional notes
     * @returns {Promise<ApiResponse>}
     */
    async updateExchangeRate(currencyId, rate, notes = '') {
        return await apiService.post('update_exchange_rate.php', {
            currency_id: currencyId,
            rate_to_fancoin: rate,
            notes
        });
    }

    /**
     * Manage currencies (Admin only)
     * @param {string} action - Action to perform (create, update, delete, toggle)
     * @param {object} data - Currency data
     * @returns {Promise<ApiResponse>}
     */
    async manageCurrencies(action, data) {
        return await apiService.post('manage_currencies.php', {
            action,
            ...data
        });
    }

    /**
     * Get currency by code
     * @param {string} currencyCode - Currency code (e.g., 'USD', 'ZAR')
     * @returns {Promise<ApiResponse>}
     */
    async getCurrencyByCode(currencyCode) {
        return await apiService.get('get_currencies.php', {
            currency_code: currencyCode,
            active_only: true
        });
    }

    /**
     * Batch convert multiple amounts
     * @param {Array} conversions - Array of {amount, currencyId} objects
     * @returns {Promise<Array<ApiResponse>>}
     */
    async batchConvert(conversions) {
        const promises = conversions.map(({ amount, currencyId }) =>
            this.convertCurrency(amount, currencyId)
        );
        
        return await Promise.all(promises);
    }

    /**
     * Get currency conversion rates for display
     * @param {number} baseAmount - Base FanCoin amount (default: 100)
     * @returns {Promise<ApiResponse>}
     */
    async getConversionExamples(baseAmount = 100) {
        const currenciesResponse = await this.getCurrencies(true);
        
        if (!currenciesResponse.success) {
            return currenciesResponse;
        }

        const currencies = currenciesResponse.data.currencies || [];
        const conversions = [];

        for (const currency of currencies) {
            const conversionResponse = await this.convertCurrency(baseAmount, currency.id);
            if (conversionResponse.success) {
                conversions.push({
                    currency: currency,
                    conversion: conversionResponse.data
                });
            }
        }

        return {
            success: true,
            data: {
                base_amount: baseAmount,
                conversions: conversions
            },
            message: 'Conversion examples retrieved successfully'
        };
    }
}

// Create singleton instance
const currencyService = new CurrencyService();

export default currencyService;
