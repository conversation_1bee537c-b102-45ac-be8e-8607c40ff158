{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Currency\\\\CurrencyAmount.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencyAmount.css';\n\n/**\n * CurrencyAmount Component\n * Displays FanCoin amounts in user's preferred currency with optional FanCoin display\n *\n * Props:\n * - amount: FanCoin amount to display\n * - showBoth: Whether to show both converted amount and FanCoin (default: true)\n * - showFanCoinOnly: Force display of only FanCoin amount (default: false)\n * - className: Additional CSS classes\n * - size: Display size ('small', 'medium', 'large') (default: 'medium')\n * - loading: Show loading state (default: false)\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencyAmount = ({\n  amount,\n  showBoth = true,\n  showFanCoinOnly = false,\n  className = '',\n  size = 'medium',\n  loading = false\n}) => {\n  _s();\n  const {\n    convertToUserCurrency,\n    userCurrency,\n    loading: currencyLoading,\n    initialized\n  } = useCurrency();\n\n  // Handle loading state\n  if (loading || currencyLoading || !initialized) {\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `currency-amount loading ${size} ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"loading-placeholder\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Handle invalid amount\n  if (amount === null || amount === undefined || isNaN(amount)) {\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `currency-amount error ${size} ${className}`,\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"error-text\",\n        children: \"Invalid amount\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Force FanCoin only display\n  if (showFanCoinOnly) {\n    return /*#__PURE__*/_jsxDEV(\"span\", {\n      className: `currency-amount fancoin-only ${size} ${className}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"amount-value\",\n        children: Number(amount).toFixed(2)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-label\",\n        children: \"FanCoin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 13\n    }, this);\n  }\n\n  // Convert to user's preferred currency\n  const conversion = convertToUserCurrency(amount);\n  const isFanCoinOnly = !userCurrency || conversion.currency === 'FC';\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    className: `currency-amount ${size} ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"primary-amount\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-symbol\",\n        children: conversion.symbol\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"amount-value\",\n        children: conversion.amount.toLocaleString('en-US', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"currency-code\",\n        children: conversion.currency\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 13\n    }, this), showBoth && !isFanCoinOnly && /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"secondary-amount\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"fancoin-amount\",\n        children: [\"(\", Number(amount).toFixed(2), \" FC)\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyBalance Component\n * Specialized component for displaying user balance with emphasis\n */\n_s(CurrencyAmount, \"tShpQxKiJl6Gb60E1uniKN3+XXQ=\", false, function () {\n  return [useCurrency];\n});\n_c = CurrencyAmount;\nexport const CurrencyBalance = ({\n  amount,\n  className = '',\n  size = 'large'\n}) => {\n  return /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n    amount: amount,\n    showBoth: true,\n    className: `currency-balance ${className}`,\n    size: size\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyBetAmount Component\n * Specialized component for displaying bet amounts\n */\n_c2 = CurrencyBalance;\nexport const CurrencyBetAmount = ({\n  amount,\n  className = '',\n  size = 'medium'\n}) => {\n  return /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n    amount: amount,\n    showBoth: true,\n    className: `currency-bet-amount ${className}`,\n    size: size\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 109,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyCompact Component\n * Compact display for tables and lists\n */\n_c3 = CurrencyBetAmount;\nexport const CurrencyCompact = ({\n  amount,\n  className = '',\n  showBoth = false\n}) => {\n  return /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n    amount: amount,\n    showBoth: showBoth,\n    className: `currency-compact ${className}`,\n    size: \"small\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 124,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyInput Component\n * Input field with currency conversion preview\n */\n_c4 = CurrencyCompact;\nexport const CurrencyInput = ({\n  value,\n  onChange,\n  placeholder = \"Enter amount\",\n  className = '',\n  showConversion = true\n}) => {\n  _s2();\n  const {\n    convertToUserCurrency,\n    userCurrency\n  } = useCurrency();\n  const conversion = value ? convertToUserCurrency(parseFloat(value) || 0) : null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-input-wrapper ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-group\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"number\",\n        value: value,\n        onChange: onChange,\n        placeholder: placeholder,\n        className: \"currency-input\",\n        min: \"0\",\n        step: \"0.01\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"input-suffix\",\n        children: \"FanCoin\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 160,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 13\n    }, this), showConversion && conversion && value && parseFloat(value) > 0 && conversion.currency !== 'FC' && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversion-preview\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"conversion-text\",\n        children: [\"\\u2248 \", conversion.symbol, conversion.amount.toLocaleString('en-US', {\n          minimumFractionDigits: 2,\n          maximumFractionDigits: 2\n        }), \" \", conversion.currency]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyComparison Component\n * Shows before/after amounts for transactions\n */\n_s2(CurrencyInput, \"8VqEg4q7Qc1T+jbN/0s//E8wxGM=\", false, function () {\n  return [useCurrency];\n});\n_c5 = CurrencyInput;\nexport const CurrencyComparison = ({\n  beforeAmount,\n  afterAmount,\n  type = 'transaction',\n  // 'transaction', 'bet', 'transfer'\n  className = ''\n}) => {\n  const difference = afterAmount - beforeAmount;\n  const isPositive = difference > 0;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-comparison ${type} ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"amount-row before\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label\",\n        children: \"Before:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n        amount: beforeAmount,\n        showBoth: false,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `amount-row change ${isPositive ? 'positive' : 'negative'}`,\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label\",\n        children: \"Change:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 199,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"change-indicator\",\n        children: [isPositive ? '+' : '', /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n          amount: difference,\n          showBoth: false,\n          size: \"small\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"amount-row after\",\n      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"label\",\n        children: \"After:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CurrencyAmount, {\n        amount: afterAmount,\n        showBoth: false,\n        size: \"small\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 192,\n    columnNumber: 9\n  }, this);\n};\n\n/**\n * CurrencyTooltip Component\n * Shows detailed conversion information on hover\n */\n_c6 = CurrencyComparison;\nexport const CurrencyTooltip = ({\n  amount,\n  children,\n  className = ''\n}) => {\n  _s3();\n  var _rate$rate_to_fancoin;\n  const {\n    convertToUserCurrency,\n    userCurrency,\n    exchangeRates\n  } = useCurrency();\n  if (!userCurrency) return children;\n  const conversion = convertToUserCurrency(amount);\n  const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `currency-tooltip-wrapper ${className}`,\n    title: `${amount} FanCoin = ${conversion.formatted}\\n` + `Exchange Rate: 1 FanCoin = ${userCurrency.currency_symbol}${(rate === null || rate === void 0 ? void 0 : (_rate$rate_to_fancoin = rate.rate_to_fancoin) === null || _rate$rate_to_fancoin === void 0 ? void 0 : _rate$rate_to_fancoin.toFixed(4)) || '1.0000'} ${userCurrency.currency_code}`,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 227,\n    columnNumber: 9\n  }, this);\n};\n_s3(CurrencyTooltip, \"DMB7/7eBn4grc+RxCM2/M/xwEyM=\", false, function () {\n  return [useCurrency];\n});\n_c7 = CurrencyTooltip;\nexport default CurrencyAmount;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"CurrencyAmount\");\n$RefreshReg$(_c2, \"CurrencyBalance\");\n$RefreshReg$(_c3, \"CurrencyBetAmount\");\n$RefreshReg$(_c4, \"CurrencyCompact\");\n$RefreshReg$(_c5, \"CurrencyInput\");\n$RefreshReg$(_c6, \"CurrencyComparison\");\n$RefreshReg$(_c7, \"CurrencyTooltip\");", "map": {"version": 3, "names": ["React", "useCurrency", "jsxDEV", "_jsxDEV", "CurrencyAmount", "amount", "showBoth", "showFanCoinOnly", "className", "size", "loading", "_s", "convertToUserCurrency", "userCurrency", "currencyLoading", "initialized", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "undefined", "isNaN", "Number", "toFixed", "conversion", "isFanCoinOnly", "currency", "symbol", "toLocaleString", "minimumFractionDigits", "maximumFractionDigits", "_c", "CurrencyBalance", "_c2", "CurrencyBetAmount", "_c3", "CurrencyCompact", "_c4", "CurrencyInput", "value", "onChange", "placeholder", "showConversion", "_s2", "parseFloat", "type", "min", "step", "_c5", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "beforeAmount", "afterAmount", "difference", "isPositive", "_c6", "CurrencyTooltip", "_s3", "_rate$rate_to_fancoin", "exchangeRates", "rate", "find", "r", "currency_id", "id", "title", "formatted", "currency_symbol", "rate_to_fancoin", "currency_code", "_c7", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Currency/CurrencyAmount.js"], "sourcesContent": ["import React from 'react';\nimport { useCurrency } from '../../contexts/CurrencyContext';\nimport './CurrencyAmount.css';\n\n/**\n * CurrencyAmount Component\n * Displays FanCoin amounts in user's preferred currency with optional FanCoin display\n *\n * Props:\n * - amount: FanCoin amount to display\n * - showBoth: Whether to show both converted amount and FanCoin (default: true)\n * - showFanCoinOnly: Force display of only FanCoin amount (default: false)\n * - className: Additional CSS classes\n * - size: Display size ('small', 'medium', 'large') (default: 'medium')\n * - loading: Show loading state (default: false)\n */\nconst CurrencyAmount = ({\n    amount,\n    showBoth = true,\n    showFanCoinOnly = false,\n    className = '',\n    size = 'medium',\n    loading = false\n}) => {\n    const {\n        convertToUserCurrency,\n        userCurrency,\n        loading: currencyLoading,\n        initialized\n    } = useCurrency();\n\n    // Handle loading state\n    if (loading || currencyLoading || !initialized) {\n        return (\n            <span className={`currency-amount loading ${size} ${className}`}>\n                <span className=\"loading-placeholder\">Loading...</span>\n            </span>\n        );\n    }\n\n    // Handle invalid amount\n    if (amount === null || amount === undefined || isNaN(amount)) {\n        return (\n            <span className={`currency-amount error ${size} ${className}`}>\n                <span className=\"error-text\">Invalid amount</span>\n            </span>\n        );\n    }\n\n    // Force FanCoin only display\n    if (showFanCoinOnly) {\n        return (\n            <span className={`currency-amount fancoin-only ${size} ${className}`}>\n                <span className=\"amount-value\">{Number(amount).toFixed(2)}</span>\n                <span className=\"currency-label\">FanCoin</span>\n            </span>\n        );\n    }\n\n    // Convert to user's preferred currency\n    const conversion = convertToUserCurrency(amount);\n    const isFanCoinOnly = !userCurrency || conversion.currency === 'FC';\n\n    return (\n        <span className={`currency-amount ${size} ${className}`}>\n            <span className=\"primary-amount\">\n                <span className=\"currency-symbol\">{conversion.symbol}</span>\n                <span className=\"amount-value\">\n                    {conversion.amount.toLocaleString('en-US', {\n                        minimumFractionDigits: 2,\n                        maximumFractionDigits: 2\n                    })}\n                </span>\n                <span className=\"currency-code\">{conversion.currency}</span>\n            </span>\n\n            {showBoth && !isFanCoinOnly && (\n                <span className=\"secondary-amount\">\n                    <span className=\"fancoin-amount\">\n                        ({Number(amount).toFixed(2)} FC)\n                    </span>\n                </span>\n            )}\n        </span>\n    );\n};\n\n/**\n * CurrencyBalance Component\n * Specialized component for displaying user balance with emphasis\n */\nexport const CurrencyBalance = ({ amount, className = '', size = 'large' }) => {\n    return (\n        <CurrencyAmount \n            amount={amount}\n            showBoth={true}\n            className={`currency-balance ${className}`}\n            size={size}\n        />\n    );\n};\n\n/**\n * CurrencyBetAmount Component\n * Specialized component for displaying bet amounts\n */\nexport const CurrencyBetAmount = ({ amount, className = '', size = 'medium' }) => {\n    return (\n        <CurrencyAmount \n            amount={amount}\n            showBoth={true}\n            className={`currency-bet-amount ${className}`}\n            size={size}\n        />\n    );\n};\n\n/**\n * CurrencyCompact Component\n * Compact display for tables and lists\n */\nexport const CurrencyCompact = ({ amount, className = '', showBoth = false }) => {\n    return (\n        <CurrencyAmount \n            amount={amount}\n            showBoth={showBoth}\n            className={`currency-compact ${className}`}\n            size=\"small\"\n        />\n    );\n};\n\n/**\n * CurrencyInput Component\n * Input field with currency conversion preview\n */\nexport const CurrencyInput = ({\n    value,\n    onChange,\n    placeholder = \"Enter amount\",\n    className = '',\n    showConversion = true\n}) => {\n    const { convertToUserCurrency, userCurrency } = useCurrency();\n\n    const conversion = value ? convertToUserCurrency(parseFloat(value) || 0) : null;\n\n    return (\n        <div className={`currency-input-wrapper ${className}`}>\n            <div className=\"input-group\">\n                <input\n                    type=\"number\"\n                    value={value}\n                    onChange={onChange}\n                    placeholder={placeholder}\n                    className=\"currency-input\"\n                    min=\"0\"\n                    step=\"0.01\"\n                />\n                <span className=\"input-suffix\">FanCoin</span>\n            </div>\n\n            {showConversion && conversion && value && parseFloat(value) > 0 && conversion.currency !== 'FC' && (\n                <div className=\"conversion-preview\">\n                    <span className=\"conversion-text\">\n                        ≈ {conversion.symbol}\n                        {conversion.amount.toLocaleString('en-US', {\n                            minimumFractionDigits: 2,\n                            maximumFractionDigits: 2\n                        })} {conversion.currency}\n                    </span>\n                </div>\n            )}\n        </div>\n    );\n};\n\n/**\n * CurrencyComparison Component\n * Shows before/after amounts for transactions\n */\nexport const CurrencyComparison = ({ \n    beforeAmount, \n    afterAmount, \n    type = 'transaction', // 'transaction', 'bet', 'transfer'\n    className = '' \n}) => {\n    const difference = afterAmount - beforeAmount;\n    const isPositive = difference > 0;\n    \n    return (\n        <div className={`currency-comparison ${type} ${className}`}>\n            <div className=\"amount-row before\">\n                <span className=\"label\">Before:</span>\n                <CurrencyAmount amount={beforeAmount} showBoth={false} size=\"small\" />\n            </div>\n            \n            <div className={`amount-row change ${isPositive ? 'positive' : 'negative'}`}>\n                <span className=\"label\">Change:</span>\n                <span className=\"change-indicator\">\n                    {isPositive ? '+' : ''}\n                    <CurrencyAmount amount={difference} showBoth={false} size=\"small\" />\n                </span>\n            </div>\n            \n            <div className=\"amount-row after\">\n                <span className=\"label\">After:</span>\n                <CurrencyAmount amount={afterAmount} showBoth={false} size=\"small\" />\n            </div>\n        </div>\n    );\n};\n\n/**\n * CurrencyTooltip Component\n * Shows detailed conversion information on hover\n */\nexport const CurrencyTooltip = ({ amount, children, className = '' }) => {\n    const { convertToUserCurrency, userCurrency, exchangeRates } = useCurrency();\n\n    if (!userCurrency) return children;\n\n    const conversion = convertToUserCurrency(amount);\n    const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);\n\n    return (\n        <div className={`currency-tooltip-wrapper ${className}`} title={\n            `${amount} FanCoin = ${conversion.formatted}\\n` +\n            `Exchange Rate: 1 FanCoin = ${userCurrency.currency_symbol}${rate?.rate_to_fancoin?.toFixed(4) || '1.0000'} ${userCurrency.currency_code}`\n        }>\n            {children}\n        </div>\n    );\n};\n\nexport default CurrencyAmount;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,gCAAgC;AAC5D,OAAO,sBAAsB;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAC,MAAA,IAAAC,OAAA;AAYA,MAAMC,cAAc,GAAGA,CAAC;EACpBC,MAAM;EACNC,QAAQ,GAAG,IAAI;EACfC,eAAe,GAAG,KAAK;EACvBC,SAAS,GAAG,EAAE;EACdC,IAAI,GAAG,QAAQ;EACfC,OAAO,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACF,MAAM;IACFC,qBAAqB;IACrBC,YAAY;IACZH,OAAO,EAAEI,eAAe;IACxBC;EACJ,CAAC,GAAGd,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIS,OAAO,IAAII,eAAe,IAAI,CAACC,WAAW,EAAE;IAC5C,oBACIZ,OAAA;MAAMK,SAAS,EAAE,2BAA2BC,IAAI,IAAID,SAAS,EAAG;MAAAQ,QAAA,eAC5Db,OAAA;QAAMK,SAAS,EAAC,qBAAqB;QAAAQ,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrD,CAAC;EAEf;;EAEA;EACA,IAAIf,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKgB,SAAS,IAAIC,KAAK,CAACjB,MAAM,CAAC,EAAE;IAC1D,oBACIF,OAAA;MAAMK,SAAS,EAAE,yBAAyBC,IAAI,IAAID,SAAS,EAAG;MAAAQ,QAAA,eAC1Db,OAAA;QAAMK,SAAS,EAAC,YAAY;QAAAQ,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEf;;EAEA;EACA,IAAIb,eAAe,EAAE;IACjB,oBACIJ,OAAA;MAAMK,SAAS,EAAE,gCAAgCC,IAAI,IAAID,SAAS,EAAG;MAAAQ,QAAA,gBACjEb,OAAA;QAAMK,SAAS,EAAC,cAAc;QAAAQ,QAAA,EAAEO,MAAM,CAAClB,MAAM,CAAC,CAACmB,OAAO,CAAC,CAAC;MAAC;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACjEjB,OAAA;QAAMK,SAAS,EAAC,gBAAgB;QAAAQ,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEf;;EAEA;EACA,MAAMK,UAAU,GAAGb,qBAAqB,CAACP,MAAM,CAAC;EAChD,MAAMqB,aAAa,GAAG,CAACb,YAAY,IAAIY,UAAU,CAACE,QAAQ,KAAK,IAAI;EAEnE,oBACIxB,OAAA;IAAMK,SAAS,EAAE,mBAAmBC,IAAI,IAAID,SAAS,EAAG;IAAAQ,QAAA,gBACpDb,OAAA;MAAMK,SAAS,EAAC,gBAAgB;MAAAQ,QAAA,gBAC5Bb,OAAA;QAAMK,SAAS,EAAC,iBAAiB;QAAAQ,QAAA,EAAES,UAAU,CAACG;MAAM;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eAC5DjB,OAAA;QAAMK,SAAS,EAAC,cAAc;QAAAQ,QAAA,EACzBS,UAAU,CAACpB,MAAM,CAACwB,cAAc,CAAC,OAAO,EAAE;UACvCC,qBAAqB,EAAE,CAAC;UACxBC,qBAAqB,EAAE;QAC3B,CAAC;MAAC;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACPjB,OAAA;QAAMK,SAAS,EAAC,eAAe;QAAAQ,QAAA,EAAES,UAAU,CAACE;MAAQ;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC,EAENd,QAAQ,IAAI,CAACoB,aAAa,iBACvBvB,OAAA;MAAMK,SAAS,EAAC,kBAAkB;MAAAQ,QAAA,eAC9Bb,OAAA;QAAMK,SAAS,EAAC,gBAAgB;QAAAQ,QAAA,GAAC,GAC5B,EAACO,MAAM,CAAClB,MAAM,CAAC,CAACmB,OAAO,CAAC,CAAC,CAAC,EAAC,MAChC;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEf,CAAC;;AAED;AACA;AACA;AACA;AAHAT,EAAA,CAvEMP,cAAc;EAAA,QAaZH,WAAW;AAAA;AAAA+B,EAAA,GAbb5B,cAAc;AA2EpB,OAAO,MAAM6B,eAAe,GAAGA,CAAC;EAAE5B,MAAM;EAAEG,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAQ,CAAC,KAAK;EAC3E,oBACIN,OAAA,CAACC,cAAc;IACXC,MAAM,EAAEA,MAAO;IACfC,QAAQ,EAAE,IAAK;IACfE,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAC3CC,IAAI,EAAEA;EAAK;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AAHAc,GAAA,GAXaD,eAAe;AAe5B,OAAO,MAAME,iBAAiB,GAAGA,CAAC;EAAE9B,MAAM;EAAEG,SAAS,GAAG,EAAE;EAAEC,IAAI,GAAG;AAAS,CAAC,KAAK;EAC9E,oBACIN,OAAA,CAACC,cAAc;IACXC,MAAM,EAAEA,MAAO;IACfC,QAAQ,EAAE,IAAK;IACfE,SAAS,EAAE,uBAAuBA,SAAS,EAAG;IAC9CC,IAAI,EAAEA;EAAK;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AAHAgB,GAAA,GAXaD,iBAAiB;AAe9B,OAAO,MAAME,eAAe,GAAGA,CAAC;EAAEhC,MAAM;EAAEG,SAAS,GAAG,EAAE;EAAEF,QAAQ,GAAG;AAAM,CAAC,KAAK;EAC7E,oBACIH,OAAA,CAACC,cAAc;IACXC,MAAM,EAAEA,MAAO;IACfC,QAAQ,EAAEA,QAAS;IACnBE,SAAS,EAAE,oBAAoBA,SAAS,EAAG;IAC3CC,IAAI,EAAC;EAAO;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV,CAAC;;AAED;AACA;AACA;AACA;AAHAkB,GAAA,GAXaD,eAAe;AAe5B,OAAO,MAAME,aAAa,GAAGA,CAAC;EAC1BC,KAAK;EACLC,QAAQ;EACRC,WAAW,GAAG,cAAc;EAC5BlC,SAAS,GAAG,EAAE;EACdmC,cAAc,GAAG;AACrB,CAAC,KAAK;EAAAC,GAAA;EACF,MAAM;IAAEhC,qBAAqB;IAAEC;EAAa,CAAC,GAAGZ,WAAW,CAAC,CAAC;EAE7D,MAAMwB,UAAU,GAAGe,KAAK,GAAG5B,qBAAqB,CAACiC,UAAU,CAACL,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI;EAE/E,oBACIrC,OAAA;IAAKK,SAAS,EAAE,0BAA0BA,SAAS,EAAG;IAAAQ,QAAA,gBAClDb,OAAA;MAAKK,SAAS,EAAC,aAAa;MAAAQ,QAAA,gBACxBb,OAAA;QACI2C,IAAI,EAAC,QAAQ;QACbN,KAAK,EAAEA,KAAM;QACbC,QAAQ,EAAEA,QAAS;QACnBC,WAAW,EAAEA,WAAY;QACzBlC,SAAS,EAAC,gBAAgB;QAC1BuC,GAAG,EAAC,GAAG;QACPC,IAAI,EAAC;MAAM;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC,eACFjB,OAAA;QAAMK,SAAS,EAAC,cAAc;QAAAQ,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,EAELuB,cAAc,IAAIlB,UAAU,IAAIe,KAAK,IAAIK,UAAU,CAACL,KAAK,CAAC,GAAG,CAAC,IAAIf,UAAU,CAACE,QAAQ,KAAK,IAAI,iBAC3FxB,OAAA;MAAKK,SAAS,EAAC,oBAAoB;MAAAQ,QAAA,eAC/Bb,OAAA;QAAMK,SAAS,EAAC,iBAAiB;QAAAQ,QAAA,GAAC,SAC5B,EAACS,UAAU,CAACG,MAAM,EACnBH,UAAU,CAACpB,MAAM,CAACwB,cAAc,CAAC,OAAO,EAAE;UACvCC,qBAAqB,EAAE,CAAC;UACxBC,qBAAqB,EAAE;QAC3B,CAAC,CAAC,EAAC,GAAC,EAACN,UAAU,CAACE,QAAQ;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHAwB,GAAA,CAzCaL,aAAa;EAAA,QAO0BtC,WAAW;AAAA;AAAAgD,GAAA,GAPlDV,aAAa;AA6C1B,OAAO,MAAMW,kBAAkB,GAAGA,CAAC;EAC/BC,YAAY;EACZC,WAAW;EACXN,IAAI,GAAG,aAAa;EAAE;EACtBtC,SAAS,GAAG;AAChB,CAAC,KAAK;EACF,MAAM6C,UAAU,GAAGD,WAAW,GAAGD,YAAY;EAC7C,MAAMG,UAAU,GAAGD,UAAU,GAAG,CAAC;EAEjC,oBACIlD,OAAA;IAAKK,SAAS,EAAE,uBAAuBsC,IAAI,IAAItC,SAAS,EAAG;IAAAQ,QAAA,gBACvDb,OAAA;MAAKK,SAAS,EAAC,mBAAmB;MAAAQ,QAAA,gBAC9Bb,OAAA;QAAMK,SAAS,EAAC,OAAO;QAAAQ,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCjB,OAAA,CAACC,cAAc;QAACC,MAAM,EAAE8C,YAAa;QAAC7C,QAAQ,EAAE,KAAM;QAACG,IAAI,EAAC;MAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAENjB,OAAA;MAAKK,SAAS,EAAE,qBAAqB8C,UAAU,GAAG,UAAU,GAAG,UAAU,EAAG;MAAAtC,QAAA,gBACxEb,OAAA;QAAMK,SAAS,EAAC,OAAO;QAAAQ,QAAA,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCjB,OAAA;QAAMK,SAAS,EAAC,kBAAkB;QAAAQ,QAAA,GAC7BsC,UAAU,GAAG,GAAG,GAAG,EAAE,eACtBnD,OAAA,CAACC,cAAc;UAACC,MAAM,EAAEgD,UAAW;UAAC/C,QAAQ,EAAE,KAAM;UAACG,IAAI,EAAC;QAAO;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENjB,OAAA;MAAKK,SAAS,EAAC,kBAAkB;MAAAQ,QAAA,gBAC7Bb,OAAA;QAAMK,SAAS,EAAC,OAAO;QAAAQ,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrCjB,OAAA,CAACC,cAAc;QAACC,MAAM,EAAE+C,WAAY;QAAC9C,QAAQ,EAAE,KAAM;QAACG,IAAI,EAAC;MAAO;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AACA;AACA;AACA;AAHAmC,GAAA,GAhCaL,kBAAkB;AAoC/B,OAAO,MAAMM,eAAe,GAAGA,CAAC;EAAEnD,MAAM;EAAEW,QAAQ;EAAER,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAiD,GAAA;EAAA,IAAAC,qBAAA;EACrE,MAAM;IAAE9C,qBAAqB;IAAEC,YAAY;IAAE8C;EAAc,CAAC,GAAG1D,WAAW,CAAC,CAAC;EAE5E,IAAI,CAACY,YAAY,EAAE,OAAOG,QAAQ;EAElC,MAAMS,UAAU,GAAGb,qBAAqB,CAACP,MAAM,CAAC;EAChD,MAAMuD,IAAI,GAAGD,aAAa,CAACE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,KAAKlD,YAAY,CAACmD,EAAE,CAAC;EAEvE,oBACI7D,OAAA;IAAKK,SAAS,EAAE,4BAA4BA,SAAS,EAAG;IAACyD,KAAK,EAC1D,GAAG5D,MAAM,cAAcoB,UAAU,CAACyC,SAAS,IAAI,GAC/C,8BAA8BrD,YAAY,CAACsD,eAAe,GAAG,CAAAP,IAAI,aAAJA,IAAI,wBAAAF,qBAAA,GAAJE,IAAI,CAAEQ,eAAe,cAAAV,qBAAA,uBAArBA,qBAAA,CAAuBlC,OAAO,CAAC,CAAC,CAAC,KAAI,QAAQ,IAAIX,YAAY,CAACwD,aAAa,EAC3I;IAAArD,QAAA,EACIA;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAACqC,GAAA,CAhBWD,eAAe;EAAA,QACuCvD,WAAW;AAAA;AAAAqE,GAAA,GADjEd,eAAe;AAkB5B,eAAepD,cAAc;AAAC,IAAA4B,EAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAE,GAAA,EAAAW,GAAA,EAAAM,GAAA,EAAAe,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAArC,GAAA;AAAAqC,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAjC,GAAA;AAAAiC,YAAA,CAAAtB,GAAA;AAAAsB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}