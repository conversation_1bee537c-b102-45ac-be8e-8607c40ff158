import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import MainLayout from '../components/Layout/MainLayout';
import ErrorAlert from '../components/ErrorAlert';
import { handleError, retryOperation } from '../utils/errorHandler';
import HeroSlider from '../components/WelcomePage/HeroSlider';
import ChallengesList from '../components/WelcomePage/ChallengesList';
import RecentBets from '../components/WelcomePage/RecentBets';
import './WelcomeSplash.css';

const API_BASE_URL = '/backend';

const WelcomeSplash = () => {
  const [recentChallenges, setRecentChallenges] = useState([]);
  const [recentBets, setRecentBets] = useState([]);
  const [loading, setLoading] = useState({
    challenges: true,
    bets: true
  });
  const [error, setError] = useState(null);
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userName, setUserName] = useState('');

  const sliderImages = [
    process.env.PUBLIC_URL + '/slider/Slider1.png',
    process.env.PUBLIC_URL + '/slider/Slider2.png'
  ];

  useEffect(() => {
    const fetchData = async () => {
      await Promise.all([
        fetchRecentChallenges(),
        fetchRecentBets()
      ]);
    };

    fetchData();

    const userId = localStorage.getItem('userId');
    setIsLoggedIn(!!userId);

    if (userId) {
      const storedUserName = localStorage.getItem('userName');
      if (storedUserName) {
        setUserName(storedUserName);
      } else {
        fetchUserName(userId);
      }
    }
  }, []);

  const fetchRecentChallenges = async () => {
    try {
      setLoading(prev => ({ ...prev, challenges: true }));
      const response = await retryOperation(async () => {
        const result = await axios.get(`${API_BASE_URL}/handlers/recent_challenges.php`);
        return result;
      });

      if (response.data.success && Array.isArray(response.data.challenges)) {
        const challenges = response.data.challenges.map(challenge => ({
          ...challenge,
          end_time: new Date(challenge.end_time)
        }));
        setRecentChallenges(challenges);
      } else {
        // If no challenges or unexpected response format, set empty array
        setRecentChallenges([]);
      }
    } catch (err) {
      console.error("Error fetching challenges:", err);
      // Don't show error to user, just set empty array
      setRecentChallenges([]);
    } finally {
      setLoading(prev => ({ ...prev, challenges: false }));
    }
  };

  const fetchRecentBets = async () => {
    try {
      setLoading(prev => ({ ...prev, bets: true }));
      const response = await retryOperation(async () => {
        const result = await axios.get(`${API_BASE_URL}/handlers/welcome_recent_bets.php`);
        return result;
      });

      if (response.data.success && Array.isArray(response.data.bets)) {
        setRecentBets(response.data.bets);
      } else {
        // If no bets or unexpected response format, set empty array
        setRecentBets([]);
      }
    } catch (err) {
      console.error("Error fetching recent bets:", err);
      // Don't show error to user, just set empty array
      setRecentBets([]);
    } finally {
      setLoading(prev => ({ ...prev, bets: false }));
    }
  };

  const fetchUserName = async (userId) => {
    try {
      const response = await retryOperation(async () => {
        const result = await axios.get(`${API_BASE_URL}/handlers/get_user_info.php?user_id=${userId}`);
        if (!result.data.success) {
          throw new Error(result.data.message || 'Failed to fetch user information');
        }
        return result;
      });

      if (response.data.user && response.data.user.username) {
        setUserName(response.data.user.username);
        localStorage.setItem('userName', response.data.user.username);
      }
    } catch (err) {
      const appError = handleError(err, { component: 'WelcomeSplash', operation: 'fetchUserName' });
      console.error(appError);
      // Don't set error state to avoid showing error alert for this operation
    }
  };

  return (
    <MainLayout>
      <div className="welcome welcome-splash">
        <div className="welcome__content">
          {error && <ErrorAlert error={error} onClose={() => setError(null)} />}

          {/* Hero Section */}
          <HeroSlider sliderImages={sliderImages} />

          {/* Live Challenges Section */}
          <section className="section challenges-section">
            <div className="section__header">
              <h2 className="section__title">LIVE CHALLENGES</h2>
              <div className="section__actions">
                {isLoggedIn ? (
                  <>
                    <span className="user-welcome">Welcome, {userName}</span>
                    <Link to="/user/dashboard" className="section__link">Dashboard</Link>
                  </>
                ) : (
                  <Link to="/login" className="section__link login-link">Login</Link>
                )}
              </div>
            </div>
            <ChallengesList
              recentChallenges={recentChallenges}
              loading={loading.challenges}
              isLoggedIn={isLoggedIn}
              API_BASE_URL={API_BASE_URL}
            />
          </section>

          {/* Recent Bets Section */}
          <section className="section recent-bets-section">
            <div className="section__header">
              <h2 className="section__title">RECENT BETS</h2>
              <Link to="/user/recent-bets" className="section__link">View All</Link>
            </div>
            <RecentBets
              recentBets={recentBets}
              loading={loading.bets}
              API_BASE_URL={API_BASE_URL}
            />
          </section>
        </div>
      </div>
    </MainLayout>
  );
};

export default WelcomeSplash;
