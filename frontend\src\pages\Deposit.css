/**
 * Enhanced Deposit Page Styles with Currency Support
 */

.deposit-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.deposit-container h1 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
    text-align: center;
}

.deposit-subtitle {
    text-align: center;
    color: #6c757d;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

/* Step Container */
.step-container {
    margin-top: 2rem;
}

.step-container h2 {
    margin: 0 0 1.5rem 0;
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #495057;
    font-weight: 500;
    font-size: 1rem;
}

.form-group input[type="file"] {
    width: 100%;
    padding: 0.75rem;
    border: 2px dashed #ced4da;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-group input[type="file"]:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Conversion Info */
.conversion-info {
    margin-top: 1rem;
    padding: 1rem;
    background: #e8f5e8;
    border: 1px solid #c3e6c3;
    border-radius: 8px;
}

.conversion-info p {
    margin: 0;
    color: #2d5a2d;
    font-weight: 500;
}

/* Amount Summary */
.amount-summary {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.amount-summary p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #495057;
}

/* Payment Methods Grid */
.payment-methods-grid {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.payment-method-card {
    padding: 1.5rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
}

.payment-method-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
    transform: translateY(-2px);
}

.payment-method-card h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.payment-method-card p {
    margin: 0 0 1rem 0;
    color: #6c757d;
    font-size: 0.95rem;
}

.method-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    margin-top: 1rem;
}

.method-details p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #495057;
}

.method-details p:last-child {
    margin-bottom: 0;
}

.no-methods {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
}

/* Payment Summary */
.payment-summary {
    background: #e3f2fd;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #bbdefb;
}

.payment-summary p {
    margin: 0 0 0.5rem 0;
    color: #1565c0;
    font-size: 1rem;
}

.payment-summary p:last-child {
    margin-bottom: 0;
}

/* Instructions */
.instructions {
    background: #fff3cd;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #ffeaa7;
}

.instructions h3 {
    margin: 0 0 1rem 0;
    color: #856404;
    font-size: 1.1rem;
    font-weight: 600;
}

.instructions p {
    margin: 0;
    color: #856404;
    line-height: 1.5;
}

/* Success Step */
.success-step {
    text-align: center;
}

.success-content {
    background: #d4edda;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    border: 1px solid #c3e6cb;
}

.success-content p {
    margin: 0 0 1rem 0;
    color: #155724;
    font-size: 1.1rem;
    line-height: 1.5;
}

.success-content p:last-child {
    margin-bottom: 0;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 120px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #545b62;
    transform: translateY(-1px);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Error and Success Messages */
.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #f5c6cb;
    font-weight: 500;
}

.success-message {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    border: 1px solid #c3e6cb;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .deposit-container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .deposit-container h1 {
        font-size: 1.75rem;
    }
    
    .step-container h2 {
        font-size: 1.25rem;
    }
    
    .payment-method-card {
        padding: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
        justify-content: center;
    }
    
    .payment-summary,
    .instructions,
    .success-content {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .deposit-container {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .deposit-subtitle {
        font-size: 1rem;
    }
    
    .conversion-info,
    .amount-summary {
        padding: 0.75rem;
    }
}
