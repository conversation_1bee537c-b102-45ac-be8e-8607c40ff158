{"ast": null, "code": "/**\n * Centralized API Services Export\n * \n * This file exports all API services for easy importing throughout the application.\n * It provides a single point of access to all API functionality.\n */\n\n// Core API service\nexport { default as apiService, ApiResponse } from './apiService';\n\n// Domain-specific services\nexport { default as currencyService } from './currencyService';\nexport { default as userService } from './userService';\nexport { default as betService } from './betService';\n\n// Service utilities and hooks\nexport { default as useApiService } from '../hooks/useApiService';\n\n/**\n * Service factory for creating custom service instances\n * @param {string} baseEndpoint - Base endpoint for the service\n * @returns {object} Service instance with CRUD methods\n */\nexport const createService = baseEndpoint => {\n  const apiService = require('./apiService').default;\n  return {\n    getAll: (params = {}) => apiService.get(`${baseEndpoint}.php`, params),\n    getById: id => apiService.get(`${baseEndpoint}.php`, {\n      id\n    }),\n    create: data => apiService.post(`${baseEndpoint}.php`, data),\n    update: (id, data) => apiService.put(`${baseEndpoint}.php`, {\n      id,\n      ...data\n    }),\n    delete: id => apiService.delete(`${baseEndpoint}.php`, {\n      id\n    })\n  };\n};\n\n/**\n * Common API endpoints for quick access\n */\nexport const endpoints = {\n  // Authentication\n  login: 'login.php',\n  register: 'register.php',\n  logout: 'logout.php',\n  // User management\n  userData: 'user_data.php',\n  updateProfile: 'update_user_profile.php',\n  changePassword: 'change_password.php',\n  // Currency system\n  currencies: 'get_currencies.php',\n  exchangeRates: 'get_exchange_rates.php',\n  convertCurrency: 'convert_currency.php',\n  // Betting\n  bets: 'get_bets.php',\n  createBet: 'create_bet.php',\n  acceptBet: 'accept_bet.php',\n  // Admin\n  adminDashboard: 'admin_dashboard_data.php',\n  userManagement: 'user_management.php',\n  betManagement: 'bet_management.php',\n  // Teams and leagues\n  teams: 'team_management.php',\n  leagues: 'league_management.php',\n  // Settings\n  generalSettings: 'get_general_settings.php',\n  securitySettings: 'get_security_settings.php',\n  siteConfig: 'get_site_config.php'\n};\n\n/**\n * Service status checker\n * @returns {Promise<object>} Service health status\n */\nexport const checkServiceHealth = async () => {\n  const apiService = require('./apiService').default;\n  try {\n    var _response$data, _response$data2;\n    const response = await apiService.get('health_check.php');\n    return {\n      healthy: response.success,\n      timestamp: new Date().toISOString(),\n      services: {\n        api: response.success,\n        database: ((_response$data = response.data) === null || _response$data === void 0 ? void 0 : _response$data.database) || false,\n        cache: ((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : _response$data2.cache) || false\n      }\n    };\n  } catch (error) {\n    return {\n      healthy: false,\n      timestamp: new Date().toISOString(),\n      error: error.message,\n      services: {\n        api: false,\n        database: false,\n        cache: false\n      }\n    };\n  }\n};", "map": {"version": 3, "names": ["default", "apiService", "ApiResponse", "currencyService", "userService", "betService", "useApiService", "createService", "baseEndpoint", "require", "getAll", "params", "get", "getById", "id", "create", "data", "post", "update", "put", "delete", "endpoints", "login", "register", "logout", "userData", "updateProfile", "changePassword", "currencies", "exchangeRates", "convertCurrency", "bets", "createBet", "acceptBet", "adminDashboard", "userManagement", "betManagement", "teams", "leagues", "generalSettings", "securitySettings", "siteConfig", "checkServiceHealth", "_response$data", "_response$data2", "response", "healthy", "success", "timestamp", "Date", "toISOString", "services", "api", "database", "cache", "error", "message"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/index.js"], "sourcesContent": ["/**\n * Centralized API Services Export\n * \n * This file exports all API services for easy importing throughout the application.\n * It provides a single point of access to all API functionality.\n */\n\n// Core API service\nexport { default as apiService, ApiResponse } from './apiService';\n\n// Domain-specific services\nexport { default as currencyService } from './currencyService';\nexport { default as userService } from './userService';\nexport { default as betService } from './betService';\n\n// Service utilities and hooks\nexport { default as useApiService } from '../hooks/useApiService';\n\n/**\n * Service factory for creating custom service instances\n * @param {string} baseEndpoint - Base endpoint for the service\n * @returns {object} Service instance with CRUD methods\n */\nexport const createService = (baseEndpoint) => {\n    const apiService = require('./apiService').default;\n    \n    return {\n        getAll: (params = {}) => apiService.get(`${baseEndpoint}.php`, params),\n        getById: (id) => apiService.get(`${baseEndpoint}.php`, { id }),\n        create: (data) => apiService.post(`${baseEndpoint}.php`, data),\n        update: (id, data) => apiService.put(`${baseEndpoint}.php`, { id, ...data }),\n        delete: (id) => apiService.delete(`${baseEndpoint}.php`, { id })\n    };\n};\n\n/**\n * Common API endpoints for quick access\n */\nexport const endpoints = {\n    // Authentication\n    login: 'login.php',\n    register: 'register.php',\n    logout: 'logout.php',\n    \n    // User management\n    userData: 'user_data.php',\n    updateProfile: 'update_user_profile.php',\n    changePassword: 'change_password.php',\n    \n    // Currency system\n    currencies: 'get_currencies.php',\n    exchangeRates: 'get_exchange_rates.php',\n    convertCurrency: 'convert_currency.php',\n    \n    // Betting\n    bets: 'get_bets.php',\n    createBet: 'create_bet.php',\n    acceptBet: 'accept_bet.php',\n    \n    // Admin\n    adminDashboard: 'admin_dashboard_data.php',\n    userManagement: 'user_management.php',\n    betManagement: 'bet_management.php',\n    \n    // Teams and leagues\n    teams: 'team_management.php',\n    leagues: 'league_management.php',\n    \n    // Settings\n    generalSettings: 'get_general_settings.php',\n    securitySettings: 'get_security_settings.php',\n    siteConfig: 'get_site_config.php'\n};\n\n/**\n * Service status checker\n * @returns {Promise<object>} Service health status\n */\nexport const checkServiceHealth = async () => {\n    const apiService = require('./apiService').default;\n    \n    try {\n        const response = await apiService.get('health_check.php');\n        return {\n            healthy: response.success,\n            timestamp: new Date().toISOString(),\n            services: {\n                api: response.success,\n                database: response.data?.database || false,\n                cache: response.data?.cache || false\n            }\n        };\n    } catch (error) {\n        return {\n            healthy: false,\n            timestamp: new Date().toISOString(),\n            error: error.message,\n            services: {\n                api: false,\n                database: false,\n                cache: false\n            }\n        };\n    }\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,OAAO,IAAIC,UAAU,EAAEC,WAAW,QAAQ,cAAc;;AAEjE;AACA,SAASF,OAAO,IAAIG,eAAe,QAAQ,mBAAmB;AAC9D,SAASH,OAAO,IAAII,WAAW,QAAQ,eAAe;AACtD,SAASJ,OAAO,IAAIK,UAAU,QAAQ,cAAc;;AAEpD;AACA,SAASL,OAAO,IAAIM,aAAa,QAAQ,wBAAwB;;AAEjE;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAIC,YAAY,IAAK;EAC3C,MAAMP,UAAU,GAAGQ,OAAO,CAAC,cAAc,CAAC,CAACT,OAAO;EAElD,OAAO;IACHU,MAAM,EAAEA,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKV,UAAU,CAACW,GAAG,CAAC,GAAGJ,YAAY,MAAM,EAAEG,MAAM,CAAC;IACtEE,OAAO,EAAGC,EAAE,IAAKb,UAAU,CAACW,GAAG,CAAC,GAAGJ,YAAY,MAAM,EAAE;MAAEM;IAAG,CAAC,CAAC;IAC9DC,MAAM,EAAGC,IAAI,IAAKf,UAAU,CAACgB,IAAI,CAAC,GAAGT,YAAY,MAAM,EAAEQ,IAAI,CAAC;IAC9DE,MAAM,EAAEA,CAACJ,EAAE,EAAEE,IAAI,KAAKf,UAAU,CAACkB,GAAG,CAAC,GAAGX,YAAY,MAAM,EAAE;MAAEM,EAAE;MAAE,GAAGE;IAAK,CAAC,CAAC;IAC5EI,MAAM,EAAGN,EAAE,IAAKb,UAAU,CAACmB,MAAM,CAAC,GAAGZ,YAAY,MAAM,EAAE;MAAEM;IAAG,CAAC;EACnE,CAAC;AACL,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMO,SAAS,GAAG;EACrB;EACAC,KAAK,EAAE,WAAW;EAClBC,QAAQ,EAAE,cAAc;EACxBC,MAAM,EAAE,YAAY;EAEpB;EACAC,QAAQ,EAAE,eAAe;EACzBC,aAAa,EAAE,yBAAyB;EACxCC,cAAc,EAAE,qBAAqB;EAErC;EACAC,UAAU,EAAE,oBAAoB;EAChCC,aAAa,EAAE,wBAAwB;EACvCC,eAAe,EAAE,sBAAsB;EAEvC;EACAC,IAAI,EAAE,cAAc;EACpBC,SAAS,EAAE,gBAAgB;EAC3BC,SAAS,EAAE,gBAAgB;EAE3B;EACAC,cAAc,EAAE,0BAA0B;EAC1CC,cAAc,EAAE,qBAAqB;EACrCC,aAAa,EAAE,oBAAoB;EAEnC;EACAC,KAAK,EAAE,qBAAqB;EAC5BC,OAAO,EAAE,uBAAuB;EAEhC;EACAC,eAAe,EAAE,0BAA0B;EAC3CC,gBAAgB,EAAE,2BAA2B;EAC7CC,UAAU,EAAE;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC1C,MAAMzC,UAAU,GAAGQ,OAAO,CAAC,cAAc,CAAC,CAACT,OAAO;EAElD,IAAI;IAAA,IAAA2C,cAAA,EAAAC,eAAA;IACA,MAAMC,QAAQ,GAAG,MAAM5C,UAAU,CAACW,GAAG,CAAC,kBAAkB,CAAC;IACzD,OAAO;MACHkC,OAAO,EAAED,QAAQ,CAACE,OAAO;MACzBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCC,QAAQ,EAAE;QACNC,GAAG,EAAEP,QAAQ,CAACE,OAAO;QACrBM,QAAQ,EAAE,EAAAV,cAAA,GAAAE,QAAQ,CAAC7B,IAAI,cAAA2B,cAAA,uBAAbA,cAAA,CAAeU,QAAQ,KAAI,KAAK;QAC1CC,KAAK,EAAE,EAAAV,eAAA,GAAAC,QAAQ,CAAC7B,IAAI,cAAA4B,eAAA,uBAAbA,eAAA,CAAeU,KAAK,KAAI;MACnC;IACJ,CAAC;EACL,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ,OAAO;MACHT,OAAO,EAAE,KAAK;MACdE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCK,KAAK,EAAEA,KAAK,CAACC,OAAO;MACpBL,QAAQ,EAAE;QACNC,GAAG,EAAE,KAAK;QACVC,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE;MACX;IACJ,CAAC;EACL;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}