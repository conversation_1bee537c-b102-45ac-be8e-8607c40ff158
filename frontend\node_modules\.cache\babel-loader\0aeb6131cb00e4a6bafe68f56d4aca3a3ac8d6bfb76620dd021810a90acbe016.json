{"ast": null, "code": "/**\n * User API Service\n * \n * Handles all user-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\nclass UserService {\n  /**\n   * Get user data by ID\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserData(userId) {\n    return await apiService.get('user_data.php', {\n      id: userId\n    });\n  }\n\n  /**\n   * Update user profile\n   * @param {number} userId - User ID\n   * @param {object} userData - User data to update\n   * @returns {Promise<ApiResponse>}\n   */\n  async updateUserProfile(userId, userData) {\n    return await apiService.post('update_user_profile.php', {\n      user_id: userId,\n      ...userData\n    });\n  }\n\n  /**\n   * Get user balance\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserBalance(userId) {\n    return await apiService.get('get_user_balance.php', {\n      user_id: userId\n    });\n  }\n\n  /**\n   * Get user friends\n   * @param {number} userId - User ID\n   * @param {string} status - Friend status filter\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserFriends(userId, status = 'accepted') {\n    return await apiService.get('get_friends.php', {\n      user_id: userId,\n      status\n    });\n  }\n\n  /**\n   * Send friend request\n   * @param {number} fromUserId - Sender user ID\n   * @param {number} toUserId - Recipient user ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async sendFriendRequest(fromUserId, toUserId) {\n    return await apiService.post('send_friend_request.php', {\n      from_user_id: fromUserId,\n      to_user_id: toUserId\n    });\n  }\n\n  /**\n   * Accept friend request\n   * @param {number} requestId - Friend request ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async acceptFriendRequest(requestId) {\n    return await apiService.post('accept_friend_request.php', {\n      request_id: requestId\n    });\n  }\n\n  /**\n   * Reject friend request\n   * @param {number} requestId - Friend request ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async rejectFriendRequest(requestId) {\n    return await apiService.post('reject_friend_request.php', {\n      request_id: requestId\n    });\n  }\n\n  /**\n   * Transfer points between users\n   * @param {number} fromUserId - Sender user ID\n   * @param {number} toUserId - Recipient user ID\n   * @param {number} amount - Amount to transfer\n   * @param {string} note - Optional transfer note\n   * @returns {Promise<ApiResponse>}\n   */\n  async transferPoints(fromUserId, toUserId, amount, note = '') {\n    return await apiService.post('transfer_points.php', {\n      from_user_id: fromUserId,\n      to_user_id: toUserId,\n      amount,\n      note\n    });\n  }\n\n  /**\n   * Get user achievements\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserAchievements(userId) {\n    return await apiService.get('get_user_achievements.php', {\n      user_id: userId\n    });\n  }\n\n  /**\n   * Get user transaction history\n   * @param {number} userId - User ID\n   * @param {number} page - Page number\n   * @param {number} limit - Items per page\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserTransactions(userId, page = 1, limit = 20) {\n    return await apiService.get('get_user_transactions.php', {\n      user_id: userId,\n      page,\n      limit\n    });\n  }\n\n  /**\n   * Change user password\n   * @param {number} userId - User ID\n   * @param {string} currentPassword - Current password\n   * @param {string} newPassword - New password\n   * @returns {Promise<ApiResponse>}\n   */\n  async changePassword(userId, currentPassword, newPassword) {\n    return await apiService.post('change_password.php', {\n      user_id: userId,\n      current_password: currentPassword,\n      new_password: newPassword\n    });\n  }\n\n  /**\n   * Upload user avatar\n   * @param {number} userId - User ID\n   * @param {File} avatarFile - Avatar image file\n   * @param {function} onProgress - Upload progress callback\n   * @returns {Promise<ApiResponse>}\n   */\n  async uploadAvatar(userId, avatarFile, onProgress = null) {\n    const formData = new FormData();\n    formData.append('user_id', userId);\n    formData.append('avatar', avatarFile);\n    return await apiService.upload('upload_avatar.php', formData, onProgress);\n  }\n\n  /**\n   * Get user statistics\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserStats(userId) {\n    return await apiService.get('get_user_stats.php', {\n      user_id: userId\n    });\n  }\n\n  /**\n   * Search users\n   * @param {string} query - Search query\n   * @param {number} limit - Maximum results\n   * @returns {Promise<ApiResponse>}\n   */\n  async searchUsers(query, limit = 10) {\n    return await apiService.get('search_users.php', {\n      query,\n      limit\n    });\n  }\n\n  /**\n   * Get user leaderboard position\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserLeaderboardPosition(userId) {\n    return await apiService.get('get_user_leaderboard_position.php', {\n      user_id: userId\n    });\n  }\n}\n\n// Create singleton instance\nconst userService = new UserService();\nexport default userService;", "map": {"version": 3, "names": ["apiService", "UserService", "getUserData", "userId", "get", "id", "updateUserProfile", "userData", "post", "user_id", "getUserBalance", "getUserFriends", "status", "sendFriendRequest", "fromUserId", "toUserId", "from_user_id", "to_user_id", "acceptFriendRequest", "requestId", "request_id", "rejectFriendRequest", "transferPoints", "amount", "note", "getUserAchievements", "getUserTransactions", "page", "limit", "changePassword", "currentPassword", "newPassword", "current_password", "new_password", "uploadAvatar", "avatar<PERSON>ile", "onProgress", "formData", "FormData", "append", "upload", "getUserStats", "searchUsers", "query", "getUserLeaderboardPosition", "userService"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/userService.js"], "sourcesContent": ["/**\n * User API Service\n * \n * Handles all user-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\n\nclass UserService {\n    /**\n     * Get user data by ID\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserData(userId) {\n        return await apiService.get('user_data.php', { id: userId });\n    }\n\n    /**\n     * Update user profile\n     * @param {number} userId - User ID\n     * @param {object} userData - User data to update\n     * @returns {Promise<ApiResponse>}\n     */\n    async updateUserProfile(userId, userData) {\n        return await apiService.post('update_user_profile.php', {\n            user_id: userId,\n            ...userData\n        });\n    }\n\n    /**\n     * Get user balance\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserBalance(userId) {\n        return await apiService.get('get_user_balance.php', { user_id: userId });\n    }\n\n    /**\n     * Get user friends\n     * @param {number} userId - User ID\n     * @param {string} status - Friend status filter\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserFriends(userId, status = 'accepted') {\n        return await apiService.get('get_friends.php', {\n            user_id: userId,\n            status\n        });\n    }\n\n    /**\n     * Send friend request\n     * @param {number} fromUserId - Sender user ID\n     * @param {number} toUserId - Recipient user ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async sendFriendRequest(fromUserId, toUserId) {\n        return await apiService.post('send_friend_request.php', {\n            from_user_id: fromUserId,\n            to_user_id: toUserId\n        });\n    }\n\n    /**\n     * Accept friend request\n     * @param {number} requestId - Friend request ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async acceptFriendRequest(requestId) {\n        return await apiService.post('accept_friend_request.php', {\n            request_id: requestId\n        });\n    }\n\n    /**\n     * Reject friend request\n     * @param {number} requestId - Friend request ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async rejectFriendRequest(requestId) {\n        return await apiService.post('reject_friend_request.php', {\n            request_id: requestId\n        });\n    }\n\n    /**\n     * Transfer points between users\n     * @param {number} fromUserId - Sender user ID\n     * @param {number} toUserId - Recipient user ID\n     * @param {number} amount - Amount to transfer\n     * @param {string} note - Optional transfer note\n     * @returns {Promise<ApiResponse>}\n     */\n    async transferPoints(fromUserId, toUserId, amount, note = '') {\n        return await apiService.post('transfer_points.php', {\n            from_user_id: fromUserId,\n            to_user_id: toUserId,\n            amount,\n            note\n        });\n    }\n\n    /**\n     * Get user achievements\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserAchievements(userId) {\n        return await apiService.get('get_user_achievements.php', {\n            user_id: userId\n        });\n    }\n\n    /**\n     * Get user transaction history\n     * @param {number} userId - User ID\n     * @param {number} page - Page number\n     * @param {number} limit - Items per page\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserTransactions(userId, page = 1, limit = 20) {\n        return await apiService.get('get_user_transactions.php', {\n            user_id: userId,\n            page,\n            limit\n        });\n    }\n\n    /**\n     * Change user password\n     * @param {number} userId - User ID\n     * @param {string} currentPassword - Current password\n     * @param {string} newPassword - New password\n     * @returns {Promise<ApiResponse>}\n     */\n    async changePassword(userId, currentPassword, newPassword) {\n        return await apiService.post('change_password.php', {\n            user_id: userId,\n            current_password: currentPassword,\n            new_password: newPassword\n        });\n    }\n\n    /**\n     * Upload user avatar\n     * @param {number} userId - User ID\n     * @param {File} avatarFile - Avatar image file\n     * @param {function} onProgress - Upload progress callback\n     * @returns {Promise<ApiResponse>}\n     */\n    async uploadAvatar(userId, avatarFile, onProgress = null) {\n        const formData = new FormData();\n        formData.append('user_id', userId);\n        formData.append('avatar', avatarFile);\n\n        return await apiService.upload('upload_avatar.php', formData, onProgress);\n    }\n\n    /**\n     * Get user statistics\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserStats(userId) {\n        return await apiService.get('get_user_stats.php', { user_id: userId });\n    }\n\n    /**\n     * Search users\n     * @param {string} query - Search query\n     * @param {number} limit - Maximum results\n     * @returns {Promise<ApiResponse>}\n     */\n    async searchUsers(query, limit = 10) {\n        return await apiService.get('search_users.php', {\n            query,\n            limit\n        });\n    }\n\n    /**\n     * Get user leaderboard position\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserLeaderboardPosition(userId) {\n        return await apiService.get('get_user_leaderboard_position.php', {\n            user_id: userId\n        });\n    }\n}\n\n// Create singleton instance\nconst userService = new UserService();\n\nexport default userService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,UAAU,MAAM,cAAc;AAErC,MAAMC,WAAW,CAAC;EACd;AACJ;AACA;AACA;AACA;EACI,MAAMC,WAAWA,CAACC,MAAM,EAAE;IACtB,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,eAAe,EAAE;MAAEC,EAAE,EAAEF;IAAO,CAAC,CAAC;EAChE;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMG,iBAAiBA,CAACH,MAAM,EAAEI,QAAQ,EAAE;IACtC,OAAO,MAAMP,UAAU,CAACQ,IAAI,CAAC,yBAAyB,EAAE;MACpDC,OAAO,EAAEN,MAAM;MACf,GAAGI;IACP,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMG,cAAcA,CAACP,MAAM,EAAE;IACzB,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,sBAAsB,EAAE;MAAEK,OAAO,EAAEN;IAAO,CAAC,CAAC;EAC5E;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMQ,cAAcA,CAACR,MAAM,EAAES,MAAM,GAAG,UAAU,EAAE;IAC9C,OAAO,MAAMZ,UAAU,CAACI,GAAG,CAAC,iBAAiB,EAAE;MAC3CK,OAAO,EAAEN,MAAM;MACfS;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,iBAAiBA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IAC1C,OAAO,MAAMf,UAAU,CAACQ,IAAI,CAAC,yBAAyB,EAAE;MACpDQ,YAAY,EAAEF,UAAU;MACxBG,UAAU,EAAEF;IAChB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMG,mBAAmBA,CAACC,SAAS,EAAE;IACjC,OAAO,MAAMnB,UAAU,CAACQ,IAAI,CAAC,2BAA2B,EAAE;MACtDY,UAAU,EAAED;IAChB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAME,mBAAmBA,CAACF,SAAS,EAAE;IACjC,OAAO,MAAMnB,UAAU,CAACQ,IAAI,CAAC,2BAA2B,EAAE;MACtDY,UAAU,EAAED;IAChB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMG,cAAcA,CAACR,UAAU,EAAEC,QAAQ,EAAEQ,MAAM,EAAEC,IAAI,GAAG,EAAE,EAAE;IAC1D,OAAO,MAAMxB,UAAU,CAACQ,IAAI,CAAC,qBAAqB,EAAE;MAChDQ,YAAY,EAAEF,UAAU;MACxBG,UAAU,EAAEF,QAAQ;MACpBQ,MAAM;MACNC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMC,mBAAmBA,CAACtB,MAAM,EAAE;IAC9B,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,2BAA2B,EAAE;MACrDK,OAAO,EAAEN;IACb,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMuB,mBAAmBA,CAACvB,MAAM,EAAEwB,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAE;IACpD,OAAO,MAAM5B,UAAU,CAACI,GAAG,CAAC,2BAA2B,EAAE;MACrDK,OAAO,EAAEN,MAAM;MACfwB,IAAI;MACJC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,cAAcA,CAAC1B,MAAM,EAAE2B,eAAe,EAAEC,WAAW,EAAE;IACvD,OAAO,MAAM/B,UAAU,CAACQ,IAAI,CAAC,qBAAqB,EAAE;MAChDC,OAAO,EAAEN,MAAM;MACf6B,gBAAgB,EAAEF,eAAe;MACjCG,YAAY,EAAEF;IAClB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMG,YAAYA,CAAC/B,MAAM,EAAEgC,UAAU,EAAEC,UAAU,GAAG,IAAI,EAAE;IACtD,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAEpC,MAAM,CAAC;IAClCkC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEJ,UAAU,CAAC;IAErC,OAAO,MAAMnC,UAAU,CAACwC,MAAM,CAAC,mBAAmB,EAAEH,QAAQ,EAAED,UAAU,CAAC;EAC7E;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMK,YAAYA,CAACtC,MAAM,EAAE;IACvB,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,oBAAoB,EAAE;MAAEK,OAAO,EAAEN;IAAO,CAAC,CAAC;EAC1E;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMuC,WAAWA,CAACC,KAAK,EAAEf,KAAK,GAAG,EAAE,EAAE;IACjC,OAAO,MAAM5B,UAAU,CAACI,GAAG,CAAC,kBAAkB,EAAE;MAC5CuC,KAAK;MACLf;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMgB,0BAA0BA,CAACzC,MAAM,EAAE;IACrC,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,mCAAmC,EAAE;MAC7DK,OAAO,EAAEN;IACb,CAAC,CAAC;EACN;AACJ;;AAEA;AACA,MAAM0C,WAAW,GAAG,IAAI5C,WAAW,CAAC,CAAC;AAErC,eAAe4C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}