.league-leaderboard {
    padding: 20px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
}

.header-left {
    display: flex;
    gap: 20px;
}

.title-section {
    display: flex;
    flex-direction: column;
}

.title-section h1 {
    margin: 0;
    color: #333;
}

.season-info {
    display: flex;
    gap: 20px;
    color: #666;
    font-size: 0.9em;
    margin-top: 5px;
}

.back-button {
    background: none;
    border: none;
    color: #007bff;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
}

.back-button:hover {
    text-decoration: underline;
}

.period-selector select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
}

.leaderboard-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.leaderboard-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 80px 2fr 1fr 1fr 1fr 100px;
    padding: 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-row {
    display: grid;
    grid-template-columns: 80px 2fr 1fr 1fr 1fr 100px;
    padding: 15px;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s;
}

.table-row:hover {
    background-color: #f8f9fa;
}

.rank-col {
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 5px;
}

.rank-col.gold { color: #ffd700; }
.rank-col.silver { color: #c0c0c0; }
.rank-col.bronze { color: #cd7f32; }

.rank-badge {
    font-size: 1.2em;
}

.user-col {
    display: flex;
    align-items: center;
    gap: 10px;
}

.username {
    font-weight: 500;
}

.badges {
    display: flex;
    gap: 5px;
}

.badge {
    font-size: 1.2em;
    cursor: help;
}

.points-col {
    font-weight: 600;
    color: #28a745;
}

.stats-col, .winrate-col {
    color: #666;
}

.view-stats-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s;
}

.view-stats-button:hover {
    background-color: #0056b3;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.stats-modal {
    background: white;
    border-radius: 10px;
    padding: 30px;
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
}

.close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.stats-content {
    margin-top: 20px;
}

.stats-section {
    margin-bottom: 30px;
}

.stats-section h3 {
    color: #333;
    margin-bottom: 15px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.stat-item .label {
    color: #666;
    font-size: 0.9em;
}

.stat-item .value {
    font-size: 1.2em;
    font-weight: 600;
    color: #333;
}

.stat-item.wins .value { color: #28a745; }
.stat-item.draws .value { color: #ffc107; }
.stat-item.losses .value { color: #dc3545; }

.performance-chart {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.performance-dot {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: help;
}

.performance-dot.win { background-color: #28a745; }
.performance-dot.draw { background-color: #ffc107; }
.performance-dot.loss { background-color: #dc3545; }

.error-message {
    background-color: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-size: 18px;
}
