import React from 'react';

function DashboardOverview({ dashboardData, getTeamLogo }) {
    if (!dashboardData) return <div>Loading...</div>;

    return (
        <div className="dashboard">
            <div className="card card-half">
                <h2>User Overview</h2>
                <div className="stats">
                    <div className="stat-item">
                        <div className="stat-value">{dashboardData.totalUsers}</div>
                        <div className="stat-label">Total Users</div>
                    </div>
                </div>
            </div>
            <div className="card card-half">
                <h2>Challenge Overview</h2>
                <div className="stats">
                    <div className="stat-item">
                        <div className="stat-value">{dashboardData.activeChallenges}</div>
                        <div className="stat-label">Active Challenges</div>
                    </div>
                </div>
            </div>
            <div className="card card-half">
                <h2>Total Wins</h2>
                <div className="stats">
                    <div className="stat-item">
                        <div className="stat-value">${dashboardData.totalWins}</div>
                        <div className="stat-label">Total Winnings</div>
                    </div>
                </div>
            </div>
            <div className="card card-half">
                <h2>Total Bets</h2>
                <div className="stats">
                    <div className="stat-item">
                        <div className="stat-value">{dashboardData.totalBets}</div>
                        <div className="stat-label">Bets Placed</div>
                    </div>
                </div>
            </div>
            <div className="card card-full">
                <h2>Top Teams Overview</h2>
                <div className="teams-container">
                    {dashboardData.teamStats && dashboardData.teamStats.map(team => (
                        <div key={team.id} className="team-card">
                            <img
                                src={getTeamLogo(team.name)}
                                alt={team.name}
                                className="team-logo-circle"
                            />
                            <p className="team-name">{team.name}</p>
                            <div className="team-stats">
                                <span className="user-count">{team.user_count}</span>
                                <span className="user-label">Users</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}

export default DashboardOverview;
