<?php
/**
 * Test OTP Email Functionality
 * Quick test to verify OTP email sending is working
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get the first admin for testing
    $stmt = $conn->query("SELECT admin_id, username, email FROM admins LIMIT 1");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("No admin found for testing");
    }

    echo "Testing OTP email for admin: {$admin['username']} ({$admin['email']})\n\n";

    // Test OTP generation and email sending
    $testData = [
        'admin_id' => $admin['admin_id']
    ];

    // Make a request to the OTP sender
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/FanBet247/backend/handlers/admin_send_otp.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Response Code: $httpCode\n";
    echo "Response: $response\n\n";

    $responseData = json_decode($response, true);
    
    if ($responseData && $responseData['success']) {
        echo "✅ OTP email sent successfully!\n";
        echo "Email sent to: " . ($responseData['email_masked'] ?? 'N/A') . "\n";
        echo "Expires in: " . ($responseData['expires_in'] ?? 'N/A') . " seconds\n";
        
        // Check if OTP was stored in database
        $stmt = $conn->prepare("
            SELECT otp, expires_at, created_at 
            FROM admin_otp 
            WHERE admin_id = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$admin['admin_id']]);
        $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($otpRecord) {
            echo "✅ OTP stored in database\n";
            echo "OTP Code: " . $otpRecord['otp'] . "\n";
            echo "Expires at: " . $otpRecord['expires_at'] . "\n";
            echo "Created at: " . $otpRecord['created_at'] . "\n";
        } else {
            echo "❌ OTP not found in database\n";
        }
        
    } else {
        echo "❌ OTP email failed\n";
        echo "Error: " . ($responseData['message'] ?? 'Unknown error') . "\n";
    }

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
}
?>
