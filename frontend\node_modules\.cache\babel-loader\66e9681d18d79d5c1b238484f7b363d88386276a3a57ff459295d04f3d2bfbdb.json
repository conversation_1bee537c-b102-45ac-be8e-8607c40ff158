{"ast": null, "code": "/**\n * Currency API Service\n * \n * Handles all currency-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\nclass CurrencyService {\n  /**\n   * Get all available currencies\n   * @param {boolean} activeOnly - Whether to fetch only active currencies\n   * @returns {Promise<ApiResponse>}\n   */\n  async getCurrencies(activeOnly = true) {\n    return await apiService.get('get_currencies.php', {\n      active_only: activeOnly\n    });\n  }\n\n  /**\n   * Get current exchange rates\n   * @returns {Promise<ApiResponse>}\n   */\n  async getExchangeRates() {\n    return await apiService.get('get_exchange_rates.php');\n  }\n\n  /**\n   * Convert FanCoin amount to specific currency\n   * @param {number} amount - FanCoin amount to convert\n   * @param {number} currencyId - Target currency ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async convertCurrency(amount, currencyId) {\n    return await apiService.get('convert_currency.php', {\n      amount,\n      currency_id: currencyId\n    });\n  }\n\n  /**\n   * Get user's currency preference\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserCurrencyPreference(userId) {\n    return await apiService.get('get_user_currency_preference.php', {\n      user_id: userId\n    });\n  }\n\n  /**\n   * Update user's currency preference\n   * @param {number} userId - User ID\n   * @param {number} currencyId - New currency ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async updateUserCurrencyPreference(userId, currencyId) {\n    return await apiService.post('update_user_currency_preference.php', {\n      user_id: userId,\n      currency_id: currencyId\n    });\n  }\n\n  /**\n   * Update exchange rate (Admin only)\n   * @param {number} currencyId - Currency ID\n   * @param {number} rate - New exchange rate\n   * @param {string} notes - Optional notes\n   * @returns {Promise<ApiResponse>}\n   */\n  async updateExchangeRate(currencyId, rate, notes = '') {\n    return await apiService.post('update_exchange_rate.php', {\n      currency_id: currencyId,\n      rate_to_fancoin: rate,\n      notes\n    });\n  }\n\n  /**\n   * Manage currencies (Admin only)\n   * @param {string} action - Action to perform (create, update, delete, toggle)\n   * @param {object} data - Currency data\n   * @returns {Promise<ApiResponse>}\n   */\n  async manageCurrencies(action, data) {\n    return await apiService.post('manage_currencies.php', {\n      action,\n      ...data\n    });\n  }\n\n  /**\n   * Get currency by code\n   * @param {string} currencyCode - Currency code (e.g., 'USD', 'ZAR')\n   * @returns {Promise<ApiResponse>}\n   */\n  async getCurrencyByCode(currencyCode) {\n    return await apiService.get('get_currencies.php', {\n      currency_code: currencyCode,\n      active_only: true\n    });\n  }\n\n  /**\n   * Batch convert multiple amounts\n   * @param {Array} conversions - Array of {amount, currencyId} objects\n   * @returns {Promise<Array<ApiResponse>>}\n   */\n  async batchConvert(conversions) {\n    const promises = conversions.map(({\n      amount,\n      currencyId\n    }) => this.convertCurrency(amount, currencyId));\n    return await Promise.all(promises);\n  }\n\n  /**\n   * Get currency conversion rates for display\n   * @param {number} baseAmount - Base FanCoin amount (default: 100)\n   * @returns {Promise<ApiResponse>}\n   */\n  async getConversionExamples(baseAmount = 100) {\n    const currenciesResponse = await this.getCurrencies(true);\n    if (!currenciesResponse.success) {\n      return currenciesResponse;\n    }\n    const currencies = currenciesResponse.data.currencies || [];\n    const conversions = [];\n    for (const currency of currencies) {\n      const conversionResponse = await this.convertCurrency(baseAmount, currency.id);\n      if (conversionResponse.success) {\n        conversions.push({\n          currency: currency,\n          conversion: conversionResponse.data\n        });\n      }\n    }\n    return {\n      success: true,\n      data: {\n        base_amount: baseAmount,\n        conversions: conversions\n      },\n      message: 'Conversion examples retrieved successfully'\n    };\n  }\n}\n\n// Create singleton instance\nconst currencyService = new CurrencyService();\nexport default currencyService;", "map": {"version": 3, "names": ["apiService", "CurrencyService", "getCurrencies", "activeOnly", "get", "active_only", "getExchangeRates", "convertCurrency", "amount", "currencyId", "currency_id", "getUserCurrencyPreference", "userId", "user_id", "updateUserCurrencyPreference", "post", "updateExchangeRate", "rate", "notes", "rate_to_fancoin", "manageCurrencies", "action", "data", "getCurrencyByCode", "currencyCode", "currency_code", "batchConvert", "conversions", "promises", "map", "Promise", "all", "getConversionExamples", "baseAmount", "currenciesResponse", "success", "currencies", "currency", "conversionResponse", "id", "push", "conversion", "base_amount", "message", "currencyService"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/currencyService.js"], "sourcesContent": ["/**\n * Currency API Service\n * \n * Handles all currency-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\n\nclass CurrencyService {\n    /**\n     * Get all available currencies\n     * @param {boolean} activeOnly - Whether to fetch only active currencies\n     * @returns {Promise<ApiResponse>}\n     */\n    async getCurrencies(activeOnly = true) {\n        return await apiService.get('get_currencies.php', { active_only: activeOnly });\n    }\n\n    /**\n     * Get current exchange rates\n     * @returns {Promise<ApiResponse>}\n     */\n    async getExchangeRates() {\n        return await apiService.get('get_exchange_rates.php');\n    }\n\n    /**\n     * Convert FanCoin amount to specific currency\n     * @param {number} amount - FanCoin amount to convert\n     * @param {number} currencyId - Target currency ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async convertCurrency(amount, currencyId) {\n        return await apiService.get('convert_currency.php', {\n            amount,\n            currency_id: currencyId\n        });\n    }\n\n    /**\n     * Get user's currency preference\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserCurrencyPreference(userId) {\n        return await apiService.get('get_user_currency_preference.php', {\n            user_id: userId\n        });\n    }\n\n    /**\n     * Update user's currency preference\n     * @param {number} userId - User ID\n     * @param {number} currencyId - New currency ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async updateUserCurrencyPreference(userId, currencyId) {\n        return await apiService.post('update_user_currency_preference.php', {\n            user_id: userId,\n            currency_id: currencyId\n        });\n    }\n\n    /**\n     * Update exchange rate (Admin only)\n     * @param {number} currencyId - Currency ID\n     * @param {number} rate - New exchange rate\n     * @param {string} notes - Optional notes\n     * @returns {Promise<ApiResponse>}\n     */\n    async updateExchangeRate(currencyId, rate, notes = '') {\n        return await apiService.post('update_exchange_rate.php', {\n            currency_id: currencyId,\n            rate_to_fancoin: rate,\n            notes\n        });\n    }\n\n    /**\n     * Manage currencies (Admin only)\n     * @param {string} action - Action to perform (create, update, delete, toggle)\n     * @param {object} data - Currency data\n     * @returns {Promise<ApiResponse>}\n     */\n    async manageCurrencies(action, data) {\n        return await apiService.post('manage_currencies.php', {\n            action,\n            ...data\n        });\n    }\n\n    /**\n     * Get currency by code\n     * @param {string} currencyCode - Currency code (e.g., 'USD', 'ZAR')\n     * @returns {Promise<ApiResponse>}\n     */\n    async getCurrencyByCode(currencyCode) {\n        return await apiService.get('get_currencies.php', {\n            currency_code: currencyCode,\n            active_only: true\n        });\n    }\n\n    /**\n     * Batch convert multiple amounts\n     * @param {Array} conversions - Array of {amount, currencyId} objects\n     * @returns {Promise<Array<ApiResponse>>}\n     */\n    async batchConvert(conversions) {\n        const promises = conversions.map(({ amount, currencyId }) =>\n            this.convertCurrency(amount, currencyId)\n        );\n        \n        return await Promise.all(promises);\n    }\n\n    /**\n     * Get currency conversion rates for display\n     * @param {number} baseAmount - Base FanCoin amount (default: 100)\n     * @returns {Promise<ApiResponse>}\n     */\n    async getConversionExamples(baseAmount = 100) {\n        const currenciesResponse = await this.getCurrencies(true);\n        \n        if (!currenciesResponse.success) {\n            return currenciesResponse;\n        }\n\n        const currencies = currenciesResponse.data.currencies || [];\n        const conversions = [];\n\n        for (const currency of currencies) {\n            const conversionResponse = await this.convertCurrency(baseAmount, currency.id);\n            if (conversionResponse.success) {\n                conversions.push({\n                    currency: currency,\n                    conversion: conversionResponse.data\n                });\n            }\n        }\n\n        return {\n            success: true,\n            data: {\n                base_amount: baseAmount,\n                conversions: conversions\n            },\n            message: 'Conversion examples retrieved successfully'\n        };\n    }\n}\n\n// Create singleton instance\nconst currencyService = new CurrencyService();\n\nexport default currencyService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,UAAU,MAAM,cAAc;AAErC,MAAMC,eAAe,CAAC;EAClB;AACJ;AACA;AACA;AACA;EACI,MAAMC,aAAaA,CAACC,UAAU,GAAG,IAAI,EAAE;IACnC,OAAO,MAAMH,UAAU,CAACI,GAAG,CAAC,oBAAoB,EAAE;MAAEC,WAAW,EAAEF;IAAW,CAAC,CAAC;EAClF;;EAEA;AACJ;AACA;AACA;EACI,MAAMG,gBAAgBA,CAAA,EAAG;IACrB,OAAO,MAAMN,UAAU,CAACI,GAAG,CAAC,wBAAwB,CAAC;EACzD;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMG,eAAeA,CAACC,MAAM,EAAEC,UAAU,EAAE;IACtC,OAAO,MAAMT,UAAU,CAACI,GAAG,CAAC,sBAAsB,EAAE;MAChDI,MAAM;MACNE,WAAW,EAAED;IACjB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAME,yBAAyBA,CAACC,MAAM,EAAE;IACpC,OAAO,MAAMZ,UAAU,CAACI,GAAG,CAAC,kCAAkC,EAAE;MAC5DS,OAAO,EAAED;IACb,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,4BAA4BA,CAACF,MAAM,EAAEH,UAAU,EAAE;IACnD,OAAO,MAAMT,UAAU,CAACe,IAAI,CAAC,qCAAqC,EAAE;MAChEF,OAAO,EAAED,MAAM;MACfF,WAAW,EAAED;IACjB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMO,kBAAkBA,CAACP,UAAU,EAAEQ,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAE;IACnD,OAAO,MAAMlB,UAAU,CAACe,IAAI,CAAC,0BAA0B,EAAE;MACrDL,WAAW,EAAED,UAAU;MACvBU,eAAe,EAAEF,IAAI;MACrBC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,gBAAgBA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACjC,OAAO,MAAMtB,UAAU,CAACe,IAAI,CAAC,uBAAuB,EAAE;MAClDM,MAAM;MACN,GAAGC;IACP,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMC,iBAAiBA,CAACC,YAAY,EAAE;IAClC,OAAO,MAAMxB,UAAU,CAACI,GAAG,CAAC,oBAAoB,EAAE;MAC9CqB,aAAa,EAAED,YAAY;MAC3BnB,WAAW,EAAE;IACjB,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMqB,YAAYA,CAACC,WAAW,EAAE;IAC5B,MAAMC,QAAQ,GAAGD,WAAW,CAACE,GAAG,CAAC,CAAC;MAAErB,MAAM;MAAEC;IAAW,CAAC,KACpD,IAAI,CAACF,eAAe,CAACC,MAAM,EAAEC,UAAU,CAC3C,CAAC;IAED,OAAO,MAAMqB,OAAO,CAACC,GAAG,CAACH,QAAQ,CAAC;EACtC;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMI,qBAAqBA,CAACC,UAAU,GAAG,GAAG,EAAE;IAC1C,MAAMC,kBAAkB,GAAG,MAAM,IAAI,CAAChC,aAAa,CAAC,IAAI,CAAC;IAEzD,IAAI,CAACgC,kBAAkB,CAACC,OAAO,EAAE;MAC7B,OAAOD,kBAAkB;IAC7B;IAEA,MAAME,UAAU,GAAGF,kBAAkB,CAACZ,IAAI,CAACc,UAAU,IAAI,EAAE;IAC3D,MAAMT,WAAW,GAAG,EAAE;IAEtB,KAAK,MAAMU,QAAQ,IAAID,UAAU,EAAE;MAC/B,MAAME,kBAAkB,GAAG,MAAM,IAAI,CAAC/B,eAAe,CAAC0B,UAAU,EAAEI,QAAQ,CAACE,EAAE,CAAC;MAC9E,IAAID,kBAAkB,CAACH,OAAO,EAAE;QAC5BR,WAAW,CAACa,IAAI,CAAC;UACbH,QAAQ,EAAEA,QAAQ;UAClBI,UAAU,EAAEH,kBAAkB,CAAChB;QACnC,CAAC,CAAC;MACN;IACJ;IAEA,OAAO;MACHa,OAAO,EAAE,IAAI;MACbb,IAAI,EAAE;QACFoB,WAAW,EAAET,UAAU;QACvBN,WAAW,EAAEA;MACjB,CAAC;MACDgB,OAAO,EAAE;IACb,CAAC;EACL;AACJ;;AAEA;AACA,MAAMC,eAAe,GAAG,IAAI3C,eAAe,CAAC,CAAC;AAE7C,eAAe2C,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}