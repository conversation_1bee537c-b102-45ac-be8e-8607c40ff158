-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:56 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `shipment`
--

-- --------------------------------------------------------

--
-- Table structure for table `addresses`
--

CREATE TABLE `addresses` (
  `id` int(11) NOT NULL,
  `client_id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_line1` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `address_line2` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `state` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'shipping',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `company` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `city` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `state` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `postal_code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `country` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`id`, `name`, `company`, `email`, `phone`, `address`, `city`, `state`, `postal_code`, `country`, `notes`, `created_at`, `updated_at`) VALUES
(1, 'hh', 'hh', '<EMAIL>', '1111', '22 kent Road', 'Sandton', 'Johannesburg', '2194', '11', '11', '2025-04-22 07:10:53', '2025-04-22 08:51:53'),
(2, 'Paul walker', 'Walker Glass', '<EMAIL>', '91919191919', '11 Car town Road', 'Capetown', 'Capetown', '1782', 'South Africa', '', '2025-04-22 08:52:41', '2025-04-22 08:52:41'),
(3, 'Paul Striker', 'Stricker Corp', '<EMAIL>', '*********', '11 Way Road', 'Dallas', 'Texas', '70576', 'United States', 'drop at door', '2025-04-22 18:22:25', '2025-04-22 18:22:25'),
(4, 'Yola  Knet', 'Frost Company', '<EMAIL>', '*********', '11 Purple Magic', 'London', 'London', 'GB 657L', 'United Kingdom', '', '2025-04-22 18:29:37', '2025-04-22 18:29:37');

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `position` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `department` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hire_date` date DEFAULT NULL,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `notes` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE `settings` (
  `id` int(11) NOT NULL,
  `category` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `description` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `category`, `name`, `value`, `type`, `description`, `created_at`, `updated_at`) VALUES
(1, 'general', 'site_name', 'ELTA Courier', 'string', 'Website name', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(2, 'general', 'company_name', 'ELTA Courier Services', 'string', 'Company legal name', '2025-04-21 07:43:19', '2025-04-21 10:06:40'),
(3, 'general', 'logo_url', NULL, 'string', 'Company logo URL', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(4, 'general', 'support_email', '<EMAIL>', 'string', 'Support email address', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(5, 'general', 'phone', '+1234567890', 'string', 'Support phone number', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(6, 'packages', 'enable_multiple_packages', 'true', 'boolean', 'Allow multiple packages per shipment', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(7, 'packages', 'weight_unit', 'kg', 'string', 'Weight unit (kg/lbs)', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(8, 'packages', 'dimension_unit', 'cm', 'string', 'Dimension unit (cm/inch)', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(9, 'packages', 'piece_types', '[\"Box\",\"Pallet\",\"Envelope\",\"Other\"]', 'json', 'Available piece types', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(10, 'tracking', 'number_prefix', 'ELT', 'string', 'Tracking number prefix', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(11, 'tracking', 'number_suffix', NULL, 'string', 'Tracking number suffix', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(12, 'tracking', 'number_digits', '8', 'integer', 'Number of digits in tracking number', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(13, 'tracking', 'auto_generate', 'true', 'boolean', 'Auto-generate tracking numbers', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(14, 'email', 'from_email', '<EMAIL>', 'string', 'System email sender address', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(15, 'email', 'from_name', 'ELTA Courier', 'string', 'System email sender name', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(16, 'email', 'client_notifications', 'true', 'boolean', 'Enable client email notifications', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(17, 'email', 'admin_notifications', 'true', 'boolean', 'Enable admin email notifications', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(18, 'email', 'client_notify_statuses', '[\"Delivered\",\"In Transit\",\"Exception\"]', 'json', 'Status changes that trigger client notifications', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(19, 'email', 'admin_notify_statuses', '[\"Exception\"]', 'json', 'Status changes that trigger admin notifications', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(20, 'email', 'admin_emails', '[\"<EMAIL>\"]', 'json', 'Admin email addresses for notifications', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(21, 'shipment', 'statuses', '[\"Pending\",\"Picked Up\",\"In Transit\",\"Out for Delivery\",\"Delivered\",\"Exception\",\"Cancelled\"]', 'json', 'Available shipment statuses', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(22, 'shipment', 'types', '[\"Standard\",\"Express\",\"Same Day\"]', 'json', 'Available shipment types', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(23, 'shipment', 'modes', '[\"Ground\",\"Air\",\"Sea\"]', 'json', 'Available shipment modes', '2025-04-21 07:43:19', '2025-04-21 07:43:19'),
(24, 'shipment', 'payment_modes', '[\"Cash\",\"Credit Card\",\"Account\",{\"name\":\"E wallet\",\"description\":\"E wallet\"}]', 'json', 'Available payment modes', '2025-04-21 07:43:19', '2025-04-22 17:41:54'),
(25, 'general', 'app_name', 'ELTA Courier', 'string', 'The name of the application', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(26, 'general', 'company_address', '123 Shipping Lane, Logistics City, LC 12345', 'text', 'Your company\'s physical address', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(27, 'general', 'company_phone', '+****************', 'string', 'Your company\'s contact phone number', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(28, 'general', 'company_email', '<EMAIL>', 'string', 'Your company\'s contact email address', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(29, 'general', 'currency', 'USD', 'string', 'The currency used for pricing', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(30, 'general', 'timezone', 'UTC', 'string', 'The timezone used for dates and times', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(31, 'general', 'date_format', 'Y-m-d', 'string', 'The format used for displaying dates', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(33, 'packages', 'package_types', '[{\"name\":\"Envelope\",\"max_weight\":0.5,\"dimensions\":\"30x20x1\",\"price\":5.99},{\"name\":\"Small Box\",\"max_weight\":2,\"dimensions\":\"20x20x20\",\"price\":9.99},{\"name\":\"Medium Box\",\"max_weight\":5,\"dimensions\":\"30x30x30\",\"price\":14.99},{\"name\":\"Large Box\",\"max_weight\":10,\"dimensions\":\"40x40x40\",\"price\":19.99}]', 'json', 'Available package types', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(34, 'packages', 'distance_price_factor', '0.1', 'float', 'Price per kilometer', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(35, 'packages', 'weight_price_factor', '0.5', 'float', 'Additional price per kg above the package\'s base weight', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(36, 'packages', 'express_delivery_factor', '1.5', 'float', 'Multiplier for express delivery', '2025-04-21 10:06:40', '2025-04-21 10:06:40'),
(37, 'emails', 'mail_mailer', 'smtp', 'string', 'The mail driver to use for sending emails', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(38, 'emails', 'mail_host', 'smtp.example.com', 'string', 'The SMTP server host', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(39, 'emails', 'mail_port', '587', 'integer', 'The SMTP server port', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(40, 'emails', 'mail_username', '<EMAIL>', 'string', 'The SMTP server username', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(41, 'emails', 'mail_password', 'password', 'string', 'The SMTP server password', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(42, 'emails', 'mail_encryption', 'tls', 'string', 'The encryption protocol to use', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(43, 'emails', 'mail_from_address', '<EMAIL>', 'string', 'The email address that will appear in the \"From\" field', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(44, 'emails', 'mail_from_name', 'ELTA Courier', 'string', 'The name that will appear in the \"From\" field', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(45, 'emails', 'notify_admin_new_shipment', '1', 'boolean', 'Notify administrators when a new shipment is created', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(46, 'emails', 'notify_customer_status_change', '1', 'boolean', 'Notify customers when their shipment status changes', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(47, 'emails', 'notify_customer_delivery', '1', 'boolean', 'Notify customers when their shipment is delivered', '2025-04-21 10:06:41', '2025-04-21 10:06:41'),
(48, 'shipment_options', 'shipment_types', '[{\"name\":\"Air Freightx\",\"description\":\"Air freight shipments\"},{\"name\":\"International Shipping\",\"description\":\"International shipments\"},{\"name\":\"Truckload\",\"description\":\"Full truckload shipments\"},{\"name\":\"Van Move\",\"description\":\"Van transportation\"},{\"name\":\"Sea Freight\",\"description\":\"Sea freight shipments\"},{\"name\":\"Ground Transport\",\"description\":\"Ground transportation\"},{\"name\":\"Next Day\",\"description\":\"Next day delivery\"}]', 'string', NULL, '2025-04-22 16:37:29', '2025-04-22 17:11:30'),
(49, 'shipment_options', 'shipment_modes', '[{\"name\":\"Sea Transport\",\"description\":\"Sea transport\"},{\"name\":\"Land Shipping\",\"description\":\"Land shipping\"},{\"name\":\"Air Freight\",\"description\":\"Air freight\"},{\"name\":\"Ground Transport\",\"description\":\"Ground transportation\"}]', 'string', NULL, '2025-04-22 16:37:29', '2025-04-22 16:37:29'),
(50, 'shipment_options', 'carriers', '[{\"name\":\"DHL\",\"description\":\"DHL Express\"},{\"name\":\"USPS\",\"description\":\"United States Postal Service\"},{\"name\":\"FedEx\",\"description\":\"FedEx\"},{\"name\":\"TreasureSwap\",\"description\":\"TreasureSwap Delivery\"}]', 'string', NULL, '2025-04-22 16:37:29', '2025-04-22 16:37:29'),
(51, 'shipment_options', 'locations', '[\"Afghanistan\",\"Albania\",\"Algeria\",\"American Samoa\",\"Andorra\",\"Angola\",\"Anguilla\",\"Antigua & Barbuda\",\"Argentina\",\"Armenia\",\"Aruba\",\"Australia\",\"Austria\",\"Azerbaijan\",\"Bahamas\",\"Bahrain\",\"Bangladesh\",\"Barbados\",\"Belarus\",\"Belgium\",\"Belize\",\"Benin\",\"Bermuda\",\"Bhutan\",\"Bolivia\",\"Bosnia & Herzegovina\",\"Botswana\",\"Brazil\",\"British Virgin Is.\",\"Brunei\",\"Bulgaria\",\"Burkina Faso\",\"Burma\",\"Burundi\",\"Cambodia\",\"Cameroon\",\"Canada\",\"Cape Verde\",\"Cayman Islands\",\"Central African Rep.\",\"Chad\",\"Chile\",\"China\",\"Colombia\",\"Comoros\",\"Congo, Dem. Rep.\",\"Congo, Repub. of the\",\"Cook Islands\",\"Costa Rica\",\"Cote d\'Ivoire\",\"Croatia\",\"Cuba\",\"Cyprus\",\"Czech Republic\",\"Denmark\",\"Djibouti\",\"Dominica\",\"Dominican Republic\",\"East Timor\",\"Ecuador\",\"Egypt\",\"El Salvador\",\"Equatorial Guinea\",\"Eritrea\",\"Estonia\",\"Ethiopia\",\"Faroe Islands\",\"Fiji\",\"Finland\",\"France\",\"French Guiana\",\"French Polynesia\",\"Gabon\",\"Gambia, The\",\"Gaza Strip\",\"Georgia\",\"Germany\",\"Ghana\",\"Gibraltar\",\"Greece\",\"Greenland\",\"Grenada\",\"Guadeloupe\",\"Guam\",\"Guatemala\",\"Guernsey\",\"Guinea\",\"Guinea-Bissau\",\"Guyana\",\"Haiti\",\"Honduras\",\"Hong Kong\",\"Hungary\",\"Iceland\",\"India\",\"Indonesia\",\"Iran\",\"Iraq\",\"Ireland\",\"Isle of Man\",\"Israel\",\"Italy\",\"Jamaica\",\"Japan\",\"Jersey\",\"Jordan\",\"Kazakhstan\",\"Kenya\",\"Kiribati\",\"Korea, North\",\"Korea, South\",\"Kuwait\",\"Kyrgyzstan\",\"Laos\",\"Latvia\",\"Lebanon\",\"Lesotho\",\"Liberia\",\"Libya\",\"Liechtenstein\",\"Lithuania\",\"Luxembourg\",\"Macau\",\"Macedonia\",\"Madagascar\",\"Malawi\",\"Malaysia\",\"Maldives\",\"Mali\",\"Malta\",\"Marshall Islands\",\"Martinique\",\"Mauritania\",\"Mauritius\",\"Mayotte\",\"Mexico\",\"Micronesia, Fed. St.\",\"Moldova\",\"Monaco\",\"Mongolia\",\"Montserrat\",\"Morocco\",\"Mozambique\",\"Namibia\",\"Nauru\",\"Nepal\",\"Netherlands\",\"Netherlands Antilles\",\"New Caledonia\",\"New Zealand\",\"Nicaragua\",\"Niger\",\"Nigeria\",\"N. Mariana Islands\",\"Norway\",\"Oman\",\"Pakistan\",\"Palau\",\"Panama\",\"Papua New Guinea\",\"Paraguay\",\"Peru\",\"Philippines\",\"Poland\",\"Portugal\",\"Puerto Rico\",\"Qatar\",\"Reunion\",\"Romania\",\"Russia\",\"Rwanda\",\"Saint Helena\",\"Saint Kitts & Nevis\",\"Saint Lucia\",\"St Pierre & Miquelon\",\"Saint Vincent and the Grenadines\",\"Samoa\",\"San Marino\",\"Sao Tome & Principe\",\"Saudi Arabia\",\"Senegal\",\"Serbia\",\"Seychelles\",\"Sierra Leone\",\"Singapore\",\"Slovakia\",\"Slovenia\",\"Solomon Islands\",\"Somalia\",\"South Africa\",\"Spain\",\"Sri Lanka\",\"Sudan\",\"Suriname\",\"Swaziland\",\"Sweden\",\"Switzerland\",\"Syria\",\"Taiwan\",\"Tajikistan\",\"Tanzania\",\"Thailand\",\"Togo\",\"Tonga\",\"Trinidad & Tobago\",\"Tunisia\",\"Turkey\",\"Turkmenistan\",\"Turks & Caicos Is\",\"Tuvalu\",\"Uganda\",\"Ukraine\",\"United Arab Emirates\",\"United Kingdom\",\"United States\",\"Uruguay\",\"Uzbekistan\",\"Vanuatu\",\"Venezuela\",\"Vietnam\",\"Virgin Islands\",\"Wallis and Futuna\",\"West Bank\",\"Western Sahara\",\"Yemen\",\"Zambia\",\"Zimbabwe\"]', 'string', NULL, '2025-04-22 16:37:29', '2025-04-22 16:37:29'),
(52, 'shipment', 'payment_modes_deprecated', '[\"Cash\",\"Credit Card\",\"Account\",{\"name\":\"E wallet\",\"description\":\"E wallet\"}]', 'string', NULL, '2025-04-22 16:59:01', '2025-05-22 07:46:14');

-- --------------------------------------------------------

--
-- Table structure for table `shipments`
--

CREATE TABLE `shipments` (
  `id` int(11) NOT NULL,
  `tracking_number` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `client_id` int(11) DEFAULT NULL,
  `employee_id` int(11) DEFAULT NULL,
  `shipper_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `shipper_address` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `shipper_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipper_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receiver_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `receiver_address` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `receiver_phone` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `receiver_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `origin` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `destination` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `shipment_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `shipment_mode` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `carrier` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payment_mode` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `current_location` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pickup_date` datetime NOT NULL,
  `departure_date` datetime DEFAULT NULL,
  `expected_delivery_date` datetime DEFAULT NULL,
  `total_weight` decimal(10,2) DEFAULT NULL,
  `comments` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `courier` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `product` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quantity` int(11) DEFAULT NULL,
  `total_freight` decimal(10,2) DEFAULT NULL,
  `carrier_reference_no` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pickup_time` time DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `shipments`
--

INSERT INTO `shipments` (`id`, `tracking_number`, `client_id`, `employee_id`, `shipper_name`, `shipper_address`, `shipper_phone`, `shipper_email`, `receiver_name`, `receiver_address`, `receiver_phone`, `receiver_email`, `origin`, `destination`, `shipment_type`, `shipment_mode`, `carrier`, `payment_mode`, `status`, `current_location`, `pickup_date`, `departure_date`, `expected_delivery_date`, `total_weight`, `comments`, `created_at`, `updated_at`, `courier`, `product`, `quantity`, `total_freight`, `carrier_reference_no`, `pickup_time`) VALUES
(1, 'TSD250422752003', 2, NULL, 'hh', '22 kent Road\r\nSandton, Johannesburg 2194\r\n11', '1111', '<EMAIL>', 'Paul walker', '11 Car town Road\r\nCapetown, Capetown 1782\r\nSouth Africa', '91919191919', '<EMAIL>', 'California', 'Joahnensburg', 'Document', 'Rail', 'DHL', 'Credit Card', 'delayed', NULL, '2025-04-22 00:00:00', NULL, '2025-04-09 00:00:00', '100.00', '', '2025-04-22 09:35:46', '2025-04-22 19:26:36', NULL, NULL, NULL, '10000.00', '191991918', '23:37:00'),
(2, 'TSD250422838063', 2, NULL, 'Paul Striker', '11 Way Road\r\nDallas, Texas 70576\r\nUnited States', '*********', '<EMAIL>', 'Paul walker', '11 Car town Road\r\nCapetown, Capetown 1782\r\nSouth Africa', '91919191919', '<EMAIL>', 'California', 'Johannesburg', 'International Shipping', 'Air Freight', 'FedEx', 'E wallet', 'pending', NULL, '2025-04-22 00:00:00', NULL, '2025-04-24 00:00:00', '10.00', '', '2025-04-22 19:12:41', '2025-04-22 19:12:41', NULL, NULL, NULL, '1000.00', '277272727277', '21:12:00'),
(3, 'TSD250422999001', NULL, NULL, 'John Doe', '123 Main St, New York', NULL, NULL, 'Jane Smith', '456 Oak St, Los Angeles', NULL, NULL, 'New York', 'Los Angeles', NULL, NULL, NULL, NULL, 'in_transit', NULL, '2025-04-22 22:25:56', NULL, NULL, NULL, NULL, '2025-04-22 20:25:56', '2025-04-22 20:25:56', NULL, NULL, NULL, NULL, NULL, NULL),
(4, 'TSD250422999002', NULL, NULL, 'Mike Johnson', '789 Pine St, Chicago', NULL, NULL, 'Sarah Williams', '321 Elm St, Miami', NULL, NULL, 'Chicago', 'Miami', NULL, NULL, NULL, NULL, 'out_for_delivery', NULL, '2025-04-22 22:26:03', NULL, NULL, NULL, NULL, '2025-04-22 20:26:03', '2025-04-22 20:26:03', NULL, NULL, NULL, NULL, NULL, NULL),
(5, 'TSD250422999003', NULL, NULL, 'Robert Brown', '555 Maple Ave, Boston', NULL, NULL, 'Emily Davis', '777 Cedar Rd, Seattle', NULL, NULL, 'Boston', 'Seattle', NULL, NULL, NULL, NULL, 'in_transit', NULL, '2025-04-22 22:26:17', NULL, NULL, NULL, NULL, '2025-04-22 20:26:17', '2025-04-22 20:26:17', NULL, NULL, NULL, NULL, NULL, NULL),
(6, 'TSD250423273119', 3, NULL, 'Paul Striker', '11 Way Road\r\nDallas, Texas 70576\r\nUnited States', '*********', '<EMAIL>', 'Yola  Knet', '11 Purple Magic\r\nLondon, London GB 657L\r\nUnited Kingdom', '*********', '<EMAIL>', 'Johannesburg', 'California, USA', 'International Shipping', 'Land Shipping', 'DHL', 'Credit Card', 'Custom Clearance', NULL, '2025-04-23 00:00:00', NULL, '2025-04-25 00:00:00', '60.00', '', '2025-04-23 10:35:45', '2025-04-23 14:59:04', NULL, NULL, NULL, '30000.00', '1010100101', '12:35:00');

-- --------------------------------------------------------

--
-- Table structure for table `shipment_history`
--

CREATE TABLE `shipment_history` (
  `id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `status` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `date_time` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `shipment_history`
--

INSERT INTO `shipment_history` (`id`, `shipment_id`, `status`, `location`, `message`, `date_time`, `created_at`, `updated_at`) VALUES
(1, 1, 'pending', 'California', 'Shipment created', '2025-04-22 09:35:46', '2025-04-22 09:35:46', '2025-04-22 09:35:46'),
(2, 2, 'pending', 'California', 'Shipment created', '2025-04-22 19:12:41', '2025-04-22 19:12:41', '2025-04-22 19:12:41'),
(3, 1, 'delayed', 'Manchester', 'Status updated to Delayed', '2025-04-22 19:26:00', '2025-04-22 19:26:36', '2025-04-22 19:26:36'),
(4, 6, 'pending', 'Johannesburg', 'Shipment created', '2025-04-23 10:35:45', '2025-04-23 10:35:45', '2025-04-23 10:35:45'),
(5, 6, 'in_transit', 'California, USA', 'Transit', '2025-04-23 13:58:00', '2025-04-23 13:58:31', '2025-04-23 13:58:31'),
(6, 6, 'cancelled', 'Norwalk, Connecticut', 'package in Transit in Norwalk,CT', '2025-04-24 19:59:00', '2025-04-23 14:00:15', '2025-04-23 14:00:15'),
(7, 6, 'delayed', 'Norwalk, Connecticut', 'package is delayed  in Norwalk,CT', '2025-04-24 14:00:00', '2025-04-23 14:01:27', '2025-04-23 14:01:27'),
(8, 6, 'Custom Clearance', 'California, USA', 'Shipment is pending for Custom Clearance', '2025-04-23 14:57:00', '2025-04-23 14:59:04', '2025-04-23 14:59:04');

-- --------------------------------------------------------

--
-- Table structure for table `shipment_packages`
--

CREATE TABLE `shipment_packages` (
  `id` int(11) NOT NULL,
  `shipment_id` int(11) NOT NULL,
  `piece_type` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `length` decimal(10,2) DEFAULT NULL,
  `width` decimal(10,2) DEFAULT NULL,
  `height` decimal(10,2) DEFAULT NULL,
  `weight` decimal(10,2) DEFAULT NULL,
  `volume` decimal(10,2) DEFAULT NULL,
  `volumetric_weight` decimal(10,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `shipment_packages`
--

INSERT INTO `shipment_packages` (`id`, `shipment_id`, `piece_type`, `description`, `quantity`, `length`, `width`, `height`, `weight`, `volume`, `volumetric_weight`, `created_at`, `updated_at`) VALUES
(1, 1, 'Box', 'Consignement', 1, '23.00', '2.00', '2.00', '100.00', '0.00', '0.02', '2025-04-22 09:35:46', '2025-04-22 09:35:46'),
(2, 1, 'Box', '', 1, '0.00', '0.00', '0.00', '0.00', NULL, NULL, '2025-04-22 09:35:46', '2025-04-22 09:35:46'),
(3, 2, 'Large Box', 'Consignment', 1, '40.00', '40.00', '40.00', '10.00', '0.06', '12.80', '2025-04-22 19:12:41', '2025-04-22 19:12:41'),
(4, 6, 'Large Box', 'Consignment Box', 1, '40.00', '40.00', '40.00', '10.00', '0.06', '12.80', '2025-04-23 10:35:45', '2025-04-23 10:35:45'),
(5, 6, 'Large Box', 'Security Deposit Box', 1, '30.00', '30.00', '40.00', '50.00', '0.04', '7.20', '2025-04-23 10:35:45', '2025-04-23 10:35:45');

-- --------------------------------------------------------

--
-- Table structure for table `status_options`
--

CREATE TABLE `status_options` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `color` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `status_options`
--

INSERT INTO `status_options` (`id`, `name`, `description`, `color`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'pending', 'Shipment is pending processing', '#f0ad4e', 1, '2025-04-21 16:03:01', '2025-04-21 16:03:01'),
(2, 'in_transit', 'Shipment is in transit', '#5bc0de', 1, '2025-04-21 16:03:01', '2025-04-21 16:03:01'),
(3, 'delivered', 'Shipment has been delivered', '#5cb85c', 1, '2025-04-21 16:03:01', '2025-04-21 16:03:01'),
(4, 'delayed', 'Shipment is delayed', '#d9534f', 1, '2025-04-21 16:03:01', '2025-04-21 16:03:01'),
(5, 'cancelled', 'Shipment has been cancelled', '#777777', 0, '2025-04-21 16:03:01', '2025-04-23 14:20:48'),
(6, 'Custom Clearance', 'Shipment is pending custom clearance', '#1e819f', 1, '2025-04-23 14:05:25', '2025-04-23 14:27:56'),
(7, 'Tax Clearance', 'Shipment is pending for Tax Clearance Certificate', '#ff4000', 1, '2025-04-23 14:12:59', '2025-04-23 14:21:37');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('admin','staff') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'staff',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_login` datetime DEFAULT NULL,
  `password_reset_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `password_reset_expires` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `name`, `role`, `is_active`, `last_login`, `password_reset_token`, `password_reset_expires`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$f0J9b5aNOrXlL1inEzzqhuyKwvqEZxcJ6xkOdc9W2o2dK1P5GhbGG', '<EMAIL>', 'Administrator', 'admin', 1, '2025-05-22 09:37:30', NULL, NULL, '2025-04-21 07:43:19', '2025-05-22 07:37:30');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `addresses`
--
ALTER TABLE `addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `client_id` (`client_id`);

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_phone` (`phone`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_name` (`name`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_status` (`status`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_setting` (`category`,`name`),
  ADD KEY `idx_category` (`category`);

--
-- Indexes for table `shipments`
--
ALTER TABLE `shipments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tracking_number` (`tracking_number`),
  ADD KEY `idx_tracking_number` (`tracking_number`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_pickup_date` (`pickup_date`),
  ADD KEY `idx_client_id` (`client_id`),
  ADD KEY `idx_employee_id` (`employee_id`);

--
-- Indexes for table `shipment_history`
--
ALTER TABLE `shipment_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_shipment_id` (`shipment_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_date_time` (`date_time`);

--
-- Indexes for table `shipment_packages`
--
ALTER TABLE `shipment_packages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_shipment_id` (`shipment_id`);

--
-- Indexes for table `status_options`
--
ALTER TABLE `status_options`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `password_reset_token` (`password_reset_token`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `addresses`
--
ALTER TABLE `addresses`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `clients`
--
ALTER TABLE `clients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `shipments`
--
ALTER TABLE `shipments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `shipment_history`
--
ALTER TABLE `shipment_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `shipment_packages`
--
ALTER TABLE `shipment_packages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `status_options`
--
ALTER TABLE `status_options`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `addresses`
--
ALTER TABLE `addresses`
  ADD CONSTRAINT `addresses_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `shipments`
--
ALTER TABLE `shipments`
  ADD CONSTRAINT `shipments_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `shipments_ibfk_2` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `shipment_history`
--
ALTER TABLE `shipment_history`
  ADD CONSTRAINT `shipment_history_ibfk_1` FOREIGN KEY (`shipment_id`) REFERENCES `shipments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `shipment_packages`
--
ALTER TABLE `shipment_packages`
  ADD CONSTRAINT `shipment_packages_ibfk_1` FOREIGN KEY (`shipment_id`) REFERENCES `shipments` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
