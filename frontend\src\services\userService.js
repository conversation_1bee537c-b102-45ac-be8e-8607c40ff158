/**
 * User API Service
 * 
 * Handles all user-related API calls with consistent error handling
 * and response formatting.
 */

import apiService from './apiService';

class UserService {
    /**
     * Get user data by ID
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserData(userId) {
        return await apiService.get('user_data.php', { id: userId });
    }

    /**
     * Update user profile
     * @param {number} userId - User ID
     * @param {object} userData - User data to update
     * @returns {Promise<ApiResponse>}
     */
    async updateUserProfile(userId, userData) {
        return await apiService.post('update_user_profile.php', {
            user_id: userId,
            ...userData
        });
    }

    /**
     * Get user balance
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserBalance(userId) {
        return await apiService.get('get_user_balance.php', { user_id: userId });
    }

    /**
     * Get user friends
     * @param {number} userId - User ID
     * @param {string} status - Friend status filter
     * @returns {Promise<ApiResponse>}
     */
    async getUserFriends(userId, status = 'accepted') {
        return await apiService.get('get_friends.php', {
            user_id: userId,
            status
        });
    }

    /**
     * Send friend request
     * @param {number} fromUserId - Sender user ID
     * @param {number} toUserId - Recipient user ID
     * @returns {Promise<ApiResponse>}
     */
    async sendFriendRequest(fromUserId, toUserId) {
        return await apiService.post('send_friend_request.php', {
            from_user_id: fromUserId,
            to_user_id: toUserId
        });
    }

    /**
     * Accept friend request
     * @param {number} requestId - Friend request ID
     * @returns {Promise<ApiResponse>}
     */
    async acceptFriendRequest(requestId) {
        return await apiService.post('accept_friend_request.php', {
            request_id: requestId
        });
    }

    /**
     * Reject friend request
     * @param {number} requestId - Friend request ID
     * @returns {Promise<ApiResponse>}
     */
    async rejectFriendRequest(requestId) {
        return await apiService.post('reject_friend_request.php', {
            request_id: requestId
        });
    }

    /**
     * Transfer points between users
     * @param {number} fromUserId - Sender user ID
     * @param {number} toUserId - Recipient user ID
     * @param {number} amount - Amount to transfer
     * @param {string} note - Optional transfer note
     * @returns {Promise<ApiResponse>}
     */
    async transferPoints(fromUserId, toUserId, amount, note = '') {
        return await apiService.post('transfer_points.php', {
            from_user_id: fromUserId,
            to_user_id: toUserId,
            amount,
            note
        });
    }

    /**
     * Get user achievements
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserAchievements(userId) {
        return await apiService.get('get_user_achievements.php', {
            user_id: userId
        });
    }

    /**
     * Get user transaction history
     * @param {number} userId - User ID
     * @param {number} page - Page number
     * @param {number} limit - Items per page
     * @returns {Promise<ApiResponse>}
     */
    async getUserTransactions(userId, page = 1, limit = 20) {
        return await apiService.get('get_user_transactions.php', {
            user_id: userId,
            page,
            limit
        });
    }

    /**
     * Change user password
     * @param {number} userId - User ID
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise<ApiResponse>}
     */
    async changePassword(userId, currentPassword, newPassword) {
        return await apiService.post('change_password.php', {
            user_id: userId,
            current_password: currentPassword,
            new_password: newPassword
        });
    }

    /**
     * Upload user avatar
     * @param {number} userId - User ID
     * @param {File} avatarFile - Avatar image file
     * @param {function} onProgress - Upload progress callback
     * @returns {Promise<ApiResponse>}
     */
    async uploadAvatar(userId, avatarFile, onProgress = null) {
        const formData = new FormData();
        formData.append('user_id', userId);
        formData.append('avatar', avatarFile);

        return await apiService.upload('upload_avatar.php', formData, onProgress);
    }

    /**
     * Get user statistics
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserStats(userId) {
        return await apiService.get('get_user_stats.php', { user_id: userId });
    }

    /**
     * Search users
     * @param {string} query - Search query
     * @param {number} limit - Maximum results
     * @returns {Promise<ApiResponse>}
     */
    async searchUsers(query, limit = 10) {
        return await apiService.get('search_users.php', {
            query,
            limit
        });
    }

    /**
     * Get user leaderboard position
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getUserLeaderboardPosition(userId) {
        return await apiService.get('get_user_leaderboard_position.php', {
            user_id: userId
        });
    }
}

// Create singleton instance
const userService = new UserService();

export default userService;
