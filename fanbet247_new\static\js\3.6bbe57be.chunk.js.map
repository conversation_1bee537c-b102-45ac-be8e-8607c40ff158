{"version": 3, "sources": ["../node_modules/web-vitals/dist/web-vitals.js"], "names": ["__webpack_require__", "r", "__webpack_exports__", "d", "h", "L", "F", "P", "e", "t", "n", "i", "name", "value", "delta", "entries", "id", "concat", "Date", "now", "Math", "floor", "random", "a", "PerformanceObserver", "supportedEntryTypes", "includes", "self", "getEntries", "map", "observe", "type", "buffered", "o", "document", "visibilityState", "removeEventListener", "addEventListener", "u", "persisted", "c", "f", "s", "m", "timeStamp", "v", "setTimeout", "firstHiddenTime", "disconnect", "startTime", "push", "window", "performance", "getEntriesByName", "requestAnimationFrame", "p", "l", "hadRecentInput", "length", "takeRecords", "T", "passive", "capture", "y", "g", "w", "E", "entryType", "target", "cancelable", "processingStart", "for<PERSON>ach", "S", "b", "once", "getEntriesByType", "timing", "max", "navigationStart", "responseStart", "readyState"], "mappings": "yFAAAA,EAAAC,EAAAC,GAAAF,EAAAG,EAAAD,EAAA,2BAAAE,IAAAJ,EAAAG,EAAAD,EAAA,2BAAAC,IAAAH,EAAAG,EAAAD,EAAA,2BAAAG,IAAAL,EAAAG,EAAAD,EAAA,2BAAAI,IAAAN,EAAAG,EAAAD,EAAA,4BAAAK,IAAA,IAAAC,EACAC,EACAC,EACAC,EACAV,EAAA,SAAAO,EAAAC,GACA,OACAG,KAAAJ,EACAK,WAAA,IAAAJ,GAAA,EAAAA,EACAK,MAAA,EACAC,QAAA,GACAC,GAAA,MAAAC,OAAAC,KAAAC,MAAA,KAAAF,OAAAG,KAAAC,MAAA,cAAAD,KAAAE,UAAA,QAGAC,EAAA,SAAAf,EAAAC,GACA,IACA,GAAAe,oBAAAC,oBAAAC,SAAAlB,GAAA,CACA,mBAAAA,KAAA,2BAAAmB,MAAA,OACA,IAAAjB,EAAA,IAAAc,oBAAA,SAAAhB,GACA,OAAAA,EAAAoB,aAAAC,IAAApB,KAEA,OAAAC,EAAAoB,QAAA,CACAC,KAAAvB,EACAwB,UAAA,IACStB,GAEJ,MAAAF,MAELyB,EAAA,SAAAzB,EAAAC,GACA,IAAAC,EAAA,SAAAA,EAAAC,GACA,aAAAA,EAAAoB,MAAA,WAAAG,SAAAC,kBAAA3B,EAAAG,GAAAF,IAAA2B,oBAAA,mBAAA1B,GAAA,GAAA0B,oBAAA,WAAA1B,GAAA,MAEA2B,iBAAA,mBAAA3B,GAAA,GAAA2B,iBAAA,WAAA3B,GAAA,IAEA4B,EAAA,SAAA9B,GACA6B,iBAAA,oBAAA5B,GACAA,EAAA8B,WAAA/B,EAAAC,KACK,IAEL+B,EAAA,SAAAhC,EAAAC,EAAAC,GACA,IAAAC,EACA,gBAAAV,GACAQ,EAAAI,OAAA,IAAAZ,GAAAS,KAAAD,EAAAK,MAAAL,EAAAI,OAAAF,GAAA,IAAAF,EAAAK,YAAA,IAAAH,OAAAF,EAAAI,MAAAL,EAAAC,OAGAgC,GAAA,EACAC,EAAA,WACA,iBAAAR,SAAAC,gBAAA,OAEAQ,EAAA,WACAV,EAAA,SAAAzB,GACA,IAAAC,EAAAD,EAAAoC,UACAH,EAAAhC,IACK,IAELoC,EAAA,WACA,OAAAJ,EAAA,IAAAA,EAAAC,IAAAC,IAAAL,EAAA,WACAQ,WAAA,WACAL,EAAAC,IAAAC,KACO,MACF,CACLI,sBACA,OAAAN,KAIAtC,EAAA,SAAAK,EAAAC,GACA,IAAAC,EACAC,EAAAkC,IACAZ,EAAAhC,EAAA,OACAwC,EAAA,SAAAjC,GACA,2BAAAA,EAAAI,OAAA+B,KAAAK,aAAAxC,EAAAyC,UAAAtC,EAAAoC,kBAAAd,EAAApB,MAAAL,EAAAyC,UAAAhB,EAAAlB,QAAAmC,KAAA1C,GAAAE,GAAA,MAEAgC,EAAAS,OAAAC,yBAAAC,kBAAAD,YAAAC,iBAAA,6BACAV,EAAAD,EAAA,KAAAnB,EAAA,QAAAkB,IACAC,GAAAC,KAAAjC,EAAA8B,EAAAhC,EAAAyB,EAAAxB,GAAAiC,GAAAD,EAAAC,GAAAJ,EAAA,SAAA3B,GACAsB,EAAAhC,EAAA,OAAAS,EAAA8B,EAAAhC,EAAAyB,EAAAxB,GAAA6C,sBAAA,WACAA,sBAAA,WACArB,EAAApB,MAAAuC,YAAAjC,MAAAR,EAAAiC,UAAAlC,GAAA,WAKA6C,GAAA,EACAC,GAAA,EACApD,EAAA,SAAAI,EAAAC,GACA8C,IAAApD,EAAA,SAAAK,GACAgD,EAAAhD,EAAAK,QACK0C,GAAA,GACL,IAAA7C,EACAC,EAAA,SAAAF,GACA+C,GAAA,GAAAhD,EAAAC,IAEAgC,EAAAxC,EAAA,SACAyC,EAAA,EACAC,EAAA,GACAE,EAAA,SAAArC,GACA,IAAAA,EAAAiD,eAAA,CACA,IAAAhD,EAAAkC,EAAA,GACAhC,EAAAgC,IAAAe,OAAA,GACAhB,GAAAlC,EAAAyC,UAAAtC,EAAAsC,UAAA,KAAAzC,EAAAyC,UAAAxC,EAAAwC,UAAA,KAAAP,GAAAlC,EAAAK,MAAA8B,EAAAO,KAAA1C,KAAAkC,EAAAlC,EAAAK,MAAA8B,EAAA,CAAAnC,IAAAkC,EAAAD,EAAA5B,QAAA4B,EAAA5B,MAAA6B,EAAAD,EAAA1B,QAAA4B,EAAAjC,OAGAN,EAAAmB,EAAA,eAAAsB,GACAzC,IAAAM,EAAA8B,EAAA7B,EAAA8B,EAAAhC,GAAAwB,EAAA,WACA7B,EAAAuD,cAAA9B,IAAAgB,GAAAnC,GAAA,KACK4B,EAAA,WACLI,EAAA,EAAAc,GAAA,EAAAf,EAAAxC,EAAA,SAAAS,EAAA8B,EAAA7B,EAAA8B,EAAAhC,OAGAmD,EAAA,CACAC,SAAA,EACAC,SAAA,GAEAC,EAAA,IAAA7C,KACA8C,EAAA,SAAArD,EAAAV,GACAO,MAAAP,EAAAQ,EAAAE,EAAAD,EAAA,IAAAQ,KAAA+C,EAAA7B,qBAAA8B,MAEAA,EAAA,WACA,GAAAzD,GAAA,GAAAA,EAAAC,EAAAqD,EAAA,CACA,IAAA9D,EAAA,CACAkE,UAAA,cACAvD,KAAAJ,EAAAuB,KACAqC,OAAA5D,EAAA4D,OACAC,WAAA7D,EAAA6D,WACApB,UAAAzC,EAAAoC,UACA0B,gBAAA9D,EAAAoC,UAAAnC,GAEAE,EAAA4D,QAAA,SAAA/D,GACAA,EAAAP,KACOU,EAAA,KAGP6D,EAAA,SAAAhE,GACA,GAAAA,EAAA6D,WAAA,CACA,IAAA5D,GAAAD,EAAAoC,UAAA,SAAA1B,KAAAkC,YAAAjC,OAAAX,EAAAoC,UACA,eAAApC,EAAAuB,KAAA,SAAAvB,EAAAC,GACA,IAAAC,EAAA,WACAsD,EAAAxD,EAAAC,GAAAR,KAEAU,EAAA,WACAV,KAEAA,EAAA,WACAmC,oBAAA,YAAA1B,EAAAkD,GAAAxB,oBAAA,gBAAAzB,EAAAiD,IAEAvB,iBAAA,YAAA3B,EAAAkD,GAAAvB,iBAAA,gBAAA1B,EAAAiD,GAVA,CAWOnD,EAAAD,GAAAwD,EAAAvD,EAAAD,KAGPyD,EAAA,SAAAzD,GACA,mDAAA+D,QAAA,SAAA9D,GACA,OAAAD,EAAAC,EAAA+D,EAAAZ,MAGAvD,EAAA,SAAAK,EAAA+B,GACA,IAAAC,EACAC,EAAAE,IACA1C,EAAAF,EAAA,OACAsD,EAAA,SAAA/C,GACAA,EAAAyC,UAAAN,EAAAI,kBAAA5C,EAAAU,MAAAL,EAAA8D,gBAAA9D,EAAAyC,UAAA9C,EAAAY,QAAAmC,KAAA1C,GAAAkC,GAAA,KAEAc,EAAAjC,EAAA,cAAAgC,GACAb,EAAAF,EAAA9B,EAAAP,EAAAsC,GAAAe,GAAAvB,EAAA,WACAuB,EAAAG,cAAA9B,IAAA0B,GAAAC,EAAAR,eACK,GAAAQ,GAAAlB,EAAA,WACL,IAAAf,EACApB,EAAAF,EAAA,OAAAyC,EAAAF,EAAA9B,EAAAP,EAAAsC,GAAA9B,EAAA,GAAAF,GAAA,EAAAD,EAAA,KAAAyD,EAAA5B,kBAAAd,EAAAgC,EAAA5C,EAAAuC,KAAA3B,GAAA2C,OAGAO,EAAA,GACAnE,EAAA,SAAAE,EAAAC,GACA,IAAAC,EACAC,EAAAkC,IACAJ,EAAAxC,EAAA,OACAyC,EAAA,SAAAlC,GACA,IAAAC,EAAAD,EAAAyC,UACAxC,EAAAE,EAAAoC,kBAAAN,EAAA5B,MAAAJ,EAAAgC,EAAA1B,QAAAmC,KAAA1C,GAAAE,MAEAiC,EAAApB,EAAA,2BAAAmB,GACA,GAAAC,EAAA,CACAjC,EAAA8B,EAAAhC,EAAAiC,EAAAhC,GACA,IAAAN,EAAA,WACAsE,EAAAhC,EAAAzB,MAAA2B,EAAAgB,cAAA9B,IAAAa,GAAAC,EAAAK,aAAAyB,EAAAhC,EAAAzB,KAAA,EAAAN,GAAA,KAEA,oBAAA6D,QAAA,SAAA/D,GACA6B,iBAAA7B,EAAAL,EAAA,CACAuE,MAAA,EACAZ,SAAA,MAEO7B,EAAA9B,GAAA,GAAAmC,EAAA,SAAA3B,GACP8B,EAAAxC,EAAA,OAAAS,EAAA8B,EAAAhC,EAAAiC,EAAAhC,GAAA6C,sBAAA,WACAA,sBAAA,WACAb,EAAA5B,MAAAuC,YAAAjC,MAAAR,EAAAiC,UAAA6B,EAAAhC,EAAAzB,KAAA,EAAAN,GAAA,WAMAH,EAAA,SAAAC,GACA,IAAAC,EACAC,EAAAT,EAAA,QACAQ,EAAA,WACA,IACA,IAAAA,EAAA2C,YAAAuB,iBAAA,6BACA,IAAAnE,EAAA4C,YAAAwB,OACAnE,EAAA,CACA0D,UAAA,aACAlB,UAAA,GAEA,QAAAvC,KAAAF,EAAA,oBAAAE,GAAA,WAAAA,IAAAD,EAAAC,GAAAU,KAAAyD,IAAArE,EAAAE,GAAAF,EAAAsE,gBAAA,IACA,OAAArE,EAPA,GASA,GAAAC,EAAAG,MAAAH,EAAAI,MAAAL,EAAAsE,cAAArE,EAAAG,MAAA,GAAAH,EAAAG,MAAAuC,YAAAjC,MAAA,OACAT,EAAAK,QAAA,CAAAN,GAAAD,EAAAE,GACO,MAAAF,MACF,aAAA0B,SAAA8C,WAAAlC,WAAArC,EAAA,GAAA4B,iBAAA,kBACL,OAAAS,WAAArC,EAAA", "file": "static/js/3.6bbe57be.chunk.js", "sourcesContent": ["var e,\n  t,\n  n,\n  i,\n  r = function (e, t) {\n    return {\n      name: e,\n      value: void 0 === t ? -1 : t,\n      delta: 0,\n      entries: [],\n      id: \"v2-\".concat(Date.now(), \"-\").concat(Math.floor(8999999999999 * Math.random()) + 1e12)\n    };\n  },\n  a = function (e, t) {\n    try {\n      if (PerformanceObserver.supportedEntryTypes.includes(e)) {\n        if (\"first-input\" === e && !(\"PerformanceEventTiming\" in self)) return;\n        var n = new PerformanceObserver(function (e) {\n          return e.getEntries().map(t);\n        });\n        return n.observe({\n          type: e,\n          buffered: !0\n        }), n;\n      }\n    } catch (e) {}\n  },\n  o = function (e, t) {\n    var n = function n(i) {\n      \"pagehide\" !== i.type && \"hidden\" !== document.visibilityState || (e(i), t && (removeEventListener(\"visibilitychange\", n, !0), removeEventListener(\"pagehide\", n, !0)));\n    };\n    addEventListener(\"visibilitychange\", n, !0), addEventListener(\"pagehide\", n, !0);\n  },\n  u = function (e) {\n    addEventListener(\"pageshow\", function (t) {\n      t.persisted && e(t);\n    }, !0);\n  },\n  c = function (e, t, n) {\n    var i;\n    return function (r) {\n      t.value >= 0 && (r || n) && (t.delta = t.value - (i || 0), (t.delta || void 0 === i) && (i = t.value, e(t)));\n    };\n  },\n  f = -1,\n  s = function () {\n    return \"hidden\" === document.visibilityState ? 0 : 1 / 0;\n  },\n  m = function () {\n    o(function (e) {\n      var t = e.timeStamp;\n      f = t;\n    }, !0);\n  },\n  v = function () {\n    return f < 0 && (f = s(), m(), u(function () {\n      setTimeout(function () {\n        f = s(), m();\n      }, 0);\n    })), {\n      get firstHiddenTime() {\n        return f;\n      }\n    };\n  },\n  d = function (e, t) {\n    var n,\n      i = v(),\n      o = r(\"FCP\"),\n      f = function (e) {\n        \"first-contentful-paint\" === e.name && (m && m.disconnect(), e.startTime < i.firstHiddenTime && (o.value = e.startTime, o.entries.push(e), n(!0)));\n      },\n      s = window.performance && performance.getEntriesByName && performance.getEntriesByName(\"first-contentful-paint\")[0],\n      m = s ? null : a(\"paint\", f);\n    (s || m) && (n = c(e, o, t), s && f(s), u(function (i) {\n      o = r(\"FCP\"), n = c(e, o, t), requestAnimationFrame(function () {\n        requestAnimationFrame(function () {\n          o.value = performance.now() - i.timeStamp, n(!0);\n        });\n      });\n    }));\n  },\n  p = !1,\n  l = -1,\n  h = function (e, t) {\n    p || (d(function (e) {\n      l = e.value;\n    }), p = !0);\n    var n,\n      i = function (t) {\n        l > -1 && e(t);\n      },\n      f = r(\"CLS\", 0),\n      s = 0,\n      m = [],\n      v = function (e) {\n        if (!e.hadRecentInput) {\n          var t = m[0],\n            i = m[m.length - 1];\n          s && e.startTime - i.startTime < 1e3 && e.startTime - t.startTime < 5e3 ? (s += e.value, m.push(e)) : (s = e.value, m = [e]), s > f.value && (f.value = s, f.entries = m, n());\n        }\n      },\n      h = a(\"layout-shift\", v);\n    h && (n = c(i, f, t), o(function () {\n      h.takeRecords().map(v), n(!0);\n    }), u(function () {\n      s = 0, l = -1, f = r(\"CLS\", 0), n = c(i, f, t);\n    }));\n  },\n  T = {\n    passive: !0,\n    capture: !0\n  },\n  y = new Date(),\n  g = function (i, r) {\n    e || (e = r, t = i, n = new Date(), w(removeEventListener), E());\n  },\n  E = function () {\n    if (t >= 0 && t < n - y) {\n      var r = {\n        entryType: \"first-input\",\n        name: e.type,\n        target: e.target,\n        cancelable: e.cancelable,\n        startTime: e.timeStamp,\n        processingStart: e.timeStamp + t\n      };\n      i.forEach(function (e) {\n        e(r);\n      }), i = [];\n    }\n  },\n  S = function (e) {\n    if (e.cancelable) {\n      var t = (e.timeStamp > 1e12 ? new Date() : performance.now()) - e.timeStamp;\n      \"pointerdown\" == e.type ? function (e, t) {\n        var n = function () {\n            g(e, t), r();\n          },\n          i = function () {\n            r();\n          },\n          r = function () {\n            removeEventListener(\"pointerup\", n, T), removeEventListener(\"pointercancel\", i, T);\n          };\n        addEventListener(\"pointerup\", n, T), addEventListener(\"pointercancel\", i, T);\n      }(t, e) : g(t, e);\n    }\n  },\n  w = function (e) {\n    [\"mousedown\", \"keydown\", \"touchstart\", \"pointerdown\"].forEach(function (t) {\n      return e(t, S, T);\n    });\n  },\n  L = function (n, f) {\n    var s,\n      m = v(),\n      d = r(\"FID\"),\n      p = function (e) {\n        e.startTime < m.firstHiddenTime && (d.value = e.processingStart - e.startTime, d.entries.push(e), s(!0));\n      },\n      l = a(\"first-input\", p);\n    s = c(n, d, f), l && o(function () {\n      l.takeRecords().map(p), l.disconnect();\n    }, !0), l && u(function () {\n      var a;\n      d = r(\"FID\"), s = c(n, d, f), i = [], t = -1, e = null, w(addEventListener), a = p, i.push(a), E();\n    });\n  },\n  b = {},\n  F = function (e, t) {\n    var n,\n      i = v(),\n      f = r(\"LCP\"),\n      s = function (e) {\n        var t = e.startTime;\n        t < i.firstHiddenTime && (f.value = t, f.entries.push(e), n());\n      },\n      m = a(\"largest-contentful-paint\", s);\n    if (m) {\n      n = c(e, f, t);\n      var d = function () {\n        b[f.id] || (m.takeRecords().map(s), m.disconnect(), b[f.id] = !0, n(!0));\n      };\n      [\"keydown\", \"click\"].forEach(function (e) {\n        addEventListener(e, d, {\n          once: !0,\n          capture: !0\n        });\n      }), o(d, !0), u(function (i) {\n        f = r(\"LCP\"), n = c(e, f, t), requestAnimationFrame(function () {\n          requestAnimationFrame(function () {\n            f.value = performance.now() - i.timeStamp, b[f.id] = !0, n(!0);\n          });\n        });\n      });\n    }\n  },\n  P = function (e) {\n    var t,\n      n = r(\"TTFB\");\n    t = function () {\n      try {\n        var t = performance.getEntriesByType(\"navigation\")[0] || function () {\n          var e = performance.timing,\n            t = {\n              entryType: \"navigation\",\n              startTime: 0\n            };\n          for (var n in e) \"navigationStart\" !== n && \"toJSON\" !== n && (t[n] = Math.max(e[n] - e.navigationStart, 0));\n          return t;\n        }();\n        if (n.value = n.delta = t.responseStart, n.value < 0 || n.value > performance.now()) return;\n        n.entries = [t], e(n);\n      } catch (e) {}\n    }, \"complete\" === document.readyState ? setTimeout(t, 0) : addEventListener(\"load\", function () {\n      return setTimeout(t, 0);\n    });\n  };\nexport { h as getCLS, d as getFCP, L as getFID, F as getLCP, P as getTTFB };"], "sourceRoot": ""}