/* Modern Challenge System Styles */
.challenge-system {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.challenge-system h1 {
  color: #166534;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.75rem;
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.header-actions button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.header-actions button:first-child {
  background-color: #166534;
  color: white;
}

.header-actions button:first-child:hover {
  background-color: #15803d;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-actions button:last-child {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.header-actions button:last-child:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Form Styles */
.challenge-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Match Settings Section */
.match-settings {
  background-color: white;
  border-radius: 6px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.match-type-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.match-settings-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  align-items: start;
}

.match-type-section h3 {
  grid-column: 1 / -1;
  color: #166534;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group select,
.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #f9fafb;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-group select:focus,
.form-group input:focus {
  border-color: #166534;
  outline: none;
  box-shadow: 0 0 0 2px rgba(22, 101, 52, 0.1);
}

.odds-explanation {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

/* Team Container */
.team-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  width: 100%;
  position: relative;
  align-items: stretch;
  gap: 1rem;
}

/* VS Divider */
.vs-divider {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #166534;
  border-radius: 50%;
  color: white;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  align-self: center;
  justify-self: center;
  grid-column: 2;
}

/* Team Sections */
.team-section {
  background-color: white;
  border-radius: 6px;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  position: relative;
  width: 100%;
}

.team1 {
  grid-column: 1;
}

.team2 {
  grid-column: 3;
}

.team-section h3 {
  color: #166534;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.team-section label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.team-section select,
.team-section input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #f9fafb;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  margin-bottom: 0.75rem;
}

.team-section select:focus,
.team-section input:focus {
  border-color: #166534;
  outline: none;
  box-shadow: 0 0 0 2px rgba(22, 101, 52, 0.1);
}

/* Logo Container */
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  background-color: #f9fafb;
  border-radius: 6px;
  margin: 0.75rem 0;
  border: 1px solid #e5e7eb;
  height: 150px;
}

.logo-preview {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
}

.empty-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9fafb;
}

/* Time Section */
.time-section {
  background-color: white;
  border-radius: 6px;
  padding: 1.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.time-section h3 {
  color: #166534;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.time-groups-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.time-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.time-section label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.9rem;
}

.time-icon {
  color: #166534;
}

.time-section input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #f9fafb;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.time-section input:focus {
  border-color: #166534;
  outline: none;
  box-shadow: 0 0 0 2px rgba(22, 101, 52, 0.1);
}

/* Challenge Preview */
.challenge-preview {
  background-color: white;
  border-radius: 6px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  margin-top: 1.5rem;
}

.challenge-preview h3 {
  color: #166534;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.25rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.preview-teams-container {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.preview-team {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100%;
}

.preview-team:first-child {
  grid-column: 1;
}

.preview-team:last-child {
  grid-column: 3;
}

.preview-team .logo-container {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 50%;
  margin-bottom: 0.75rem;
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
}

.preview-team .logo-preview {
  width: 100%;
  height: 100%;
  object-fit: contain;
  max-width: 80px;
  max-height: 80px;
}

.preview-team-name {
  font-weight: 700;
  font-size: 1rem;
  color: #111827;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
}

.preview-odds {
  font-size: 0.85rem;
  color: #4b5563;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.preview-advantage {
  font-size: 0.75rem;
  color: #16a34a;
  font-weight: 600;
  background-color: #dcfce7;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

.preview-vs {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  background-color: #166534;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  grid-column: 2;
  justify-self: center;
  align-self: center;
}

.preview-time-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 1.25rem;
  border: 1px solid #e5e7eb;
}

.preview-time-details p {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 0.5rem;
  margin: 0;
}

.preview-time-details p span {
  font-weight: 600;
  color: #166534;
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.time-detail-icon {
  color: #166534;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

/* Required Field Indicator */
.required-field::after {
  content: "*";
  color: #ef4444;
  margin-left: 0.25rem;
}

/* Empty Preview State */
.empty-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px dashed #d1d5db;
  color: #6b7280;
  text-align: center;
}

.empty-preview svg {
  font-size: 2rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

/* Notification Messages */
.success-message,
.error-message {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: slideInRight 0.3s ease-out, fadeOut 0.3s ease-in 2.7s forwards;
  max-width: 300px;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  border-left: 3px solid #16a34a;
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  border-left: 3px solid #dc2626;
}

.success-message::before {
  content: "✓";
  font-weight: bold;
  background-color: #16a34a;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

.error-message::before {
  content: "!";
  font-weight: bold;
  background-color: #dc2626;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .challenge-system {
    padding: 1.5rem;
    max-width: 100%;
  }

  .header-actions {
    gap: 0.75rem;
  }

  .header-actions button {
    padding: 0.6rem 1.2rem;
    font-size: 0.85rem;
  }

  .match-settings-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .time-groups-container {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
}

@media (max-width: 992px) {
  .challenge-system {
    padding: 1.25rem;
  }

  .preview-time-details {
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
  }

  .preview-time-details p {
    padding: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }

  .preview-time-details p:nth-last-child(-n+2) {
    border-bottom: none;
  }
}

@media (max-width: 768px) {
  .challenge-system {
    padding: 1rem;
  }

  .challenge-system h1 {
    font-size: 1.4rem;
    margin-bottom: 1rem;
  }

  .header-actions {
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
  }

  .header-actions button {
    width: 100%;
    padding: 0.6rem 1rem;
  }

  .team-container {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
  }

  .vs-divider {
    grid-column: 1;
    grid-row: 2;
    justify-self: center;
  }

  .team1 {
    grid-column: 1;
    grid-row: 1;
  }

  .team2 {
    grid-column: 1;
    grid-row: 3;
  }

  .team-section {
    width: 100%;
  }

  .match-settings-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .time-groups-container {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .logo-container {
    padding: 0.75rem;
    height: 120px;
  }

  .logo-preview {
    max-width: 80px;
    max-height: 80px;
  }

  .preview-teams-container {
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 1.5rem;
  }

  .preview-team:first-child {
    grid-column: 1;
    grid-row: 1;
  }

  .preview-team:last-child {
    grid-column: 1;
    grid-row: 3;
  }

  .preview-vs {
    grid-column: 1;
    grid-row: 2;
    justify-self: center;
  }

  .preview-time-details {
    grid-template-columns: 1fr;
    padding: 1rem;
  }

  .preview-time-details p {
    padding: 0.5rem 0;
  }

  .preview-time-details p:last-child {
    border-bottom: none;
  }
}

@media (max-width: 480px) {
  .challenge-system {
    padding: 0.75rem;
    border-radius: 4px;
  }

  .challenge-system h1 {
    font-size: 1.2rem;
    padding-bottom: 0.5rem;
  }

  .match-settings,
  .team-section,
  .time-section,
  .challenge-preview {
    padding: 0.75rem;
    border-radius: 4px;
  }

  .match-type-section h3,
  .team-section h3,
  .time-section h3,
  .challenge-preview h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }

  .form-group label,
  .team-section label,
  .time-section label {
    font-size: 0.8rem;
  }

  .form-group select,
  .form-group input,
  .team-section select,
  .team-section input,
  .time-section input {
    padding: 0.6rem;
    font-size: 0.85rem;
  }

  .preview-team .logo-container {
    width: 70px;
    height: 70px;
  }

  .preview-team .logo-preview {
    max-width: 60px;
    max-height: 60px;
  }

  .preview-team-name {
    font-size: 0.85rem;
  }

  .preview-odds {
    font-size: 0.75rem;
  }

  .preview-advantage {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
  }

  .preview-vs {
    width: 32px;
    height: 32px;
    font-size: 1rem;
  }
}
