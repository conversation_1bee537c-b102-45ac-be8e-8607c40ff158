/**
 * Centralized API Services Export
 * 
 * This file exports all API services for easy importing throughout the application.
 * It provides a single point of access to all API functionality.
 */

// Core API service
export { default as apiService, ApiResponse } from './apiService';

// Domain-specific services
export { default as currencyService } from './currencyService';
export { default as userService } from './userService';
export { default as betService } from './betService';

// Service utilities and hooks
export { default as useApiService } from '../hooks/useApiService';

/**
 * Service factory for creating custom service instances
 * @param {string} baseEndpoint - Base endpoint for the service
 * @returns {object} Service instance with CRUD methods
 */
export const createService = (baseEndpoint) => {
    const apiService = require('./apiService').default;
    
    return {
        getAll: (params = {}) => apiService.get(`${baseEndpoint}.php`, params),
        getById: (id) => apiService.get(`${baseEndpoint}.php`, { id }),
        create: (data) => apiService.post(`${baseEndpoint}.php`, data),
        update: (id, data) => apiService.put(`${baseEndpoint}.php`, { id, ...data }),
        delete: (id) => apiService.delete(`${baseEndpoint}.php`, { id })
    };
};

/**
 * Common API endpoints for quick access
 */
export const endpoints = {
    // Authentication
    login: 'login.php',
    register: 'register.php',
    logout: 'logout.php',
    
    // User management
    userData: 'user_data.php',
    updateProfile: 'update_user_profile.php',
    changePassword: 'change_password.php',
    
    // Currency system
    currencies: 'get_currencies.php',
    exchangeRates: 'get_exchange_rates.php',
    convertCurrency: 'convert_currency.php',
    
    // Betting
    bets: 'get_bets.php',
    createBet: 'create_bet.php',
    acceptBet: 'accept_bet.php',
    
    // Admin
    adminDashboard: 'admin_dashboard_data.php',
    userManagement: 'user_management.php',
    betManagement: 'bet_management.php',
    
    // Teams and leagues
    teams: 'team_management.php',
    leagues: 'league_management.php',
    
    // Settings
    generalSettings: 'get_general_settings.php',
    securitySettings: 'get_security_settings.php',
    siteConfig: 'get_site_config.php'
};

/**
 * Service status checker
 * @returns {Promise<object>} Service health status
 */
export const checkServiceHealth = async () => {
    const apiService = require('./apiService').default;
    
    try {
        const response = await apiService.get('health_check.php');
        return {
            healthy: response.success,
            timestamp: new Date().toISOString(),
            services: {
                api: response.success,
                database: response.data?.database || false,
                cache: response.data?.cache || false
            }
        };
    } catch (error) {
        return {
            healthy: false,
            timestamp: new Date().toISOString(),
            error: error.message,
            services: {
                api: false,
                database: false,
                cache: false
            }
        };
    }
};
