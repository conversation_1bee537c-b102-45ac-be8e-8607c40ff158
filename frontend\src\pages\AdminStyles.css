.admin-container {
  padding: 20px;
  width: 100%;
  margin: 0;
}

.admin-description {
  margin-bottom: 30px;
  color: #666;
  font-size: 1.1rem;
  line-height: 1.5;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.settings-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.settings-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.settings-card h3 {
  margin-top: 0;
  color: #333;
  font-size: 1.3rem;
  margin-bottom: 10px;
}

.settings-card p {
  color: #666;
  margin-bottom: 20px;
  min-height: 60px;
}

.btn {
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  display: inline-block;
  text-decoration: none;
  text-align: center;
}

.btn-primary {
  background: #409cff;
  color: white;
}

.btn-primary:hover {
  background: #3b7dff;
}

.btn-secondary {
  background: #f0f0f0;
  color: #333;
}

.btn-secondary:hover {
  background: #e0e0e0;
}

.admin-form {
  background: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #409cff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 156, 255, 0.2);
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  margin-bottom: 0;
  margin-left: 10px;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
}

.form-actions {
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
}

.form-section {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.form-section h2 {
  margin-top: 0;
  color: #333;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.field-description {
  font-size: 0.9rem;
  color: #666;
  margin-top: 5px;
  margin-bottom: 10px;
}

.checkbox-options {
  margin-top: 10px;
}

.checkbox-options label {
  display: block;
  margin-bottom: 5px;
  font-weight: normal;
}

.success-alert,
.warning-alert,
.error-alert {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-weight: 500;
}

.success-alert {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.warning-alert {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
}

.error-alert {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.test-email-section {
  margin-top: 40px;
  background: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #eee;
}

.test-email-form {
  margin-top: 20px;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.2rem;
  color: #666;
}

/* Logo upload styles */
.logo-upload-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.logo-preview {
  max-width: 200px;
  max-height: 100px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-preview img {
  max-width: 100%;
  max-height: 80px;
  object-fit: contain;
}

.upload-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #409cff;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
  font-weight: 600;
}

.upload-button:hover {
  background: #3b7dff;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 2rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.loading {
  text-align: center;
  padding: 40px;
  font-size: 1.1rem;
  color: #666;
}

@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .form-actions button {
    width: 100%;
    margin-bottom: 10px;
  }
}
