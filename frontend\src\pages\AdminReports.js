import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { 
    FaChartBar, 
    FaDownload, 
    FaCalendarAlt, 
    FaUsers, 
    FaDollarSign, 
    FaGamepad,
    FaFileExport,
    FaSpinner
} from 'react-icons/fa';
import './AdminStyles.css';

const API_BASE_URL = '/backend';

function AdminReports() {
    const [reportData, setReportData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [reportType, setReportType] = useState('overview');
    const [dateRange, setDateRange] = useState({
        startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0], // First day of current month
        endDate: new Date().toISOString().split('T')[0] // Today
    });

    useEffect(() => {
        fetchReport();
    }, [reportType, dateRange]);

    const fetchReport = async () => {
        setLoading(true);
        setError('');
        try {
            const params = new URLSearchParams({
                type: reportType,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/reports.php?${params}`);
            
            if (response.data.success) {
                setReportData(response.data.data);
            } else {
                setError(response.data.message || 'Failed to fetch report');
            }
        } catch (err) {
            setError('Failed to fetch report data');
            console.error('Report fetch error:', err);
        } finally {
            setLoading(false);
        }
    };

    const exportReport = async (format = 'csv') => {
        try {
            const params = new URLSearchParams({
                type: reportType,
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
                format: format
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/reports.php?${params}`, {
                responseType: 'blob'
            });

            // Create download link
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', `${reportType}_report_${new Date().toISOString().split('T')[0]}.csv`);
            document.body.appendChild(link);
            link.click();
            link.remove();
        } catch (err) {
            setError('Failed to export report');
            console.error('Export error:', err);
        }
    };

    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount || 0);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString();
    };

    const renderOverviewReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* Main Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaUsers className="text-blue-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Total Users</h3>
                                <p className="text-2xl font-bold text-blue-600">{reportData.total_users}</p>
                                <p className="text-sm text-gray-500">+{reportData.new_users} new this period</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaGamepad className="text-green-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Active Users</h3>
                                <p className="text-2xl font-bold text-green-600">{reportData.active_users}</p>
                                <p className="text-sm text-gray-500">Users who placed bets</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaChartBar className="text-purple-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Total Bets</h3>
                                <p className="text-2xl font-bold text-purple-600">{reportData.total_bets}</p>
                                <p className="text-sm text-gray-500">Bets placed this period</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaDollarSign className="text-yellow-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Bet Volume</h3>
                                <p className="text-2xl font-bold text-yellow-600">
                                    {formatCurrency(reportData.total_bet_amount)}
                                </p>
                                <p className="text-sm text-gray-500">Total amount wagered</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Additional Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaChartBar className="text-indigo-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Challenges</h3>
                                <p className="text-2xl font-bold text-indigo-600">{reportData.total_challenges}</p>
                                <p className="text-sm text-gray-500">{reportData.active_challenges} active</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaUsers className="text-pink-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Leagues</h3>
                                <p className="text-2xl font-bold text-pink-600">{reportData.total_leagues}</p>
                                <p className="text-sm text-gray-500">{reportData.active_memberships} memberships</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaDollarSign className="text-green-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Transactions</h3>
                                <p className="text-2xl font-bold text-green-600">{reportData.total_transactions}</p>
                                <p className="text-sm text-gray-500">{formatCurrency(reportData.total_transaction_volume)} volume</p>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white rounded-lg shadow-md p-6">
                        <div className="flex items-center">
                            <FaChartBar className="text-orange-500 text-3xl mr-4" />
                            <div>
                                <h3 className="text-lg font-semibold text-gray-800">Credit Requests</h3>
                                <p className="text-2xl font-bold text-orange-600">{reportData.total_credit_requests}</p>
                                <p className="text-sm text-gray-500">{reportData.pending_credit_requests} pending</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    };

    const renderFinancialReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* Transaction Summary */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Transaction Summary</h3>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-4 py-2 text-left">Transaction Type</th>
                                    <th className="px-4 py-2 text-left">Count</th>
                                    <th className="px-4 py-2 text-left">Total Amount</th>
                                    <th className="px-4 py-2 text-left">Average Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reportData.transaction_summary?.map((transaction, index) => (
                                    <tr key={index} className="border-b">
                                        <td className="px-4 py-2 capitalize">{transaction.type.replace('_', ' ')}</td>
                                        <td className="px-4 py-2">{transaction.count}</td>
                                        <td className="px-4 py-2">{formatCurrency(transaction.total_amount)}</td>
                                        <td className="px-4 py-2">{formatCurrency(transaction.avg_amount)}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>

                {/* Top Users */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Users by Transaction Volume</h3>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-4 py-2 text-left">Username</th>
                                    <th className="px-4 py-2 text-left">Full Name</th>
                                    <th className="px-4 py-2 text-left">Transactions</th>
                                    <th className="px-4 py-2 text-left">Total Volume</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reportData.top_users?.map((user, index) => (
                                    <tr key={index} className="border-b">
                                        <td className="px-4 py-2 font-semibold">{user.username}</td>
                                        <td className="px-4 py-2">{user.full_name}</td>
                                        <td className="px-4 py-2">{user.transaction_count}</td>
                                        <td className="px-4 py-2">{formatCurrency(user.total_volume)}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    };

    const renderUserActivityReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* Activity Types */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity Types</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {reportData.activity_types?.map((activity, index) => (
                            <div key={index} className="bg-gray-50 rounded-lg p-4">
                                <h4 className="font-semibold text-gray-800 capitalize">
                                    {activity.activity_type.replace('_', ' ')}
                                </h4>
                                <p className="text-2xl font-bold text-blue-600">{activity.count}</p>
                            </div>
                        ))}
                    </div>
                </div>

                {/* Most Active Users */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Most Active Users</h3>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-4 py-2 text-left">Username</th>
                                    <th className="px-4 py-2 text-left">Full Name</th>
                                    <th className="px-4 py-2 text-left">Activity Count</th>
                                    <th className="px-4 py-2 text-left">Last Activity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reportData.most_active_users?.map((user, index) => (
                                    <tr key={index} className="border-b">
                                        <td className="px-4 py-2 font-semibold">{user.username}</td>
                                        <td className="px-4 py-2">{user.full_name}</td>
                                        <td className="px-4 py-2">{user.activity_count}</td>
                                        <td className="px-4 py-2">{formatDate(user.last_activity)}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    };

    const renderBettingReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* Betting Statistics */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Betting Statistics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                            <p className="text-3xl font-bold text-blue-600">{reportData.betting_stats?.total_bets}</p>
                            <p className="text-gray-600">Total Bets</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-green-600">{reportData.betting_stats?.completed_bets}</p>
                            <p className="text-gray-600">Completed Bets</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-yellow-600">{reportData.betting_stats?.open_bets}</p>
                            <p className="text-gray-600">Open Bets</p>
                        </div>
                    </div>
                    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="text-center">
                            <p className="text-2xl font-bold text-purple-600">
                                {formatCurrency(reportData.betting_stats?.avg_bet_amount)}
                            </p>
                            <p className="text-gray-600">Average Bet Amount</p>
                        </div>
                        <div className="text-center">
                            <p className="text-2xl font-bold text-indigo-600">
                                {formatCurrency(reportData.betting_stats?.total_bet_volume)}
                            </p>
                            <p className="text-gray-600">Total Bet Volume</p>
                        </div>
                    </div>
                </div>

                {/* Top Bettors */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Top Bettors</h3>
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-4 py-2 text-left">Username</th>
                                    <th className="px-4 py-2 text-left">Full Name</th>
                                    <th className="px-4 py-2 text-left">Bet Count</th>
                                    <th className="px-4 py-2 text-left">Total Bet Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {reportData.top_bettors?.map((bettor, index) => (
                                    <tr key={index} className="border-b">
                                        <td className="px-4 py-2 font-semibold">{bettor.username}</td>
                                        <td className="px-4 py-2">{bettor.full_name}</td>
                                        <td className="px-4 py-2">{bettor.bet_count}</td>
                                        <td className="px-4 py-2">{formatCurrency(bettor.total_bet_amount)}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        );
    };

    const renderSystemReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* System Overview */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">System Overview</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                            <p className="text-3xl font-bold text-blue-600">{reportData.database_stats?.total_users}</p>
                            <p className="text-gray-600">Total Users</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-green-600">{reportData.database_stats?.total_bets}</p>
                            <p className="text-gray-600">Total Bets</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-purple-600">{reportData.database_stats?.total_challenges}</p>
                            <p className="text-gray-600">Total Challenges</p>
                        </div>
                    </div>
                </div>

                {/* Period Activity */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Activity This Period</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                        <div className="bg-blue-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-blue-600">{reportData.period_activity?.new_users}</p>
                            <p className="text-sm text-gray-600">New Users</p>
                        </div>
                        <div className="bg-green-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-green-600">{reportData.period_activity?.new_bets}</p>
                            <p className="text-sm text-gray-600">New Bets</p>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-purple-600">{reportData.period_activity?.new_challenges}</p>
                            <p className="text-sm text-gray-600">New Challenges</p>
                        </div>
                        <div className="bg-yellow-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-yellow-600">{reportData.period_activity?.new_transactions}</p>
                            <p className="text-sm text-gray-600">Transactions</p>
                        </div>
                        <div className="bg-orange-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-orange-600">{reportData.period_activity?.new_credit_requests}</p>
                            <p className="text-sm text-gray-600">Credit Requests</p>
                        </div>
                    </div>
                </div>

                {/* System Health */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">System Health</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="bg-yellow-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-yellow-600">{reportData.system_health?.open_bets}</p>
                            <p className="text-sm text-gray-600">Open Bets</p>
                        </div>
                        <div className="bg-blue-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-blue-600">{reportData.system_health?.open_challenges}</p>
                            <p className="text-sm text-gray-600">Open Challenges</p>
                        </div>
                        <div className="bg-red-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-red-600">{reportData.system_health?.pending_credit_requests}</p>
                            <p className="text-sm text-gray-600">Pending Credits</p>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-4 text-center">
                            <p className="text-2xl font-bold text-purple-600">{reportData.system_health?.pending_memberships}</p>
                            <p className="text-sm text-gray-600">Pending Memberships</p>
                        </div>
                    </div>
                </div>

                {/* Financial Summary */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Financial Summary</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div className="text-center">
                            <p className="text-3xl font-bold text-green-600">
                                {formatCurrency(reportData.financial_summary?.total_deposits)}
                            </p>
                            <p className="text-gray-600">Total Deposits ({reportData.financial_summary?.deposit_count})</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-red-600">
                                {formatCurrency(reportData.financial_summary?.total_withdrawals)}
                            </p>
                            <p className="text-gray-600">Total Withdrawals ({reportData.financial_summary?.withdrawal_count})</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-blue-600">
                                {formatCurrency(reportData.financial_summary?.total_bet_volume)}
                            </p>
                            <p className="text-gray-600">Bet Volume</p>
                        </div>
                    </div>
                </div>

                {/* Email Stats */}
                {reportData.email_stats && (
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Email Statistics</h3>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-indigo-600">{reportData.email_stats.total_emails}</p>
                            <p className="text-gray-600">Emails Sent This Period</p>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderChallengesReport = () => {
        if (!reportData) return null;

        return (
            <div className="space-y-6">
                {/* Challenge Statistics */}
                <div className="bg-white rounded-lg shadow-md p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">Challenge Statistics</h3>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                        <div className="text-center">
                            <p className="text-3xl font-bold text-blue-600">{reportData.challenge_stats?.total_challenges}</p>
                            <p className="text-gray-600">Total Challenges</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-green-600">{reportData.challenge_stats?.open_challenges}</p>
                            <p className="text-gray-600">Open</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-yellow-600">{reportData.challenge_stats?.closed_challenges}</p>
                            <p className="text-gray-600">Closed</p>
                        </div>
                        <div className="text-center">
                            <p className="text-3xl font-bold text-purple-600">{reportData.challenge_stats?.settled_challenges}</p>
                            <p className="text-gray-600">Settled</p>
                        </div>
                    </div>
                </div>

                {/* Result Distribution */}
                {reportData.result_distribution && reportData.result_distribution.length > 0 && (
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Result Distribution</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {reportData.result_distribution.map((result, index) => (
                                <div key={index} className="bg-gray-50 rounded-lg p-4 text-center">
                                    <p className="text-2xl font-bold text-blue-600">{result.count}</p>
                                    <p className="text-sm text-gray-600 capitalize">{result.result.replace('_', ' ')}</p>
                                    <p className="text-xs text-gray-500">{result.percentage}%</p>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Popular Teams */}
                {reportData.popular_teams && reportData.popular_teams.length > 0 && (
                    <div className="bg-white rounded-lg shadow-md p-6">
                        <h3 className="text-lg font-semibold text-gray-800 mb-4">Most Popular Teams</h3>
                        <div className="overflow-x-auto">
                            <table className="w-full">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-2 text-left">Team</th>
                                        <th className="px-4 py-2 text-left">Appearances</th>
                                        <th className="px-4 py-2 text-left">Wins</th>
                                        <th className="px-4 py-2 text-left">Win %</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {reportData.popular_teams.map((team, index) => (
                                        <tr key={index} className="border-b">
                                            <td className="px-4 py-2 font-semibold">{team.team_name}</td>
                                            <td className="px-4 py-2">{team.appearances}</td>
                                            <td className="px-4 py-2">{team.wins}</td>
                                            <td className="px-4 py-2">{team.win_percentage}%</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>
                )}
            </div>
        );
    };

    const renderReportContent = () => {
        switch (reportType) {
            case 'overview':
                return renderOverviewReport();
            case 'financial':
                return renderFinancialReport();
            case 'user_activity':
                return renderUserActivityReport();
            case 'betting':
                return renderBettingReport();
            case 'system':
                return renderSystemReport();
            case 'challenges':
                return renderChallengesReport();
            default:
                return <div>Select a report type to view data</div>;
        }
    };

    return (
        <div className="admin-container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">Reports & Analytics</h1>
                    <p className="admin-description">
                        Generate comprehensive reports on user activity, financial data, and betting statistics.
                    </p>
                </div>
                <button
                    onClick={() => exportReport('csv')}
                    disabled={!reportData || loading}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <FaFileExport className="mr-2" />
                    Export CSV
                </button>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {/* Report Controls */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Report Type */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Report Type</label>
                        <select
                            value={reportType}
                            onChange={(e) => setReportType(e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                            <option value="overview">Overview</option>
                            <option value="financial">Financial</option>
                            <option value="user_activity">User Activity</option>
                            <option value="betting">Betting Statistics</option>
                            <option value="system">System Report</option>
                            <option value="challenges">Challenges Report</option>
                        </select>
                    </div>

                    {/* Start Date */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                        <input
                            type="date"
                            value={dateRange.startDate}
                            onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        />
                    </div>

                    {/* End Date */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                        <input
                            type="date"
                            value={dateRange.endDate}
                            onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        />
                    </div>
                </div>
            </div>

            {/* Report Content */}
            {loading ? (
                <div className="flex items-center justify-center py-12">
                    <FaSpinner className="animate-spin text-4xl text-green-600 mr-4" />
                    <span className="text-lg text-gray-600">Generating report...</span>
                </div>
            ) : (
                renderReportContent()
            )}
        </div>
    );
}

export default AdminReports;
