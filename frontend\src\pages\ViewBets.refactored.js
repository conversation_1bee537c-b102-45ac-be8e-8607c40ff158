import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { betService, userService } from '../services';
import useApiService from '../hooks/useApiService';
import './ViewBets.css';

function ViewBets() {
  const [outgoingBets, setOutgoingBets] = useState([]);
  const [teams, setTeams] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage] = useState(20);
  const [searchTerm, setSearchTerm] = useState('');
  const userId = localStorage.getItem('userId');
  const navigate = useNavigate();
  const location = useLocation();
  const newBetRef = useRef(null);

  const [showLinkModal, setShowLinkModal] = useState(false);
  const [selectedBetLink, setSelectedBetLink] = useState('');
  const [showBetDetailsModal, setShowBetDetailsModal] = useState(false);
  const [selectedBet, setSelectedBet] = useState(null);
  const [newBetId, setNewBetId] = useState(location.state?.newBetId || null);

  // Use the API service hook for consistent error handling and loading states
  const {
    loading,
    error,
    execute,
    reset: resetApiState
  } = useApiService({
    onError: (error) => {
      console.error('API Error:', error);
    }
  });

  const fetchBets = useCallback(async () => {
    try {
      console.log('Fetching bets...');
      
      const response = await execute(() => 
        betService.getUserBets(userId, currentPage, itemsPerPage, searchTerm)
      );

      if (response.success) {
        setOutgoingBets(response.data.bets || []);
        setTotalPages(response.data.pagination?.totalPages || 1);
      }
    } catch (error) {
      // Error is already handled by the useApiService hook
      console.error('Failed to fetch bets:', error.message);
    }
  }, [userId, currentPage, itemsPerPage, searchTerm, execute]);

  const fetchTeams = useCallback(async () => {
    try {
      const response = await execute(() => 
        userService.get('team_management.php')
      );

      if (response.success && response.data.status === 200) {
        setTeams(response.data.data || []);
      }
    } catch (error) {
      console.error('Failed to fetch teams:', error.message);
    }
  }, [execute]);

  const cancelBet = useCallback(async (betId) => {
    if (!window.confirm('Are you sure you want to cancel this bet?')) {
      return;
    }

    try {
      const response = await execute(() => 
        betService.cancelBet(betId, userId)
      );

      if (response.success) {
        // Refresh bets list
        await fetchBets();
        alert('Bet cancelled successfully!');
      } else {
        alert(response.message || 'Failed to cancel bet');
      }
    } catch (error) {
      alert('Error cancelling bet: ' + error.message);
    }
  }, [execute, userId, fetchBets]);

  const copyBetLink = useCallback((betId) => {
    const betLink = `${window.location.origin}/join-challenge/${betId}`;
    setSelectedBetLink(betLink);
    setShowLinkModal(true);
  }, []);

  const handleCopyLink = useCallback(() => {
    navigator.clipboard.writeText(selectedBetLink).then(() => {
      alert('Link copied to clipboard!');
      setShowLinkModal(false);
    }).catch(() => {
      alert('Failed to copy link');
    });
  }, [selectedBetLink]);

  const viewBetDetails = useCallback((bet) => {
    setSelectedBet(bet);
    setShowBetDetailsModal(true);
  }, []);

  const getTeamLogo = useCallback((teamName) => {
    const team = teams.find(team => team.name === teamName);
    return team ? `/backend/${team.logo}` : '';
  }, [teams]);

  const formatDate = useCallback((dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }, []);

  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  }, []);

  const handlePageChange = useCallback((newPage) => {
    setCurrentPage(newPage);
  }, []);

  // Initial data fetch
  useEffect(() => {
    if (userId) {
      Promise.all([fetchBets(), fetchTeams()]);
    } else {
      navigate('/login');
    }
  }, [userId, navigate, fetchBets, fetchTeams]);

  // Refetch bets when page or search changes
  useEffect(() => {
    if (userId) {
      fetchBets();
    }
  }, [currentPage, searchTerm, fetchBets, userId]);

  // Scroll to new bet if redirected from bet creation
  useEffect(() => {
    if (newBetId && newBetRef.current) {
      newBetRef.current.scrollIntoView({ behavior: 'smooth' });
      setNewBetId(null);
    }
  }, [newBetId, outgoingBets]);

  // Reset API state when component unmounts
  useEffect(() => {
    return () => {
      resetApiState();
    };
  }, [resetApiState]);

  if (loading && outgoingBets.length === 0) {
    return (
      <div className="view-bets-container">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading your bets...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="view-bets-container">
      <div className="view-bets-header">
        <h2>My Bets</h2>
        <div className="search-container">
          <input
            type="text"
            placeholder="Search bets..."
            value={searchTerm}
            onChange={handleSearch}
            className="search-input"
          />
        </div>
      </div>

      {error && (
        <div className="error-message">
          <p>⚠️ {error.message || 'An error occurred while loading bets'}</p>
          <button onClick={() => fetchBets()} className="retry-button">
            Retry
          </button>
        </div>
      )}

      {outgoingBets.length === 0 && !loading ? (
        <div className="no-bets-message">
          <p>No bets found. <a href="/challenges">Create your first bet!</a></p>
        </div>
      ) : (
        <>
          <div className="bets-list">
            {outgoingBets.map((bet) => (
              <div 
                key={bet.bet_id} 
                className={`bet-card ${bet.bet_id === newBetId ? 'new-bet' : ''}`}
                ref={bet.bet_id === newBetId ? newBetRef : null}
              >
                <div className="bet-header">
                  <div className="bet-teams">
                    <div className="team">
                      <img 
                        src={getTeamLogo(bet.team1_name)} 
                        alt={bet.team1_name}
                        className="team-logo"
                        onError={(e) => { e.target.style.display = 'none'; }}
                      />
                      <span>{bet.team1_name}</span>
                    </div>
                    <span className="vs">VS</span>
                    <div className="team">
                      <img 
                        src={getTeamLogo(bet.team2_name)} 
                        alt={bet.team2_name}
                        className="team-logo"
                        onError={(e) => { e.target.style.display = 'none'; }}
                      />
                      <span>{bet.team2_name}</span>
                    </div>
                  </div>
                  <div className="bet-amount">
                    <span className="amount">{bet.amount} FC</span>
                  </div>
                </div>

                <div className="bet-details">
                  <p><strong>Your Pick:</strong> {bet.user_pick}</p>
                  <p><strong>Status:</strong> 
                    <span className={`status ${bet.status.toLowerCase()}`}>
                      {bet.status}
                    </span>
                  </p>
                  <p><strong>Created:</strong> {formatDate(bet.created_at)}</p>
                  {bet.match_date && (
                    <p><strong>Match Date:</strong> {formatDate(bet.match_date)}</p>
                  )}
                </div>

                <div className="bet-actions">
                  <button 
                    onClick={() => viewBetDetails(bet)}
                    className="btn btn-info"
                  >
                    View Details
                  </button>
                  <button 
                    onClick={() => copyBetLink(bet.bet_id)}
                    className="btn btn-secondary"
                  >
                    Copy Link
                  </button>
                  {bet.status === 'pending' && (
                    <button 
                      onClick={() => cancelBet(bet.bet_id)}
                      className="btn btn-danger"
                    >
                      Cancel
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="pagination">
              <button 
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="btn btn-secondary"
              >
                Previous
              </button>
              <span className="page-info">
                Page {currentPage} of {totalPages}
              </span>
              <button 
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="btn btn-secondary"
              >
                Next
              </button>
            </div>
          )}
        </>
      )}

      {/* Link Modal */}
      {showLinkModal && (
        <div className="modal-overlay" onClick={() => setShowLinkModal(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <h3>Share Bet Link</h3>
            <div className="link-container">
              <input 
                type="text" 
                value={selectedBetLink} 
                readOnly 
                className="link-input"
              />
              <button onClick={handleCopyLink} className="btn btn-primary">
                Copy Link
              </button>
            </div>
            <button 
              onClick={() => setShowLinkModal(false)} 
              className="btn btn-secondary"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Bet Details Modal */}
      {showBetDetailsModal && selectedBet && (
        <div className="modal-overlay" onClick={() => setShowBetDetailsModal(false)}>
          <div className="modal-content bet-details-modal" onClick={(e) => e.stopPropagation()}>
            <h3>Bet Details</h3>
            <div className="bet-details-content">
              <p><strong>Match:</strong> {selectedBet.team1_name} vs {selectedBet.team2_name}</p>
              <p><strong>Your Pick:</strong> {selectedBet.user_pick}</p>
              <p><strong>Amount:</strong> {selectedBet.amount} FC</p>
              <p><strong>Status:</strong> {selectedBet.status}</p>
              <p><strong>Created:</strong> {formatDate(selectedBet.created_at)}</p>
              {selectedBet.match_date && (
                <p><strong>Match Date:</strong> {formatDate(selectedBet.match_date)}</p>
              )}
              {selectedBet.description && (
                <p><strong>Description:</strong> {selectedBet.description}</p>
              )}
            </div>
            <button 
              onClick={() => setShowBetDetailsModal(false)} 
              className="btn btn-secondary"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default ViewBets;
