{"ast": null, "code": "/**\n * Betting API Service\n * \n * Handles all betting-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\nclass BetService {\n  /**\n   * Get user bets with pagination\n   * @param {number} userId - User ID\n   * @param {number} page - Page number\n   * @param {number} limit - Items per page\n   * @param {string} search - Search term\n   * @returns {Promise<ApiResponse>}\n   */\n  async getUserBets(userId, page = 1, limit = 10, search = '') {\n    return await apiService.get('get_bets.php', {\n      userId,\n      page,\n      limit,\n      search\n    });\n  }\n\n  /**\n   * Get accepted bets for user\n   * @param {number} userId - User ID\n   * @param {number} page - Page number\n   * @param {number} limit - Items per page\n   * @returns {Promise<ApiResponse>}\n   */\n  async getAcceptedBets(userId, page = 1, limit = 10) {\n    return await apiService.get('get_accepted_bets.php', {\n      userId,\n      page,\n      limit\n    });\n  }\n\n  /**\n   * Get incoming bets for user\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getIncomingBets(userId) {\n    return await apiService.get('get_bets.php', {\n      userId\n    });\n  }\n\n  /**\n   * Create a new bet\n   * @param {object} betData - Bet creation data\n   * @returns {Promise<ApiResponse>}\n   */\n  async createBet(betData) {\n    return await apiService.post('create_bet.php', betData);\n  }\n\n  /**\n   * Accept a bet\n   * @param {number} betId - Bet ID\n   * @param {number} userId - User ID accepting the bet\n   * @returns {Promise<ApiResponse>}\n   */\n  async acceptBet(betId, userId) {\n    return await apiService.post('accept_bet.php', {\n      bet_id: betId,\n      user_id: userId\n    });\n  }\n\n  /**\n   * Reject a bet\n   * @param {number} betId - Bet ID\n   * @param {number} userId - User ID rejecting the bet\n   * @returns {Promise<ApiResponse>}\n   */\n  async rejectBet(betId, userId) {\n    return await apiService.post('reject_bet.php', {\n      bet_id: betId,\n      user_id: userId\n    });\n  }\n\n  /**\n   * Cancel a bet\n   * @param {number} betId - Bet ID\n   * @param {number} userId - User ID canceling the bet\n   * @returns {Promise<ApiResponse>}\n   */\n  async cancelBet(betId, userId) {\n    return await apiService.post('cancel_bet.php', {\n      bet_id: betId,\n      user_id: userId\n    });\n  }\n\n  /**\n   * Get bet details\n   * @param {number} betId - Bet ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getBetDetails(betId) {\n    return await apiService.get('get_bet_details.php', {\n      bet_id: betId\n    });\n  }\n\n  /**\n   * Get recent bets for welcome page\n   * @param {number} limit - Number of bets to fetch\n   * @returns {Promise<ApiResponse>}\n   */\n  async getRecentBets(limit = 5) {\n    return await apiService.get('welcome_recent_bets.php', {\n      limit\n    });\n  }\n\n  /**\n   * Get bet statistics for user\n   * @param {number} userId - User ID\n   * @returns {Promise<ApiResponse>}\n   */\n  async getBetStats(userId) {\n    return await apiService.get('get_bet_stats.php', {\n      user_id: userId\n    });\n  }\n\n  /**\n   * Settle a bet (Admin only)\n   * @param {number} betId - Bet ID\n   * @param {number} winnerId - Winner user ID\n   * @param {string} notes - Settlement notes\n   * @returns {Promise<ApiResponse>}\n   */\n  async settleBet(betId, winnerId, notes = '') {\n    return await apiService.post('settle_bet.php', {\n      bet_id: betId,\n      winner_id: winnerId,\n      notes\n    });\n  }\n\n  /**\n   * Get all bets for admin management\n   * @param {object} filters - Filter options\n   * @returns {Promise<ApiResponse>}\n   */\n  async getAllBets(filters = {}) {\n    const params = new URLSearchParams({\n      page: filters.page || 1,\n      limit: filters.limit || 20,\n      sortBy: filters.sortBy || 'created_at',\n      order: filters.order || 'DESC',\n      ...(filters.search && {\n        search: filters.search\n      }),\n      ...(filters.status && {\n        status: filters.status\n      }),\n      ...(filters.dateFrom && {\n        dateFrom: filters.dateFrom\n      }),\n      ...(filters.dateTo && {\n        dateTo: filters.dateTo\n      })\n    });\n    return await apiService.get(`get_all_bets.php?${params.toString()}`);\n  }\n\n  /**\n   * Update bet status (Admin only)\n   * @param {number} betId - Bet ID\n   * @param {string} status - New status\n   * @param {string} reason - Reason for status change\n   * @returns {Promise<ApiResponse>}\n   */\n  async updateBetStatus(betId, status, reason = '') {\n    return await apiService.post('update_bet_status.php', {\n      bet_id: betId,\n      status,\n      reason\n    });\n  }\n\n  /**\n   * Get bet history for user\n   * @param {number} userId - User ID\n   * @param {string} type - Bet type filter ('all', 'won', 'lost', 'pending')\n   * @param {number} page - Page number\n   * @param {number} limit - Items per page\n   * @returns {Promise<ApiResponse>}\n   */\n  async getBetHistory(userId, type = 'all', page = 1, limit = 20) {\n    return await apiService.get('get_bet_history.php', {\n      user_id: userId,\n      type,\n      page,\n      limit\n    });\n  }\n}\n\n// Create singleton instance\nconst betService = new BetService();\nexport default betService;", "map": {"version": 3, "names": ["apiService", "BetService", "getUserBets", "userId", "page", "limit", "search", "get", "getAcceptedBets", "getIncomingBets", "createBet", "betData", "post", "acceptBet", "betId", "bet_id", "user_id", "rejectBet", "cancelBet", "getBetDetails", "getRecentBets", "getBetStats", "settleBet", "winnerId", "notes", "winner_id", "getAllBets", "filters", "params", "URLSearchParams", "sortBy", "order", "status", "dateFrom", "dateTo", "toString", "updateBetStatus", "reason", "getBetHistory", "type", "betService"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/betService.js"], "sourcesContent": ["/**\n * Betting API Service\n * \n * Handles all betting-related API calls with consistent error handling\n * and response formatting.\n */\n\nimport apiService from './apiService';\n\nclass BetService {\n    /**\n     * Get user bets with pagination\n     * @param {number} userId - User ID\n     * @param {number} page - Page number\n     * @param {number} limit - Items per page\n     * @param {string} search - Search term\n     * @returns {Promise<ApiResponse>}\n     */\n    async getUserBets(userId, page = 1, limit = 10, search = '') {\n        return await apiService.get('get_bets.php', {\n            userId,\n            page,\n            limit,\n            search\n        });\n    }\n\n    /**\n     * Get accepted bets for user\n     * @param {number} userId - User ID\n     * @param {number} page - Page number\n     * @param {number} limit - Items per page\n     * @returns {Promise<ApiResponse>}\n     */\n    async getAcceptedBets(userId, page = 1, limit = 10) {\n        return await apiService.get('get_accepted_bets.php', {\n            userId,\n            page,\n            limit\n        });\n    }\n\n    /**\n     * Get incoming bets for user\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getIncomingBets(userId) {\n        return await apiService.get('get_bets.php', { userId });\n    }\n\n    /**\n     * Create a new bet\n     * @param {object} betData - Bet creation data\n     * @returns {Promise<ApiResponse>}\n     */\n    async createBet(betData) {\n        return await apiService.post('create_bet.php', betData);\n    }\n\n    /**\n     * Accept a bet\n     * @param {number} betId - Bet ID\n     * @param {number} userId - User ID accepting the bet\n     * @returns {Promise<ApiResponse>}\n     */\n    async acceptBet(betId, userId) {\n        return await apiService.post('accept_bet.php', {\n            bet_id: betId,\n            user_id: userId\n        });\n    }\n\n    /**\n     * Reject a bet\n     * @param {number} betId - Bet ID\n     * @param {number} userId - User ID rejecting the bet\n     * @returns {Promise<ApiResponse>}\n     */\n    async rejectBet(betId, userId) {\n        return await apiService.post('reject_bet.php', {\n            bet_id: betId,\n            user_id: userId\n        });\n    }\n\n    /**\n     * Cancel a bet\n     * @param {number} betId - Bet ID\n     * @param {number} userId - User ID canceling the bet\n     * @returns {Promise<ApiResponse>}\n     */\n    async cancelBet(betId, userId) {\n        return await apiService.post('cancel_bet.php', {\n            bet_id: betId,\n            user_id: userId\n        });\n    }\n\n    /**\n     * Get bet details\n     * @param {number} betId - Bet ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getBetDetails(betId) {\n        return await apiService.get('get_bet_details.php', { bet_id: betId });\n    }\n\n    /**\n     * Get recent bets for welcome page\n     * @param {number} limit - Number of bets to fetch\n     * @returns {Promise<ApiResponse>}\n     */\n    async getRecentBets(limit = 5) {\n        return await apiService.get('welcome_recent_bets.php', { limit });\n    }\n\n    /**\n     * Get bet statistics for user\n     * @param {number} userId - User ID\n     * @returns {Promise<ApiResponse>}\n     */\n    async getBetStats(userId) {\n        return await apiService.get('get_bet_stats.php', { user_id: userId });\n    }\n\n    /**\n     * Settle a bet (Admin only)\n     * @param {number} betId - Bet ID\n     * @param {number} winnerId - Winner user ID\n     * @param {string} notes - Settlement notes\n     * @returns {Promise<ApiResponse>}\n     */\n    async settleBet(betId, winnerId, notes = '') {\n        return await apiService.post('settle_bet.php', {\n            bet_id: betId,\n            winner_id: winnerId,\n            notes\n        });\n    }\n\n    /**\n     * Get all bets for admin management\n     * @param {object} filters - Filter options\n     * @returns {Promise<ApiResponse>}\n     */\n    async getAllBets(filters = {}) {\n        const params = new URLSearchParams({\n            page: filters.page || 1,\n            limit: filters.limit || 20,\n            sortBy: filters.sortBy || 'created_at',\n            order: filters.order || 'DESC',\n            ...(filters.search && { search: filters.search }),\n            ...(filters.status && { status: filters.status }),\n            ...(filters.dateFrom && { dateFrom: filters.dateFrom }),\n            ...(filters.dateTo && { dateTo: filters.dateTo })\n        });\n\n        return await apiService.get(`get_all_bets.php?${params.toString()}`);\n    }\n\n    /**\n     * Update bet status (Admin only)\n     * @param {number} betId - Bet ID\n     * @param {string} status - New status\n     * @param {string} reason - Reason for status change\n     * @returns {Promise<ApiResponse>}\n     */\n    async updateBetStatus(betId, status, reason = '') {\n        return await apiService.post('update_bet_status.php', {\n            bet_id: betId,\n            status,\n            reason\n        });\n    }\n\n    /**\n     * Get bet history for user\n     * @param {number} userId - User ID\n     * @param {string} type - Bet type filter ('all', 'won', 'lost', 'pending')\n     * @param {number} page - Page number\n     * @param {number} limit - Items per page\n     * @returns {Promise<ApiResponse>}\n     */\n    async getBetHistory(userId, type = 'all', page = 1, limit = 20) {\n        return await apiService.get('get_bet_history.php', {\n            user_id: userId,\n            type,\n            page,\n            limit\n        });\n    }\n}\n\n// Create singleton instance\nconst betService = new BetService();\n\nexport default betService;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,UAAU,MAAM,cAAc;AAErC,MAAMC,UAAU,CAAC;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,WAAWA,CAACC,MAAM,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IACzD,OAAO,MAAMN,UAAU,CAACO,GAAG,CAAC,cAAc,EAAE;MACxCJ,MAAM;MACNC,IAAI;MACJC,KAAK;MACLC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAME,eAAeA,CAACL,MAAM,EAAEC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAE;IAChD,OAAO,MAAML,UAAU,CAACO,GAAG,CAAC,uBAAuB,EAAE;MACjDJ,MAAM;MACNC,IAAI;MACJC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMI,eAAeA,CAACN,MAAM,EAAE;IAC1B,OAAO,MAAMH,UAAU,CAACO,GAAG,CAAC,cAAc,EAAE;MAAEJ;IAAO,CAAC,CAAC;EAC3D;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMO,SAASA,CAACC,OAAO,EAAE;IACrB,OAAO,MAAMX,UAAU,CAACY,IAAI,CAAC,gBAAgB,EAAED,OAAO,CAAC;EAC3D;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAME,SAASA,CAACC,KAAK,EAAEX,MAAM,EAAE;IAC3B,OAAO,MAAMH,UAAU,CAACY,IAAI,CAAC,gBAAgB,EAAE;MAC3CG,MAAM,EAAED,KAAK;MACbE,OAAO,EAAEb;IACb,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMc,SAASA,CAACH,KAAK,EAAEX,MAAM,EAAE;IAC3B,OAAO,MAAMH,UAAU,CAACY,IAAI,CAAC,gBAAgB,EAAE;MAC3CG,MAAM,EAAED,KAAK;MACbE,OAAO,EAAEb;IACb,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMe,SAASA,CAACJ,KAAK,EAAEX,MAAM,EAAE;IAC3B,OAAO,MAAMH,UAAU,CAACY,IAAI,CAAC,gBAAgB,EAAE;MAC3CG,MAAM,EAAED,KAAK;MACbE,OAAO,EAAEb;IACb,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMgB,aAAaA,CAACL,KAAK,EAAE;IACvB,OAAO,MAAMd,UAAU,CAACO,GAAG,CAAC,qBAAqB,EAAE;MAAEQ,MAAM,EAAED;IAAM,CAAC,CAAC;EACzE;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMM,aAAaA,CAACf,KAAK,GAAG,CAAC,EAAE;IAC3B,OAAO,MAAML,UAAU,CAACO,GAAG,CAAC,yBAAyB,EAAE;MAAEF;IAAM,CAAC,CAAC;EACrE;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAMgB,WAAWA,CAAClB,MAAM,EAAE;IACtB,OAAO,MAAMH,UAAU,CAACO,GAAG,CAAC,mBAAmB,EAAE;MAAES,OAAO,EAAEb;IAAO,CAAC,CAAC;EACzE;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMmB,SAASA,CAACR,KAAK,EAAES,QAAQ,EAAEC,KAAK,GAAG,EAAE,EAAE;IACzC,OAAO,MAAMxB,UAAU,CAACY,IAAI,CAAC,gBAAgB,EAAE;MAC3CG,MAAM,EAAED,KAAK;MACbW,SAAS,EAAEF,QAAQ;MACnBC;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;EACI,MAAME,UAAUA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3B,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MAC/BzB,IAAI,EAAEuB,OAAO,CAACvB,IAAI,IAAI,CAAC;MACvBC,KAAK,EAAEsB,OAAO,CAACtB,KAAK,IAAI,EAAE;MAC1ByB,MAAM,EAAEH,OAAO,CAACG,MAAM,IAAI,YAAY;MACtCC,KAAK,EAAEJ,OAAO,CAACI,KAAK,IAAI,MAAM;MAC9B,IAAIJ,OAAO,CAACrB,MAAM,IAAI;QAAEA,MAAM,EAAEqB,OAAO,CAACrB;MAAO,CAAC,CAAC;MACjD,IAAIqB,OAAO,CAACK,MAAM,IAAI;QAAEA,MAAM,EAAEL,OAAO,CAACK;MAAO,CAAC,CAAC;MACjD,IAAIL,OAAO,CAACM,QAAQ,IAAI;QAAEA,QAAQ,EAAEN,OAAO,CAACM;MAAS,CAAC,CAAC;MACvD,IAAIN,OAAO,CAACO,MAAM,IAAI;QAAEA,MAAM,EAAEP,OAAO,CAACO;MAAO,CAAC;IACpD,CAAC,CAAC;IAEF,OAAO,MAAMlC,UAAU,CAACO,GAAG,CAAC,oBAAoBqB,MAAM,CAACO,QAAQ,CAAC,CAAC,EAAE,CAAC;EACxE;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,eAAeA,CAACtB,KAAK,EAAEkB,MAAM,EAAEK,MAAM,GAAG,EAAE,EAAE;IAC9C,OAAO,MAAMrC,UAAU,CAACY,IAAI,CAAC,uBAAuB,EAAE;MAClDG,MAAM,EAAED,KAAK;MACbkB,MAAM;MACNK;IACJ,CAAC,CAAC;EACN;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,aAAaA,CAACnC,MAAM,EAAEoC,IAAI,GAAG,KAAK,EAAEnC,IAAI,GAAG,CAAC,EAAEC,KAAK,GAAG,EAAE,EAAE;IAC5D,OAAO,MAAML,UAAU,CAACO,GAAG,CAAC,qBAAqB,EAAE;MAC/CS,OAAO,EAAEb,MAAM;MACfoC,IAAI;MACJnC,IAAI;MACJC;IACJ,CAAC,CAAC;EACN;AACJ;;AAEA;AACA,MAAMmC,UAAU,GAAG,IAAIvC,UAAU,CAAC,CAAC;AAEnC,eAAeuC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}