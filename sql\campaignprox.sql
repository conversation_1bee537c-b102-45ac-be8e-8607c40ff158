-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:55 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `campaignprox`
--

-- --------------------------------------------------------

--
-- Table structure for table `ab_test_campaigns`
--

CREATE TABLE `ab_test_campaigns` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `test_type` enum('subject','content','sender','time') NOT NULL,
  `variant_a_id` int(11) NOT NULL,
  `variant_b_id` int(11) NOT NULL,
  `test_size` int(11) DEFAULT '30' COMMENT 'Percentage of recipients to include in test',
  `winner_metric` enum('opens','clicks','conversions') NOT NULL DEFAULT 'opens',
  `status` enum('pending','running','completed') NOT NULL DEFAULT 'pending',
  `winner_variant` char(1) DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `ab_test_results`
--

CREATE TABLE `ab_test_results` (
  `id` int(11) NOT NULL,
  `test_id` int(11) NOT NULL,
  `variant` char(1) NOT NULL,
  `recipients` int(11) NOT NULL DEFAULT '0',
  `delivered` int(11) NOT NULL DEFAULT '0',
  `opens` int(11) NOT NULL DEFAULT '0',
  `clicks` int(11) NOT NULL DEFAULT '0',
  `conversions` int(11) NOT NULL DEFAULT '0',
  `delivery_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `open_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `click_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `conversion_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `api_settings`
--

CREATE TABLE `api_settings` (
  `id` int(11) NOT NULL,
  `api_name` varchar(50) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `quota_remaining` int(11) DEFAULT NULL,
  `quota_max` int(11) DEFAULT NULL,
  `quota_reset_date` timestamp NULL DEFAULT NULL,
  `quotas` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `appearance_settings`
--

CREATE TABLE `appearance_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `primary_color` varchar(20) DEFAULT '#d63939',
  `theme` varchar(20) DEFAULT 'light',
  `dashboard_layout` varchar(20) DEFAULT 'default',
  `default_chart_type` varchar(20) DEFAULT 'line',
  `font_size` varchar(20) DEFAULT 'normal',
  `logo_url` varchar(255) DEFAULT NULL,
  `favicon_url` varchar(255) DEFAULT NULL,
  `logo_width` int(11) DEFAULT '200',
  `logo_height` int(11) DEFAULT '60',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `appearance_settings`
--

INSERT INTO `appearance_settings` (`id`, `user_id`, `primary_color`, `theme`, `dashboard_layout`, `default_chart_type`, `font_size`, `logo_url`, `favicon_url`, `logo_width`, `logo_height`, `created_at`, `updated_at`) VALUES
(1, 2, '#4263eb', 'light', 'default', 'bar', 'normal', 'uploads/logos/logo_2_1745475183.png', 'uploads/favicons/favicon_2_1745475183.png', 300, 80, '2025-04-01 17:25:30', '2025-04-24 06:13:34'),
(2, 1, '#d63939', 'light', 'default', 'line', 'normal', NULL, NULL, 200, 60, '2025-04-01 17:25:30', '2025-04-01 17:25:30');

-- --------------------------------------------------------

--
-- Table structure for table `campaigns`
--

CREATE TABLE `campaigns` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subjects` text COLLATE utf8mb4_unicode_ci,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `template_ids` text COLLATE utf8mb4_unicode_ci,
  `template_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `contact_group_id` int(11) DEFAULT NULL,
  `contact_group_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `smtp_id` int(11) DEFAULT NULL,
  `status` enum('draft','pending','active','sent','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'draft',
  `sender_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sender_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reply_to` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `scheduled_at` datetime DEFAULT NULL,
  `started_at` datetime DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `send_speed` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal',
  `priority` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal',
  `batch_size` int(11) NOT NULL DEFAULT '50',
  `track_opens` tinyint(1) NOT NULL DEFAULT '1',
  `track_clicks` tinyint(1) NOT NULL DEFAULT '1',
  `personalize` tinyint(1) NOT NULL DEFAULT '1',
  `sent_at` datetime DEFAULT NULL,
  `recipients_count` int(11) DEFAULT '0',
  `sent_count` int(11) DEFAULT '0',
  `failed_count` int(11) DEFAULT '0',
  `created_by` int(11) NOT NULL,
  `emails_sent` int(11) NOT NULL DEFAULT '0',
  `opens` int(11) NOT NULL DEFAULT '0',
  `clicks` int(11) NOT NULL DEFAULT '0',
  `delivery_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `open_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `click_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `budget` decimal(10,2) NOT NULL DEFAULT '0.00',
  `conversions` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `campaigns`
--

INSERT INTO `campaigns` (`id`, `name`, `subject`, `subjects`, `content`, `template_id`, `template_ids`, `template_type`, `contact_group_id`, `contact_group_type`, `smtp_id`, `status`, `sender_name`, `sender_email`, `reply_to`, `scheduled_at`, `started_at`, `completed_at`, `send_speed`, `priority`, `batch_size`, `track_opens`, `track_clicks`, `personalize`, `sent_at`, `recipients_count`, `sent_count`, `failed_count`, `created_by`, `emails_sent`, `opens`, `clicks`, `delivery_rate`, `open_rate`, `click_rate`, `budget`, `conversions`, `created_at`, `updated_at`) VALUES
(4, 'Welcome Series', 'Welcome to Our Service', NULL, '<h1>Welcome to Our Service</h1><p>Thank you for joining us. We are excited to have you on board!</p>', NULL, NULL, NULL, NULL, NULL, 10, 'active', 'Marketing Team', '<EMAIL>', NULL, '2025-03-26 13:43:49', NULL, NULL, 'normal', 'normal', 50, 1, 1, 1, '2025-03-24 13:43:49', 500, 324, 0, 4, 0, 0, 0, '0.00', '0.00', '0.00', '0.00', 0, '2025-03-26 11:43:49', '2025-04-21 13:52:13'),
(5, 'Monthly Newsletter', 'May Update: New Features', NULL, '<h1>May Updates</h1><p>Check out our new features this month:</p><ul><li>Feature 1</li><li>Feature 2</li></ul>', NULL, NULL, NULL, NULL, NULL, 10, 'active', 'Marketing Team', '<EMAIL>', NULL, '2025-03-26 13:43:49', NULL, NULL, 'normal', 'normal', 50, 1, 1, 1, '2025-03-21 13:43:49', 1500, 1250, 0, 4, 0, 0, 0, '0.00', '0.00', '0.00', '0.00', 0, '2025-03-26 11:43:49', '2025-04-21 13:52:13'),
(6, 'Product Update', 'Exciting New Features Coming Soon', NULL, '<h1>Coming Soon</h1><p>Stay tuned for exciting new features!</p>', NULL, NULL, NULL, NULL, NULL, 10, 'draft', 'Product Team', '<EMAIL>', NULL, NULL, NULL, NULL, 'normal', 'normal', 50, 1, 1, 1, NULL, 0, 0, 0, 4, 0, 0, 0, '0.00', '0.00', '0.00', '0.00', 0, '2025-03-26 11:43:49', '2025-04-21 13:52:13');

-- --------------------------------------------------------

--
-- Table structure for table `campaign_recipients`
--

CREATE TABLE `campaign_recipients` (
  `campaign_id` int(11) NOT NULL,
  `contact_id` int(11) NOT NULL,
  `status` enum('pending','sent','failed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `sent_at` datetime DEFAULT NULL,
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `click_tracking`
--

CREATE TABLE `click_tracking` (
  `id` int(11) NOT NULL,
  `log_id` int(11) NOT NULL,
  `url` text NOT NULL,
  `clicked_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `contacts`
--

CREATE TABLE `contacts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `email` varchar(255) NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `subscribed` tinyint(1) DEFAULT '1',
  `status` enum('active','inactive','unsubscribed') DEFAULT 'active',
  `confirmed` tinyint(1) DEFAULT '0',
  `unsubscribe_reason` varchar(255) DEFAULT NULL,
  `unsubscribed_at` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `contacts`
--

INSERT INTO `contacts` (`id`, `user_id`, `email`, `first_name`, `last_name`, `group_id`, `subscribed`, `status`, `confirmed`, `unsubscribe_reason`, `unsubscribed_at`, `created_at`, `updated_at`) VALUES
(1, 1, '<EMAIL>', 'John', 'Doe', NULL, 1, 'active', 0, NULL, NULL, '2025-03-26 11:40:24', '2025-04-02 10:07:06'),
(2, 1, '<EMAIL>', 'Jane', 'Smith', NULL, 1, 'active', 0, NULL, NULL, '2025-03-26 11:40:24', '2025-04-02 10:07:06'),
(3, 1, '<EMAIL>', 'Bob', 'Johnson', NULL, 1, 'active', 0, NULL, NULL, '2025-03-26 11:40:24', '2025-04-02 10:07:06'),
(6, 1, '<EMAIL>', 'Demo', 'Homexx', NULL, 1, 'active', 0, NULL, NULL, '2025-03-27 06:49:47', '2025-04-02 10:07:06'),
(7, 1, '<EMAIL>', 'Demo', 'Hosting', NULL, 1, 'active', 1, NULL, NULL, '2025-03-27 07:31:16', '2025-04-02 10:07:06'),
(8, 1, '<EMAIL>', 'pal', 'al', NULL, 1, 'active', 0, NULL, NULL, '2025-03-29 05:11:41', '2025-04-02 10:07:06'),
(9, 1, '<EMAIL>', 'Kristapsthedev', 'Test', NULL, 1, 'active', 0, NULL, NULL, '2025-05-13 15:10:26', '2025-05-13 15:10:26'),
(10, 1, '<EMAIL>', 'Demohome4041', 'Test', NULL, 1, 'active', 0, NULL, NULL, '2025-05-13 15:10:26', '2025-05-13 15:10:26'),
(11, 1, '<EMAIL>', 'Thedemohomex', 'Test', NULL, 1, 'active', 0, NULL, NULL, '2025-05-13 15:10:26', '2025-05-13 15:10:26');

-- --------------------------------------------------------

--
-- Table structure for table `contact_groups`
--

CREATE TABLE `contact_groups` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `contact_groups`
--

INSERT INTO `contact_groups` (`id`, `user_id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 1, 'Default Group', 'Default contact group', '2025-04-02 09:33:17', '2025-04-02 09:33:17'),
(13, 2, 'ee', 'ee', '2025-04-02 11:50:46', '2025-04-02 11:50:46'),
(18, 2, 'test', 'test', '2025-05-09 19:45:16', '2025-05-09 19:45:16'),
(19, 1, 'Test Group 2025-05-13 15:10:26', 'Test group for bulk email testing', '2025-05-13 15:10:26', '2025-05-13 15:10:26'),
(20, 1, 'Test PDF Group 2025-05-13 15:10:33', 'Test group for bulk PDF email testing', '2025-05-13 15:10:33', '2025-05-13 15:10:33'),
(21, 1, 'Test Group 2025-05-13 15:18:24', 'Test group for bulk email testing', '2025-05-13 15:18:24', '2025-05-13 15:18:24'),
(22, 1, 'Test PDF Group 2025-05-13 15:18:59', 'Test group for bulk PDF email testing', '2025-05-13 15:18:59', '2025-05-13 15:18:59');

-- --------------------------------------------------------

--
-- Table structure for table `contact_group_members`
--

CREATE TABLE `contact_group_members` (
  `group_id` int(11) NOT NULL,
  `contact_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contact_group_members`
--

INSERT INTO `contact_group_members` (`group_id`, `contact_id`, `created_at`) VALUES
(1, 1, '2025-04-02 10:07:37'),
(1, 2, '2025-04-02 10:07:37'),
(1, 3, '2025-04-02 10:07:37'),
(1, 6, '2025-04-02 10:07:37'),
(1, 7, '2025-04-02 10:07:37'),
(1, 8, '2025-04-02 10:07:37'),
(18, 7, '2025-05-09 20:16:28'),
(21, 6, '2025-05-13 15:18:24'),
(21, 9, '2025-05-13 15:18:24'),
(21, 10, '2025-05-13 15:18:24'),
(21, 11, '2025-05-13 15:18:24'),
(22, 6, '2025-05-13 15:18:59'),
(22, 9, '2025-05-13 15:18:59'),
(22, 10, '2025-05-13 15:18:59'),
(22, 11, '2025-05-13 15:18:59');

-- --------------------------------------------------------

--
-- Table structure for table `email_batch_progress`
--

CREATE TABLE `email_batch_progress` (
  `batch_id` varchar(255) NOT NULL,
  `total_emails` int(11) DEFAULT NULL,
  `sent_count` int(11) DEFAULT '0',
  `error_count` int(11) DEFAULT '0',
  `status` varchar(50) DEFAULT NULL,
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_update` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `completed_at` timestamp NULL DEFAULT NULL,
  `current_position` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `email_batch_progress`
--

INSERT INTO `email_batch_progress` (`batch_id`, `total_emails`, `sent_count`, `error_count`, `status`, `start_time`, `last_update`, `completed_at`, `current_position`) VALUES
('batch_67e39dd5d68e73.57850484', 1, 1, 0, 'Completed', '2025-03-26 06:25:25', '2025-03-26 06:25:31', '2025-03-26 06:25:31', 0),
('batch_67e39e55dcdd20.63639618', 1, 1, 0, 'Completed', '2025-03-26 06:27:33', '2025-03-26 06:27:37', '2025-03-26 06:27:37', 0),
('batch_67e39e7c53b1f9.53209089', 1, 0, 1, 'Failed', '2025-03-26 06:28:12', '2025-03-26 06:28:15', NULL, 0),
('batch_67e39e919a5da8.35685902', 1, 1, 0, 'Completed', '2025-03-26 06:28:33', '2025-03-26 06:28:37', '2025-03-26 06:28:37', 0),
('batch_67e3a7523ccfb5.39964484', 1, 0, 1, 'Failed', '2025-03-26 07:05:54', '2025-03-26 07:05:57', NULL, 0),
('batch_67e3e310abe7b6.48064126', 1, 1, 0, 'Completed', '2025-03-26 11:20:49', '2025-03-26 11:20:53', '2025-03-26 11:20:53', 0),
('batch_67e50d49a0dff7.65631349', 1, 1, 0, 'Completed', '2025-03-27 08:33:13', '2025-03-27 08:33:17', '2025-03-27 08:33:17', 0),
('batch_67e58b7b9f7223.54795795', 1, 1, 0, 'Completed', '2025-03-27 17:31:39', '2025-03-27 17:31:43', '2025-03-27 17:31:43', 0),
('batch_67e9795a01d2f6.46174586', 1, 1, 0, 'Completed', '2025-03-30 17:03:23', '2025-03-30 17:03:28', '2025-03-30 17:03:28', 0),
('batch_67eac8155df977.69650584', 1, 0, 1, 'Failed', '2025-03-31 16:51:33', '2025-03-31 16:51:34', NULL, 0),
('batch_67eac88fda3588.28388997', 1, 0, 1, 'Failed', '2025-03-31 16:53:35', '2025-03-31 16:53:36', NULL, 0),
('batch_67eac8ad3078d7.31455444', 1, 1, 0, 'Completed', '2025-03-31 16:54:05', '2025-03-31 16:54:09', '2025-03-31 16:54:09', 0),
('batch_67eac9d899b7c9.24482645', 1, 0, 1, 'Failed', '2025-03-31 16:59:04', '2025-03-31 16:59:05', NULL, 0),
('batch_67eacbbd929818.82499405', 1, 0, 1, 'Failed', '2025-03-31 17:07:09', '2025-03-31 17:07:12', NULL, 0),
('batch_67eacc071ddd65.84152966', 1, 0, 1, 'Failed', '2025-03-31 17:08:23', '2025-03-31 17:08:25', NULL, 0),
('batch_67eacdb596a9a7.93872209', 1, 0, 1, 'Failed', '2025-03-31 17:15:33', '2025-03-31 17:15:35', NULL, 0),
('batch_67eaeeaee969f4.08902578', 1, 0, 1, 'Failed', '2025-03-31 19:36:14', '2025-03-31 19:36:17', NULL, 0),
('batch_67eaefee1cdde8.12083403', 1, 0, 1, 'Failed', '2025-03-31 19:41:34', '2025-03-31 19:41:36', NULL, 0),
('batch_67eaf0d77991e5.57975748', 1, 0, 1, 'Failed', '2025-03-31 19:45:27', '2025-03-31 19:45:29', NULL, 0),
('batch_67eaf1e054aeb5.58821855', 1, 1, 0, 'Completed', '2025-03-31 19:49:52', '2025-03-31 19:49:55', '2025-03-31 19:49:55', 0),
('batch_67eed559c5dcc8.48500241', 1, 0, 1, 'Failed', '2025-04-03 18:37:13', '2025-04-03 18:37:14', NULL, 0),
('batch_67eedd3294cd13.18347848', 1, 0, 1, 'Failed', '2025-04-03 19:10:42', '2025-04-03 19:10:42', NULL, 0),
('batch_67eee0cfcfa2a1.22608645', 1, 0, 1, 'Failed', '2025-04-03 19:26:07', '2025-04-03 19:26:08', NULL, 0),
('batch_67f689b60ee838.14003523', 1, 1, 0, 'Completed', '2025-04-09 14:52:38', '2025-04-09 14:52:42', '2025-04-09 14:52:42', 0),
('batch_68054c3f0f6994.30801481', 1, 1, 0, 'Completed', '2025-04-20 19:34:23', '2025-04-20 19:34:27', '2025-04-20 19:34:27', 0),
('batch_680a21bbcab355.32929642', 1, 1, 0, 'Completed', '2025-04-24 11:34:20', '2025-04-24 11:34:23', '2025-04-24 11:34:23', 0),
('batch_680af6a2403584.99398845', 1, 1, 0, 'Completed', '2025-04-25 02:42:42', '2025-04-25 02:42:45', '2025-04-25 02:42:45', 0),
('batch_681e060014a095.52108037', 1, 1, 0, 'Completed', '2025-05-09 13:41:20', '2025-05-09 13:41:23', '2025-05-09 13:41:23', 0),
('batch_681e06a85b45c6.75505433', 1, 0, 1, 'Failed', '2025-05-09 13:44:08', '2025-05-09 13:44:11', NULL, 0),
('batch_681e0f42abcd17.36147011', 1, 1, 0, 'Completed', '2025-05-09 14:20:50', '2025-05-09 14:20:54', '2025-05-09 14:20:54', 0),
('batch_681e0f73cccdc6.21193988', 1, 1, 0, 'Completed', '2025-05-09 14:21:39', '2025-05-09 14:21:42', '2025-05-09 14:21:42', 0),
('batch_681e17199d3d72.38364585', 1, 1, 0, 'Completed', '2025-05-09 14:54:17', '2025-05-09 14:54:21', '2025-05-09 14:54:21', 0),
('batch_681e1746b2b0b8.46168213', 1, 1, 0, 'Completed', '2025-05-09 14:55:02', '2025-05-09 14:55:05', '2025-05-09 14:55:05', 0),
('pdf_68095e346421d7.97384033', 1, 1, 0, 'Completed', '2025-04-23 21:40:04', '2025-04-23 21:40:09', '2025-04-23 21:40:09', 0),
('pdf_68095f1b66f842.05332608', 1, 1, 0, 'Completed', '2025-04-23 21:43:55', '2025-04-23 21:44:00', '2025-04-23 21:44:00', 0),
('pdf_68096090c32553.91131225', 1, 1, 0, 'Completed', '2025-04-23 21:50:08', '2025-04-23 21:50:13', '2025-04-23 21:50:13', 0),
('pdf_680964c305dab6.85177029', 1, 1, 0, 'Completed', '2025-04-23 22:08:03', '2025-04-23 22:08:07', '2025-04-23 22:08:07', 0),
('pdf_680968ca1f8d80.59503170', 1, 1, 0, 'Completed', '2025-04-23 22:25:14', '2025-04-23 22:25:18', '2025-04-23 22:25:18', 0),
('pdf_680a15f87ac571.94377751', 1, 1, 0, 'Completed', '2025-04-24 10:44:08', '2025-04-24 10:44:14', '2025-04-24 10:44:14', 0),
('pdf_680a19e3dfd3d9.55746924', 1, 1, 0, 'Completed', '2025-04-24 11:00:51', '2025-04-24 11:00:57', '2025-04-24 11:00:57', 0),
('pdf_680a6c89312866.13696901', 1, 1, 0, 'Completed', '2025-04-24 16:53:29', '2025-04-24 16:53:38', '2025-04-24 16:53:38', 0),
('pdf_680b087c568c87.41921839', 1, 1, 0, 'Completed', '2025-04-25 03:58:52', '2025-04-25 03:58:56', '2025-04-25 03:58:56', 0);

-- --------------------------------------------------------

--
-- Table structure for table `email_log`
--

CREATE TABLE `email_log` (
  `id` int(11) NOT NULL,
  `batch_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `contact_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `template_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'regular',
  `recipient_email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `reference_number` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'Pending',
  `error_message` text COLLATE utf8mb4_unicode_ci,
  `sent` tinyint(1) DEFAULT '0',
  `opened` tinyint(1) DEFAULT '0',
  `opened_at` timestamp NULL DEFAULT NULL,
  `clicked` tinyint(1) DEFAULT '0',
  `clicked_at` timestamp NULL DEFAULT NULL,
  `open_count` int(11) DEFAULT '0',
  `click_count` int(11) DEFAULT '0',
  `first_opened_at` timestamp NULL DEFAULT NULL,
  `last_opened_at` timestamp NULL DEFAULT NULL,
  `first_clicked_at` timestamp NULL DEFAULT NULL,
  `last_clicked_at` timestamp NULL DEFAULT NULL,
  `device_type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'Unknown',
  `device_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'Unknown',
  `os_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'Unknown',
  `browser_name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'Unknown',
  `message_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `has_attachment` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `email_log`
--

INSERT INTO `email_log` (`id`, `batch_id`, `campaign_id`, `contact_id`, `template_id`, `template_type`, `recipient_email`, `subject`, `reference_number`, `sent_at`, `status`, `error_message`, `sent`, `opened`, `opened_at`, `clicked`, `clicked_at`, `open_count`, `click_count`, `first_opened_at`, `last_opened_at`, `first_clicked_at`, `last_clicked_at`, `device_type`, `device_name`, `os_name`, `browser_name`, `message_id`, `has_attachment`) VALUES
(3, NULL, NULL, NULL, NULL, 'regular', '<EMAIL>', 'Welcome to Our Community', NULL, '2025-03-25 21:19:40', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(4, 'batch_67e39dd5d68e73.57850484', NULL, NULL, 3, '0', '<EMAIL>', 'Welcome to our Community', NULL, '2025-03-26 06:25:31', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(5, 'batch_67e39e55dcdd20.63639618', NULL, NULL, 3, '0', '<EMAIL>', 'Welcome to our Community', NULL, '2025-03-26 06:27:37', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(6, 'batch_67e39e919a5da8.35685902', NULL, NULL, 6, '0', '<EMAIL>', 'The Big Raffle - You are a Winner!', NULL, '2025-03-26 06:28:37', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(7, 'batch_67e3e310abe7b6.48064126', NULL, NULL, 6, '0', '<EMAIL>', 'The Big Raffle - You are a Winner!', NULL, '2025-03-26 11:20:52', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(8, 'batch_67e50d49a0dff7.65631349', NULL, NULL, 7, '0', '<EMAIL>', 'Sales', NULL, '2025-03-27 08:33:17', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(9, 'batch_67e58b7b9f7223.54795795', NULL, NULL, 8, '0', '<EMAIL>', 'Products Newsletter', NULL, '2025-03-27 17:31:43', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(10, 'batch_67e9795a01d2f6.46174586', NULL, NULL, 6, '0', '<EMAIL>', 'The Big Raffle - You are a Winner!', NULL, '2025-03-30 17:03:28', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(11, 'batch_67eac8ad3078d7.31455444', NULL, NULL, 8, '0', '<EMAIL>', 'Products Newsletter', NULL, '2025-03-31 16:54:09', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(12, 'batch_67eaf1e054aeb5.58821855', NULL, NULL, 7, '0', '<EMAIL>', 'Sales', NULL, '2025-03-31 19:49:55', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(13, 'batch_67f689b60ee838.14003523', NULL, NULL, 7, 'html', '<EMAIL>', 'Sales', NULL, '2025-04-09 14:52:42', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(14, 'batch_68054c3f0f6994.30801481', NULL, NULL, 9, 'html', '<EMAIL>', 'Image Generation Invitation', NULL, '2025-04-20 19:34:27', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(15, 'batch_680a21bbcab355.32929642', NULL, NULL, 26, 'richtext', '<EMAIL>', 'You have Got an Attachment!', NULL, '2025-04-24 11:34:23', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(16, 'batch_680af6a2403584.99398845', NULL, NULL, 26, 'richtext', '<EMAIL>', 'You have Got an Attachment!', NULL, '2025-04-25 02:42:45', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(17, 'batch_681e060014a095.52108037', NULL, NULL, 7, 'richtext', '<EMAIL>', 'Sales', NULL, '2025-05-09 13:41:23', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(18, 'batch_681e0f42abcd17.36147011', NULL, NULL, 8, 'richtext', '<EMAIL>', 'Products Newsletter', NULL, '2025-05-09 14:20:53', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(19, 'batch_681e0f73cccdc6.21193988', NULL, NULL, 8, 'richtext', '<EMAIL>', 'Products Newsletter', NULL, '2025-05-09 14:21:42', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(20, 'batch_681e17199d3d72.38364585', NULL, NULL, 8, 'richtext', '<EMAIL>', 'Products Newsletter', NULL, '2025-05-09 14:54:21', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(21, 'batch_681e1746b2b0b8.46168213', NULL, NULL, 7, 'richtext', '<EMAIL>', 'Sales', NULL, '2025-05-09 14:55:05', 'Sent', NULL, 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', '<<EMAIL>>', 0),
(22, 'test_682362c100259', NULL, 6, 1, 'regular', NULL, 'Thank You for Your Support!', NULL, '2025-05-13 15:18:30', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(23, 'test_682362c100259', NULL, 9, 1, 'regular', NULL, 'Thank You for Your Support!', NULL, '2025-05-13 15:18:34', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(24, 'test_682362c100259', NULL, 10, 1, 'regular', NULL, 'Thank You for Your Support!', NULL, '2025-05-13 15:18:38', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(25, 'test_682362c100259', NULL, 11, 1, 'regular', NULL, 'Thank You for Your Support!', NULL, '2025-05-13 15:18:42', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(26, 'test_pdf_682362e3e8b32', NULL, 6, NULL, 'regular', NULL, 'Test PDF Email from Campaign', NULL, '2025-05-13 15:19:04', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(27, 'test_pdf_682362e3e8b32', NULL, 9, NULL, 'regular', NULL, 'Test PDF Email from Campaign', NULL, '2025-05-13 15:19:08', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(28, 'test_pdf_682362e3e8b32', NULL, 10, NULL, 'regular', NULL, 'Test PDF Email from Campaign', NULL, '2025-05-13 15:19:12', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0),
(29, 'test_pdf_682362e3e8b32', NULL, 11, NULL, 'regular', NULL, 'Test PDF Email from Campaign', NULL, '2025-05-13 15:19:16', 'error', 'SMTP Error: Could not connect to SMTP host. Failed to connect to server', 0, 0, NULL, 0, NULL, 0, 0, NULL, NULL, NULL, NULL, 'Unknown', 'Unknown', 'Unknown', 'Unknown', NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `email_opens`
--

CREATE TABLE `email_opens` (
  `id` int(11) NOT NULL,
  `log_id` int(11) NOT NULL,
  `opened_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `device_type` varchar(20) DEFAULT 'Unknown',
  `device_name` varchar(50) DEFAULT 'Unknown',
  `os_name` varchar(50) DEFAULT 'Unknown',
  `browser_name` varchar(50) DEFAULT 'Unknown'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `email_send_logs`
--

CREATE TABLE `email_send_logs` (
  `id` int(11) NOT NULL,
  `smtp_id` int(11) NOT NULL,
  `send_date` date NOT NULL,
  `send_count` int(11) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `category` varchar(255) DEFAULT 'Default',
  `subject` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `email_templates`
--

INSERT INTO `email_templates` (`id`, `name`, `category`, `subject`, `content`, `created_at`, `updated_at`) VALUES
(1, 'Thank You Email', 'Default', 'Thank You for Your Support!', 'Dear Customer, (Ref: {ref}) Thank you for your support of . We truly appreciate your trust in us. Your continued support allows us to serve our community and make a difference. If you have any questions or need assistance, please contact us. Best regards,\nThe Team', '2025-03-26 06:07:59', '2025-04-24 16:03:48'),
(2, 'Welcome Email', 'Default', 'Welcome to {{companyName}}!', 'Dear Customer, (Ref: {ref}) Welcome to ! We are thrilled to have you join our community. Your account has been successfully created and is now active. You can now access all of our services and features. If you need any assistance or have any questions, feel free to reply to this email or contact our support team. We look forward to serving you! Best regards,\nThe Team', '2025-03-26 06:13:09', '2025-04-24 16:03:48'),
(3, 'Welcome Email', 'Default', 'Welcome to our Community', 'Dear Customer, (Ref: {ref}) Welcome to our community! We are thrilled to have you join us. Your account has been created successfully and is now ready to use. Best regards,\nThe Team', '2025-03-26 06:13:17', '2025-04-24 16:03:48'),
(5, 'Event Invitation', 'Default', 'You\'re Invited!', 'Dear Customer, (Ref: {ref}) We would like to invite you to our upcoming event. Date: June 15, 2023\nTime: 7:00 PM\nLocation: Main Conference Center Please RSVP by June 1, 2023 if you plan to attend. We look forward to seeing you! Best regards,\nThe Team', '2025-03-26 06:15:26', '2025-04-24 16:03:48'),
(6, 'Follow-up Email', 'Default', 'Following Up on Our Conversation', 'Dear Customer, (Ref: {ref}) I hope this email finds you well. I wanted to follow up on our recent conversation about [topic]. As discussed, we [brief summary of what was discussed]. Please let me know if you have any questions or if there\'s anything else I can help with. Best regards,\nThe Team', '2025-03-26 06:15:32', '2025-04-24 16:03:48'),
(7, 'Follow-up Email', 'Default', 'Following Up', 'Dear Customer, (Ref: {ref}) I hope this email finds you well. I wanted to follow up on our recent conversation. Let me know if you have any questions. Best regards,\nThe Team', '2025-03-26 06:15:47', '2025-04-24 16:03:48');

-- --------------------------------------------------------

--
-- Table structure for table `encrypted_links`
--

CREATE TABLE `encrypted_links` (
  `id` int(11) NOT NULL,
  `original_url` text NOT NULL,
  `encrypted_url` text NOT NULL,
  `token` varchar(1024) DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_used` timestamp NULL DEFAULT NULL,
  `use_count` int(11) DEFAULT '0',
  `clicks` int(11) DEFAULT '0',
  `last_clicked_at` timestamp NULL DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `encrypted_links`
--

INSERT INTO `encrypted_links` (`id`, `original_url`, `encrypted_url`, `token`, `created_by`, `created_at`, `last_used`, `use_count`, `clicks`, `last_clicked_at`, `description`) VALUES
(4, 'https://demohomex.com/flux-ai/', 'https://demohomex.com/test/redirect.php?token=KjbQXXPj2Ozh1Sknzan02HZTs99k8BwJ%2F%2FJTGzHpMxvGDrNWFqfVpn45DOEOcMZ72CGHcEUD2PepS8bu8CyEg5BMBpO%2F1Ukxcy3QC1pZ1Sw%3D', 'KjbQXXPj2Ozh1Sknzan02HZTs99k8BwJ//JTGzHpMxvGDrNWFqfVpn45DOEOcMZ72CGHcEUD2PepS8bu8CyEg5BMBpO/1Ukxcy3QC1pZ1Sw=', 2, '2025-04-20 19:55:04', NULL, 0, 0, NULL, 'Image generation'),
(5, 'https://demohomex.com/flux-ai/', 'https://demohomex.com/test/redirect.php?token=eCwpm84RtvHqMnZjoYZ33%2F9%2FmJWxffCvHGkfLmRB33jNSOovwjvgP5iwA2eqEJZ8FAXq7lFtCvrRAZkD9E7p7I%2BDmv8qm%2Bqr2cPaTHpmGxI%3D', 'eCwpm84RtvHqMnZjoYZ33/9/mJWxffCvHGkfLmRB33jNSOovwjvgP5iwA2eqEJZ8FAXq7lFtCvrRAZkD9E7p7I+Dmv8qm+qr2cPaTHpmGxI=', 2, '2025-04-20 20:14:42', NULL, 0, 0, NULL, 'image'),
(6, 'https://demohomex.com/flux-ai/', 'https://demohomex.com/test/redirect.php?token=Ux%2BvBfjeawCkrvVDvF58URsc8l9FG9y7m0SlgDN3lcDI8xEh8QEYnU6POKTS3KdF4WCyR%2BacK591eZOfZT462y7dw4is6SsbPHOvKqabTsk%3D', 'Ux+vBfjeawCkrvVDvF58URsc8l9FG9y7m0SlgDN3lcDI8xEh8QEYnU6POKTS3KdF4WCyR+acK591eZOfZT462y7dw4is6SsbPHOvKqabTsk=', 2, '2025-04-20 20:26:35', NULL, 0, 0, NULL, '');

-- --------------------------------------------------------

--
-- Table structure for table `error_settings`
--

CREATE TABLE `error_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `error_level` varchar(20) NOT NULL DEFAULT 'all',
  `log_errors` tinyint(1) NOT NULL DEFAULT '1',
  `display_errors` tinyint(1) NOT NULL DEFAULT '1',
  `error_reporting` varchar(255) NOT NULL DEFAULT 'E_ALL',
  `log_file_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `error_settings`
--

INSERT INTO `error_settings` (`id`, `user_id`, `error_level`, `log_errors`, `display_errors`, `error_reporting`, `log_file_path`, `created_at`, `updated_at`) VALUES
(1, 2, 'all', 1, 1, 'E_ALL', NULL, '2025-04-01 18:20:26', '2025-04-01 18:20:26'),
(2, 1, 'all', 1, 1, 'E_ALL', NULL, '2025-04-01 18:20:26', '2025-04-01 18:20:26');

-- --------------------------------------------------------

--
-- Table structure for table `html_templates`
--

CREATE TABLE `html_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `category` varchar(50) DEFAULT 'Custom',
  `subject` varchar(255) DEFAULT NULL,
  `content` mediumtext,
  `template_type` varchar(50) DEFAULT 'richtext',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `html_templates`
--

INSERT INTO `html_templates` (`id`, `name`, `category`, `subject`, `content`, `template_type`, `created_at`, `updated_at`) VALUES
(1, 'Account Details Reset Notification', 'Custom', 'Password Reset Notification', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\">\r\n    <title>Password Reset Alert</title>\r\n    <style type=\"text/css\">\r\n        #preheader { display: none !important; visibility: hidden; opacity: 0; color: #f4f4f4; height: 0; width: 0; }\r\n        body {\r\n            font-family: \'Roboto\', \'Helvetica\', \'Arial\', sans-serif;\r\n            margin: 0;\r\n            padding: 0;\r\n            background-color: #f4f4f4;\r\n            color: #454545;\r\n        }\r\n        .wrapper {\r\n            width: 100%;\r\n            padding: 60px 20px;\r\n            background-color: #f4f4f4;\r\n        }\r\n        .container {\r\n            width: 100%;\r\n            max-width: 600px;\r\n            margin: 0 auto;\r\n            background-color: #ffffff;\r\n            border-radius: 8px;\r\n            border: 1px solid #e0e0e0;\r\n            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);\r\n        }\r\n        .header {\r\n            background-color: #28a745; /* Green header */\r\n            padding: 50px 20px;\r\n            text-align: center;\r\n        }\r\n        .header h1 {\r\n            margin: 0;\r\n            font-size: 28px;\r\n            color: #ffffff;\r\n            font-weight: 300;\r\n        }\r\n        .content {\r\n            padding: 60px 40px;\r\n        }\r\n        .content p {\r\n            font-size: 16px;\r\n            line-height: 26px;\r\n            margin: 0 0 40px;\r\n        }\r\n        .content .highlight {\r\n            margin-bottom: 50px;\r\n            font-weight: 600;\r\n        }\r\n        .button {\r\n            display: inline-block;\r\n            padding: 14px 30px;\r\n            background-color: #28a745; /* Green button */\r\n            color: #ffffff;\r\n            text-decoration: none;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n            border-radius: 4px;\r\n            text-align: center;\r\n            margin-bottom: 50px;\r\n        }\r\n        .button:hover {\r\n            background-color: #218838; /* Darker green on hover */\r\n        }\r\n        .footer {\r\n            padding: 50px 20px;\r\n            font-size: 12px;\r\n            color: #666666;\r\n            background-color: #f9f9f9;\r\n            text-align: center;\r\n            border-top: 1px solid #e0e0e0;\r\n        }\r\n        .footer a {\r\n            color: #28a745; /* Green link */\r\n            text-decoration: none;\r\n        }\r\n        .footer strong {\r\n            font-weight: 600;\r\n        }\r\n        @media only screen and (max-width: 600px) {\r\n            .content { padding: 40px 20px; }\r\n            .header { padding: 40px 15px; }\r\n            .footer { padding: 40px 15px; }\r\n            .button { width: 100%; box-sizing: border-box; }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <!-- Preheader -->\r\n    <span id=\"preheader\">Please review your password reset alert.</span>\r\n    \r\n    <!-- Plain-text version -->\r\n    <!--[\r\n    Greetings,\r\n\r\n    A password reset attempt has been detected for your account.\r\n\r\n    If this was your action, you can ignore this message. If not, please preserve your current password to protect your account.\r\n\r\n    Preserve Current Password: {{secureLink}}\r\n\r\n    Taking quick action will safeguard your mailbox access.\r\n\r\n    CONFIDENTIALITY NOTICE:\r\n    This email may contain sensitive details. If you’re not the intended recipient, notify the sender, delete this message, and destroy any copies.\r\n\r\n    To unsubscribe: [Unsubscribe Link]\r\n    ]-->\r\n\r\n    <!-- HTML Email -->\r\n    <div class=\"wrapper\">\r\n        <div class=\"container\">\r\n            <div class=\"header\">\r\n                <h1>Password Reset Alert</h1>\r\n            </div>\r\n            <div class=\"content\">\r\n                <p>Greetings,</p>\r\n                <p class=\"highlight\">A password reset attempt has been detected for your account.</p>\r\n                <p>If this was your action, you can disregard this notice. Otherwise, please preserve your current password to maintain account security.</p>\r\n                <p><a href=\"{{secureLink}}\" class=\"button\" target=\"_blank\">Preserve Current Password</a></p>\r\n                <p>Taking quick action will safeguard your access to your mailbox.</p>\r\n            </div>\r\n            <div class=\"footer\">\r\n                <p><strong>CONFIDENTIALITY NOTICE:</strong><br>\r\n                This email and any attachments may contain sensitive details. If you are not the intended recipient, please notify the sender immediately, delete this email, and destroy any copies. Unauthorized use or dissemination is prohibited and may be illegal.</p>\r\n                <p><a href=\"[Unsubscribe Link]\" target=\"_blank\">Unsubscribe</a></p>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 'richtext', '2025-03-26 05:18:25', '2025-03-26 06:48:56'),
(6, 'You are a Winner!', 'Custom', 'The Big Raffle - You are a Winner!', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>The Big Raffle - You\'re a Winner!</title>\r\n    <style>\r\n        body, html {\r\n            margin: 0;\r\n            padding: 0;\r\n            font-family: Arial, Helvetica, sans-serif;\r\n            background-color: #f4f4f4;\r\n        }\r\n        \r\n        .container {\r\n            width: 100%;\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            background-color: #ffffff;\r\n            border-radius: 10px;\r\n            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\r\n            overflow: hidden;\r\n        }\r\n        \r\n        .flyer {\r\n            background: linear-gradient(135deg, #8A2BE2, #5a1ca5);\r\n            border-radius: 10px 10px 0 0;\r\n            text-align: center;\r\n            padding: 30px;\r\n        }\r\n        \r\n        .winner-banner {\r\n            background-color: #FFD700;\r\n            color: #8A2BE2;\r\n            font-size: 22px;\r\n            font-weight: bold;\r\n            padding: 10px 20px;\r\n            border-radius: 8px;\r\n            display: inline-block;\r\n        }\r\n        \r\n        .title {\r\n            font-size: 40px;\r\n            font-weight: bold;\r\n            color: #fff;\r\n            margin: 15px 0;\r\n        }\r\n        \r\n        .lotto-ball {\r\n            width: 60px;\r\n            height: 60px;\r\n            border-radius: 50%;\r\n            display: inline-flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            font-size: 22px;\r\n            font-weight: bold;\r\n            color: white;\r\n            margin: 5px;\r\n        }\r\n        \r\n        .ball-1 { background-color: #FF69B4; }\r\n        .ball-2 { background-color: #FFD700; }\r\n        .ball-3 { background-color: #00CED1; }\r\n        .ball-4 { background-color: #32CD32; }\r\n        .ball-5 { background-color: #FF6347; }\r\n        \r\n        .powerball {\r\n            background-color: #FF0000;\r\n            width: 70px;\r\n            height: 70px;\r\n            font-size: 24px;\r\n        }\r\n        \r\n        .claim-section {\r\n            text-align: center;\r\n            padding: 20px;\r\n        }\r\n        \r\n        .claim-button {\r\n            display: inline-block;\r\n            padding: 15px 30px;\r\n            background-color: #FFD700;\r\n            color: #8A2BE2;\r\n            font-weight: bold;\r\n            text-decoration: none;\r\n            border-radius: 6px;\r\n            font-size: 20px;\r\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n        }\r\n        \r\n        .instructions {\r\n            background-color: #f8f8f8;\r\n            border-radius: 8px;\r\n            padding: 20px;\r\n            margin: 20px;\r\n        }\r\n        \r\n        .footer {\r\n            background-color: #8A2BE2;\r\n            color: white;\r\n            padding: 15px;\r\n            font-size: 12px;\r\n            text-align: center;\r\n            border-radius: 0 0 10px 10px;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <table class=\"container\">\r\n        <tr>\r\n            <td class=\"flyer\">\r\n                <div class=\"winner-banner\">CONGRATULATIONS!</div>\r\n                <div class=\"title\">THE BIG RAFFLE</div>\r\n                <p style=\"color: white; font-size: 24px;\">You\'re a WINNER!</p>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td align=\"center\" style=\"padding: 20px;\">\r\n                <div class=\"lotto-ball ball-1\">12</div>\r\n                <div class=\"lotto-ball ball-2\">23</div>\r\n                <div class=\"lotto-ball ball-3\">34</div>\r\n                <div class=\"lotto-ball ball-4\">45</div>\r\n                <div class=\"lotto-ball ball-5\">56</div>\r\n                <div class=\"lotto-ball powerball\">7</div>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"claim-section\">\r\n                <p style=\"font-size: 18px;\">Congratulations! Your Powerball numbers have been drawn, and you\'re a winner! A life-changing prize awaits you.</p>\r\n                <a href=\"mailto:<EMAIL>?subject=I%20Won%20The%20Big%20Raffle&body=Hello,%0A%0AI%20received%20your%20email%20notification%20and%20I%20am%20confirming%20that%20I%20am%20the%20Powerball%20winner!%0A%0AMy%20contact%20details:%0AName:%20%0APhone:%20%0AAddress:%20%0A%0AI\'m%20excited%20to%20claim%20my%20prize!%0A%0AThank%20you,%0A\" class=\"claim-button\">CLAIM YOUR PRIZE NOW</a>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"instructions\">\r\n                <h3 style=\"color: #8A2BE2;\">How to Claim Your Prize:</h3>\r\n                <ol>\r\n                    <li>Click the claim button above</li>\r\n                    <li>Verify your identity</li>\r\n                    <li>Choose your payment method</li>\r\n                    <li>Receive your prize within 24 hours</li>\r\n                </ol>\r\n            </td>\r\n        </tr>\r\n        <tr>\r\n            <td class=\"footer\">\r\n                <p>© 2025 The Big Raffle. All rights reserved.</p>\r\n                <p>If you did not register for this raffle, please disregard this email.</p>\r\n                <p><a href=\"{{unsubscribeLink}}\" style=\"color: #FFD700; text-decoration: none;\">Unsubscribe</a></p>\r\n            </td>\r\n        </tr>\r\n    </table>\r\n</body>\r\n</html>', 'richtext', '2025-03-26 05:35:41', '2025-03-31 16:56:36'),
(7, 'Sales', 'Custom', 'Sales', '<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\"><html dir=\"ltr\" xmlns=\"http://www.w3.org/1999/xhtml\" xmlns:o=\"urn:schemas-microsoft-com:office:office\" lang=\"en\"><head><meta charset=\"UTF-8\"><meta content=\"width=device-width, initial-scale=1\" name=\"viewport\"><meta name=\"x-apple-disable-message-reformatting\"><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\"><meta content=\"telephone=no\" name=\"format-detection\"><title>New Message</title> <!--[if (mso 16)]><style type=\"text/css\"> a {text-decoration: none;}  </style><![endif]--><!--[if gte mso 9]><style>sup { font-size: 100% !important; }</style><![endif]--><!--[if gte mso 9]><noscript> <xml> <o:OfficeDocumentSettings> <o:AllowPNG></o:AllowPNG> <o:PixelsPerInch>96</o:PixelsPerInch> </o:OfficeDocumentSettings> </xml> </noscript>\r\n<![endif]--><!--[if mso]><xml> <w:WordDocument xmlns:w=\"urn:schemas-microsoft-com:office:word\"> <w:DontUseAdvancedTypographyReadingMail/> </w:WordDocument> </xml>\r\n<![endif]--><style type=\"text/css\">.rollover:hover .rollover-first { max-height:0px!important; display:none!important;}.rollover:hover .rollover-second { max-height:none!important; display:block!important;}.rollover span { font-size:0px;}u + .body img ~ div div { display:none;}#outlook a { padding:0;}span.MsoHyperlink,span.MsoHyperlinkFollowed { color:inherit; mso-style-priority:99;}a.es-button { mso-style-priority:100!important; text-decoration:none!important;}a[x-apple-data-detectors],#MessageViewBody a { color:inherit!important; text-decoration:none!important; font-size:inherit!important; font-family:inherit!important; font-weight:inherit!important; line-height:inherit!important;}.es-desk-hidden { display:none; float:left; overflow:hidden; width:0; max-height:0; line-height:0; mso-hide:all;}@media only screen and (max-width:600px) {.es-m-p0r { padding-right:0px!important } .es-m-p20b { padding-bottom:20px!important }\r\n .es-p-default { } *[class=\"gmail-fix\"] { display:none!important } p, a { line-height:150%!important } h1, h1 a { line-height:120%!important } h2, h2 a { line-height:120%!important } h3, h3 a { line-height:120%!important } h4, h4 a { line-height:120%!important } h5, h5 a { line-height:120%!important } h6, h6 a { line-height:120%!important } .es-header-body p { } .es-content-body p { } .es-footer-body p { } .es-infoblock p { } h1 { font-size:36px!important; text-align:left } h2 { font-size:26px!important; text-align:left } h3 { font-size:20px!important; text-align:left } h4 { font-size:24px!important; text-align:left } h5 { font-size:20px!important; text-align:left } h6 { font-size:16px!important; text-align:left } .es-header-body h1 a, .es-content-body h1 a, .es-footer-body h1 a { font-size:36px!important } .es-header-body h2 a, .es-content-body h2 a, .es-footer-body h2 a { font-size:26px!important }\r\n .es-header-body h3 a, .es-content-body h3 a, .es-footer-body h3 a { font-size:20px!important } .es-header-body h4 a, .es-content-body h4 a, .es-footer-body h4 a { font-size:24px!important } .es-header-body h5 a, .es-content-body h5 a, .es-footer-body h5 a { font-size:20px!important } .es-header-body h6 a, .es-content-body h6 a, .es-footer-body h6 a { font-size:16px!important } .es-menu td a { font-size:12px!important } .es-header-body p, .es-header-body a { font-size:14px!important } .es-content-body p, .es-content-body a { font-size:14px!important } .es-footer-body p, .es-footer-body a { font-size:14px!important } .es-infoblock p, .es-infoblock a { font-size:12px!important } .es-m-txt-c, .es-m-txt-c h1, .es-m-txt-c h2, .es-m-txt-c h3, .es-m-txt-c h4, .es-m-txt-c h5, .es-m-txt-c h6 { text-align:center!important }\r\n .es-m-txt-r, .es-m-txt-r h1, .es-m-txt-r h2, .es-m-txt-r h3, .es-m-txt-r h4, .es-m-txt-r h5, .es-m-txt-r h6 { text-align:right!important } .es-m-txt-j, .es-m-txt-j h1, .es-m-txt-j h2, .es-m-txt-j h3, .es-m-txt-j h4, .es-m-txt-j h5, .es-m-txt-j h6 { text-align:justify!important } .es-m-txt-l, .es-m-txt-l h1, .es-m-txt-l h2, .es-m-txt-l h3, .es-m-txt-l h4, .es-m-txt-l h5, .es-m-txt-l h6 { text-align:left!important } .es-m-txt-r img, .es-m-txt-c img, .es-m-txt-l img { display:inline!important } .es-m-txt-r .rollover:hover .rollover-second, .es-m-txt-c .rollover:hover .rollover-second, .es-m-txt-l .rollover:hover .rollover-second { display:inline!important } .es-m-txt-r .rollover span, .es-m-txt-c .rollover span, .es-m-txt-l .rollover span { line-height:0!important; font-size:0!important; display:block } .es-spacer { display:inline-table }\r\n a.es-button, button.es-button { font-size:20px!important; padding:10px 20px 10px 20px!important; line-height:120%!important } a.es-button, button.es-button, .es-button-border { display:inline-block!important } .es-m-fw, .es-m-fw.es-fw, .es-m-fw .es-button { display:block!important } .es-m-il, .es-m-il .es-button, .es-social, .es-social td, .es-menu { display:inline-block!important } .es-adaptive table, .es-left, .es-right { width:100%!important } .es-content table, .es-header table, .es-footer table, .es-content, .es-footer, .es-header { width:100%!important; max-width:600px!important } .adapt-img { width:100%!important; height:auto!important } .es-mobile-hidden, .es-hidden { display:none!important } .es-desk-hidden { width:auto!important; overflow:visible!important; float:none!important; max-height:inherit!important; line-height:inherit!important } tr.es-desk-hidden { display:table-row!important }\r\n table.es-desk-hidden { display:table!important } td.es-desk-menu-hidden { display:table-cell!important } .es-menu td { width:1%!important } table.es-table-not-adapt, .esd-block-html table { width:auto!important } .h-auto { height:auto!important } }@media screen and (max-width:384px) {.mail-message-content { width:414px!important } }</style>\r\n </head> <body class=\"body\" style=\"width:100%;height:100%;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;padding:0;Margin:0\"><div dir=\"ltr\" class=\"es-wrapper-color\" lang=\"en\" style=\"background-color:#FAFAFA\"><!--[if gte mso 9]><v:background xmlns:v=\"urn:schemas-microsoft-com:vml\" fill=\"t\"> <v:fill type=\"tile\" color=\"#fafafa\"></v:fill> </v:background><![endif]--><table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" class=\"es-wrapper\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;padding:0;Margin:0;width:100%;height:100%;background-repeat:repeat;background-position:center top;background-color:#FAFAFA\"><tr><td valign=\"top\" style=\"padding:0;Margin:0\"><table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" class=\"es-content\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important\"><tr>\r\n<td align=\"center\" class=\"es-info-area\" style=\"padding:0;Margin:0\"><table align=\"center\" cellpadding=\"0\" cellspacing=\"0\" bgcolor=\"#00000000\" class=\"es-content-body\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;width:600px\" role=\"none\"><tr><td align=\"left\" style=\"padding:20px;Margin:0\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"center\" class=\"es-infoblock\" style=\"padding:0;Margin:0\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:18px;letter-spacing:0;color:#CCCCCC;font-size:12px\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:underline;color:#CCCCCC;font-size:12px\">View online version</a></p> </td></tr></table></td></tr></table></td></tr></table></td></tr></table> <table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" class=\"es-header\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important;background-color:transparent;background-repeat:repeat;background-position:center top\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0\"><table bgcolor=\"#ffffff\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\" class=\"es-header-body\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;width:600px\"><tr><td align=\"left\" style=\"Margin:0;padding-top:10px;padding-right:20px;padding-bottom:10px;padding-left:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td valign=\"top\" align=\"center\" class=\"es-m-p0r\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;padding-bottom:20px;font-size:0px\"><img src=\"https://eukuino.stripocdn.email/content/guids/CABINET_887f48b6a2f22ad4fb67bc2a58c0956b/images/93351617889024778.png\" alt=\"Logo\" width=\"200\" title=\"Logo\" style=\"display:block;font-size:12px;border:0;outline:none;text-decoration:none\" height=\"48\"></td> </tr><tr><td style=\"padding:0;Margin:0\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" class=\"es-menu\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr class=\"links\">\r\n<td align=\"center\" valign=\"top\" width=\"25%\" style=\"Margin:0;border:0;padding-top:15px;padding-bottom:15px;padding-right:5px;padding-left:5px\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#666666;font-size:14px\">Shop</a></div></td> <td align=\"center\" valign=\"top\" width=\"25%\" style=\"Margin:0;border:0;padding-top:15px;padding-bottom:15px;padding-right:5px;padding-left:5px\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#666666;font-size:14px\">New</a></div></td>\r\n<td align=\"center\" valign=\"top\" width=\"25%\" style=\"Margin:0;border:0;padding-top:15px;padding-bottom:15px;padding-right:5px;padding-left:5px\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#666666;font-size:14px\">Sale</a></div></td> <td align=\"center\" valign=\"top\" width=\"25%\" style=\"Margin:0;border:0;padding-top:15px;padding-bottom:15px;padding-right:5px;padding-left:5px\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#666666;font-size:14px\">About</a></div></td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table>\r\n <table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" class=\"es-content\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important\"><tr><td align=\"center\" style=\"padding:0;Margin:0\"><table bgcolor=\"#ffffff\" align=\"center\" cellpadding=\"0\" cellspacing=\"0\" class=\"es-content-body\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:#FFFFFF;width:600px\"><tr><td align=\"left\" style=\"Margin:0;padding-right:20px;padding-bottom:10px;padding-left:20px;padding-top:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" style=\"padding:0;Margin:0;padding-top:10px;padding-bottom:10px;font-size:0px\"><img src=\"https://eukuino.stripocdn.email/content/guids/CABINET_37653718160e384c1b6ac6f5fb44958e/images/86381617879516269.png\" alt=\"\" width=\"300\" class=\"adapt-img\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\" height=\"302\"></td> </tr><tr><td align=\"center\" style=\"padding:0;Margin:0;padding-bottom:10px;padding-top:20px\"><h3 class=\"es-m-txt-c\" style=\"Margin:0;font-family:arial, \'helvetica neue\', helvetica, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:20px;font-style:normal;font-weight:bold;line-height:20px;color:#333333\">OUR BEST SELLERS!</h3></td>\r\n</tr><tr><td align=\"center\" style=\"padding:0;Margin:0;padding-bottom:10px\"><h1 class=\"es-m-txt-c\" style=\"Margin:0;font-family:arial, \'helvetica neue\', helvetica, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:70px;font-style:normal;font-weight:bold;line-height:70px;color:#333333\">ENJOY 25% OFF</h1></td></tr> <tr><td align=\"center\" style=\"padding:0;Margin:0;padding-top:5px;padding-bottom:5px\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:21px;letter-spacing:0;color:#333333;font-size:14px\">Our Brand is celebrating its fifth&nbsp;anniversary. For those who want to celebrate with us, we give 25% OFF sitewide and free shipping.</p>\r\n<p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:21px;letter-spacing:0;color:#333333;font-size:14px\">The sale lasts from May 30th through June 30th.&nbsp;</p></td></tr></table></td></tr></table></td></tr> <tr><td align=\"left\" style=\"Margin:0;padding-top:10px;padding-right:20px;padding-bottom:10px;padding-left:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:separate;border-spacing:0px;border-left:2px dashed #cccccc;border-right:2px dashed #cccccc;border-top:2px dashed #cccccc;border-bottom:2px dashed #cccccc;border-radius:5px\" role=\"presentation\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;padding-right:20px;padding-left:20px;padding-top:20px\"><h2 class=\"es-m-txt-c\" style=\"Margin:0;font-family:arial, \'helvetica neue\', helvetica, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:26px;font-style:normal;font-weight:bold;line-height:31.2px;color:#333333\">Your promo code</h2> </td></tr><tr><td align=\"center\" style=\"Margin:0;padding-top:10px;padding-right:20px;padding-left:20px;padding-bottom:20px\"><h1 class=\"es-m-txt-c\" style=\"Margin:0;font-family:arial, \'helvetica neue\', helvetica, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:46px;font-style:normal;font-weight:bold;line-height:55.2px;color:#333333\"><strong><a target=\"_blank\" style=\"mso-line-height-rule:exactly;text-decoration:none;color:#5C68E2;font-size:46px\" href=\"\">FGH-123-VBN</a></strong></h1></td></tr></table></td></tr></table></td></tr> <tr>\r\n<td align=\"left\" style=\"padding:0;Margin:0;padding-right:20px;padding-bottom:10px;padding-left:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:separate;border-spacing:0px;border-radius:5px\" role=\"presentation\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;padding-top:10px;padding-bottom:10px\"><span class=\"es-button-border\" style=\"border-style:solid;border-color:#2CB543;background:#5C68E2;border-width:0px;display:inline-block;border-radius:6px;width:auto\"><a href=\"\" target=\"_blank\" class=\"es-button\" style=\"mso-style-priority:100 !important;text-decoration:none !important;mso-line-height-rule:exactly;color:#FFFFFF;font-size:20px;padding:10px 30px 10px 30px;display:inline-block;background:#5C68E2;border-radius:6px;font-family:arial, \'helvetica neue\', helvetica, sans-serif;font-weight:normal;font-style:normal;line-height:24px;width:auto;text-align:center;letter-spacing:0;mso-padding-alt:0;mso-border-alt:10px solid #5C68E2;border-left-width:30px;border-right-width:30px\">GO SHOPPING</a> </span></td></tr></table></td></tr></table></td></tr> <tr>\r\n<td align=\"left\" style=\"padding:0;Margin:0;padding-right:20px;padding-left:20px;padding-top:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" style=\"Margin:0;padding-right:20px;padding-left:20px;padding-bottom:5px;padding-top:15px\"><h2 class=\"es-m-txt-c\" style=\"Margin:0;font-family:arial, \'helvetica neue\', helvetica, sans-serif;mso-line-height-rule:exactly;letter-spacing:0;font-size:26px;font-style:normal;font-weight:bold;line-height:31.2px;color:#333333\">How does it work?</h2></td></tr></table></td></tr></table> </td></tr> <tr>\r\n<td align=\"left\" style=\"padding:20px;Margin:0\"><!--[if mso]><table style=\"width:560px\" cellpadding=\"0\" cellspacing=\"0\"><tr><td style=\"width:194px\" valign=\"top\"><![endif]--><table cellpadding=\"0\" cellspacing=\"0\" align=\"left\" class=\"es-left\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;float:left\"><tr><td align=\"center\" class=\"es-m-p0r es-m-p20b\" style=\"padding:0;Margin:0;width:174px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" style=\"padding:0;Margin:0;font-size:0px\"><img src=\"https://eukuino.stripocdn.email/content/guids/CABINET_37653718160e384c1b6ac6f5fb44958e/images/2851617878322771.png\" alt=\"\" width=\"45\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\" height=\"49\"></td> </tr><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;padding-top:10px;padding-bottom:10px\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:21px;letter-spacing:0;color:#333333;font-size:14px\">Choose any piece of clothing that you like.</p></td></tr></table></td><td class=\"es-hidden\" style=\"padding:0;Margin:0;width:20px\"></td></tr></table> <!--[if mso]></td><td style=\"width:173px\" valign=\"top\"><![endif]--><table cellpadding=\"0\" cellspacing=\"0\" align=\"left\" class=\"es-left\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;float:left\"><tr><td align=\"center\" class=\"es-m-p20b\" style=\"padding:0;Margin:0;width:173px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;font-size:0px\"><img src=\"https://eukuino.stripocdn.email/content/guids/CABINET_37653718160e384c1b6ac6f5fb44958e/images/2851617878322771.png\" alt=\"\" width=\"45\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\" height=\"49\"></td></tr> <tr><td align=\"center\" style=\"padding:0;Margin:0;padding-top:10px;padding-bottom:10px\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:21px;letter-spacing:0;color:#333333;font-size:14px\">At the checkout, redeem your promo code.</p></td></tr></table></td></tr></table> <!--[if mso]></td><td style=\"width:20px\"></td><td style=\"width:173px\" valign=\"top\"><![endif]--><table cellpadding=\"0\" cellspacing=\"0\" align=\"right\" class=\"es-right\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;float:right\"><tr>\r\n<td align=\"center\" style=\"padding:0;Margin:0;width:173px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" style=\"padding:0;Margin:0;font-size:0px\"><img src=\"https://eukuino.stripocdn.email/content/guids/CABINET_37653718160e384c1b6ac6f5fb44958e/images/2851617878322771.png\" alt=\"\" width=\"45\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\" height=\"49\"></td></tr> <tr><td align=\"center\" style=\"padding:0;Margin:0;padding-top:10px;padding-bottom:10px\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:21px;letter-spacing:0;color:#333333;font-size:14px\">Wait for your order to come. Enjoy!</p></td></tr></table></td></tr></table><!--[if mso]></td></tr></table><![endif]--></td></tr></table></td></tr></table>\r\n <table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" class=\"es-footer\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important;background-color:transparent;background-repeat:repeat;background-position:center top\"><tr><td align=\"center\" style=\"padding:0;Margin:0\"><table align=\"center\" cellpadding=\"0\" cellspacing=\"0\" class=\"es-footer-body\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;width:640px\" role=\"none\"><tr><td align=\"left\" style=\"Margin:0;padding-right:20px;padding-left:20px;padding-bottom:20px;padding-top:20px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"left\" style=\"padding:0;Margin:0;width:600px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" style=\"padding:0;Margin:0;padding-top:15px;padding-bottom:15px;font-size:0\"><table cellpadding=\"0\" cellspacing=\"0\" class=\"es-table-not-adapt es-social\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;padding-right:40px\"><img title=\"Facebook\" src=\"https://eukuino.stripocdn.email/content/assets/img/social-icons/logo-black/facebook-logo-black.png\" alt=\"Fb\" width=\"32\" height=\"32\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\"></td>\r\n <td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;padding-right:40px\"><img title=\"X\" src=\"https://eukuino.stripocdn.email/content/assets/img/social-icons/logo-black/x-logo-black.png\" alt=\"X\" width=\"32\" height=\"32\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\"></td><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;padding-right:40px\"><img title=\"Instagram\" src=\"https://eukuino.stripocdn.email/content/assets/img/social-icons/logo-black/instagram-logo-black.png\" alt=\"Inst\" width=\"32\" height=\"32\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\"></td><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0\"><img title=\"Youtube\" src=\"https://eukuino.stripocdn.email/content/assets/img/social-icons/logo-black/youtube-logo-black.png\" alt=\"Yt\" width=\"32\" height=\"32\" style=\"display:block;font-size:14px;border:0;outline:none;text-decoration:none\"></td></tr> </table>\r\n</td></tr><tr><td align=\"center\" style=\"padding:0;Margin:0;padding-bottom:35px\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:18px;letter-spacing:0;color:#333333;font-size:12px\">Style Casual&nbsp;© 2021 Style Casual, Inc. All Rights Reserved.</p><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:18px;letter-spacing:0;color:#333333;font-size:12px\">4562 Hazy Panda Limits, Chair Crossing, Kentucky, US, 607898</p></td></tr> <tr><td style=\"padding:0;Margin:0\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" class=\"es-menu\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr class=\"links\">\r\n<td align=\"center\" valign=\"top\" width=\"33.33%\" style=\"Margin:0;border:0;padding-top:5px;padding-bottom:5px;padding-right:5px;padding-left:5px\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#999999;font-size:12px\">Visit Us </a></div></td> <td align=\"center\" valign=\"top\" width=\"33.33%\" style=\"Margin:0;border:0;padding-top:5px;padding-bottom:5px;padding-right:5px;padding-left:5px;border-left:1px solid #cccccc\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#999999;font-size:12px\">Privacy Policy</a></div></td>\r\n<td align=\"center\" valign=\"top\" width=\"33.33%\" style=\"Margin:0;border:0;padding-top:5px;padding-bottom:5px;padding-right:5px;padding-left:5px;border-left:1px solid #cccccc\"><div style=\"vertical-align:middle;display:block\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:none;font-family:arial, \'helvetica neue\', helvetica, sans-serif;display:block;color:#999999;font-size:12px\">Terms of Use</a></div></td></tr></table></td></tr></table></td></tr> </table></td></tr></table></td></tr></table> <table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" class=\"es-content\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;width:100%;table-layout:fixed !important\"><tr>\r\n<td align=\"center\" class=\"es-info-area\" style=\"padding:0;Margin:0\"><table align=\"center\" cellpadding=\"0\" cellspacing=\"0\" bgcolor=\"#00000000\" class=\"es-content-body\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px;background-color:transparent;width:600px\" role=\"none\"><tr><td align=\"left\" style=\"padding:20px;Margin:0\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"none\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr><td align=\"center\" valign=\"top\" style=\"padding:0;Margin:0;width:560px\"><table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"mso-table-lspace:0pt;mso-table-rspace:0pt;border-collapse:collapse;border-spacing:0px\"><tr>\r\n<td align=\"center\" class=\"es-infoblock\" style=\"padding:0;Margin:0\"><p style=\"Margin:0;mso-line-height-rule:exactly;font-family:arial, \'helvetica neue\', helvetica, sans-serif;line-height:18px;letter-spacing:0;color:#CCCCCC;font-size:12px\"><a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:underline;color:#CCCCCC;font-size:12px\"></a>No longer want to receive these emails?&nbsp;<a href=\"\" target=\"_blank\" style=\"mso-line-height-rule:exactly;text-decoration:underline;color:#CCCCCC;font-size:12px\">Unsubscribe</a>.<a target=\"_blank\" href=\"\" style=\"mso-line-height-rule:exactly;text-decoration:underline;color:#CCCCCC;font-size:12px\"></a></p> </td></tr></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></div></body></html>', 'richtext', '2025-03-27 08:32:58', '2025-03-27 08:32:58');
INSERT INTO `html_templates` (`id`, `name`, `category`, `subject`, `content`, `template_type`, `created_at`, `updated_at`) VALUES
(8, 'New Letter', 'Custom', 'Products Newsletter', '<!DOCTYPE html> <html xmlns=\"http://www.w3.org/1999/xhtml\"> <head> <meta name=\"ROBOTS\" content=\"NOINDEX, NOFOLLOW\" /> <meta name=\"referrer\" content=\"no-referrer\" /> <meta name=\"x-apple-disable-message-reformatting\" /> <link href=\"https://fonts.googleapis.com/css?family=Arvo\" rel=\"stylesheet\" type=\"text/css\" /> <meta name=\"viewport\" content=\"width=device-width\" charset=\"utf-8\" />  <title>Converse</title> </head> <body> <div class=\"preheader\" style=\"font-size: 1px; display: none !important;\">Go on, you know you deserve it.</div> <div class=\"preheader\" style=\"font-size: 1px; display: none !important;\"></div> <center> <table cellpadding=\"0\" cellspacing=\"0\" align=\"center\" width=\"640\" class=\"wrapper\"> <tbody> <tr> <td>  <table width=\"100%\" cellpadding=\"0\" cellspacing=\"0\"> <tbody> <tr> <td> <table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"min-width: 100%; \" class=\"stylingblock-content-wrapper\"> <tbody> <tr> <td class=\"stylingblock-content-wrapper camarker-inner\"></td> </tr> </tbody> </table> </td> </tr> </tbody> </table>   <table width=\"600\" cellpadding=\"0\" cellspacing=\"0\" class=\"preheader\"> <tbody> <tr> <td align=\"left\" valign=\"top\"><a href=\"#\" target=\"_blank\" style=\"text-decoration:none; color:#333; font-size:8pt; font-family: Helvetica, Arial, sans-serif;\"><span style=\"color:#333333;\">And your little one does, too.</span></a></td> </tr> </tbody> </table>   <div style=\"display: none; max-height: 0px; overflow: hidden;\">  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌ ‌  </div>  <table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" bgcolor=\"#ffffff\" style=\"\" align=\"center\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"><img src=\"https://cdn.sender.net/template_library/template/1190/12db6b319b7a73a6723a2406ebfa1b750d35c79b.jpeg\" border=\"0\" alt=\"Converse Logo\" data-mc-image-bytes=\"4582\" data-mc-width=\"245\" data-mc-height=\"89\" data-mc-natural-width=\"245\" data-mc-natural-height=\"89\" /></a></td> </tr> </tbody> </table>   <table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" bgcolor=\"#FFFFFF\" class=\"hide\" style=\"border-top: 1px #e4e4e4 solid;\"> <tbody> <tr> <td align=\"center\" bgcolor=\"#FFFFFF\"><a href=\"#\" target=\"new\"><img src=\"https://cdn.sender.net/template_library/template/1190/70f3e4dff7fdbef5ff5704b5ecba9d63dd3649df.jpeg\" border=\"0\" style=\"display:block\" alt=\"New Arrivals\" data-mc-image-bytes=\"3806\" data-mc-width=\"235\" data-mc-height=\"65\" data-mc-natural-width=\"235\" data-mc-natural-height=\"65\" /></a></td> <td align=\"center\" bgcolor=\"#FFFFFF\"><a href=\"#\" target=\"new\"><img src=\"https://cdn.sender.net/template_library/template/1190/a2fb26708f5ea1036df7c158a7f46cfda2614094.jpeg\" border=\"0\" style=\"display:block\" alt=\"Men\" data-mc-image-bytes=\"2282\" data-mc-width=\"76\" data-mc-height=\"65\" data-mc-natural-width=\"76\" data-mc-natural-height=\"65\" /></a></td> <td align=\"center\" bgcolor=\"#FFFFFF\"><a href=\"#\" target=\"new\"><img src=\"https://cdn.sender.net/template_library/template/1190/649d63ac11c2a4e29165d9657561c70010bb5f08.jpeg\" border=\"0\" style=\"display:block\" alt=\"Women\" data-mc-image-bytes=\"2836\" data-mc-width=\"95\" data-mc-height=\"67\" data-mc-natural-width=\"95\" data-mc-natural-height=\"67\" /></a></td> <td align=\"center\" bgcolor=\"#FFFFFF\"><a href=\"#\" target=\"new\"><img src=\"https://cdn.sender.net/template_library/template/1190/edb6976ad6bf90fe1292d835d3d6c08f7a48f59a.jpeg\" border=\"0\" style=\"display:block\" alt=\"Bestsellers\" data-mc-image-bytes=\"3741\" data-mc-width=\"234\" data-mc-height=\"65\" data-mc-natural-width=\"234\" data-mc-natural-height=\"65\" /></a></td> </tr> </tbody> </table>   <table cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" role=\"presentation\" style=\"min-width: 100%; \" class=\"stylingblock-content-wrapper\"> <tbody> <tr> <td class=\"stylingblock-content-wrapper camarker-inner\"> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#000000\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/19c67e80962268361e1d359e6987499fcbfaa6da.jpeg\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" data-mc-image-bytes=\"17026\" data-mc-width=\"640\" data-mc-height=\"68\" data-mc-natural-width=\"640\" data-mc-natural-height=\"68\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/c9e9d2746e1560edd26a16585d51a26e39779d18.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"329090\" data-mc-width=\"640\" data-mc-height=\"934\" data-mc-natural-width=\"640\" data-mc-natural-height=\"934\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/04e25db4ee3557da48e5454cc9ed1c36e2bd1d38.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"20735\" data-mc-width=\"640\" data-mc-height=\"226\" data-mc-natural-width=\"640\" data-mc-natural-height=\"226\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/7975608546415a04c9080a7dc2454ab09466a9dd.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"246826\" data-mc-width=\"640\" data-mc-height=\"795\" data-mc-natural-width=\"640\" data-mc-natural-height=\"795\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/20fd5a16c613538262fce5f7bbe375039c66b3b3.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"24698\" data-mc-width=\"640\" data-mc-height=\"263\" data-mc-natural-width=\"640\" data-mc-natural-height=\"263\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/748b43c401d4fd7023b665eda93ef80871052a2e.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"174809\" data-mc-width=\"640\" data-mc-height=\"901\" data-mc-natural-width=\"640\" data-mc-natural-height=\"901\" /></a></td> </tr> </tbody> </table> <table style=\"border-left: 1px silver solid; border-right: 1px silver solid;\" width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" bgcolor=\"#ffffff\"> <tbody> <tr> <td align=\"center\"><a href=\"#\" target=\"_blank\"> <img src=\"https://cdn.sender.net/template_library/template/1190/b9a7ed109e4c475880804adaf3a69489570aa37c.gif\" class=\"image\" alt=\"\" style=\"font-family: Arial; color:#999; font-size: 36px; display: block;\" border=\"0\" align=\"middle\" width=\"640\" data-mc-image-bytes=\"22720\" data-mc-width=\"640\" data-mc-height=\"327\" data-mc-natural-width=\"640\" data-mc-natural-height=\"327\" /></a></td> </tr> </tbody> </table> </td> </tr> </tbody> </table>    <div style=\"display:none\" class=\"mobile_display\"> <table bgcolor=\"#000000\" cellspacing=\"0\" align=\"center\" width=\"100%\" style=\"border-left: 1px silver solid; border-right: 1px silver solid; border-bottom:\" solid\";=\"\" border-bottom-color:\"#e5e5e5\"=\"\" padding-top:\"20px\";=\"\" padding-bottom:\"20px\";\"=\"\"> <tbody> <tr> <td style=\"line-height:0px;font-size:0px;height:0px;margin:0;padding:0;\" class=\"mobile\" align=\"center\"><a href=\"#\" target=\"new\" style=\"text-decoration:none; color:black\"><img src=\"https://cdn.sender.net/template_library/template/1190/76acc5a8b0cf6bd76662c9439f696c03bb7634a2.jpeg\" data-mc-image-bytes=\"2993\" data-mc-width=\"84\" data-mc-height=\"19\" data-mc-natural-width=\"84\" data-mc-natural-height=\"19\" /></a> </td> </tr> </tbody> </table> <table cellspacing=\"0\" align=\"center\" width=\"100%\" style=\"border-left: 1px silver solid; border-right: 1px silver solid; border-right: 1px silver solid; border-bottom:\" solid\";=\"\" border-bottom-color:\"#e5e5e5\"=\"\" padding-top:\"20px\";=\"\" padding-bottom:\"20px\";\"=\"\"> <tbody> <tr> <td style=\"line-height:0px;font-size:0px;height:0px;margin:0;padding:0;\" class=\"mobile\" align=\"center\"><a href=\"#\" target=\"new\" style=\"text-decoration:none; color:black\"><img src=\"https://cdn.sender.net/template_library/template/1190/1ace29549d144ab7f1361133847005802db813a7.jpeg\" data-mc-image-bytes=\"2480\" data-mc-width=\"55\" data-mc-height=\"19\" data-mc-natural-width=\"55\" data-mc-natural-height=\"19\" /></a> </td> </tr> </tbody> </table> <table cellspacing=\"0\" align=\"center\" width=\"100%\" style=\"border-left: 1px silver solid; border-right: 1px silver solid; border-right: 1px silver solid; border-bottom:\" solid\";=\"\" border-bottom-color:\"#e5e5e5\"=\"\" padding-top:\"20px\";=\"\" padding-bottom:\"20px\";\"=\"\"> <tbody> <tr> <td style=\"line-height:0px;font-size:0px;height:0px;margin:0;padding:0;\" class=\"mobile\" align=\"center\"><a href=\"#\" target=\"new\" style=\"text-decoration:none; color:black\"><img src=\"https://cdn.sender.net/template_library/template/1190/943d6154cfe37d7a128668314456d5354eb1526c.jpeg\" data-mc-image-bytes=\"2791\" data-mc-width=\"84\" data-mc-height=\"18\" data-mc-natural-width=\"84\" data-mc-natural-height=\"18\" /> </a> </td> </tr> </tbody> </table> <table cellspacing=\"0\" align=\"center\" width=\"100%\" style=\"border-left: 1px silver solid; border-right: 1px silver solid; border-right: 1px silver solid; border-bottom:\" solid\";=\"\" border-bottom-color:\"#e5e5e5\"=\"\" padding-top:\"20px\";=\"\" padding-bottom:\"20px\";\"=\"\"> <tbody> <tr> <td style=\"line-height:0px;font-size:0px;height:0px;margin:0;padding:0;\" class=\"mobile\" align=\"center\"><a href=\"#\" target=\"new\" style=\"text-decoration:none; color:black\"><img src=\"https://cdn.sender.net/template_library/template/1190/0e3472d92a42928b74316e13cf1afc7a28cdff8e.jpeg\" data-mc-image-bytes=\"3627\" data-mc-width=\"130\" data-mc-height=\"18\" data-mc-natural-width=\"130\" data-mc-natural-height=\"18\" /></a> </td> </tr> </tbody> </table> <table cellspacing=\"0\" align=\"center\" width=\"100%\" style=\"border-left: 1px silver solid; border-right: 1px silver solid; border-right: 1px silver solid; border-bottom:\" solid\";=\"\" border-bottom-color:\"#e5e5e5\"=\"\" padding-top:\"20px\";=\"\" padding-bottom:\"20px\";\"=\"\"> <tbody> <tr> <td style=\"line-height:0px;font-size:0px;height:0px;margin:0;padding:0;\" class=\"mobile\" align=\"center\"><a href=\"#\" target=\"new\" style=\"text-decoration:none; color:black\"><img src=\"https://cdn.sender.net/template_library/template/1190/bbb9a7f4e54e64a612ab80af981ac5545fe8b6f1.jpeg\" data-mc-image-bytes=\"3907\" data-mc-width=\"124\" data-mc-height=\"19\" data-mc-natural-width=\"124\" data-mc-natural-height=\"19\" /></a> </td> </tr> </tbody> </table> </div>     <table cellpadding=\"0\" cellspacing=\"0\" width=\"640\" bgcolor=\"#000000\" align=\"center\" class=\"wrapper\"> <tbody> <tr> <td align=\"center\" bgcolor=\"#000000\" class=\"hide\"><img src=\"https://cdn.sender.net/template_library/template/1190/cc11c2eddbf1336db9ab7254a25abe161059ebfa.jpeg\" border=\"0\" style=\"display:block\" data-mc-image-bytes=\"1443\" data-mc-width=\"263\" data-mc-height=\"68\" data-mc-natural-width=\"263\" data-mc-natural-height=\"68\" /></td> <td align=\"center\" bgcolor=\"#000000\"><img src=\"https://cdn.sender.net/template_library/template/1190/692c61c12efa6f304d2fba1ee9e60abc71839f0a.jpeg\" border=\"0\" style=\"display:block\" alt=\"Follow Us\" data-mc-image-bytes=\"3516\" data-mc-width=\"114\" data-mc-height=\"68\" data-mc-natural-width=\"114\" data-mc-natural-height=\"68\" /></td> <td align=\"center\" bgcolor=\"#000000\" class=\"hide\"><a href=\"#\" target=\"new\"><img src=\"https://cdn.sender.net/template_library/template/1190/383bc3db6b20222e76a774fed890a23a6c8a3eb7.jpeg\" border=\"0\" style=\"display:block\" data-mc-image-bytes=\"1443\" data-mc-width=\"263\" data-mc-height=\"68\" data-mc-natural-width=\"263\" data-mc-natural-height=\"68\" /></a></td> </tr> </tbody> </table>   <table cellpadding=\"0\" cellspacing=\"0\" width=\"640\" bgcolor=\"#000000\" align=\"center\" class=\"wrapper\"> <tbody> <tr> <td align=\"center\" bgcolor=\"#000000\" class=\"hide\"><img src=\"https://cdn.sender.net/template_library/template/1190/1deb3369ccdbcad6a38a54854769202eaee8c1d5.jpeg\" border=\"0\" style=\"display:block\" data-mc-image-bytes=\"1360\" data-mc-width=\"135\" data-mc-height=\"84\" data-mc-natural-width=\"135\" data-mc-natural-height=\"84\" /></td> <td align=\"center\" bgcolor=\"#000000\"><a href=\"#\" target=\"_blank\"><img src=\"https://cdn.sender.net/template_library/template/1190/40ae3955739b48608aa2ec13799e950c8be59c1c.jpeg\" border=\"0\" style=\"display:block\" alt=\"Instagram\" data-mc-image-bytes=\"3011\" data-mc-width=\"99\" data-mc-height=\"84\" data-mc-natural-width=\"99\" data-mc-natural-height=\"84\" /></a></td> <td align=\"center\" bgcolor=\"#000000\"><a href=\"#\" target=\"_blank\"><img src=\"https://cdn.sender.net/template_library/template/1190/f3e42bfcd0a57821a35e8b8124bd941dc53bbabf.jpeg\" border=\"0\" style=\"display:block\" alt=\"YouTube\" data-mc-image-bytes=\"5411\" data-mc-width=\"84\" data-mc-height=\"84\" data-mc-natural-width=\"84\" data-mc-natural-height=\"84\" /></a></td> <td align=\"center\" bgcolor=\"#000000\"><a href=\"#\" target=\"_blank\"><img src=\"https://cdn.sender.net/template_library/template/1190/fac93c0933f4e066c499035540935629b51e8f77.jpeg\" border=\"0\" style=\"display:block\" alt=\"Facebook\" data-mc-image-bytes=\"2830\" data-mc-width=\"87\" data-mc-height=\"84\" data-mc-natural-width=\"87\" data-mc-natural-height=\"84\" /></a></td> <td align=\"center\" bgcolor=\"#000000\"><a href=\"#\" target=\"_blank\"><img src=\"https://cdn.sender.net/template_library/template/1190/af39f1a259e6ff30b6cf8678ba1957352ce431a7.jpeg\" border=\"0\" style=\"display:block\" alt=\"Twitter\" data-mc-image-bytes=\"2963\" data-mc-width=\"102\" data-mc-height=\"84\" data-mc-natural-width=\"102\" data-mc-natural-height=\"84\" /></a></td> <td align=\"center\" bgcolor=\"#000000\" class=\"hide\"><img src=\"https://cdn.sender.net/template_library/template/1190/f514a9b3b200ec0a0ce97382ca909c5bba8e1a6f.jpeg\" border=\"0\" style=\"display:block\" data-mc-image-bytes=\"1360\" data-mc-width=\"131\" data-mc-height=\"84\" data-mc-natural-width=\"131\" data-mc-natural-height=\"84\" /></td> </tr> </tbody> </table>   <table cellpadding=\"0\" cellspacing=\"0\" width=\"640\" bgcolor=\"#ededed\" class=\"wrapper\" align=\"center\"> <tbody> <tr> <td align=\"center\">  <table cellpadding=\"0\" cellspacing=\"0\" width=\"75%\" bgcolor=\"#ededed\" class=\"legal_text\"> <tbody> <tr> <td align=\"center\" bgcolor=\"#ededed\" style=\"font-family:Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #7f7f7f; font-weight: 200; padding-top: 30px\"><a href=\"#\" style=\"color: #7f7f7f\" target=\"_\">Privacy Policy</a>     <a href=\"#\" style=\"color: #7f7f7f\" target=\"_\">Get Help</a>     <a href=\"#\" style=\"color: #7f7f7f\" target=\"_\">Unsubscribe</a> </td> </tr> <tr> <td align=\"center\" bgcolor=\"#ededed\" style=\"font-family:Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #7f7f7f; font-weight: 200; padding-top: 30px; padding-bottom: 20px\"> This e-mail was sent from a notification-only address that cannot accept incoming e-mail. Please do not reply to this message. You received this message because you previously expressed interest in news and offers from Converse by supplying this email address. Converse respects your privacy and does not share your personal information with third parties.<br /><br /> To ensure that future Converse news is received, add <EMAIL> to your address book. <br /><a href=\"#\" style=\"color: #7f7f7f\" target=\"_\">Click here to unsubscribe.</a></td> </tr> <tr> <td> <hr /> </td> </tr> <tr> <td align=\"center\" bgcolor=\"#ededed\" style=\"font-family:Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #7f7f7f; font-weight: 200; padding-top: 20px; padding-bottom: 20px\">© 2022 CONVERSE, Inc. All Rights Reserved.<br /> Converse Retail B.V. Colosseum 1, 1213 NL Hilversum, The Netherlands. </td> </tr> </tbody> </table>  </td> </tr> </tbody> </table>    </td> </tr> </tbody> </table>  </center> </body> </html>', 'richtext', '2025-03-27 17:30:51', '2025-03-27 17:30:51'),
(9, 'Flux Ai', 'Custom', 'Image Generation Invitation', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Exclusive Invitation</title>\r\n</head>\r\n<body style=\"margin: 0; padding: 0; background-color: #f5f7fa; font-family: Arial, Helvetica, sans-serif;\">\r\n    <table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 20px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\">\r\n        <tr>\r\n            <td style=\"padding: 40px; text-align: center; position: relative;\">\r\n                <!-- Header Image -->\r\n                <img src=\"https://picsum.photos/600/250\" alt=\"Invitation Image\" style=\"width: 100%; max-width: 600px; height: auto; border-radius: 12px; border: 2px solid #4a90e2; display: block; margin: 0 auto 25px;\">\r\n                \r\n                <!-- Title -->\r\n                <h1 style=\"font-size: 32px; color: #333333; margin: 0 0 15px; font-weight: bold; text-transform: uppercase; letter-spacing: 1.5px;\">You\'re Invited!</h1>\r\n                \r\n                <!-- Subtitle -->\r\n                <p style=\"font-size: 20px; color: #555555; margin: 0 0 20px; font-style: italic;\">Join Our Exclusive Community</p>\r\n                \r\n                <!-- Main Text -->\r\n                <p style=\"font-size: 16px; color: #666666; line-height: 1.8; margin: 0 0 25px;\">We are excited to invite you to join the DemoHomeX community! By using the link below, you\'ll gain access to exclusive features, a vibrant community, and personalized content designed just for you. Don\'t miss this opportunity to connect and explore with us.</p>\r\n                \r\n                <!-- Call to Action Button -->\r\n                <a href=\"https://demohomex.com/test/redirect.php?token=%2F%2BGq2J8sWkgkh%2Bf3Vu5Q4NH4acWhqvB9w29gbL1KEMnoX3FgPMv7zCcoLQZtxfn3OhiqPHucHPvJWuG9tKAlBOSo2MIPWHhNoNQOpT292Jo%3D\" style=\"display: inline-block; background: linear-gradient(90deg, #4a90e2, #50c9c3); color: #ffffff; padding: 14px 35px; border-radius: 30px; text-decoration: none; font-weight: bold; font-size: 18px; margin: 0 0 25px;\">Accept Your Invitation</a>\r\n                \r\n                <!-- Why Join Section -->\r\n                <table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f8f9fa; border-radius: 10px; padding: 20px; margin: 0 0 25px; text-align: left;\">\r\n                    <tr>\r\n                        <td>\r\n                            <h3 style=\"font-size: 18px; color: #333333; margin: 0 0 12px;\">Why Join DemoHomeX?</h3>\r\n                            <ul style=\"list-style: none; padding: 0; margin: 0; font-size: 14px; color: #444444;\">\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Unlock premium content and exclusive features.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Connect with like-minded individuals in a secure environment.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Receive personalized recommendations and updates.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Enjoy a seamless and user-friendly platform experience.</li>\r\n                            </ul>\r\n                        </td>\r\n                    </tr>\r\n                </table>\r\n                \r\n                <!-- Invitation Guidelines Section -->\r\n                <table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"background-color: #f8f9fa; border-radius: 10px; padding: 20px; margin: 0 0 25px; text-align: left;\">\r\n                    <tr>\r\n                        <td>\r\n                            <h3 style=\"font-size: 18px; color: #333333; margin: 0 0 12px;\">Invitation Guidelines</h3>\r\n                            <ul style=\"list-style: none; padding: 0; margin: 0; font-size: 14px; color: #444444;\">\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> This unique invitation link is for your personal use only.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Please do not share the link publicly to maintain exclusivity.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> Complete your registration within 7 days to secure your spot.</li>\r\n                                <li style=\"margin: 0 0 10px; position: relative; padding-left: 25px;\"><span style=\"position: absolute; left: 0; color: #4a90e2; font-size: 18px;\">✔</span> If you encounter any issues, our support team is here to help.</li>\r\n                            </ul>\r\n                        </td>\r\n                    </tr>\r\n                </table>\r\n                \r\n                <!-- Footer -->\r\n                <table role=\"presentation\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"font-size: 13px; color: #888888; margin-top: 25px; border-top: 1px solid #e0e0e0; padding-top: 15px;\">\r\n                    <tr>\r\n                        <td style=\"text-align: center;\">\r\n                            <p style=\"margin: 0 0 10px;\">Need assistance? <a href=\"mailto:<EMAIL>\" style=\"color: #4a90e2; text-decoration: none;\">Contact Our Support Team</a></p>\r\n                            <p style=\"margin: 0;\">© 2025 DemoHomeX. All rights reserved.</p>\r\n                        </td>\r\n                    </tr>\r\n                </table>\r\n            </td>\r\n        </tr>\r\n    </table>\r\n</body>\r\n</html>', 'richtext', '2025-04-20 18:50:59', '2025-04-20 19:33:28'),
(25, 'trac2', 'Custom', 'track2', '<!DOCTYPE html>\r\n<html>\r\n<head>\r\n  <meta charset=\"UTF-8\">\r\n  <title>Email Notification</title>\r\n  <style>\r\n    body {\r\n      font-family: Arial, sans-serif;\r\n      background: #f6f6f6;\r\n      margin: 0;\r\n      padding: 20px;\r\n    }\r\n    .email-container {\r\n      max-width: 600px;\r\n      margin: auto;\r\n      background: #ffffff;\r\n      padding: 20px;\r\n      border: 1px solid #dddddd;\r\n    }\r\n    h1 {\r\n      font-size: 20px;\r\n      color: #333333;\r\n    }\r\n    p {\r\n      font-size: 14px;\r\n      color: #555555;\r\n    }\r\n    .button {\r\n      display: inline-block;\r\n      margin-top: 20px;\r\n      padding: 10px 20px;\r\n      background-color: #4f46e5;\r\n      color: #ffffff;\r\n      text-decoration: none;\r\n      border-radius: 4px;\r\n    }\r\n    .footer {\r\n      font-size: 12px;\r\n      color: #999999;\r\n      text-align: center;\r\n      margin-top: 30px;\r\n    }\r\n  </style>\r\n</head>\r\n<body>\r\n  <div class=\"email-container\">\r\n    <h1>You\'ve Got an Attachment!</h1>\r\n    <p>Click the button below to view your special file.</p>\r\n    <a class=\"button\" href=\"#\" target=\"_blank\">View Attachment</a>\r\n    <p class=\"footer\">This link expires in 7 days.<br>\r\n    <a href=\"#\">Unsubscribe</a></p>\r\n  </div>\r\n</body>\r\n</html>\r\n', 'richtext', '2025-04-23 20:09:59', '2025-04-23 20:09:59'),
(26, 'Welcome to the family', 'Custom', 'Welcome to the Family', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n  <meta charset=\"UTF-8\">\r\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n  <title>Thank You Email</title>\r\n  <style>\r\n    body {\r\n      margin: 0;\r\n      padding: 0;\r\n      background-color: #f4f4f4;\r\n      font-family: \'Segoe UI\', Tahoma, Geneva, Verdana, sans-serif;\r\n    }\r\n    .email-container {\r\n      max-width: 600px;\r\n      margin: auto;\r\n      background-color: #ffffff;\r\n      border-radius: 8px;\r\n      overflow: hidden;\r\n      box-shadow: 0 2px 5px rgba(0,0,0,0.1);\r\n    }\r\n    .content {\r\n      padding: 40px 30px;\r\n      text-align: center;\r\n    }\r\n    .content h2 {\r\n      margin: 0;\r\n      color: #333;\r\n      font-size: 18px;\r\n      letter-spacing: 1px;\r\n    }\r\n    .content h1 {\r\n      margin: 10px 0 20px;\r\n      font-size: 32px;\r\n      color: #9b2c2c;\r\n      font-weight: bold;\r\n    }\r\n    .content img {\r\n      max-width: 100%;\r\n      height: auto;\r\n      margin-bottom: 20px;\r\n      border-radius: 10px;\r\n    }\r\n    .content p {\r\n      color: #555555;\r\n      font-size: 15px;\r\n      margin-bottom: 20px;\r\n    }\r\n    .cta-button {\r\n      display: inline-block;\r\n      background-color: #000000;\r\n      color: #ffffff;\r\n      padding: 12px 25px;\r\n      text-decoration: none;\r\n      font-size: 14px;\r\n      border-radius: 4px;\r\n    }\r\n    .footer {\r\n      background-color: #f2f2f2;\r\n      padding: 20px;\r\n      font-size: 12px;\r\n      text-align: center;\r\n      color: #666666;\r\n    }\r\n    .footer a {\r\n      color: #666666;\r\n      text-decoration: underline;\r\n    }\r\n    .social-icons img {\r\n      width: 30px;\r\n      margin: 0 5px;\r\n    }\r\n    .nav {\r\n      margin-top: 10px;\r\n    }\r\n    .nav a {\r\n      margin: 0 8px;\r\n      text-decoration: none;\r\n      color: #444;\r\n    }\r\n  </style>\r\n</head>\r\n<body>\r\n  <div class=\"email-container\">\r\n    <div class=\"content\">\r\n      <h2>WELCOME TO THE DEMOHOMEX FAMILY</h2>\r\n      <h1>Thank You for Joining Us!</h1>\r\n\r\n      <!-- Lorem Image -->\r\n      <img src=\"https://picsum.photos/600/300\" alt=\"Random Welcome Image\">\r\n\r\n      <p>We\'re thrilled to welcome you aboard! Your decision to join us means the world, and we can\'t wait for you to explore everything we have to offer. At our core, we believe in community, creativity, and connection—values that you now help strengthen.</p>\r\n      <p>Click below to begin your personalized journey and discover the unique benefits waiting just for you.</p>\r\n      <a href=\"https://demohomex.com/test/redirect.php?token=Ux%2BvBfjeawCkrvVDvF58URsc8l9FG9y7m0SlgDN3lcDI8xEh8QEYnU6POKTS3KdF4WCyR%2BacK591eZOfZT462y7dw4is6SsbPHOvKqabTsk%3D[-base64email-]\" class=\"cta-button\">Get Started</a>\r\n    </div>\r\n    <div class=\"footer\">\r\n      <p>If you have any questions, please email us at <a href=\"mailto:<EMAIL>\"><EMAIL></a></p>\r\n      <p>Or visit our FAQs. You can also chat with a live human during our operating hours.</p>\r\n      <hr style=\"margin: 20px 0;\">\r\n      <div class=\"social-icons\">\r\n        <a href=\"#\"><img src=\"https://cdn-icons-png.flaticon.com/512/145/145802.png\" alt=\"Facebook\"></a>\r\n        <a href=\"#\"><img src=\"https://cdn-icons-png.flaticon.com/512/145/145812.png\" alt=\"Twitter\"></a>\r\n        <a href=\"#\"><img src=\"https://cdn-icons-png.flaticon.com/512/145/145807.png\" alt=\"LinkedIn\"></a>\r\n        <a href=\"#\"><img src=\"https://cdn-icons-png.flaticon.com/512/145/145805.png\" alt=\"Instagram\"></a>\r\n      </div>\r\n      <div class=\"nav\">\r\n        <a href=\"#\">Home</a> |\r\n        <a href=\"#\">Page</a> |\r\n        <a href=\"#\">About Us</a> |\r\n        <a href=\"#\">Contact Us</a>\r\n      </div>\r\n      <p style=\"margin-top: 20px;\">You received this email as a registered user of <a href=\"https://demohomex.com\">demohomex.com</a>. You can <a href=\"#\">unsubscribe</a> from these emails.</p>\r\n    </div>\r\n  </div>\r\n</body>\r\n</html>\r\n', 'richtext', '2025-05-09 15:19:22', '2025-05-09 15:19:22'),
(27, '1', 'Custom', '1', '<!DOCTYPE html>\r\n<html lang=\"en\">\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Birthday Email Card</title>\r\n    <link href=\"https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400&display=swap\" rel=\"stylesheet\">\r\n    <style>\r\n        body {\r\n            margin: 0;\r\n            height: 100vh;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            background: linear-gradient(135deg, #f8e1e9, #d4e8f1);\r\n            font-family: \'Poppins\', sans-serif;\r\n            overflow: hidden;\r\n        }\r\n        .card-container {\r\n            perspective: 1000px;\r\n        }\r\n        .card {\r\n            width: 450px;\r\n            height: 600px;\r\n            background: linear-gradient(145deg, #fff5f7, #e6f0fa);\r\n            border-radius: 20px;\r\n            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);\r\n            position: relative;\r\n            transform-style: preserve-3d;\r\n            transition: transform 0.6s;\r\n        }\r\n        .card:hover {\r\n            transform: rotateY(10deg) rotateX(5deg);\r\n        }\r\n        .card-front, .card-inside {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 100%;\r\n            backface-visibility: hidden;\r\n            border-radius: 20px;\r\n            overflow: hidden;\r\n        }\r\n        .card-front {\r\n            background: url(\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 450 600\"><rect width=\"450\" height=\"600\" fill=\"none\"/><circle cx=\"50\" cy=\"50\" r=\"20\" fill=\"rgba(255,182,193,0.3)\"/><circle cx=\"400\" cy=\"550\" r=\"25\" fill=\"rgba(173,216,230,0.3)\"/></svg>\') no-repeat;\r\n            display: flex;\r\n            justify-content: center;\r\n            align-items: center;\r\n            flex-direction: column;\r\n        }\r\n        .card-front h1 {\r\n            font-family: \'Dancing Script\', cursive;\r\n            font-size: 60px;\r\n            color: #ff6f91;\r\n            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);\r\n            margin: 0;\r\n        }\r\n        .card-front .subtitle {\r\n            font-family: \'Poppins\', sans-serif;\r\n            font-size: 18px;\r\n            color: #4a90e2;\r\n            margin-top: 10px;\r\n        }\r\n        .card-inside {\r\n            transform: rotateY(180deg);\r\n            background: #fff;\r\n            padding: 30px;\r\n            display: flex;\r\n            flex-direction: column;\r\n            justify-content: space-between;\r\n            border: 2px solid #ff6f91;\r\n        }\r\n        .wishes {\r\n            font-family: \'Dancing Script\', cursive;\r\n            font-size: 24px;\r\n            color: #333;\r\n            line-height: 1.6;\r\n            text-align: center;\r\n        }\r\n        .wishes strong {\r\n            color: #ff6f91;\r\n            font-weight: 700;\r\n        }\r\n        .simple-note {\r\n            font-family: \'Poppins\', sans-serif;\r\n            font-size: 16px;\r\n            color: #666;\r\n            text-align: center;\r\n            margin-top: 20px;\r\n            border-top: 1px dashed #ff6f91;\r\n            padding-top: 10px;\r\n        }\r\n        .signature {\r\n            font-family: \'Dancing Script\', cursive;\r\n            font-size: 20px;\r\n            color: #4a90e2;\r\n            text-align: right;\r\n            margin-top: 20px;\r\n        }\r\n        .decor-top, .decor-bottom {\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 20px;\r\n            background: url(\'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 450 20\"><path d=\"M0 10 Q112.5 0 225 10 T450 10\" stroke=\"rgba(255,105,145,0.5)\" stroke-width=\"2\" fill=\"none\"/></svg>\') repeat-x;\r\n        }\r\n        .decor-top {\r\n            top: 0;\r\n        }\r\n        .decor-bottom {\r\n            bottom: 0;\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"card-container\">\r\n        <div class=\"card\">\r\n            <div class=\"card-front\">\r\n                <h1>Happy Birthday</h1>\r\n                <p class=\"subtitle\">A Special Day for You!</p>\r\n            </div>\r\n            <div class=\"card-inside\">\r\n                <div class=\"decor-top\"></div>\r\n                <div class=\"wishes\">\r\n                    <p>Wishing you a day filled with <strong>joy</strong>, laughter, and all the things that make your heart soar!</p>\r\n                    <p>May this year bring you endless <strong>happiness</strong>, new adventures, and dreams that come true.</p>\r\n                    <p>Here\'s to celebrating <strong>you</strong>—the most amazing person we know—on your special day!</p>\r\n                    <p>Let’s make today unforgettable with love, surprises, and all your favorite things.</p>\r\n                </div>\r\n                <div class=\"simple-note\">\r\n                    Hey friend! Wishing you a fantastic birthday filled with love and fun. Can\'t wait to celebrate with you soon!\r\n                </div>\r\n                <div class=\"signature\">\r\n                    With love,<br>Your Friend\r\n                </div>\r\n                <div class=\"decor-bottom\"></div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 'richtext', '2025-05-09 15:44:47', '2025-05-09 15:44:47'),
(28, 'Finance Growth Partner', 'Custom', 'Welcome To Finance Growth Partner', '<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\" />\n  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\"/>\n  <title>Finance Growth Partner</title>\n</head>\n<body style=\"margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f7fa;\">\n\n  <!-- Outer Wrapper -->\n  <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\" style=\"max-width: 600px; background-color: #ffffff; margin-top: 20px;\">\n    \n    <!-- Logo Header -->\n    <tr>\n      <td align=\"center\" bgcolor=\"#0a3d62\" style=\"padding: 30px 20px;\">\n        <h1 style=\"color: #ffffff; font-size: 24px; margin: 0;\">Finance Growth Partner</h1>\n        <p style=\"color: #bdc3c7; font-size: 14px; margin-top: 5px;\">Helping You Secure & Grow Your Future</p>\n      </td>\n    </tr>\n\n    <!-- Hero Section -->\n    <tr>\n      <td bgcolor=\"#ffffff\" style=\"padding: 40px 30px 20px 30px; text-align: center;\">\n        <h2 style=\"color: #0a3d62; font-size: 22px; margin-bottom: 10px;\">Your Trusted Partner in Financial Security</h2>\n        <p style=\"color: #333333; font-size: 16px; line-height: 1.6;\">\n          We help you retrieve and secure funds so you can focus on what matters most — your future.\n        </p>\n      </td>\n    </tr>\n\n    <!-- Benefits Section -->\n    <tr>\n      <td bgcolor=\"#ffffff\" style=\"padding: 20px 30px 30px 30px;\">\n        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"100%\">\n          <tr>\n            <td style=\"padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; text-align: center;\">\n              <h3 style=\"color: #0a3d62; margin-bottom: 10px;\">Fund Retrieval</h3>\n              <p style=\"color: #555555; font-size: 14px;\">We recover lost or unclaimed funds efficiently and transparently.</p>\n            </td>\n            <td style=\"padding: 15px; border: 1px solid #e0e0e0; border-radius: 8px; text-align: center;\">\n              <h3 style=\"color: #0a3d62; margin-bottom: 10px;\">Secure Investments</h3>\n              <p style=\"color: #555555; font-size: 14px;\">Protect your assets with trusted investment strategies tailored to you.</p>\n            </td>\n          </tr>\n          <tr>\n            <td colspan=\"2\" style=\"padding-top: 20px; text-align: center;\">\n              <a href=\"https://yourwebsite.com \" style=\"background-color: #0a3d62; color: #ffffff; padding: 12px 24px; text-decoration: none; font-weight: bold; border-radius: 6px; display: inline-block;\">\n                Learn More About Us\n              </a>\n            </td>\n          </tr>\n        </table>\n      </td>\n    </tr>\n\n    <!-- Divider Line -->\n    <tr>\n      <td style=\"padding: 20px 30px 10px 30px;\">\n        <hr style=\"border: 0; border-top: 1px solid #eeeeee;\" />\n      </td>\n    </tr>\n\n    <!-- Testimonial / Trust Message -->\n    <tr>\n      <td bgcolor=\"#ffffff\" style=\"padding: 10px 30px 30px 30px; text-align: center;\">\n        <p style=\"color: #333333; font-size: 15px;\"><strong>“We are here to make financial security simple, safe, and accessible.”</strong></p>\n        <p style=\"color: #777777; font-size: 13px; margin-top: 10px;\">— The Finance Growth Partner Team</p>\n      </td>\n    </tr>\n\n    <!-- Social Media Icons -->\n    <tr>\n      <td bgcolor=\"#f9f9f9\" style=\"padding: 20px 30px; text-align: center;\">\n        <p style=\"color: #666666; font-size: 14px; margin-bottom: 10px;\">Follow Us:</p>\n        <table align=\"center\" border=\"0\" cellpadding=\"0\" cellspacing=\"0\">\n          <tr>\n            <td style=\"padding: 0 8px;\">\n              <a href=\"https://facebook.com \" style=\"text-decoration: none;\">\n                <img src=\"https://cdn-icons-png.flaticon.com/512/733/733547.png \" alt=\"Facebook\" width=\"24\" height=\"24\" style=\"display: block;\" />\n              </a>\n            </td>\n            <td style=\"padding: 0 8px;\">\n              <a href=\"https://twitter.com \" style=\"text-decoration: none;\">\n                <img src=\"https://cdn-icons-png.flaticon.com/512/733/733579.png \" alt=\"Twitter\" width=\"24\" height=\"24\" style=\"display: block;\" />\n              </a>\n            </td>\n            <td style=\"padding: 0 8px;\">\n              <a href=\"https://linkedin.com \" style=\"text-decoration: none;\">\n                <img src=\"https://cdn-icons-png.flaticon.com/512/733/733598.png \" alt=\"LinkedIn\" width=\"24\" height=\"24\" style=\"display: block;\" />\n              </a>\n            </td>\n          </tr>\n        </table>\n      </td>\n    </tr>\n\n    <!-- Footer -->\n    <tr>\n      <td bgcolor=\"#f1f1f1\" style=\"padding: 20px 30px; text-align: center; font-size: 12px; color: #666666;\">\n        &copy; 2025 Finance Growth Partner. All rights reserved.<br/>\n        123 Financial Street, Capital City, Country<br/>\n        <a href=\"mailto:<EMAIL>\" style=\"color: #0a3d62; text-decoration: underline;\"><EMAIL></a>\n      </td>\n    </tr>\n\n  </table>\n\n</body>\n</html>', 'richtext', '2025-05-13 13:37:34', '2025-05-13 13:37:34');

-- --------------------------------------------------------

--
-- Table structure for table `integrations`
--

CREATE TABLE `integrations` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `mailchimp_api_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sendgrid_api_key` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ga_tracking_id` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `campaign_sent_webhook` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email_opened_webhook` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `link_clicked_webhook` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `salesforce_org_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `salesforce_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `hubspot_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ip_whitelist`
--

CREATE TABLE `ip_whitelist` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `link_clicks`
--

CREATE TABLE `link_clicks` (
  `id` int(11) NOT NULL,
  `link_id` int(11) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `referrer` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `type` varchar(20) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `message` text NOT NULL,
  `link` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `user_id`, `type`, `title`, `message`, `link`, `is_read`, `created_at`) VALUES
(1, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 08:49:47'),
(2, 2, 'error', 'Error Notification', 'This is a test error notification', NULL, 0, '2025-04-21 08:49:49'),
(3, 2, 'info', 'Info Notification', 'This is a test info notification', NULL, 0, '2025-04-21 08:49:51'),
(4, 2, 'warning', 'Warning Notification', 'This is a test warning notification', NULL, 0, '2025-04-21 08:49:53'),
(5, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 08:50:49'),
(6, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 08:54:14'),
(7, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 08:57:46'),
(8, 2, 'error', 'Error Notification', 'This is a test error notification', NULL, 0, '2025-04-21 08:57:47'),
(9, 2, 'info', 'Info Notification', 'This is a test info notification', NULL, 0, '2025-04-21 08:57:49'),
(10, 2, 'warning', 'Warning Notification', 'This is a test warning notification', NULL, 0, '2025-04-21 08:57:50'),
(11, 2, 'error', 'Error Notification', 'This is a test error notification', NULL, 0, '2025-04-21 09:01:55'),
(12, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 10:05:42'),
(13, 2, 'error', 'Error Notification', 'This is a test error notification', NULL, 0, '2025-04-21 10:05:44'),
(14, 2, 'success', 'Success Notification', 'This is a test success notification', NULL, 0, '2025-04-21 10:32:38'),
(15, 2, 'error', 'Error Notification', 'This is a test error notification', NULL, 0, '2025-04-21 10:32:40'),
(16, 2, 'info', 'Info Notification', 'This is a test info notification', NULL, 0, '2025-04-21 10:32:42'),
(17, 2, 'warning', 'Warning Notification', 'This is a test warning notification', NULL, 0, '2025-04-21 10:32:44');

-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--

CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT '1',
  `in_app_notifications` tinyint(1) DEFAULT '1',
  `campaign_reports` tinyint(1) DEFAULT '1',
  `report_frequency` varchar(50) DEFAULT 'weekly',
  `error_alerts` tinyint(1) DEFAULT '1',
  `system_updates` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `pdf_attachments`
--

CREATE TABLE `pdf_attachments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `campaign_id` int(11) DEFAULT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(11) NOT NULL,
  `upload_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_used` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `pdf_attachments`
--

INSERT INTO `pdf_attachments` (`id`, `user_id`, `campaign_id`, `file_name`, `file_path`, `file_size`, `upload_date`, `last_used`) VALUES
(1, 2, 0, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745443617.pdf', 299821, '2025-04-23 21:26:57', '2025-04-23 21:27:03'),
(2, 2, 0, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745443775.pdf', 299821, '2025-04-23 21:29:35', '2025-04-23 21:29:40'),
(3, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745443964.pdf', 299821, '2025-04-23 21:32:44', NULL),
(4, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745444404.pdf', 299821, '2025-04-23 21:40:04', '2025-04-23 21:40:09'),
(5, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745444635.pdf', 299821, '2025-04-23 21:43:55', '2025-04-23 21:44:00'),
(6, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745445008.pdf', 299821, '2025-04-23 21:50:08', '2025-04-23 21:50:13'),
(7, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745446083.pdf', 299821, '2025-04-23 22:08:03', '2025-04-23 22:08:07'),
(8, 2, NULL, 'pdf_template3.pdf', 'uploads/pdf_attachments/pdf_2_1745447114.pdf', 300010, '2025-04-23 22:25:14', '2025-04-23 22:25:18'),
(9, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745491448.pdf', 299821, '2025-04-24 10:44:08', '2025-04-24 10:44:14'),
(10, 2, NULL, 'Green and Beige Floral Festive Flat Illustration Fall Email Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745492451.pdf', 299821, '2025-04-24 11:00:51', '2025-04-24 11:00:57'),
(11, 2, NULL, 'Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745513609.pdf', 54556, '2025-04-24 16:53:29', '2025-04-24 16:53:38'),
(12, 2, NULL, 'Newsletter.pdf', 'uploads/pdf_attachments/pdf_2_1745553532.pdf', 54556, '2025-04-25 03:58:52', '2025-04-25 03:58:56');

-- --------------------------------------------------------

--
-- Table structure for table `phone_contacts`
--

CREATE TABLE `phone_contacts` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `group_id` int(11) DEFAULT NULL,
  `status` enum('active','inactive','unsubscribed') DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `phone_contacts`
--

INSERT INTO `phone_contacts` (`id`, `user_id`, `phone`, `first_name`, `last_name`, `group_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 4, '+128828282882828', 's', 's', NULL, 'active', '2025-03-31 10:02:08', '2025-03-31 10:02:08'),
(2, 4, '+11818818188181', NULL, NULL, 1, 'active', '2025-03-31 10:04:03', '2025-03-31 10:04:03'),
(3, 4, '+19999999999999', NULL, NULL, 1, 'active', '2025-03-31 10:04:03', '2025-03-31 10:04:03'),
(4, 4, '+1111111111', NULL, NULL, NULL, 'active', '2025-03-31 10:07:12', '2025-03-31 10:07:12'),
(5, 4, '+32838838383883', 'james', 'bong', 1, 'active', '2025-03-31 10:08:49', '2025-03-31 10:08:49'),
(6, 4, '+1110100101', NULL, NULL, NULL, 'active', '2025-03-31 10:10:18', '2025-03-31 10:10:18'),
(7, 4, '+2222222222', NULL, NULL, NULL, 'active', '2025-03-31 10:10:18', '2025-03-31 10:10:18'),
(8, 4, '+3333333333', NULL, NULL, NULL, 'active', '2025-03-31 10:10:18', '2025-03-31 10:10:18'),
(9, 4, '+101010010101010', NULL, NULL, 3, 'active', '2025-03-31 11:51:29', '2025-03-31 11:51:29'),
(10, 4, '+222282882828822', NULL, NULL, 3, 'active', '2025-03-31 11:51:29', '2025-03-31 11:51:29'),
(11, 4, '+262662626266262', NULL, NULL, 3, 'active', '2025-03-31 11:51:29', '2025-03-31 11:51:29');

-- --------------------------------------------------------

--
-- Table structure for table `phone_contact_groups`
--

CREATE TABLE `phone_contact_groups` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `phone_contact_groups`
--

INSERT INTO `phone_contact_groups` (`id`, `user_id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 4, 'Alert', 'Alert', '2025-03-31 10:02:49', '2025-03-31 10:02:49'),
(2, 4, 'test333', 'tets333', '2025-03-31 10:11:20', '2025-03-31 10:11:20'),
(3, 4, 'Today sms', 'today sms', '2025-03-31 11:50:00', '2025-03-31 11:50:00');

-- --------------------------------------------------------

--
-- Table structure for table `rate_limits`
--

CREATE TABLE `rate_limits` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `timestamp` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `redirect_domains`
--

CREATE TABLE `redirect_domains` (
  `id` int(11) NOT NULL,
  `domain` varchar(255) NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_default` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `redirect_domains`
--

INSERT INTO `redirect_domains` (`id`, `domain`, `is_active`, `created_at`, `is_default`) VALUES
(1, 'demohomexx.com', 1, '2025-03-26 08:01:55', 0),
(2, 'testdrivexxx.tiiny.site', 1, '2025-04-20 17:05:17', 0),
(3, 'demohomex.com/test', 1, '2025-04-20 17:40:11', 1),
(4, 'testdrivexx.lovestoblog.com', 1, '2025-04-20 17:47:06', 0);

-- --------------------------------------------------------

--
-- Table structure for table `security_settings`
--

CREATE TABLE `security_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `two_factor_enabled` tinyint(1) DEFAULT '0',
  `two_factor_method` varchar(20) DEFAULT 'email',
  `two_factor_secret` varchar(255) DEFAULT NULL,
  `session_timeout` int(11) DEFAULT '60',
  `backup_codes` text,
  `verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `security_settings`
--

INSERT INTO `security_settings` (`id`, `user_id`, `two_factor_enabled`, `two_factor_method`, `two_factor_secret`, `session_timeout`, `backup_codes`, `verified_at`, `created_at`, `updated_at`) VALUES
(1, 1, 0, 'email', NULL, 60, NULL, NULL, '2025-04-01 17:12:06', '2025-04-01 17:12:06'),
(2, 2, 1, 'app', 'QIKFOP4INW2MFZDU', 60, '[\"5B735F65\",\"8AC38BF3\",\"6392AAB0\",\"2BE067CE\",\"3FE69E7A\",\"C207D171\",\"64786FEE\",\"93751B22\"]', '2025-05-13 13:11:14', '2025-04-01 17:12:06', '2025-05-13 13:11:14');

-- --------------------------------------------------------

--
-- Table structure for table `sms_campaigns`
--

CREATE TABLE `sms_campaigns` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `group_id` int(11) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `custom_message` text,
  `status` enum('pending','sending','completed','failed','scheduled') NOT NULL DEFAULT 'pending',
  `scheduled` datetime DEFAULT NULL,
  `sent_count` int(11) DEFAULT '0',
  `failed_count` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `sms_log`
--

CREATE TABLE `sms_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `template_id` int(11) DEFAULT NULL,
  `phone_number` varchar(20) NOT NULL,
  `message` text NOT NULL,
  `status` varchar(50) DEFAULT 'pending',
  `response_data` json DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `sms_recipients`
--

CREATE TABLE `sms_recipients` (
  `id` int(11) NOT NULL,
  `campaign_id` int(11) NOT NULL,
  `contact_id` int(11) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `status` enum('pending','sent','failed','delivered') NOT NULL DEFAULT 'pending',
  `message_id` varchar(255) DEFAULT NULL,
  `response` text,
  `sent_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `sms_settings`
--

CREATE TABLE `sms_settings` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `api_key` varchar(255) NOT NULL,
  `api_endpoint` varchar(255) DEFAULT 'https://textbelt.com/text',
  `test_mode` tinyint(1) DEFAULT '1',
  `rate_limit` int(11) DEFAULT '100',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `sms_settings`
--

INSERT INTO `sms_settings` (`id`, `user_id`, `api_key`, `api_endpoint`, `test_mode`, `rate_limit`, `created_at`, `updated_at`) VALUES
(1, 4, 'ed64350ede7ecef6da249143e0fdc1354fa7514aOnoHLf9TbShDkpgm3UVFuTpWv', 'https://textbelt.com/text', 1, 100, '2025-03-31 08:44:44', '2025-03-31 08:44:44'),
(2, 2, 'ed64350ede7ecef6da249143e0fdc1354fa7514aOnoHLf9TbShDkpgm3UVFuTpWv', 'https://textbelt.com/text', 1, 100, '2025-04-09 18:34:18', '2025-04-09 18:34:18');

-- --------------------------------------------------------

--
-- Table structure for table `sms_templates`
--

CREATE TABLE `sms_templates` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `template_name` varchar(255) NOT NULL,
  `content` text NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `rotation_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `sms_templates`
--

INSERT INTO `sms_templates` (`id`, `user_id`, `template_name`, `content`, `is_active`, `rotation_order`, `created_at`, `updated_at`) VALUES
(1, 4, 'hi', 'hi', 1, 0, '2025-03-31 11:39:22', '2025-03-31 11:39:22'),
(2, 4, 'todaydata', 'today is comig', 1, 0, '2025-03-31 11:39:57', '2025-03-31 11:39:57'),
(3, 2, 'hi', 'hi', 1, 0, '2025-04-27 07:09:58', '2025-04-27 07:09:58');

-- --------------------------------------------------------

--
-- Table structure for table `smtp_settings`
--

CREATE TABLE `smtp_settings` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Default SMTP',
  `is_active` tinyint(1) DEFAULT '0',
  `type` enum('smtp','local','amazon_ec2','mailrelay') COLLATE utf8mb4_unicode_ci DEFAULT 'smtp',
  `daily_limit` int(11) DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `smtp_server` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` int(11) NOT NULL,
  `encryption` enum('tls','ssl','none') COLLATE utf8mb4_unicode_ci DEFAULT 'tls',
  `username` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `reply_to` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `alert_emails` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `smtp_settings`
--

INSERT INTO `smtp_settings` (`id`, `name`, `is_active`, `type`, `daily_limit`, `description`, `smtp_server`, `port`, `encryption`, `username`, `password`, `from_name`, `from_email`, `reply_to`, `alert_emails`, `created_at`, `updated_at`) VALUES
(1, 'MailRelay', 0, 'mailrelay', NULL, '', 'smtp1.s.ipzmarketing.com', 587, 'tls', 'dwamzxzwajmv', '5kpOrABF3HkP3Q', 'Support Consulting', '<EMAIL>', '<EMAIL>', '<EMAIL>', '2025-03-25 20:01:16', '2025-05-09 14:54:42'),
(10, 'Titan Email', 1, 'smtp', NULL, 'Primary SMTP Server for Campaign Manager', 'smtp.hostinger.com', 465, 'ssl', '<EMAIL>', 'Money2025@Demo#', 'Support Consulting', '<EMAIL>', '<EMAIL>', '', '2025-04-02 14:34:06', '2025-05-09 14:54:42'),
(11, 'Local Server', 0, 'local', NULL, 'Local SMTP Server for Testing', 'localhost', 25, 'none', '', '', 'Local Test', 'noreply@localhost', '<EMAIL>', '', '2025-04-02 14:34:48', '2025-04-03 19:26:44'),
(12, 'Amazon EC2', 0, 'amazon_ec2', 50000, 'Direct Email via Amazon EC2', 'localhost', 587, 'tls', '', '', 'EC2 Mailer', '<EMAIL>', '<EMAIL>', NULL, '2025-04-02 14:34:48', '2025-04-02 14:34:48'),
(13, '<EMAIL>', 0, 'smtp', NULL, '<EMAIL>', '<EMAIL>', 587, 'tls', '<EMAIL>', 'Loving12', '<EMAIL>', '<EMAIL>', '<EMAIL>', '', '2025-05-07 13:08:36', '2025-05-07 13:08:36');

-- --------------------------------------------------------

--
-- Table structure for table `smtp_settings_backup`
--

CREATE TABLE `smtp_settings_backup` (
  `id` int(11) NOT NULL DEFAULT '0',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Default SMTP',
  `is_active` tinyint(1) DEFAULT '0',
  `type` enum('smtp','local','amazon_ec2','mailrelay') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'smtp',
  `daily_limit` int(11) DEFAULT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `smtp_server` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` int(11) NOT NULL,
  `encryption` enum('tls','ssl','none') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'tls',
  `username` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `from_email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `reply_to` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `alert_emails` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `smtp_settings_backup`
--

INSERT INTO `smtp_settings_backup` (`id`, `name`, `is_active`, `type`, `daily_limit`, `description`, `smtp_server`, `port`, `encryption`, `username`, `password`, `from_name`, `from_email`, `reply_to`, `alert_emails`, `created_at`, `updated_at`) VALUES
(1, 'MailRelay', 1, 'mailrelay', NULL, '', 'smtp1.s.ipzmarketing.com', 587, 'tls', 'dwamzxzwajmv', '5kpOrABF3HkP3Q', 'Support Consulting', '<EMAIL>', '<EMAIL>', '<EMAIL>', '2025-03-25 20:01:16', '2025-04-02 14:25:08');

-- --------------------------------------------------------

--
-- Table structure for table `templates`
--

CREATE TABLE `templates` (
  `id` int(11) NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subject` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `company_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `website` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` text COLLATE utf8mb4_unicode_ci,
  `is_admin` tinyint(1) DEFAULT '0',
  `status` enum('active','inactive','suspended') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `last_login` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `first_name`, `last_name`, `email`, `phone`, `company_name`, `website`, `address`, `is_admin`, `status`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'testuser', '$2y$10$fR4TJD455vVW.ZKwz7Z7OutflwA0AWINful2PhvXtJcCSz.0GY6GK', 'New', 'Masterxzyx', '<EMAIL>', '', '', '', '', 0, 'active', NULL, '2025-04-01 17:12:06', '2025-04-02 07:48:51'),
(2, 'admin', '$2y$10$3e6gczgRh6mOcqDvz3Y31OKIurAei2A6RbS2YArxXnAkrUroiVp7.', 'Campaign', 'Pro+', '<EMAIL>', '', '', '', '', 1, 'active', '2025-05-13 15:11:14', '2025-04-01 17:12:06', '2025-05-13 13:11:14');

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_daily_email_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_daily_email_stats` (
`date` date
,`total_emails` bigint(21)
,`delivered` decimal(23,0)
,`opens` decimal(23,0)
,`clicks` decimal(23,0)
,`delivery_rate` decimal(29,2)
,`open_rate` decimal(29,2)
,`click_rate` decimal(29,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_device_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_device_stats` (
`device_type` varchar(20)
,`total_opens` bigint(21)
,`unique_opens` bigint(21)
,`percentage` decimal(26,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_email_client_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_email_client_stats` (
`browser_name` varchar(50)
,`total_opens` bigint(21)
,`unique_opens` bigint(21)
,`percentage` decimal(26,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_recipient_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_recipient_stats` (
`contact_id` int(11)
,`email` varchar(255)
,`first_name` varchar(255)
,`last_name` varchar(255)
,`total_emails` bigint(21)
,`delivered` decimal(23,0)
,`opens` decimal(23,0)
,`clicks` decimal(23,0)
,`delivery_rate` decimal(29,2)
,`open_rate` decimal(29,2)
,`click_rate` decimal(29,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_template_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_template_stats` (
`template_id` int(11)
,`template_name` varchar(255)
,`template_type` varchar(20)
,`total_emails` bigint(21)
,`delivered` decimal(23,0)
,`opens` decimal(23,0)
,`clicks` decimal(23,0)
,`delivery_rate` decimal(29,2)
,`open_rate` decimal(29,2)
,`click_rate` decimal(29,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_today_email_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_today_email_stats` (
`total_emails` bigint(21)
,`sent_count` decimal(23,0)
,`failed_count` decimal(23,0)
,`unique_recipients` bigint(21)
,`batch_count` bigint(21)
,`template_count` bigint(21)
,`success_rate` decimal(29,2)
);

-- --------------------------------------------------------

--
-- Stand-in structure for view `vw_weekly_email_stats`
-- (See below for the actual view)
--
CREATE TABLE `vw_weekly_email_stats` (
`date` date
,`total_emails` bigint(21)
,`sent_count` decimal(23,0)
,`failed_count` decimal(23,0)
,`unique_recipients` bigint(21)
,`batch_count` bigint(21)
,`template_count` bigint(21)
,`success_rate` decimal(29,2)
);

-- --------------------------------------------------------

--
-- Table structure for table `webhook_logs`
--

CREATE TABLE `webhook_logs` (
  `id` int(11) NOT NULL,
  `esp_type` varchar(50) NOT NULL,
  `raw_data` longtext NOT NULL,
  `received_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `processed` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Structure for view `vw_daily_email_stats`
--
DROP TABLE IF EXISTS `vw_daily_email_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_daily_email_stats`  AS SELECT cast(`email_log`.`sent_at` as date) AS `date`, count(0) AS `total_emails`, sum((case when ((`email_log`.`status` = 'Sent') or (`email_log`.`status` = 'Delivered')) then 1 else 0 end)) AS `delivered`, sum((case when (`email_log`.`opened` = 1) then 1 else 0 end)) AS `opens`, sum((case when (`email_log`.`clicked` = 1) then 1 else 0 end)) AS `clicks`, round(((sum((case when ((`email_log`.`status` = 'Sent') or (`email_log`.`status` = 'Delivered')) then 1 else 0 end)) / count(0)) * 100),2) AS `delivery_rate`, round(((sum((case when (`email_log`.`opened` = 1) then 1 else 0 end)) / nullif(sum((case when ((`email_log`.`status` = 'Sent') or (`email_log`.`status` = 'Delivered')) then 1 else 0 end)),0)) * 100),2) AS `open_rate`, round(((sum((case when (`email_log`.`clicked` = 1) then 1 else 0 end)) / nullif(sum((case when (`email_log`.`opened` = 1) then 1 else 0 end)),0)) * 100),2) AS `click_rate` FROM `email_log` GROUP BY cast(`email_log`.`sent_at` as date) ORDER BY `date` AS `DESCdesc` ASC  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_device_stats`
--
DROP TABLE IF EXISTS `vw_device_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_device_stats`  AS SELECT `email_opens`.`device_type` AS `device_type`, count(0) AS `total_opens`, count(distinct `email_opens`.`log_id`) AS `unique_opens`, round(((count(0) / (select count(0) from `email_opens`)) * 100),2) AS `percentage` FROM `email_opens` GROUP BY `email_opens`.`device_type` ORDER BY `total_opens` AS `DESCdesc` ASC  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_email_client_stats`
--
DROP TABLE IF EXISTS `vw_email_client_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_email_client_stats`  AS SELECT `email_opens`.`browser_name` AS `browser_name`, count(0) AS `total_opens`, count(distinct `email_opens`.`log_id`) AS `unique_opens`, round(((count(0) / (select count(0) from `email_opens`)) * 100),2) AS `percentage` FROM `email_opens` GROUP BY `email_opens`.`browser_name` ORDER BY `total_opens` AS `DESCdesc` ASC  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_recipient_stats`
--
DROP TABLE IF EXISTS `vw_recipient_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_recipient_stats`  AS SELECT `c`.`id` AS `contact_id`, `c`.`email` AS `email`, `c`.`first_name` AS `first_name`, `c`.`last_name` AS `last_name`, count(`el`.`id`) AS `total_emails`, sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)) AS `delivered`, sum((case when (`el`.`opened` = 1) then 1 else 0 end)) AS `opens`, sum((case when (`el`.`clicked` = 1) then 1 else 0 end)) AS `clicks`, round(((sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)) / count(`el`.`id`)) * 100),2) AS `delivery_rate`, round(((sum((case when (`el`.`opened` = 1) then 1 else 0 end)) / nullif(sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)),0)) * 100),2) AS `open_rate`, round(((sum((case when (`el`.`clicked` = 1) then 1 else 0 end)) / nullif(sum((case when (`el`.`opened` = 1) then 1 else 0 end)),0)) * 100),2) AS `click_rate` FROM (`contacts` `c` left join `email_log` `el` on((`c`.`id` = `el`.`contact_id`))) GROUP BY `c`.`id`, `c`.`email`, `c`.`first_name`, `c`.`last_name` ORDER BY `total_emails` AS `DESCdesc` ASC  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_template_stats`
--
DROP TABLE IF EXISTS `vw_template_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_template_stats`  AS SELECT `t`.`id` AS `template_id`, `t`.`name` AS `template_name`, coalesce(`el`.`template_type`,'regular') AS `template_type`, count(`el`.`id`) AS `total_emails`, sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)) AS `delivered`, sum((case when (`el`.`opened` = 1) then 1 else 0 end)) AS `opens`, sum((case when (`el`.`clicked` = 1) then 1 else 0 end)) AS `clicks`, round(((sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)) / count(`el`.`id`)) * 100),2) AS `delivery_rate`, round(((sum((case when (`el`.`opened` = 1) then 1 else 0 end)) / nullif(sum((case when ((`el`.`status` = 'Sent') or (`el`.`status` = 'Delivered')) then 1 else 0 end)),0)) * 100),2) AS `open_rate`, round(((sum((case when (`el`.`clicked` = 1) then 1 else 0 end)) / nullif(sum((case when (`el`.`opened` = 1) then 1 else 0 end)),0)) * 100),2) AS `click_rate` FROM (`email_templates` `t` left join `email_log` `el` on((`t`.`id` = `el`.`template_id`))) GROUP BY `t`.`id`, `t`.`name`, `el`.`template_type` ORDER BY `total_emails` AS `DESCdesc` ASC  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_today_email_stats`
--
DROP TABLE IF EXISTS `vw_today_email_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_today_email_stats`  AS SELECT count(0) AS `total_emails`, sum((case when (`email_log`.`status` = 'Sent') then 1 else 0 end)) AS `sent_count`, sum((case when (`email_log`.`status` = 'Failed') then 1 else 0 end)) AS `failed_count`, count(distinct `email_log`.`recipient_email`) AS `unique_recipients`, count(distinct `email_log`.`batch_id`) AS `batch_count`, count(distinct `email_log`.`template_id`) AS `template_count`, round(((sum((case when (`email_log`.`status` = 'Sent') then 1 else 0 end)) / count(0)) * 100),2) AS `success_rate` FROM `email_log` WHERE (cast(`email_log`.`sent_at` as date) = curdate())  ;

-- --------------------------------------------------------

--
-- Structure for view `vw_weekly_email_stats`
--
DROP TABLE IF EXISTS `vw_weekly_email_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `vw_weekly_email_stats`  AS SELECT cast(`email_log`.`sent_at` as date) AS `date`, count(0) AS `total_emails`, sum((case when (`email_log`.`status` = 'Sent') then 1 else 0 end)) AS `sent_count`, sum((case when (`email_log`.`status` = 'Failed') then 1 else 0 end)) AS `failed_count`, count(distinct `email_log`.`recipient_email`) AS `unique_recipients`, count(distinct `email_log`.`batch_id`) AS `batch_count`, count(distinct `email_log`.`template_id`) AS `template_count`, round(((sum((case when (`email_log`.`status` = 'Sent') then 1 else 0 end)) / count(0)) * 100),2) AS `success_rate` FROM `email_log` WHERE (`email_log`.`sent_at` >= (curdate() - interval 7 day)) GROUP BY cast(`email_log`.`sent_at` as date) ORDER BY `date` AS `DESCdesc` ASC  ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `ab_test_campaigns`
--
ALTER TABLE `ab_test_campaigns`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_test_type` (`test_type`);

--
-- Indexes for table `ab_test_results`
--
ALTER TABLE `ab_test_results`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_test_variant` (`test_id`,`variant`),
  ADD KEY `idx_test_id` (`test_id`);

--
-- Indexes for table `api_settings`
--
ALTER TABLE `api_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `api_name` (`api_name`);

--
-- Indexes for table `appearance_settings`
--
ALTER TABLE `appearance_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `campaigns`
--
ALTER TABLE `campaigns`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `contact_group_id` (`contact_group_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `campaign_recipients`
--
ALTER TABLE `campaign_recipients`
  ADD PRIMARY KEY (`campaign_id`,`contact_id`),
  ADD KEY `contact_id` (`contact_id`);

--
-- Indexes for table `click_tracking`
--
ALTER TABLE `click_tracking`
  ADD PRIMARY KEY (`id`),
  ADD KEY `log_id` (`log_id`);

--
-- Indexes for table `contacts`
--
ALTER TABLE `contacts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_group_subscribed` (`group_id`,`subscribed`);

--
-- Indexes for table `contact_groups`
--
ALTER TABLE `contact_groups`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `contact_group_members`
--
ALTER TABLE `contact_group_members`
  ADD PRIMARY KEY (`group_id`,`contact_id`),
  ADD KEY `contact_id` (`contact_id`);

--
-- Indexes for table `email_batch_progress`
--
ALTER TABLE `email_batch_progress`
  ADD PRIMARY KEY (`batch_id`);

--
-- Indexes for table `email_log`
--
ALTER TABLE `email_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_campaign_sent_opened` (`campaign_id`,`sent`,`opened`),
  ADD KEY `idx_batch_status` (`batch_id`,`status`),
  ADD KEY `idx_template` (`template_id`,`template_type`),
  ADD KEY `idx_contact` (`contact_id`,`status`),
  ADD KEY `idx_recipient` (`recipient_email`),
  ADD KEY `idx_sent_at` (`sent_at`),
  ADD KEY `idx_device_type` (`device_type`),
  ADD KEY `idx_browser_name` (`browser_name`),
  ADD KEY `idx_message_id` (`message_id`);

--
-- Indexes for table `email_opens`
--
ALTER TABLE `email_opens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_log_id` (`log_id`),
  ADD KEY `idx_opened_at` (`opened_at`),
  ADD KEY `idx_device_type` (`device_type`);

--
-- Indexes for table `email_send_logs`
--
ALTER TABLE `email_send_logs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_smtp_date` (`smtp_id`,`send_date`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `encrypted_links`
--
ALTER TABLE `encrypted_links`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `error_settings`
--
ALTER TABLE `error_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`);

--
-- Indexes for table `html_templates`
--
ALTER TABLE `html_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `integrations`
--
ALTER TABLE `integrations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id_unique` (`user_id`);

--
-- Indexes for table `ip_whitelist`
--
ALTER TABLE `ip_whitelist`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `link_clicks`
--
ALTER TABLE `link_clicks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `link_id` (`link_id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `is_read` (`is_read`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `pdf_attachments`
--
ALTER TABLE `pdf_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `campaign_id` (`campaign_id`);

--
-- Indexes for table `phone_contacts`
--
ALTER TABLE `phone_contacts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `phone` (`phone`,`user_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `phone_contact_groups`
--
ALTER TABLE `phone_contact_groups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `rate_limits`
--
ALTER TABLE `rate_limits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `ip_key_timestamp` (`ip`,`key`,`timestamp`);

--
-- Indexes for table `redirect_domains`
--
ALTER TABLE `redirect_domains`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `security_settings`
--
ALTER TABLE `security_settings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `sms_campaigns`
--
ALTER TABLE `sms_campaigns`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `group_id` (`group_id`),
  ADD KEY `template_id` (`template_id`);

--
-- Indexes for table `sms_log`
--
ALTER TABLE `sms_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `template_id` (`template_id`);

--
-- Indexes for table `sms_recipients`
--
ALTER TABLE `sms_recipients`
  ADD PRIMARY KEY (`id`),
  ADD KEY `campaign_id` (`campaign_id`),
  ADD KEY `contact_id` (`contact_id`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `sms_settings`
--
ALTER TABLE `sms_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user` (`user_id`);

--
-- Indexes for table `sms_templates`
--
ALTER TABLE `sms_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `smtp_settings`
--
ALTER TABLE `smtp_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `templates`
--
ALTER TABLE `templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `webhook_logs`
--
ALTER TABLE `webhook_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_esp_type` (`esp_type`),
  ADD KEY `idx_received_at` (`received_at`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `ab_test_campaigns`
--
ALTER TABLE `ab_test_campaigns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ab_test_results`
--
ALTER TABLE `ab_test_results`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `api_settings`
--
ALTER TABLE `api_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `appearance_settings`
--
ALTER TABLE `appearance_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `campaigns`
--
ALTER TABLE `campaigns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `click_tracking`
--
ALTER TABLE `click_tracking`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `contacts`
--
ALTER TABLE `contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `contact_groups`
--
ALTER TABLE `contact_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `email_log`
--
ALTER TABLE `email_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `email_opens`
--
ALTER TABLE `email_opens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_send_logs`
--
ALTER TABLE `email_send_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `encrypted_links`
--
ALTER TABLE `encrypted_links`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `error_settings`
--
ALTER TABLE `error_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `html_templates`
--
ALTER TABLE `html_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `integrations`
--
ALTER TABLE `integrations`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `ip_whitelist`
--
ALTER TABLE `ip_whitelist`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `link_clicks`
--
ALTER TABLE `link_clicks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=18;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pdf_attachments`
--
ALTER TABLE `pdf_attachments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `phone_contacts`
--
ALTER TABLE `phone_contacts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `phone_contact_groups`
--
ALTER TABLE `phone_contact_groups`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `rate_limits`
--
ALTER TABLE `rate_limits`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `redirect_domains`
--
ALTER TABLE `redirect_domains`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `security_settings`
--
ALTER TABLE `security_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sms_campaigns`
--
ALTER TABLE `sms_campaigns`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sms_log`
--
ALTER TABLE `sms_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sms_recipients`
--
ALTER TABLE `sms_recipients`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `sms_settings`
--
ALTER TABLE `sms_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `sms_templates`
--
ALTER TABLE `sms_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `smtp_settings`
--
ALTER TABLE `smtp_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `templates`
--
ALTER TABLE `templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `webhook_logs`
--
ALTER TABLE `webhook_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `appearance_settings`
--
ALTER TABLE `appearance_settings`
  ADD CONSTRAINT `fk_appearance_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `campaigns`
--
ALTER TABLE `campaigns`
  ADD CONSTRAINT `campaigns_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `campaign_recipients`
--
ALTER TABLE `campaign_recipients`
  ADD CONSTRAINT `campaign_recipients_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `campaigns` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `campaign_recipients_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `contacts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contacts`
--
ALTER TABLE `contacts`
  ADD CONSTRAINT `contacts_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `contact_groups` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `contact_group_members`
--
ALTER TABLE `contact_group_members`
  ADD CONSTRAINT `contact_group_members_ibfk_1` FOREIGN KEY (`group_id`) REFERENCES `contact_groups` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `contact_group_members_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `contacts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_log`
--
ALTER TABLE `email_log`
  ADD CONSTRAINT `email_log_ibfk_1` FOREIGN KEY (`campaign_id`) REFERENCES `campaigns` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `email_log_ibfk_2` FOREIGN KEY (`contact_id`) REFERENCES `contacts` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_send_logs`
--
ALTER TABLE `email_send_logs`
  ADD CONSTRAINT `email_send_logs_ibfk_1` FOREIGN KEY (`smtp_id`) REFERENCES `smtp_settings` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `encrypted_links`
--
ALTER TABLE `encrypted_links`
  ADD CONSTRAINT `encrypted_links_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`);

--
-- Constraints for table `error_settings`
--
ALTER TABLE `error_settings`
  ADD CONSTRAINT `fk_error_settings_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `ip_whitelist`
--
ALTER TABLE `ip_whitelist`
  ADD CONSTRAINT `ip_whitelist_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `link_clicks`
--
ALTER TABLE `link_clicks`
  ADD CONSTRAINT `link_clicks_ibfk_1` FOREIGN KEY (`link_id`) REFERENCES `encrypted_links` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD CONSTRAINT `notification_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `phone_contacts`
--
ALTER TABLE `phone_contacts`
  ADD CONSTRAINT `phone_contacts_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `phone_contact_groups`
--
ALTER TABLE `phone_contact_groups`
  ADD CONSTRAINT `phone_contact_groups_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `security_settings`
--
ALTER TABLE `security_settings`
  ADD CONSTRAINT `security_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sms_log`
--
ALTER TABLE `sms_log`
  ADD CONSTRAINT `sms_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `sms_log_ibfk_2` FOREIGN KEY (`template_id`) REFERENCES `sms_templates` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `sms_settings`
--
ALTER TABLE `sms_settings`
  ADD CONSTRAINT `sms_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sms_templates`
--
ALTER TABLE `sms_templates`
  ADD CONSTRAINT `sms_templates_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `templates`
--
ALTER TABLE `templates`
  ADD CONSTRAINT `templates_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
