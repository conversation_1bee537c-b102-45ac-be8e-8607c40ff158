/* Root Variables */
:root {
  --primary-color: #2C5F2D;
  --primary-hover: #224924;
  --danger-color: #dc3545;
  --danger-hover: #c82333;
  --success-color: #28a745;
  --warning-color: #ffc107;
  --light-bg: #f8f9fa;
  --border-color: #dee2e6;
  --text-primary: #333;
  --text-secondary: #6c757d;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --radius-sm: 8px;
  --radius-md: 12px;
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
}

/* Global Styles */
body {
  font-family: 'Roboto', sans-serif;
  background-color: #f5f5f5;
  color: #333;
  margin: 0;
  padding: 0;
}

.layout {
  display: flex;
}

.main-content {
  flex-grow: 1;
  margin-left: 250px;
  padding: 20px;
  overflow-y: auto;
  min-height: 100vh;
  box-sizing: border-box;
}

/* Payment Methods Styles */
.payment-methods {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-methods h1,
.payment-methods h2 {
  color: #2c5f2d;
  margin-bottom: 20px;
}

/* Container Layout - Full width on large screens */
.payment-methods-container {
  width: 100%;
  padding: var(--spacing-md);
}

/* Full width on large screens */
@media (min-width: 1200px) {
  .payment-methods-container {
    padding: var(--spacing-md) var(--spacing-sm);
  }
}

.payment-methods-content {
  display: grid;
  gap: var(--spacing-lg);
}

/* Card Styles */
.payment-methods-card {
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-lg);
  height: fit-content;
}

.payment-methods-card h2 {
  color: var(--primary-color);
  margin-bottom: var(--spacing-md);
  font-size: 1.5rem;
  font-weight: 600;
}

/* Form Styles */
.payment-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-group label {
  font-weight: 500;
  color: var(--text-primary);
}

.form-input {
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--radius-sm);
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* Field Groups */
.field-group {
  background: var(--light-bg);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  border: 1px solid var(--border-color);
}

.field-inputs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm);
}

/* Button Styles */
.submit-button,
.add-field-button {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  margin-bottom: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.submit-button {
  background-color: #166534;
  color: white;
  font-size: 1rem;
}

.submit-button:hover {
  background-color: #15803d;
  transform: translateY(-2px);
}

.add-field-button {
  background-color: #0891b2;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.add-field-button:hover {
  background-color: #0e7490;
  transform: translateY(-2px);
}

/* Update button styles to be more specific */
.payment-form .add-field-button {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  background-color: var(--primary-color) !important;
  color: white !important;
  border: none !important;
}

.payment-form .add-field-button:hover {
  background-color: var(--primary-hover) !important;
  color: white !important;
}

/* Update remove button styles to be more specific */
.payment-form .remove-field-button {
  padding: 0.75rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  background-color: var(--danger-color) !important;
  color: white !important;
  border: none !important;
}

.payment-form .remove-field-button:hover {
  background-color: var(--danger-hover) !important;
  color: white !important;
}

.payment-form .remove-field-button:disabled {
  background-color: var(--light-bg) !important;
  color: var(--text-secondary) !important;
  cursor: not-allowed;
}

/* Remove the conflicting styles */
.remove-field-button {
  background-color: #dc2626 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 0.75rem 1rem !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.remove-field-button:hover {
  background-color: #b91c1c !important;
}

.remove-field-button:disabled {
  background-color: #dc2626 !important;
  color: white !important;
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* Payment Methods List */
.payment-methods-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
  align-items: stretch;
}

.payment-method-item {
  background: white;
  border-radius: var(--radius-sm);
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 1rem;
  align-items: center;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.payment-method-item:hover {
  background-color: #f9fafb;
  border-color: #166534;
}

.payment-method-item h3 {
  color: var(--primary-color);
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  display: inline-block;
}

.payment-method-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.payment-type {
  color: var(--text-secondary);
  font-size: 0.8rem;
  display: inline-block;
  margin-left: 0.5rem;
}

.payment-method-actions {
  display: flex;
  gap: 0.5rem;
}

.field-list {
  display: none;
}

.field-item {
  padding: 0.75rem;
  background: var(--light-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  margin-bottom: 0.5rem;
  word-break: break-word;
}

.field-item:last-child {
  margin-bottom: 0;
}

.field-item strong {
  color: var(--text-primary);
  margin-right: 0.5rem;
  display: block;
  margin-bottom: 0.25rem;
  font-size: 0.9rem;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.edit-button,
.delete-button,
.view-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  padding: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.edit-button svg,
.delete-button svg,
.view-button svg {
  width: 16px;
  height: 16px;
  color: white;
}

.view-button {
  background-color: #166534;
  color: white;
}

.view-button:hover {
  background-color: #15803d;
}

.edit-button {
  background-color: #0891b2;
  color: white;
}

.edit-button:hover {
  background-color: #0e7490;
}

.delete-button {
  background-color: #dc2626;
  color: white;
}

.delete-button:hover {
  background-color: #b91c1c;
}

/* Message Styles */
.error-message,
.success-message {
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
  font-weight: 500;
}

.error-message {
  background-color: #ffebee;
  color: var(--danger-color);
  border: 1px solid #ffcdd2;
}

.success-message {
  background-color: #e8f5e9;
  color: var(--success-color);
  border: 1px solid #c8e6c9;
}

/* Scrollbar styling */
.field-list::-webkit-scrollbar {
  width: 6px;
}

.field-list::-webkit-scrollbar-track {
  background: var(--light-bg);
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

/* Responsive Design for Payment Method Cards */
/* Large screens - adjust for sidebar modes */
@media (min-width: 1400px) {
  .payment-methods-container .grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 1200px) and (max-width: 1399px) {
  .payment-methods-container .grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1024px) and (max-width: 1199px) {
  .payment-methods-container .grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 1023px) {
  .payment-methods-container .grid {
    grid-template-columns: 1fr;
  }
}

/* Legacy responsive for old list layout */
@media (max-width: 1200px) {
  .payment-methods-list {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .payment-methods-container {
    padding: var(--spacing-sm);
  }

  .payment-methods-content {
    display: flex;
    flex-direction: column;
  }

  .field-inputs {
    grid-template-columns: 1fr;
  }

  .payment-method-item {
    padding: 0.75rem 0.5rem;
  }

  .payment-method-info h3 {
    font-size: 0.9rem;
  }

  .payment-type {
    font-size: 0.7rem;
  }

  .action-buttons {
    gap: 0.25rem;
  }

  .edit-button,
  .delete-button,
  .view-button {
    width: 32px;
    height: 32px;
  }

  .edit-button svg,
  .delete-button svg,
  .view-button svg {
    width: 14px;
    height: 14px;
  }

  .modal-header h2 {
    font-size: 1.2rem;
  }

  /* Ensure single column on mobile */
  .payment-methods-container .grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  .payment-method-item {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .payment-method-actions {
    justify-content: flex-start;
  }

  .leaderboard-modal {
    width: 95%;
    padding: 15px;
  }
}

@media (max-width: 480px) {
  .payment-methods-card {
    padding: var(--spacing-sm);
  }

  .field-group {
    padding: var(--spacing-sm);
  }
}

@media (max-width: 768px) {
  .main-content {
      margin-left: 0;
  }
}
