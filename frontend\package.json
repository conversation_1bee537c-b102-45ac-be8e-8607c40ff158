{"name": "fanbet247-admin", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^6.2.0", "@mui/material": "^6.2.0", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.7.7", "chart.js": "^4.1.1", "framer-motion": "^11.11.17", "lucide-react": "^0.445.0", "moment": "^2.30.1", "qrcode.react": "^4.2.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-countdown": "^2.3.6", "react-dom": "^18.2.0", "react-icons": "^5.3.0", "react-router-dom": "^6.4.1", "react-scripts": "5.0.1", "swiper": "^11.1.14", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "dev": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost/FanBet247", "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-jest": "^27.9.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15"}}