import React, { useState, useEffect } from 'react';
import axios from 'axios';
import {
  Fa<PERSON><PERSON><PERSON>,
  FaTrophy,
  FaMoneyBillWave,
  FaChartLine,
  FaCalendarAlt,
  FaUserFriends,
  FaFootballBall,
  FaChartBar,
  FaExchangeAlt,
  FaEye,
  FaCheckCircle,
  FaClock,
  FaBell,
  FaUserCircle
} from 'react-icons/fa';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title, Tooltip, Legend, ArcElement } from 'chart.js';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

const API_BASE_URL = '/backend';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

function AdminDashboard() {
    const [dashboardData, setDashboardData] = useState(null);
    const [teams, setTeams] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedBetDetails, setSelectedBetDetails] = useState(null);
    const [showBetModal, setShowBetModal] = useState(false);

    // Chart data states
    const [userActivityData, setUserActivityData] = useState(null);
    const [betDistributionData, setBetDistributionData] = useState(null);
    const [teamPopularityData, setTeamPopularityData] = useState(null);

    // Recent activity data
    const [recentActivity, setRecentActivity] = useState([]);

    useEffect(() => {
        const fetchAllData = async () => {
            setLoading(true);
            try {
                await Promise.all([
                    fetchDashboardData(),
                    fetchTeams(),
                    prepareChartData()
                ]);
            } catch (error) {
                console.error('Error fetching dashboard data:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchAllData();
    }, []);

    // Prepare chart data
    const prepareChartData = () => {
        // User activity chart data (last 7 days)
        const userActivityLabels = Array.from({length: 7}, (_, i) => {
            const date = new Date();
            date.setDate(date.getDate() - (6 - i));
            return date.toLocaleDateString('en-US', { weekday: 'short' });
        });

        setUserActivityData({
            labels: userActivityLabels,
            datasets: [
                {
                    label: 'New Users',
                    data: [5, 8, 12, 7, 10, 15, 20],
                    borderColor: 'rgb(59, 130, 246)',
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                    tension: 0.4
                },
                {
                    label: 'Active Users',
                    data: [25, 30, 35, 40, 38, 42, 45],
                    borderColor: 'rgb(16, 185, 129)',
                    backgroundColor: 'rgba(16, 185, 129, 0.5)',
                    tension: 0.4
                }
            ]
        });

        // Bet distribution chart data
        setBetDistributionData({
            labels: ['Football', 'Basketball', 'Tennis', 'Hockey', 'Baseball'],
            datasets: [
                {
                    label: 'Bet Distribution by Sport',
                    data: [45, 25, 15, 10, 5],
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(139, 92, 246, 0.8)'
                    ],
                    borderWidth: 1
                }
            ]
        });

        // Team popularity chart data
        setTeamPopularityData({
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
            datasets: [
                {
                    label: 'Bet Volume',
                    data: [65, 59, 80, 81, 56, 55],
                    backgroundColor: 'rgba(59, 130, 246, 0.5)',
                }
            ]
        });

        // Mock recent activity data
        setRecentActivity([
            { id: 1, user: 'JohnDoe', action: 'placed a bet', amount: 100, time: '10 minutes ago', status: 'success' },
            { id: 2, user: 'AliceSmith', action: 'registered', amount: null, time: '25 minutes ago', status: 'info' },
            { id: 3, user: 'BobJohnson', action: 'won a bet', amount: 250, time: '1 hour ago', status: 'success' },
            { id: 4, user: 'EmilyDavis', action: 'requested withdrawal', amount: 500, time: '2 hours ago', status: 'warning' },
            { id: 5, user: 'MichaelBrown', action: 'lost a bet', amount: 75, time: '3 hours ago', status: 'danger' }
        ]);
    };

    const fetchDashboardData = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/admin_dashboard_data.php`);
            if (response.data.success) {
                setDashboardData(response.data);
            } else {
                console.error('Failed to fetch dashboard data:', response.data.message);
            }
        } catch (error) {
            console.error('Error fetching dashboard data:', error);
        }
    };

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            if (response.data.status === 200) {
                setTeams(response.data.data);
            }
        } catch (err) {
            console.error('Error fetching teams:', err);
        }
    };

    const getTeamLogo = (teamName) => {
        const team = teams.find(team => team.name === teamName);
        return team ? `${API_BASE_URL}/${team.logo}` : '';
    };

    // Modal components with Tailwind CSS
    const BetDetailsModal = ({ bet, onClose }) => (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center p-4 border-b">
                    <h3 className="text-lg font-semibold text-gray-800">Bet ID: {bet.bet_id}</h3>
                    <button
                        className="text-gray-500 hover:text-gray-700 text-2xl focus:outline-none"
                        onClick={onClose}
                    >
                        ×
                    </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
                    {/* Left side - Teams */}
                    <div>
                        <div className="flex flex-col items-center space-y-6">
                            <div className="flex items-center justify-between w-full">
                                <div className="flex flex-col items-center">
                                    <img
                                        src={getTeamLogo(bet.team_a)}
                                        alt={bet.team_a}
                                        className="w-20 h-20 object-contain bg-white rounded-full p-1 shadow-md"
                                    />
                                    <div className="mt-2 text-center">
                                        <div className="font-medium text-gray-800">{bet.team_a}</div>
                                        <div className="text-sm text-gray-600">Odds: {bet.odds_user1}</div>
                                        <div className="text-sm font-medium text-blue-600">{bet.user1_name}</div>
                                    </div>
                                </div>

                                <div className="mx-4 text-xl font-bold text-gray-500">VS</div>

                                <div className="flex flex-col items-center">
                                    <img
                                        src={getTeamLogo(bet.team_b)}
                                        alt={bet.team_b}
                                        className="w-20 h-20 object-contain bg-white rounded-full p-1 shadow-md"
                                    />
                                    <div className="mt-2 text-center">
                                        <div className="font-medium text-gray-800">{bet.team_b}</div>
                                        <div className="text-sm text-gray-600">Odds: {bet.odds_user2}</div>
                                        <div className="text-sm font-medium text-blue-600">
                                            {bet.user2_name || 'WAITING FOR OPPONENT'}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right side - Details */}
                    <div>
                        <div className="mb-4">
                            <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium
                                ${bet.bet_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                bet.bet_status === 'active' ? 'bg-blue-100 text-blue-800' :
                                bet.bet_status === 'completed' ? 'bg-green-100 text-green-800' :
                                'bg-gray-100 text-gray-800'}`}>
                                {bet.bet_status.toUpperCase()}
                            </span>
                        </div>

                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">User 1 Bet Amount</div>
                                    <div className="font-medium text-gray-900">{bet.amount_user1} FanCoins</div>
                                </div>
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">User 1 Potential Return</div>
                                    <div className="font-medium text-green-600">{bet.potential_return_win_user1} FanCoins</div>
                                </div>
                            </div>

                            {bet.user2_name && (
                                <div className="grid grid-cols-2 gap-4">
                                    <div className="bg-gray-50 p-3 rounded">
                                        <div className="text-sm text-gray-500">User 2 Bet Amount</div>
                                        <div className="font-medium text-gray-900">{bet.amount_user2} FanCoins</div>
                                    </div>
                                    <div className="bg-gray-50 p-3 rounded">
                                        <div className="text-sm text-gray-500">User 2 Potential Return</div>
                                        <div className="font-medium text-green-600">{bet.potential_return_win_user2} FanCoins</div>
                                    </div>
                                </div>
                            )}

                            <div className="bg-gray-50 p-3 rounded">
                                <div className="text-sm text-gray-500">Created At</div>
                                <div className="font-medium text-gray-900">{new Date(bet.created_at).toLocaleString()}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    const UserActivityModal = ({ activity, onClose }) => (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center p-4 border-b">
                    <h3 className="text-lg font-semibold text-gray-800">User: {activity.username}</h3>
                    <button
                        className="text-gray-500 hover:text-gray-700 text-2xl focus:outline-none"
                        onClick={onClose}
                    >
                        ×
                    </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
                    {/* Left side - Teams */}
                    <div>
                        <div className="flex flex-col items-center space-y-6">
                            <div className="flex items-center justify-between w-full">
                                <div className="flex flex-col items-center">
                                    <img
                                        src={getTeamLogo(activity.latest_challenge_team_a)}
                                        alt={activity.latest_challenge_team_a}
                                        className="w-20 h-20 object-contain bg-white rounded-full p-1 shadow-md"
                                    />
                                    <div className="mt-2 text-center">
                                        <div className="font-medium text-gray-800">{activity.latest_challenge_team_a}</div>
                                        <div className="text-sm text-gray-600">Odds: {activity.odds_team_a || '1.80'}</div>
                                        <div className="text-sm text-gray-500">Goal Advantage: {activity.team_a_goal_advantage || '0'}</div>
                                    </div>
                                </div>

                                <div className="mx-4 text-xl font-bold text-gray-500">VS</div>

                                <div className="flex flex-col items-center">
                                    <img
                                        src={getTeamLogo(activity.latest_challenge_team_b)}
                                        alt={activity.latest_challenge_team_b}
                                        className="w-20 h-20 object-contain bg-white rounded-full p-1 shadow-md"
                                    />
                                    <div className="mt-2 text-center">
                                        <div className="font-medium text-gray-800">{activity.latest_challenge_team_b}</div>
                                        <div className="text-sm text-gray-600">Odds: {activity.odds_team_b || '1.80'}</div>
                                        <div className="text-sm text-gray-500">Goal Advantage: {activity.team_b_goal_advantage || '0'}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Right side - Details */}
                    <div>
                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">Total Bets</div>
                                    <div className="font-medium text-gray-900">{activity.totalBets}</div>
                                </div>
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">Balance</div>
                                    <div className="font-medium text-blue-600">{activity.balance} FanCoins</div>
                                </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">Latest Potential Return</div>
                                    <div className="font-medium text-green-600">{activity.latest_potential_return} FanCoins</div>
                                </div>
                                <div className="bg-gray-50 p-3 rounded">
                                    <div className="text-sm text-gray-500">Match Odds</div>
                                    <div className="font-medium text-gray-900">{activity.odds_team_a || '1.80'} - {activity.odds_team_b || '1.80'}</div>
                                </div>
                            </div>

                            <div className="bg-gray-50 p-3 rounded">
                                <div className="text-sm text-gray-500">Last Activity</div>
                                <div className="font-medium text-gray-900">{new Date(activity.lastActivity).toLocaleString()}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen bg-gray-50">
                <div className="text-center">
                    <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading dashboard data...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Page Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
                <p className="text-gray-600">Welcome to the FanBet247 admin dashboard</p>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Total Users */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-blue-100 p-3 mr-4">
                        <FaUsers className="text-blue-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Total Users</p>
                        <h3 className="text-2xl font-bold text-gray-800">{dashboardData?.totalUsers || 0}</h3>
                    </div>
                </div>

                {/* Active Challenges */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-green-100 p-3 mr-4">
                        <FaTrophy className="text-green-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Active Challenges</p>
                        <h3 className="text-2xl font-bold text-gray-800">{dashboardData?.activeChallenges || 0}</h3>
                    </div>
                </div>

                {/* Total Winnings */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-yellow-100 p-3 mr-4">
                        <FaMoneyBillWave className="text-yellow-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Total Winnings</p>
                        <h3 className="text-2xl font-bold text-gray-800">${dashboardData?.totalWins || 0}</h3>
                    </div>
                </div>

                {/* Total Bets */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-purple-100 p-3 mr-4">
                        <FaChartLine className="text-purple-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Total Bets</p>
                        <h3 className="text-2xl font-bold text-gray-800">{dashboardData?.totalBets || 0}</h3>
                    </div>
                </div>
            </div>

            {/* Charts and Team Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* User Activity Chart */}
                <div className="bg-white rounded-lg shadow-sm p-6 lg:col-span-2">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-lg font-semibold text-gray-800">User Activity</h2>
                        <div className="text-sm text-gray-500">Last 7 days</div>
                    </div>
                    <div className="h-80">
                        {userActivityData && <Line data={userActivityData} options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'top',
                                },
                                title: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }} />}
                    </div>
                </div>

                {/* Bet Distribution */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex justify-between items-center mb-4">
                        <h2 className="text-lg font-semibold text-gray-800">Bet Distribution</h2>
                        <div className="text-sm text-gray-500">By Sport</div>
                    </div>
                    <div className="h-80 flex items-center justify-center">
                        {betDistributionData && <Doughnut data={betDistributionData} options={{
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                }
                            }
                        }} />}
                    </div>
                </div>
            </div>

            {/* Top Teams Overview */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-semibold text-gray-800">Top Teams Overview</h2>
                    <button className="text-blue-500 hover:text-blue-700 text-sm font-medium">View All</button>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                    {dashboardData?.teamStats && dashboardData.teamStats.map(team => (
                        <div key={team.id} className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:shadow-md transition-shadow">
                            <img
                                src={`${API_BASE_URL}/${team.logo}`}
                                alt={team.name}
                                className="w-16 h-16 object-contain bg-white rounded-full p-1 shadow-sm mb-3"
                            />
                            <h3 className="text-sm font-medium text-gray-800 text-center mb-1">{team.name}</h3>
                            <div className="flex items-center text-blue-500">
                                <FaUserFriends className="mr-1 text-xs" />
                                <span className="text-sm font-semibold">{team.user_count}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>

            {/* Latest User Activity */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-semibold text-gray-800">Latest User Activity</h2>
                    <div className="flex space-x-2">
                        <button className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded text-gray-600">Today</button>
                        <button className="px-3 py-1 text-xs bg-blue-500 text-white rounded">All Time</button>
                    </div>
                </div>

                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {recentActivity.map((activity) => (
                                <tr key={activity.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0 h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                                                <FaUserCircle className="text-gray-500" />
                                            </div>
                                            <div className="ml-4">
                                                <div className="text-sm font-medium text-gray-900">{activity.user}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{activity.action}</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        {activity.amount ? (
                                            <div className="text-sm font-medium text-gray-900">{activity.amount} FanCoins</div>
                                        ) : (
                                            <div className="text-sm text-gray-500">-</div>
                                        )}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {activity.time}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            ${activity.status === 'success' ? 'bg-green-100 text-green-800' :
                                            activity.status === 'warning' ? 'bg-yellow-100 text-yellow-800' :
                                            activity.status === 'danger' ? 'bg-red-100 text-red-800' :
                                            'bg-blue-100 text-blue-800'}`}>
                                            {activity.status}
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Recent Bets */}
            <div className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-lg font-semibold text-gray-800">Recent Bets</h2>
                    <button className="text-blue-500 hover:text-blue-700 text-sm font-medium">View All Bets</button>
                </div>

                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bet ID</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User 1</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User 2</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Match</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {dashboardData?.recentBets && dashboardData.recentBets.map((bet, index) => (
                                <tr key={index} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                        #{bet.bet_id}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {bet.user1_name}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {bet.user2_name || <span className="text-yellow-500">Waiting...</span>}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center space-x-1">
                                            <img
                                                src={getTeamLogo(bet.team_a)}
                                                alt={bet.team_a}
                                                className="w-6 h-6 object-contain bg-white rounded-full"
                                            />
                                            <span className="text-xs text-gray-500">vs</span>
                                            <img
                                                src={getTeamLogo(bet.team_b)}
                                                alt={bet.team_b}
                                                className="w-6 h-6 object-contain bg-white rounded-full"
                                            />
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm font-medium text-gray-900">{bet.amount_user1} FanCoins</div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                            ${bet.bet_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                            bet.bet_status === 'active' ? 'bg-blue-100 text-blue-800' :
                                            bet.bet_status === 'completed' ? 'bg-green-100 text-green-800' :
                                            'bg-gray-100 text-gray-800'}`}>
                                            {bet.bet_status}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <button
                                            onClick={() => {
                                                setSelectedBetDetails(bet);
                                                setShowBetModal(true);
                                            }}
                                            className="text-blue-500 hover:text-blue-700 font-medium"
                                        >
                                            View Details
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>
            </div>

            {/* Modals */}
            {showBetModal && (
                selectedBetDetails.bet_id ?
                    <BetDetailsModal
                        bet={selectedBetDetails}
                        onClose={() => setShowBetModal(false)}
                    /> :
                    <UserActivityModal
                        activity={selectedBetDetails}
                        onClose={() => setShowBetModal(false)}
                    />
            )}
        </div>
    );
}
export default AdminDashboard;
