import React from 'react';
import { FaExclamationTriangle, FaInfoCircle, FaCheckCircle, FaTimes } from 'react-icons/fa';
import './CustomModal.css';

const CustomModal = ({ 
    isOpen, 
    onClose, 
    onConfirm, 
    title, 
    message, 
    type = 'confirm', // 'alert', 'confirm', 'success', 'error', 'warning'
    confirmText = 'Confirm',
    cancelText = 'Cancel',
    showCancel = true,
    confirmButtonColor = 'blue',
    children
}) => {
    if (!isOpen) return null;

    const getIcon = () => {
        switch (type) {
            case 'success':
                return <FaCheckCircle className="modal-icon success" />;
            case 'error':
                return <FaExclamationTriangle className="modal-icon error" />;
            case 'warning':
                return <FaExclamationTriangle className="modal-icon warning" />;
            case 'alert':
            case 'confirm':
            default:
                return <FaInfoCircle className="modal-icon info" />;
        }
    };

    const getConfirmButtonClass = () => {
        switch (confirmButtonColor) {
            case 'red':
                return 'btn-danger';
            case 'green':
                return 'btn-success';
            case 'yellow':
                return 'btn-warning';
            case 'blue':
            default:
                return 'btn-primary';
        }
    };

    const handleOverlayClick = (e) => {
        if (e.target === e.currentTarget) {
            onClose();
        }
    };

    const handleConfirm = () => {
        if (onConfirm) {
            onConfirm();
        }
        onClose();
    };

    return (
        <div className="custom-modal-overlay" onClick={handleOverlayClick}>
            <div className="custom-modal">
                <div className="custom-modal-header">
                    <div className="modal-title-container">
                        {getIcon()}
                        <h3 className="modal-title">{title}</h3>
                    </div>
                    <button 
                        className="modal-close-btn"
                        onClick={onClose}
                        aria-label="Close modal"
                    >
                        <FaTimes />
                    </button>
                </div>

                <div className="custom-modal-body">
                    {message && <p className="modal-message">{message}</p>}
                    {children}
                </div>

                <div className="custom-modal-footer">
                    {showCancel && (
                        <button 
                            className="btn btn-secondary"
                            onClick={onClose}
                        >
                            {cancelText}
                        </button>
                    )}
                    {(type === 'confirm' || onConfirm) && (
                        <button 
                            className={`btn ${getConfirmButtonClass()}`}
                            onClick={handleConfirm}
                        >
                            {confirmText}
                        </button>
                    )}
                    {type === 'alert' && !onConfirm && (
                        <button 
                            className="btn btn-primary"
                            onClick={onClose}
                        >
                            OK
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};

export default CustomModal;
