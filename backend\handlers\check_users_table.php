<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Check the users table structure
    $query = "DESCRIBE users";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Check if password_hash column exists
    $hasPasswordHash = false;
    $hasPassword = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'password_hash') {
            $hasPasswordHash = true;
        }
        if ($column['Field'] === 'password') {
            $hasPassword = true;
        }
    }
    
    // Also check a sample user to see what data exists
    $sampleQuery = "SELECT user_id, username, email, password, password_hash FROM users WHERE username = 'testuser' LIMIT 1";
    $sampleStmt = $conn->prepare($sampleQuery);
    $sampleStmt->execute();
    $sampleUser = $sampleStmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'table_structure' => $columns,
        'has_password_hash_column' => $hasPasswordHash,
        'has_password_column' => $hasPassword,
        'sample_user' => $sampleUser ? [
            'user_id' => $sampleUser['user_id'],
            'username' => $sampleUser['username'],
            'email' => $sampleUser['email'],
            'password_field_exists' => isset($sampleUser['password']),
            'password_hash_field_exists' => isset($sampleUser['password_hash']),
            'password_field_empty' => empty($sampleUser['password']),
            'password_hash_field_empty' => empty($sampleUser['password_hash'])
        ] : null
    ]);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Database error: " . $e->getMessage()
    ]);
}
?>
