{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\TransactionManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport axios from '../utils/axiosConfig';\nimport './TransactionManagement.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction TransactionManagement() {\n  _s();\n  const [creditRequests, setCreditRequests] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [showProofModal, setShowProofModal] = useState(false);\n  const [selectedProof, setSelectedProof] = useState(null);\n  const [successMessage, setSuccessMessage] = useState('');\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 640);\n  const [isTabletView, setIsTabletView] = useState(false);\n\n  // Sorting and pagination states\n  const [sortConfig, setSortConfig] = useState({\n    key: 'created_at',\n    direction: 'desc'\n  });\n  const [currentPage, setCurrentPage] = useState(1);\n  const [itemsPerPage] = useState(10);\n  const tableRef = useRef(null);\n  const [hasScroll, setHasScroll] = useState(false);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 640);\n      setIsTabletView(window.innerWidth > 640 && window.innerWidth <= 1366);\n    };\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  useEffect(() => {\n    fetchCreditRequests();\n  }, []);\n  useEffect(() => {\n    const checkScroll = () => {\n      if (tableRef.current) {\n        setHasScroll(tableRef.current.scrollWidth > tableRef.current.clientWidth);\n      }\n    };\n    checkScroll();\n    window.addEventListener('resize', checkScroll);\n    return () => window.removeEventListener('resize', checkScroll);\n  }, [creditRequests]);\n\n  // Sorting function\n  const sortData = (data, key, direction) => {\n    return [...data].sort((a, b) => {\n      let aValue = key.split('.').reduce((obj, k) => obj === null || obj === void 0 ? void 0 : obj[k], a);\n      let bValue = key.split('.').reduce((obj, k) => obj === null || obj === void 0 ? void 0 : obj[k], b);\n\n      // Handle special cases\n      if (key === 'amount') {\n        aValue = parseFloat(aValue);\n        bValue = parseFloat(bValue);\n      } else if (typeof aValue === 'string') {\n        aValue = aValue.toLowerCase();\n        bValue = bValue.toLowerCase();\n      }\n      if (aValue < bValue) return direction === 'asc' ? -1 : 1;\n      if (aValue > bValue) return direction === 'asc' ? 1 : -1;\n      return 0;\n    });\n  };\n\n  // Handle sort\n  const handleSort = key => {\n    setSortConfig(prevConfig => ({\n      key,\n      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n\n  // Get sorted and paginated data\n  const getSortedAndPaginatedData = () => {\n    const sortedData = sortData(creditRequests, sortConfig.key, sortConfig.direction);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    return sortedData.slice(startIndex, startIndex + itemsPerPage);\n  };\n\n  // Get sort direction indicator\n  const getSortIndicator = key => {\n    if (sortConfig.key !== key) return '↕';\n    return sortConfig.direction === 'asc' ? '↑' : '↓';\n  };\n\n  // Pagination controls\n  const totalPages = Math.ceil(creditRequests.length / itemsPerPage);\n  const pageNumbers = Array.from({\n    length: totalPages\n  }, (_, i) => i + 1);\n  const handlePageChange = pageNumber => {\n    setCurrentPage(pageNumber);\n  };\n  const fetchCreditRequests = async () => {\n    try {\n      setLoading(true);\n      setError('');\n      const response = await axios.get('admin/get_credit_requests.php');\n      if (response.data.success) {\n        setCreditRequests(response.data.requests || []);\n      } else {\n        throw new Error(response.data.message || 'Failed to fetch credit requests');\n      }\n    } catch (err) {\n      console.error('Error fetching credit requests:', err);\n      setError(err.message || 'Failed to load credit requests. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleViewProof = request => {\n    setSelectedProof({\n      url: `/backend/handlers/get_proof_image.php?request_id=${request.request_id}&user_id=${request.user_id}`,\n      amount: request.amount,\n      date: request.created_at,\n      status: request.status,\n      username: request.username\n    });\n    setShowProofModal(true);\n  };\n  const showAlert = (message, isSuccess = true) => {\n    if (isSuccess) {\n      setSuccessMessage(message);\n      setError('');\n    } else {\n      setError(message);\n      setSuccessMessage('');\n    }\n    setTimeout(() => {\n      if (isSuccess) {\n        setSuccessMessage('');\n      } else {\n        setError('');\n      }\n    }, 3000);\n  };\n  const handleUpdateStatus = async (requestId, newStatus, username) => {\n    try {\n      const response = await axios.post('admin/update_credit_request.php', {\n        request_id: requestId,\n        status: newStatus\n      });\n      if (response.data.success) {\n        const action = newStatus === 'approved' ? 'approved' : 'rejected';\n        showAlert(`Successfully ${action} credit request for ${username}`);\n        fetchCreditRequests();\n      } else {\n        throw new Error(response.data.message || 'Failed to update request status');\n      }\n    } catch (err) {\n      console.error('Error updating request status:', err);\n      showAlert(err.message || 'Failed to update request status. Please try again.', false);\n    }\n  };\n  const formatPaymentMethodDetails = method => {\n    if (!method || !method.fields) return 'N/A';\n    try {\n      const fields = Array.isArray(method.fields) ? method.fields : JSON.parse(method.fields);\n      const mainFields = fields.slice(0, 2); // Show first two fields\n\n      return mainFields.map(field => field.fieldValue).join(' - ');\n    } catch (err) {\n      console.error('Error formatting payment method:', err);\n      return method.name || 'N/A';\n    }\n  };\n  const ProofModal = ({\n    proof,\n    onClose\n  }) => {\n    if (!proof) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: onClose,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"proof-modal\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-modal\",\n          onClick: onClose,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"proof-details\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: [\"Payment Proof - \", proof.username]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Amount: \\u20A6\", parseFloat(proof.amount).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Date: \", formatDate(proof.date).full]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Status: \", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: getStatusBadgeClass(proof.status),\n              children: proof.status.charAt(0).toUpperCase() + proof.status.slice(1)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 36\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"proof-image-container\",\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: proof.url,\n            alt: \"Payment Proof\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 13\n    }, this);\n  };\n  const getStatusBadgeClass = status => {\n    switch (status) {\n      case 'approved':\n        return 'status-badge success';\n      case 'rejected':\n        return 'status-badge danger';\n      case 'expired':\n        return 'status-badge warning';\n      default:\n        return 'status-badge pending';\n    }\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    return {\n      short: new Intl.DateTimeFormat('en-US', {\n        month: 'short',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit'\n      }).format(date),\n      minimalist: new Intl.DateTimeFormat('en-US', {\n        month: 'short',\n        day: 'numeric',\n        hour: 'numeric',\n        minute: 'numeric',\n        hour12: true\n      }).format(date),\n      full: new Intl.DateTimeFormat('en-US', {\n        dateStyle: 'full',\n        timeStyle: 'long'\n      }).format(date)\n    };\n  };\n  const MobileCard = ({\n    request,\n    index\n  }) => {\n    var _request$payment_meth;\n    const formattedDate = formatDate(request.created_at);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"request-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-amount\",\n          children: [\"\\u20A6\", parseFloat(request.amount).toLocaleString()]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-date\",\n          title: formattedDate.full,\n          children: formattedDate.short\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-label\",\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-value\",\n            children: request.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-label\",\n            children: \"Payment Method\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-value\",\n            children: (_request$payment_meth = request.payment_method) === null || _request$payment_meth === void 0 ? void 0 : _request$payment_meth.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 256,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-label\",\n            children: \"Status\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: getStatusBadgeClass(request.status),\n            children: request.status.charAt(0).toUpperCase() + request.status.slice(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 21\n        }, this), request.expires_at && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-field\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-label\",\n            children: \"Expires\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"card-value\",\n            children: formatDate(request.expires_at).short\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"view-proof-btn\",\n          onClick: () => handleViewProof(request),\n          \"aria-label\": \"View payment proof\",\n          children: \"View Proof\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this), request.status === 'pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"approve-btn\",\n            onClick: () => handleUpdateStatus(request.request_id, 'approved', request.username),\n            \"aria-label\": \"Approve payment request\",\n            children: \"Approve\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reject-btn\",\n            onClick: () => handleUpdateStatus(request.request_id, 'rejected', request.username),\n            \"aria-label\": \"Reject payment request\",\n            children: \"Reject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 246,\n      columnNumber: 13\n    }, this);\n  };\n  const TabletCard = ({\n    request,\n    index\n  }) => {\n    var _request$payment_meth2;\n    const formattedDate = formatDate(request.created_at);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tablet-request-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tablet-card-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tablet-card-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tablet-card-number\",\n            children: [\"#\", startIndex + index + 1]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tablet-card-date\",\n            title: formattedDate.full,\n            children: formattedDate.short\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tablet-card-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tablet-card-username\",\n            children: request.username\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tablet-card-amount\",\n            children: [\"\\u20A6\", parseFloat(request.amount).toLocaleString()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 309,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tablet-card-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tablet-card-method\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-method-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-name\",\n              children: (_request$payment_meth2 = request.payment_method) === null || _request$payment_meth2 === void 0 ? void 0 : _request$payment_meth2.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 324,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"method-details\",\n              children: formatPaymentMethodDetails(request.payment_method)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 323,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tablet-card-status\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: getStatusBadgeClass(request.status),\n            children: request.status.charAt(0).toUpperCase() + request.status.slice(1)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tablet-card-expires\",\n          title: request.expires_at ? formatDate(request.expires_at).full : '',\n          children: [\"Expires: \", request.expires_at ? formatDate(request.expires_at).short : 'N/A']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tablet-card-actions\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"view-proof-btn\",\n          onClick: () => handleViewProof(request),\n          \"aria-label\": \"View payment proof\",\n          children: \"View Proof\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 21\n        }, this), request.status === 'pending' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"approve-btn\",\n            onClick: () => handleUpdateStatus(request.request_id, 'approved', request.username),\n            \"aria-label\": \"Approve payment request\",\n            children: \"Approve\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"reject-btn\",\n            onClick: () => handleUpdateStatus(request.request_id, 'rejected', request.username),\n            \"aria-label\": \"Reject payment request\",\n            children: \"Reject\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this);\n  };\n  const TableHeader = () => /*#__PURE__*/_jsxDEV(\"thead\", {\n    children: /*#__PURE__*/_jsxDEV(\"tr\", {\n      children: [/*#__PURE__*/_jsxDEV(\"th\", {\n        className: \"row-number\",\n        children: \"#\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        onClick: () => handleSort('created_at'),\n        className: \"sortable date-column\",\n        children: [\"Date & Expires \", getSortIndicator('created_at')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        onClick: () => handleSort('username'),\n        className: \"sortable\",\n        children: [\"User \", getSortIndicator('username')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        onClick: () => handleSort('amount'),\n        className: \"sortable\",\n        children: [\"Amount \", getSortIndicator('amount')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        onClick: () => handleSort('payment_method.name'),\n        className: \"sortable\",\n        children: [\"Payment Method \", getSortIndicator('payment_method.name')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        onClick: () => handleSort('status'),\n        className: \"sortable\",\n        children: [\"Status \", getSortIndicator('status')]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"Proof\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n        children: \"Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 390,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 371,\n    columnNumber: 9\n  }, this);\n  const Pagination = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"pagination\",\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => handlePageChange(currentPage - 1),\n      disabled: currentPage === 1,\n      className: \"pagination-button\",\n      children: \"Previous\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-numbers\",\n      children: pageNumbers.map(number => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handlePageChange(number),\n        className: `pagination-button ${currentPage === number ? 'active' : ''}`,\n        children: number\n      }, number, false, {\n        fileName: _jsxFileName,\n        lineNumber: 406,\n        columnNumber: 21\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 404,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => handlePageChange(currentPage + 1),\n      disabled: currentPage === totalPages,\n      className: \"pagination-button\",\n      children: \"Next\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 415,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 396,\n    columnNumber: 9\n  }, this);\n  const ScrollHint = () => hasScroll && !isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"scroll-hint\",\n    children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n      xmlns: \"http://www.w3.org/2000/svg\",\n      viewBox: \"0 0 20 20\",\n      fill: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        fillRule: \"evenodd\",\n        d: \"M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z\",\n        clipRule: \"evenodd\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 17\n    }, this), \"Scroll horizontally to see more\"]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 427,\n    columnNumber: 13\n  }, this);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 16\n    }, this);\n  }\n  const displayData = getSortedAndPaginatedData();\n  const startIndex = (currentPage - 1) * itemsPerPage;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"transaction-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"Credit Request Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 445,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 447,\n      columnNumber: 23\n    }, this), successMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"success-message\",\n      children: successMessage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 448,\n      columnNumber: 32\n    }, this), creditRequests.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `credit-requests-table ${hasScroll ? 'has-scroll' : ''}`,\n      ref: tableRef,\n      children: [/*#__PURE__*/_jsxDEV(ScrollHint, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 21\n      }, this), isMobile ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-cards\",\n        children: displayData.map((request, index) => /*#__PURE__*/_jsxDEV(MobileCard, {\n          request: request,\n          index: startIndex + index + 1\n        }, request.request_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 33\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 25\n      }, this) : isTabletView ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tablet-cards\",\n        children: displayData.map((request, index) => /*#__PURE__*/_jsxDEV(TabletCard, {\n          request: request,\n          index: index\n        }, request.request_id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 33\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 25\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"table-container\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            children: [/*#__PURE__*/_jsxDEV(TableHeader, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 477,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: displayData.map((request, index) => {\n                var _request$payment_meth3;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"row-number\",\n                    children: startIndex + index + 1\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"date-expires\",\n                    title: `Created: ${formatDate(request.created_at).full}${request.expires_at ? `\\nExpires: ${formatDate(request.expires_at).full}` : ''}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"date-content\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"date-line\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"date-label\",\n                          children: \"Date:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 486,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"created-date\",\n                          children: formatDate(request.created_at).minimalist\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 61\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 485,\n                        columnNumber: 57\n                      }, this), request.expires_at && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"expires-line\",\n                        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"date-label\",\n                          children: \"Exp:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 491,\n                          columnNumber: 65\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"expires-date\",\n                          children: formatDate(request.expires_at).minimalist\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 492,\n                          columnNumber: 65\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 490,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 484,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 482,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"username-display\",\n                    children: request.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 497,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"amount\",\n                    children: [\"\\u20A6\", parseFloat(request.amount).toLocaleString()]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 498,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"payment-method-info\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"method-name\",\n                        children: (_request$payment_meth3 = request.payment_method) === null || _request$payment_meth3 === void 0 ? void 0 : _request$payment_meth3.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"method-details\",\n                        children: formatPaymentMethodDetails(request.payment_method)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 502,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: getStatusBadgeClass(request.status),\n                      children: request.status.charAt(0).toUpperCase() + request.status.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"view-proof-btn\",\n                      onClick: () => handleViewProof(request),\n                      \"aria-label\": \"View payment proof\",\n                      children: \"View\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 513,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    children: request.status === 'pending' && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"action-buttons\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"approve-btn\",\n                        onClick: () => handleUpdateStatus(request.request_id, 'approved', request.username),\n                        \"aria-label\": \"Approve payment request\",\n                        children: \"Approve\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 524,\n                        columnNumber: 61\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        className: \"reject-btn\",\n                        onClick: () => handleUpdateStatus(request.request_id, 'rejected', request.username),\n                        \"aria-label\": \"Reject payment request\",\n                        children: \"Reject\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 531,\n                        columnNumber: 61\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 523,\n                      columnNumber: 57\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 521,\n                    columnNumber: 49\n                  }, this)]\n                }, request.request_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 480,\n                  columnNumber: 45\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 33\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 29\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 546,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 451,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"no-requests\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No credit requests found.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 551,\n      columnNumber: 17\n    }, this), showProofModal && /*#__PURE__*/_jsxDEV(ProofModal, {\n      proof: selectedProof,\n      onClose: () => {\n        setShowProofModal(false);\n        setSelectedProof(null);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 557,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 444,\n    columnNumber: 9\n  }, this);\n}\n_s(TransactionManagement, \"NQYy5BrVS0QzTH124MLGpmWp/ww=\");\n_c = TransactionManagement;\nexport default TransactionManagement;\nvar _c;\n$RefreshReg$(_c, \"TransactionManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "axios", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TransactionManagement", "_s", "creditRequests", "setCreditRequests", "loading", "setLoading", "error", "setError", "showProofModal", "setShowProofModal", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedProof", "successMessage", "setSuccessMessage", "isMobile", "setIsMobile", "window", "innerWidth", "isTabletView", "setIsTabletView", "sortConfig", "setSortConfig", "key", "direction", "currentPage", "setCurrentPage", "itemsPerPage", "tableRef", "hasScroll", "setHasScroll", "handleResize", "addEventListener", "removeEventListener", "fetchCreditRequests", "checkScroll", "current", "scrollWidth", "clientWidth", "sortData", "data", "sort", "a", "b", "aValue", "split", "reduce", "obj", "k", "bValue", "parseFloat", "toLowerCase", "handleSort", "prevConfig", "getSortedAndPaginatedData", "sortedData", "startIndex", "slice", "getSortIndicator", "totalPages", "Math", "ceil", "length", "pageNumbers", "Array", "from", "_", "i", "handlePageChange", "pageNumber", "response", "get", "success", "requests", "Error", "message", "err", "console", "handleViewProof", "request", "url", "request_id", "user_id", "amount", "date", "created_at", "status", "username", "show<PERSON><PERSON><PERSON>", "isSuccess", "setTimeout", "handleUpdateStatus", "requestId", "newStatus", "post", "action", "formatPaymentMethodDetails", "method", "fields", "isArray", "JSON", "parse", "mainFields", "map", "field", "fieldValue", "join", "name", "ProofModal", "proof", "onClose", "className", "onClick", "children", "e", "stopPropagation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "toLocaleString", "formatDate", "full", "getStatusBadgeClass", "char<PERSON>t", "toUpperCase", "src", "alt", "dateString", "Date", "short", "Intl", "DateTimeFormat", "month", "day", "hour", "minute", "format", "minimalist", "hour12", "dateStyle", "timeStyle", "MobileCard", "index", "_request$payment_meth", "formattedDate", "title", "payment_method", "expires_at", "TabletCard", "_request$payment_meth2", "TableHeader", "Pagination", "disabled", "number", "ScrollHint", "xmlns", "viewBox", "fill", "fillRule", "d", "clipRule", "displayData", "ref", "_request$payment_meth3", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/TransactionManagement.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport axios from '../utils/axiosConfig';\nimport './TransactionManagement.css';\n\nfunction TransactionManagement() {\n    const [creditRequests, setCreditRequests] = useState([]);\n    const [loading, setLoading] = useState(true);\n    const [error, setError] = useState('');\n    const [showProofModal, setShowProofModal] = useState(false);\n    const [selectedProof, setSelectedProof] = useState(null);\n    const [successMessage, setSuccessMessage] = useState('');\n    const [isMobile, setIsMobile] = useState(window.innerWidth <= 640);\n    const [isTabletView, setIsTabletView] = useState(false);\n    \n    // Sorting and pagination states\n    const [sortConfig, setSortConfig] = useState({ key: 'created_at', direction: 'desc' });\n    const [currentPage, setCurrentPage] = useState(1);\n    const [itemsPerPage] = useState(10);\n\n    const tableRef = useRef(null);\n    const [hasScroll, setHasScroll] = useState(false);\n\n    useEffect(() => {\n        const handleResize = () => {\n            setIsMobile(window.innerWidth <= 640);\n            setIsTabletView(window.innerWidth > 640 && window.innerWidth <= 1366);\n        };\n\n        handleResize();\n        window.addEventListener('resize', handleResize);\n        return () => window.removeEventListener('resize', handleResize);\n    }, []);\n\n    useEffect(() => {\n        fetchCreditRequests();\n    }, []);\n\n    useEffect(() => {\n        const checkScroll = () => {\n            if (tableRef.current) {\n                setHasScroll(tableRef.current.scrollWidth > tableRef.current.clientWidth);\n            }\n        };\n\n        checkScroll();\n        window.addEventListener('resize', checkScroll);\n        return () => window.removeEventListener('resize', checkScroll);\n    }, [creditRequests]);\n\n    // Sorting function\n    const sortData = (data, key, direction) => {\n        return [...data].sort((a, b) => {\n            let aValue = key.split('.').reduce((obj, k) => obj?.[k], a);\n            let bValue = key.split('.').reduce((obj, k) => obj?.[k], b);\n\n            // Handle special cases\n            if (key === 'amount') {\n                aValue = parseFloat(aValue);\n                bValue = parseFloat(bValue);\n            } else if (typeof aValue === 'string') {\n                aValue = aValue.toLowerCase();\n                bValue = bValue.toLowerCase();\n            }\n\n            if (aValue < bValue) return direction === 'asc' ? -1 : 1;\n            if (aValue > bValue) return direction === 'asc' ? 1 : -1;\n            return 0;\n        });\n    };\n\n    // Handle sort\n    const handleSort = (key) => {\n        setSortConfig(prevConfig => ({\n            key,\n            direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n        }));\n    };\n\n    // Get sorted and paginated data\n    const getSortedAndPaginatedData = () => {\n        const sortedData = sortData(creditRequests, sortConfig.key, sortConfig.direction);\n        const startIndex = (currentPage - 1) * itemsPerPage;\n        return sortedData.slice(startIndex, startIndex + itemsPerPage);\n    };\n\n    // Get sort direction indicator\n    const getSortIndicator = (key) => {\n        if (sortConfig.key !== key) return '↕';\n        return sortConfig.direction === 'asc' ? '↑' : '↓';\n    };\n\n    // Pagination controls\n    const totalPages = Math.ceil(creditRequests.length / itemsPerPage);\n    const pageNumbers = Array.from({ length: totalPages }, (_, i) => i + 1);\n\n    const handlePageChange = (pageNumber) => {\n        setCurrentPage(pageNumber);\n    };\n\n    const fetchCreditRequests = async () => {\n        try {\n            setLoading(true);\n            setError('');\n            \n            const response = await axios.get('admin/get_credit_requests.php');\n            \n            if (response.data.success) {\n                setCreditRequests(response.data.requests || []);\n            } else {\n                throw new Error(response.data.message || 'Failed to fetch credit requests');\n            }\n        } catch (err) {\n            console.error('Error fetching credit requests:', err);\n            setError(err.message || 'Failed to load credit requests. Please try again.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleViewProof = (request) => {\n        setSelectedProof({\n            url: `/backend/handlers/get_proof_image.php?request_id=${request.request_id}&user_id=${request.user_id}`,\n            amount: request.amount,\n            date: request.created_at,\n            status: request.status,\n            username: request.username\n        });\n        setShowProofModal(true);\n    };\n\n    const showAlert = (message, isSuccess = true) => {\n        if (isSuccess) {\n            setSuccessMessage(message);\n            setError('');\n        } else {\n            setError(message);\n            setSuccessMessage('');\n        }\n\n        setTimeout(() => {\n            if (isSuccess) {\n                setSuccessMessage('');\n            } else {\n                setError('');\n            }\n        }, 3000);\n    };\n\n    const handleUpdateStatus = async (requestId, newStatus, username) => {\n        try {\n            const response = await axios.post('admin/update_credit_request.php', {\n                request_id: requestId,\n                status: newStatus\n            });\n\n            if (response.data.success) {\n                const action = newStatus === 'approved' ? 'approved' : 'rejected';\n                showAlert(`Successfully ${action} credit request for ${username}`);\n                fetchCreditRequests();\n            } else {\n                throw new Error(response.data.message || 'Failed to update request status');\n            }\n        } catch (err) {\n            console.error('Error updating request status:', err);\n            showAlert(err.message || 'Failed to update request status. Please try again.', false);\n        }\n    };\n\n    const formatPaymentMethodDetails = (method) => {\n        if (!method || !method.fields) return 'N/A';\n        \n        try {\n            const fields = Array.isArray(method.fields) ? method.fields : JSON.parse(method.fields);\n            const mainFields = fields.slice(0, 2); // Show first two fields\n            \n            return mainFields.map(field => field.fieldValue).join(' - ');\n        } catch (err) {\n            console.error('Error formatting payment method:', err);\n            return method.name || 'N/A';\n        }\n    };\n\n    const ProofModal = ({ proof, onClose }) => {\n        if (!proof) return null;\n\n        return (\n            <div className=\"modal-overlay\" onClick={onClose}>\n                <div className=\"proof-modal\" onClick={e => e.stopPropagation()}>\n                    <button className=\"close-modal\" onClick={onClose}>&times;</button>\n                    <div className=\"proof-details\">\n                        <h3>Payment Proof - {proof.username}</h3>\n                        <p>Amount: ₦{parseFloat(proof.amount).toLocaleString()}</p>\n                        <p>Date: {formatDate(proof.date).full}</p>\n                        <p>Status: <span className={getStatusBadgeClass(proof.status)}>\n                            {proof.status.charAt(0).toUpperCase() + proof.status.slice(1)}\n                        </span></p>\n                    </div>\n                    <div className=\"proof-image-container\">\n                        <img src={proof.url} alt=\"Payment Proof\" />\n                    </div>\n                </div>\n            </div>\n        );\n    };\n\n    const getStatusBadgeClass = (status) => {\n        switch (status) {\n            case 'approved':\n                return 'status-badge success';\n            case 'rejected':\n                return 'status-badge danger';\n            case 'expired':\n                return 'status-badge warning';\n            default:\n                return 'status-badge pending';\n        }\n    };\n\n    const formatDate = (dateString) => {\n        const date = new Date(dateString);\n        return {\n            short: new Intl.DateTimeFormat('en-US', {\n                month: 'short',\n                day: 'numeric',\n                hour: '2-digit',\n                minute: '2-digit'\n            }).format(date),\n            minimalist: new Intl.DateTimeFormat('en-US', {\n                month: 'short',\n                day: 'numeric',\n                hour: 'numeric',\n                minute: 'numeric',\n                hour12: true\n            }).format(date),\n            full: new Intl.DateTimeFormat('en-US', {\n                dateStyle: 'full',\n                timeStyle: 'long'\n            }).format(date)\n        };\n    };\n\n    const MobileCard = ({ request, index }) => {\n        const formattedDate = formatDate(request.created_at);\n        \n        return (\n            <div className=\"request-card\">\n                <div className=\"card-header\">\n                    <div className=\"card-amount\">₦{parseFloat(request.amount).toLocaleString()}</div>\n                    <div className=\"card-date\" title={formattedDate.full}>{formattedDate.short}</div>\n                </div>\n                <div className=\"card-content\">\n                    <div className=\"card-field\">\n                        <span className=\"card-label\">User</span>\n                        <span className=\"card-value\">{request.username}</span>\n                    </div>\n                    <div className=\"card-field\">\n                        <span className=\"card-label\">Payment Method</span>\n                        <span className=\"card-value\">{request.payment_method?.name}</span>\n                    </div>\n                    <div className=\"card-field\">\n                        <span className=\"card-label\">Status</span>\n                        <span className={getStatusBadgeClass(request.status)}>\n                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}\n                        </span>\n                    </div>\n                    {request.expires_at && (\n                        <div className=\"card-field\">\n                            <span className=\"card-label\">Expires</span>\n                            <span className=\"card-value\">{formatDate(request.expires_at).short}</span>\n                        </div>\n                    )}\n                </div>\n                <div className=\"card-actions\">\n                    <button \n                        className=\"view-proof-btn\"\n                        onClick={() => handleViewProof(request)}\n                        aria-label=\"View payment proof\"\n                    >\n                        View Proof\n                    </button>\n                    {request.status === 'pending' && (\n                        <>\n                            <button \n                                className=\"approve-btn\"\n                                onClick={() => handleUpdateStatus(request.request_id, 'approved', request.username)}\n                                aria-label=\"Approve payment request\"\n                            >\n                                Approve\n                            </button>\n                            <button \n                                className=\"reject-btn\"\n                                onClick={() => handleUpdateStatus(request.request_id, 'rejected', request.username)}\n                                aria-label=\"Reject payment request\"\n                            >\n                                Reject\n                            </button>\n                        </>\n                    )}\n                </div>\n            </div>\n        );\n    };\n\n    const TabletCard = ({ request, index }) => {\n        const formattedDate = formatDate(request.created_at);\n        \n        return (\n            <div className=\"tablet-request-card\">\n                <div className=\"tablet-card-header\">\n                    <div className=\"tablet-card-row\">\n                        <div className=\"tablet-card-number\">#{startIndex + index + 1}</div>\n                        <div className=\"tablet-card-date\" title={formattedDate.full}>\n                            {formattedDate.short}\n                        </div>\n                    </div>\n                    <div className=\"tablet-card-row\">\n                        <div className=\"tablet-card-username\">{request.username}</div>\n                        <div className=\"tablet-card-amount\">₦{parseFloat(request.amount).toLocaleString()}</div>\n                    </div>\n                </div>\n                <div className=\"tablet-card-content\">\n                    <div className=\"tablet-card-method\">\n                        <div className=\"payment-method-info\">\n                            <div className=\"method-name\">{request.payment_method?.name}</div>\n                            <div className=\"method-details\">\n                                {formatPaymentMethodDetails(request.payment_method)}\n                            </div>\n                        </div>\n                    </div>\n                    <div className=\"tablet-card-status\">\n                        <span className={getStatusBadgeClass(request.status)}>\n                            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}\n                        </span>\n                    </div>\n                    <div className=\"tablet-card-expires\" title={request.expires_at ? formatDate(request.expires_at).full : ''}>\n                        Expires: {request.expires_at ? formatDate(request.expires_at).short : 'N/A'}\n                    </div>\n                </div>\n                <div className=\"tablet-card-actions\">\n                    <button \n                        className=\"view-proof-btn\"\n                        onClick={() => handleViewProof(request)}\n                        aria-label=\"View payment proof\"\n                    >\n                        View Proof\n                    </button>\n                    {request.status === 'pending' && (\n                        <>\n                            <button \n                                className=\"approve-btn\"\n                                onClick={() => handleUpdateStatus(request.request_id, 'approved', request.username)}\n                                aria-label=\"Approve payment request\"\n                            >\n                                Approve\n                            </button>\n                            <button \n                                className=\"reject-btn\"\n                                onClick={() => handleUpdateStatus(request.request_id, 'rejected', request.username)}\n                                aria-label=\"Reject payment request\"\n                            >\n                                Reject\n                            </button>\n                        </>\n                    )}\n                </div>\n            </div>\n        );\n    };\n\n    const TableHeader = () => (\n        <thead>\n            <tr>\n                <th className=\"row-number\">#</th>\n                <th onClick={() => handleSort('created_at')} className=\"sortable date-column\">\n                    Date & Expires {getSortIndicator('created_at')}\n                </th>\n                <th onClick={() => handleSort('username')} className=\"sortable\">\n                    User {getSortIndicator('username')}\n                </th>\n                <th onClick={() => handleSort('amount')} className=\"sortable\">\n                    Amount {getSortIndicator('amount')}\n                </th>\n                <th onClick={() => handleSort('payment_method.name')} className=\"sortable\">\n                    Payment Method {getSortIndicator('payment_method.name')}\n                </th>\n                <th onClick={() => handleSort('status')} className=\"sortable\">\n                    Status {getSortIndicator('status')}\n                </th>\n                <th>Proof</th>\n                <th>Actions</th>\n            </tr>\n        </thead>\n    );\n\n    const Pagination = () => (\n        <div className=\"pagination\">\n            <button \n                onClick={() => handlePageChange(currentPage - 1)}\n                disabled={currentPage === 1}\n                className=\"pagination-button\"\n            >\n                Previous\n            </button>\n            <div className=\"page-numbers\">\n                {pageNumbers.map(number => (\n                    <button\n                        key={number}\n                        onClick={() => handlePageChange(number)}\n                        className={`pagination-button ${currentPage === number ? 'active' : ''}`}\n                    >\n                        {number}\n                    </button>\n                ))}\n            </div>\n            <button \n                onClick={() => handlePageChange(currentPage + 1)}\n                disabled={currentPage === totalPages}\n                className=\"pagination-button\"\n            >\n                Next\n            </button>\n        </div>\n    );\n\n    const ScrollHint = () => (\n        hasScroll && !isMobile && (\n            <div className=\"scroll-hint\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z\" clipRule=\"evenodd\" />\n                </svg>\n                Scroll horizontally to see more\n            </div>\n        )\n    );\n\n    if (loading) {\n        return <div className=\"loading\">Loading...</div>;\n    }\n\n    const displayData = getSortedAndPaginatedData();\n    const startIndex = (currentPage - 1) * itemsPerPage;\n\n    return (\n        <div className=\"transaction-management-container\">\n            <h1>Credit Request Management</h1>\n\n            {error && <div className=\"error-message\">{error}</div>}\n            {successMessage && <div className=\"success-message\">{successMessage}</div>}\n\n            {creditRequests.length > 0 ? (\n                <div className={`credit-requests-table ${hasScroll ? 'has-scroll' : ''}`} ref={tableRef}>\n                    <ScrollHint />\n                    {isMobile ? (\n                        <div className=\"mobile-cards\">\n                            {displayData.map((request, index) => (\n                                <MobileCard \n                                    key={request.request_id} \n                                    request={request} \n                                    index={startIndex + index + 1}\n                                />\n                            ))}\n                        </div>\n                    ) : isTabletView ? (\n                        <div className=\"tablet-cards\">\n                            {displayData.map((request, index) => (\n                                <TabletCard \n                                    key={request.request_id} \n                                    request={request} \n                                    index={index}\n                                />\n                            ))}\n                        </div>\n                    ) : (\n                        <>\n                            <div className=\"table-container\">\n                                <table>\n                                    <TableHeader />\n                                    <tbody>\n                                        {displayData.map((request, index) => (\n                                            <tr key={request.request_id}>\n                                                <td className=\"row-number\">{startIndex + index + 1}</td>\n                                                <td className=\"date-expires\" \n                                                    title={`Created: ${formatDate(request.created_at).full}${request.expires_at ? `\\nExpires: ${formatDate(request.expires_at).full}` : ''}`}>\n                                                    <div className=\"date-content\">\n                                                        <div className=\"date-line\">\n                                                            <span className=\"date-label\">Date:</span>\n                                                            <span className=\"created-date\">{formatDate(request.created_at).minimalist}</span>\n                                                        </div>\n                                                        {request.expires_at && (\n                                                            <div className=\"expires-line\">\n                                                                <span className=\"date-label\">Exp:</span>\n                                                                <span className=\"expires-date\">{formatDate(request.expires_at).minimalist}</span>\n                                                            </div>\n                                                        )}\n                                                    </div>\n                                                </td>\n                                                <td className=\"username-display\">{request.username}</td>\n                                                <td className=\"amount\">₦{parseFloat(request.amount).toLocaleString()}</td>\n                                                <td>\n                                                    <div className=\"payment-method-info\">\n                                                        <div className=\"method-name\">{request.payment_method?.name}</div>\n                                                        <div className=\"method-details\">\n                                                            {formatPaymentMethodDetails(request.payment_method)}\n                                                        </div>\n                                                    </div>\n                                                </td>\n                                                <td>\n                                                    <span className={getStatusBadgeClass(request.status)}>\n                                                        {request.status.charAt(0).toUpperCase() + request.status.slice(1)}\n                                                    </span>\n                                                </td>\n                                                <td>\n                                                    <button \n                                                        className=\"view-proof-btn\"\n                                                        onClick={() => handleViewProof(request)}\n                                                        aria-label=\"View payment proof\"\n                                                    >\n                                                        View\n                                                    </button>\n                                                </td>\n                                                <td>\n                                                    {request.status === 'pending' && (\n                                                        <div className=\"action-buttons\">\n                                                            <button \n                                                                className=\"approve-btn\"\n                                                                onClick={() => handleUpdateStatus(request.request_id, 'approved', request.username)}\n                                                                aria-label=\"Approve payment request\"\n                                                            >\n                                                                Approve\n                                                            </button>\n                                                            <button \n                                                                className=\"reject-btn\"\n                                                                onClick={() => handleUpdateStatus(request.request_id, 'rejected', request.username)}\n                                                                aria-label=\"Reject payment request\"\n                                                            >\n                                                                Reject\n                                                            </button>\n                                                        </div>\n                                                    )}\n                                                </td>\n                                            </tr>\n                                        ))}\n                                    </tbody>\n                                </table>\n                            </div>\n                            <Pagination />\n                        </>\n                    )}\n                </div>\n            ) : (\n                <div className=\"no-requests\">\n                    <p>No credit requests found.</p>\n                </div>\n            )}\n\n            {showProofModal && (\n                <ProofModal \n                    proof={selectedProof} \n                    onClose={() => {\n                        setShowProofModal(false);\n                        setSelectedProof(null);\n                    }}\n                />\n            )}\n        </div>\n    );\n}\n\nexport default TransactionManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,KAAK,MAAM,sBAAsB;AACxC,OAAO,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErC,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC7B,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACgB,cAAc,EAAEC,iBAAiB,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACoB,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAACwB,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAAC;IAAE8B,GAAG,EAAE,YAAY;IAAEC,SAAS,EAAE;EAAO,CAAC,CAAC;EACtF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACkC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAEnC,MAAMmC,QAAQ,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACZ,MAAMqC,YAAY,GAAGA,CAAA,KAAM;MACvBf,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;MACrCE,eAAe,CAACH,MAAM,CAACC,UAAU,GAAG,GAAG,IAAID,MAAM,CAACC,UAAU,IAAI,IAAI,CAAC;IACzE,CAAC;IAEDa,YAAY,CAAC,CAAC;IACdd,MAAM,CAACe,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMd,MAAM,CAACgB,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACnE,CAAC,EAAE,EAAE,CAAC;EAENrC,SAAS,CAAC,MAAM;IACZwC,mBAAmB,CAAC,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAENxC,SAAS,CAAC,MAAM;IACZ,MAAMyC,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIP,QAAQ,CAACQ,OAAO,EAAE;QAClBN,YAAY,CAACF,QAAQ,CAACQ,OAAO,CAACC,WAAW,GAAGT,QAAQ,CAACQ,OAAO,CAACE,WAAW,CAAC;MAC7E;IACJ,CAAC;IAEDH,WAAW,CAAC,CAAC;IACblB,MAAM,CAACe,gBAAgB,CAAC,QAAQ,EAAEG,WAAW,CAAC;IAC9C,OAAO,MAAMlB,MAAM,CAACgB,mBAAmB,CAAC,QAAQ,EAAEE,WAAW,CAAC;EAClE,CAAC,EAAE,CAAChC,cAAc,CAAC,CAAC;;EAEpB;EACA,MAAMoC,QAAQ,GAAGA,CAACC,IAAI,EAAEjB,GAAG,EAAEC,SAAS,KAAK;IACvC,OAAO,CAAC,GAAGgB,IAAI,CAAC,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MAC5B,IAAIC,MAAM,GAAGrB,GAAG,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAGC,CAAC,CAAC,EAAEN,CAAC,CAAC;MAC3D,IAAIO,MAAM,GAAG1B,GAAG,CAACsB,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,CAAC,KAAKD,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAGC,CAAC,CAAC,EAAEL,CAAC,CAAC;;MAE3D;MACA,IAAIpB,GAAG,KAAK,QAAQ,EAAE;QAClBqB,MAAM,GAAGM,UAAU,CAACN,MAAM,CAAC;QAC3BK,MAAM,GAAGC,UAAU,CAACD,MAAM,CAAC;MAC/B,CAAC,MAAM,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAE;QACnCA,MAAM,GAAGA,MAAM,CAACO,WAAW,CAAC,CAAC;QAC7BF,MAAM,GAAGA,MAAM,CAACE,WAAW,CAAC,CAAC;MACjC;MAEA,IAAIP,MAAM,GAAGK,MAAM,EAAE,OAAOzB,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MACxD,IAAIoB,MAAM,GAAGK,MAAM,EAAE,OAAOzB,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MACxD,OAAO,CAAC;IACZ,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAM4B,UAAU,GAAI7B,GAAG,IAAK;IACxBD,aAAa,CAAC+B,UAAU,KAAK;MACzB9B,GAAG;MACHC,SAAS,EAAE6B,UAAU,CAAC9B,GAAG,KAAKA,GAAG,IAAI8B,UAAU,CAAC7B,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG;IACnF,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;EACA,MAAM8B,yBAAyB,GAAGA,CAAA,KAAM;IACpC,MAAMC,UAAU,GAAGhB,QAAQ,CAACpC,cAAc,EAAEkB,UAAU,CAACE,GAAG,EAAEF,UAAU,CAACG,SAAS,CAAC;IACjF,MAAMgC,UAAU,GAAG,CAAC/B,WAAW,GAAG,CAAC,IAAIE,YAAY;IACnD,OAAO4B,UAAU,CAACE,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG7B,YAAY,CAAC;EAClE,CAAC;;EAED;EACA,MAAM+B,gBAAgB,GAAInC,GAAG,IAAK;IAC9B,IAAIF,UAAU,CAACE,GAAG,KAAKA,GAAG,EAAE,OAAO,GAAG;IACtC,OAAOF,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,GAAG,GAAG,GAAG;EACrD,CAAC;;EAED;EACA,MAAMmC,UAAU,GAAGC,IAAI,CAACC,IAAI,CAAC1D,cAAc,CAAC2D,MAAM,GAAGnC,YAAY,CAAC;EAClE,MAAMoC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEH,MAAM,EAAEH;EAAW,CAAC,EAAE,CAACO,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAEvE,MAAMC,gBAAgB,GAAIC,UAAU,IAAK;IACrC3C,cAAc,CAAC2C,UAAU,CAAC;EAC9B,CAAC;EAED,MAAMnC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACA5B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,EAAE,CAAC;MAEZ,MAAM8D,QAAQ,GAAG,MAAM1E,KAAK,CAAC2E,GAAG,CAAC,+BAA+B,CAAC;MAEjE,IAAID,QAAQ,CAAC9B,IAAI,CAACgC,OAAO,EAAE;QACvBpE,iBAAiB,CAACkE,QAAQ,CAAC9B,IAAI,CAACiC,QAAQ,IAAI,EAAE,CAAC;MACnD,CAAC,MAAM;QACH,MAAM,IAAIC,KAAK,CAACJ,QAAQ,CAAC9B,IAAI,CAACmC,OAAO,IAAI,iCAAiC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACtE,KAAK,CAAC,iCAAiC,EAAEqE,GAAG,CAAC;MACrDpE,QAAQ,CAACoE,GAAG,CAACD,OAAO,IAAI,mDAAmD,CAAC;IAChF,CAAC,SAAS;MACNrE,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMwE,eAAe,GAAIC,OAAO,IAAK;IACjCnE,gBAAgB,CAAC;MACboE,GAAG,EAAE,oDAAoDD,OAAO,CAACE,UAAU,YAAYF,OAAO,CAACG,OAAO,EAAE;MACxGC,MAAM,EAAEJ,OAAO,CAACI,MAAM;MACtBC,IAAI,EAAEL,OAAO,CAACM,UAAU;MACxBC,MAAM,EAAEP,OAAO,CAACO,MAAM;MACtBC,QAAQ,EAAER,OAAO,CAACQ;IACtB,CAAC,CAAC;IACF7E,iBAAiB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAM8E,SAAS,GAAGA,CAACb,OAAO,EAAEc,SAAS,GAAG,IAAI,KAAK;IAC7C,IAAIA,SAAS,EAAE;MACX3E,iBAAiB,CAAC6D,OAAO,CAAC;MAC1BnE,QAAQ,CAAC,EAAE,CAAC;IAChB,CAAC,MAAM;MACHA,QAAQ,CAACmE,OAAO,CAAC;MACjB7D,iBAAiB,CAAC,EAAE,CAAC;IACzB;IAEA4E,UAAU,CAAC,MAAM;MACb,IAAID,SAAS,EAAE;QACX3E,iBAAiB,CAAC,EAAE,CAAC;MACzB,CAAC,MAAM;QACHN,QAAQ,CAAC,EAAE,CAAC;MAChB;IACJ,CAAC,EAAE,IAAI,CAAC;EACZ,CAAC;EAED,MAAMmF,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,SAAS,EAAEN,QAAQ,KAAK;IACjE,IAAI;MACA,MAAMjB,QAAQ,GAAG,MAAM1E,KAAK,CAACkG,IAAI,CAAC,iCAAiC,EAAE;QACjEb,UAAU,EAAEW,SAAS;QACrBN,MAAM,EAAEO;MACZ,CAAC,CAAC;MAEF,IAAIvB,QAAQ,CAAC9B,IAAI,CAACgC,OAAO,EAAE;QACvB,MAAMuB,MAAM,GAAGF,SAAS,KAAK,UAAU,GAAG,UAAU,GAAG,UAAU;QACjEL,SAAS,CAAC,gBAAgBO,MAAM,uBAAuBR,QAAQ,EAAE,CAAC;QAClErD,mBAAmB,CAAC,CAAC;MACzB,CAAC,MAAM;QACH,MAAM,IAAIwC,KAAK,CAACJ,QAAQ,CAAC9B,IAAI,CAACmC,OAAO,IAAI,iCAAiC,CAAC;MAC/E;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACtE,KAAK,CAAC,gCAAgC,EAAEqE,GAAG,CAAC;MACpDY,SAAS,CAACZ,GAAG,CAACD,OAAO,IAAI,oDAAoD,EAAE,KAAK,CAAC;IACzF;EACJ,CAAC;EAED,MAAMqB,0BAA0B,GAAIC,MAAM,IAAK;IAC3C,IAAI,CAACA,MAAM,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE,OAAO,KAAK;IAE3C,IAAI;MACA,MAAMA,MAAM,GAAGlC,KAAK,CAACmC,OAAO,CAACF,MAAM,CAACC,MAAM,CAAC,GAAGD,MAAM,CAACC,MAAM,GAAGE,IAAI,CAACC,KAAK,CAACJ,MAAM,CAACC,MAAM,CAAC;MACvF,MAAMI,UAAU,GAAGJ,MAAM,CAACzC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;MAEvC,OAAO6C,UAAU,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACC,UAAU,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC;IAChE,CAAC,CAAC,OAAO9B,GAAG,EAAE;MACVC,OAAO,CAACtE,KAAK,CAAC,kCAAkC,EAAEqE,GAAG,CAAC;MACtD,OAAOqB,MAAM,CAACU,IAAI,IAAI,KAAK;IAC/B;EACJ,CAAC;EAED,MAAMC,UAAU,GAAGA,CAAC;IAAEC,KAAK;IAAEC;EAAQ,CAAC,KAAK;IACvC,IAAI,CAACD,KAAK,EAAE,OAAO,IAAI;IAEvB,oBACI/G,OAAA;MAAKiH,SAAS,EAAC,eAAe;MAACC,OAAO,EAAEF,OAAQ;MAAAG,QAAA,eAC5CnH,OAAA;QAAKiH,SAAS,EAAC,aAAa;QAACC,OAAO,EAAEE,CAAC,IAAIA,CAAC,CAACC,eAAe,CAAC,CAAE;QAAAF,QAAA,gBAC3DnH,OAAA;UAAQiH,SAAS,EAAC,aAAa;UAACC,OAAO,EAAEF,OAAQ;UAAAG,QAAA,EAAC;QAAO;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAClEzH,OAAA;UAAKiH,SAAS,EAAC,eAAe;UAAAE,QAAA,gBAC1BnH,OAAA;YAAAmH,QAAA,GAAI,kBAAgB,EAACJ,KAAK,CAACtB,QAAQ;UAAA;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzCzH,OAAA;YAAAmH,QAAA,GAAG,gBAAS,EAAC/D,UAAU,CAAC2D,KAAK,CAAC1B,MAAM,CAAC,CAACqC,cAAc,CAAC,CAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC3DzH,OAAA;YAAAmH,QAAA,GAAG,QAAM,EAACQ,UAAU,CAACZ,KAAK,CAACzB,IAAI,CAAC,CAACsC,IAAI;UAAA;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1CzH,OAAA;YAAAmH,QAAA,GAAG,UAAQ,eAAAnH,OAAA;cAAMiH,SAAS,EAAEY,mBAAmB,CAACd,KAAK,CAACvB,MAAM,CAAE;cAAA2B,QAAA,EACzDJ,KAAK,CAACvB,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGhB,KAAK,CAACvB,MAAM,CAAC7B,KAAK,CAAC,CAAC;YAAC;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,uBAAuB;UAAAE,QAAA,eAClCnH,OAAA;YAAKgI,GAAG,EAAEjB,KAAK,CAAC7B,GAAI;YAAC+C,GAAG,EAAC;UAAe;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,MAAMI,mBAAmB,GAAIrC,MAAM,IAAK;IACpC,QAAQA,MAAM;MACV,KAAK,UAAU;QACX,OAAO,sBAAsB;MACjC,KAAK,UAAU;QACX,OAAO,qBAAqB;MAChC,KAAK,SAAS;QACV,OAAO,sBAAsB;MACjC;QACI,OAAO,sBAAsB;IACrC;EACJ,CAAC;EAED,MAAMmC,UAAU,GAAIO,UAAU,IAAK;IAC/B,MAAM5C,IAAI,GAAG,IAAI6C,IAAI,CAACD,UAAU,CAAC;IACjC,OAAO;MACHE,KAAK,EAAE,IAAIC,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QACpCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACZ,CAAC,CAAC,CAACC,MAAM,CAACrD,IAAI,CAAC;MACfsD,UAAU,EAAE,IAAIP,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QACzCC,KAAK,EAAE,OAAO;QACdC,GAAG,EAAE,SAAS;QACdC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBG,MAAM,EAAE;MACZ,CAAC,CAAC,CAACF,MAAM,CAACrD,IAAI,CAAC;MACfsC,IAAI,EAAE,IAAIS,IAAI,CAACC,cAAc,CAAC,OAAO,EAAE;QACnCQ,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE;MACf,CAAC,CAAC,CAACJ,MAAM,CAACrD,IAAI;IAClB,CAAC;EACL,CAAC;EAED,MAAM0D,UAAU,GAAGA,CAAC;IAAE/D,OAAO;IAAEgE;EAAM,CAAC,KAAK;IAAA,IAAAC,qBAAA;IACvC,MAAMC,aAAa,GAAGxB,UAAU,CAAC1C,OAAO,CAACM,UAAU,CAAC;IAEpD,oBACIvF,OAAA;MAAKiH,SAAS,EAAC,cAAc;MAAAE,QAAA,gBACzBnH,OAAA;QAAKiH,SAAS,EAAC,aAAa;QAAAE,QAAA,gBACxBnH,OAAA;UAAKiH,SAAS,EAAC,aAAa;UAAAE,QAAA,GAAC,QAAC,EAAC/D,UAAU,CAAC6B,OAAO,CAACI,MAAM,CAAC,CAACqC,cAAc,CAAC,CAAC;QAAA;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjFzH,OAAA;UAAKiH,SAAS,EAAC,WAAW;UAACmC,KAAK,EAAED,aAAa,CAACvB,IAAK;UAAAT,QAAA,EAAEgC,aAAa,CAACf;QAAK;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChF,CAAC,eACNzH,OAAA;QAAKiH,SAAS,EAAC,cAAc;QAAAE,QAAA,gBACzBnH,OAAA;UAAKiH,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBnH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAI;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxCzH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAElC,OAAO,CAACQ;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBnH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAc;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDzH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,GAAA+B,qBAAA,GAAEjE,OAAO,CAACoE,cAAc,cAAAH,qBAAA,uBAAtBA,qBAAA,CAAwBrC;UAAI;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBnH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAM;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC1CzH,OAAA;YAAMiH,SAAS,EAAEY,mBAAmB,CAAC5C,OAAO,CAACO,MAAM,CAAE;YAAA2B,QAAA,EAChDlC,OAAO,CAACO,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG9C,OAAO,CAACO,MAAM,CAAC7B,KAAK,CAAC,CAAC;UAAC;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EACLxC,OAAO,CAACqE,UAAU,iBACftJ,OAAA;UAAKiH,SAAS,EAAC,YAAY;UAAAE,QAAA,gBACvBnH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAC;UAAO;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3CzH,OAAA;YAAMiH,SAAS,EAAC,YAAY;YAAAE,QAAA,EAAEQ,UAAU,CAAC1C,OAAO,CAACqE,UAAU,CAAC,CAAClB;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzE,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACNzH,OAAA;QAAKiH,SAAS,EAAC,cAAc;QAAAE,QAAA,gBACzBnH,OAAA;UACIiH,SAAS,EAAC,gBAAgB;UAC1BC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACC,OAAO,CAAE;UACxC,cAAW,oBAAoB;UAAAkC,QAAA,EAClC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRxC,OAAO,CAACO,MAAM,KAAK,SAAS,iBACzBxF,OAAA,CAAAE,SAAA;UAAAiH,QAAA,gBACInH,OAAA;YACIiH,SAAS,EAAC,aAAa;YACvBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;YACpF,cAAW,yBAAyB;YAAA0B,QAAA,EACvC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzH,OAAA;YACIiH,SAAS,EAAC,YAAY;YACtBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;YACpF,cAAW,wBAAwB;YAAA0B,QAAA,EACtC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,MAAM8B,UAAU,GAAGA,CAAC;IAAEtE,OAAO;IAAEgE;EAAM,CAAC,KAAK;IAAA,IAAAO,sBAAA;IACvC,MAAML,aAAa,GAAGxB,UAAU,CAAC1C,OAAO,CAACM,UAAU,CAAC;IAEpD,oBACIvF,OAAA;MAAKiH,SAAS,EAAC,qBAAqB;MAAAE,QAAA,gBAChCnH,OAAA;QAAKiH,SAAS,EAAC,oBAAoB;QAAAE,QAAA,gBAC/BnH,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAAE,QAAA,gBAC5BnH,OAAA;YAAKiH,SAAS,EAAC,oBAAoB;YAAAE,QAAA,GAAC,GAAC,EAACzD,UAAU,GAAGuF,KAAK,GAAG,CAAC;UAAA;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnEzH,OAAA;YAAKiH,SAAS,EAAC,kBAAkB;YAACmC,KAAK,EAAED,aAAa,CAACvB,IAAK;YAAAT,QAAA,EACvDgC,aAAa,CAACf;UAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAAE,QAAA,gBAC5BnH,OAAA;YAAKiH,SAAS,EAAC,sBAAsB;YAAAE,QAAA,EAAElC,OAAO,CAACQ;UAAQ;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9DzH,OAAA;YAAKiH,SAAS,EAAC,oBAAoB;YAAAE,QAAA,GAAC,QAAC,EAAC/D,UAAU,CAAC6B,OAAO,CAACI,MAAM,CAAC,CAACqC,cAAc,CAAC,CAAC;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNzH,OAAA;QAAKiH,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChCnH,OAAA;UAAKiH,SAAS,EAAC,oBAAoB;UAAAE,QAAA,eAC/BnH,OAAA;YAAKiH,SAAS,EAAC,qBAAqB;YAAAE,QAAA,gBAChCnH,OAAA;cAAKiH,SAAS,EAAC,aAAa;cAAAE,QAAA,GAAAqC,sBAAA,GAAEvE,OAAO,CAACoE,cAAc,cAAAG,sBAAA,uBAAtBA,sBAAA,CAAwB3C;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjEzH,OAAA;cAAKiH,SAAS,EAAC,gBAAgB;cAAAE,QAAA,EAC1BjB,0BAA0B,CAACjB,OAAO,CAACoE,cAAc;YAAC;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,oBAAoB;UAAAE,QAAA,eAC/BnH,OAAA;YAAMiH,SAAS,EAAEY,mBAAmB,CAAC5C,OAAO,CAACO,MAAM,CAAE;YAAA2B,QAAA,EAChDlC,OAAO,CAACO,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG9C,OAAO,CAACO,MAAM,CAAC7B,KAAK,CAAC,CAAC;UAAC;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNzH,OAAA;UAAKiH,SAAS,EAAC,qBAAqB;UAACmC,KAAK,EAAEnE,OAAO,CAACqE,UAAU,GAAG3B,UAAU,CAAC1C,OAAO,CAACqE,UAAU,CAAC,CAAC1B,IAAI,GAAG,EAAG;UAAAT,QAAA,GAAC,WAC9F,EAAClC,OAAO,CAACqE,UAAU,GAAG3B,UAAU,CAAC1C,OAAO,CAACqE,UAAU,CAAC,CAAClB,KAAK,GAAG,KAAK;QAAA;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eACNzH,OAAA;QAAKiH,SAAS,EAAC,qBAAqB;QAAAE,QAAA,gBAChCnH,OAAA;UACIiH,SAAS,EAAC,gBAAgB;UAC1BC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACC,OAAO,CAAE;UACxC,cAAW,oBAAoB;UAAAkC,QAAA,EAClC;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EACRxC,OAAO,CAACO,MAAM,KAAK,SAAS,iBACzBxF,OAAA,CAAAE,SAAA;UAAAiH,QAAA,gBACInH,OAAA;YACIiH,SAAS,EAAC,aAAa;YACvBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;YACpF,cAAW,yBAAyB;YAAA0B,QAAA,EACvC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzH,OAAA;YACIiH,SAAS,EAAC,YAAY;YACtBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;YACpF,cAAW,wBAAwB;YAAA0B,QAAA,EACtC;UAED;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX,CACL;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEd,CAAC;EAED,MAAMgC,WAAW,GAAGA,CAAA,kBAChBzJ,OAAA;IAAAmH,QAAA,eACInH,OAAA;MAAAmH,QAAA,gBACInH,OAAA;QAAIiH,SAAS,EAAC,YAAY;QAAAE,QAAA,EAAC;MAAC;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjCzH,OAAA;QAAIkH,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,YAAY,CAAE;QAAC2D,SAAS,EAAC,sBAAsB;QAAAE,QAAA,GAAC,iBAC3D,EAACvD,gBAAgB,CAAC,YAAY,CAAC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,eACLzH,OAAA;QAAIkH,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,UAAU,CAAE;QAAC2D,SAAS,EAAC,UAAU;QAAAE,QAAA,GAAC,OACvD,EAACvD,gBAAgB,CAAC,UAAU,CAAC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACLzH,OAAA;QAAIkH,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,QAAQ,CAAE;QAAC2D,SAAS,EAAC,UAAU;QAAAE,QAAA,GAAC,SACnD,EAACvD,gBAAgB,CAAC,QAAQ,CAAC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACLzH,OAAA;QAAIkH,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,qBAAqB,CAAE;QAAC2D,SAAS,EAAC,UAAU;QAAAE,QAAA,GAAC,iBACxD,EAACvD,gBAAgB,CAAC,qBAAqB,CAAC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eACLzH,OAAA;QAAIkH,OAAO,EAAEA,CAAA,KAAM5D,UAAU,CAAC,QAAQ,CAAE;QAAC2D,SAAS,EAAC,UAAU;QAAAE,QAAA,GAAC,SACnD,EAACvD,gBAAgB,CAAC,QAAQ,CAAC;MAAA;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eACLzH,OAAA;QAAAmH,QAAA,EAAI;MAAK;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdzH,OAAA;QAAAmH,QAAA,EAAI;MAAO;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CACV;EAED,MAAMiC,UAAU,GAAGA,CAAA,kBACf1J,OAAA;IAAKiH,SAAS,EAAC,YAAY;IAAAE,QAAA,gBACvBnH,OAAA;MACIkH,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC3C,WAAW,GAAG,CAAC,CAAE;MACjDgI,QAAQ,EAAEhI,WAAW,KAAK,CAAE;MAC5BsF,SAAS,EAAC,mBAAmB;MAAAE,QAAA,EAChC;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eACTzH,OAAA;MAAKiH,SAAS,EAAC,cAAc;MAAAE,QAAA,EACxBlD,WAAW,CAACwC,GAAG,CAACmD,MAAM,iBACnB5J,OAAA;QAEIkH,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAACsF,MAAM,CAAE;QACxC3C,SAAS,EAAE,qBAAqBtF,WAAW,KAAKiI,MAAM,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAAzC,QAAA,EAExEyC;MAAM,GAJFA,MAAM;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKP,CACX;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eACNzH,OAAA;MACIkH,OAAO,EAAEA,CAAA,KAAM5C,gBAAgB,CAAC3C,WAAW,GAAG,CAAC,CAAE;MACjDgI,QAAQ,EAAEhI,WAAW,KAAKkC,UAAW;MACrCoD,SAAS,EAAC,mBAAmB;MAAAE,QAAA,EAChC;IAED;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CACR;EAED,MAAMoC,UAAU,GAAGA,CAAA,KACf9H,SAAS,IAAI,CAACd,QAAQ,iBAClBjB,OAAA;IAAKiH,SAAS,EAAC,aAAa;IAAAE,QAAA,gBACxBnH,OAAA;MAAK8J,KAAK,EAAC,4BAA4B;MAACC,OAAO,EAAC,WAAW;MAACC,IAAI,EAAC,cAAc;MAAA7C,QAAA,eAC3EnH,OAAA;QAAMiK,QAAQ,EAAC,SAAS;QAACC,CAAC,EAAC,qIAAqI;QAACC,QAAQ,EAAC;MAAS;QAAA7C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrL,CAAC,mCAEV;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAEZ;EAED,IAAIlH,OAAO,EAAE;IACT,oBAAOP,OAAA;MAAKiH,SAAS,EAAC,SAAS;MAAAE,QAAA,EAAC;IAAU;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACpD;EAEA,MAAM2C,WAAW,GAAG5G,yBAAyB,CAAC,CAAC;EAC/C,MAAME,UAAU,GAAG,CAAC/B,WAAW,GAAG,CAAC,IAAIE,YAAY;EAEnD,oBACI7B,OAAA;IAAKiH,SAAS,EAAC,kCAAkC;IAAAE,QAAA,gBAC7CnH,OAAA;MAAAmH,QAAA,EAAI;IAAyB;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EAEjChH,KAAK,iBAAIT,OAAA;MAAKiH,SAAS,EAAC,eAAe;MAAAE,QAAA,EAAE1G;IAAK;MAAA6G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrD1G,cAAc,iBAAIf,OAAA;MAAKiH,SAAS,EAAC,iBAAiB;MAAAE,QAAA,EAAEpG;IAAc;MAAAuG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EAEzEpH,cAAc,CAAC2D,MAAM,GAAG,CAAC,gBACtBhE,OAAA;MAAKiH,SAAS,EAAE,yBAAyBlF,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;MAACsI,GAAG,EAAEvI,QAAS;MAAAqF,QAAA,gBACpFnH,OAAA,CAAC6J,UAAU;QAAAvC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACbxG,QAAQ,gBACLjB,OAAA;QAAKiH,SAAS,EAAC,cAAc;QAAAE,QAAA,EACxBiD,WAAW,CAAC3D,GAAG,CAAC,CAACxB,OAAO,EAAEgE,KAAK,kBAC5BjJ,OAAA,CAACgJ,UAAU;UAEP/D,OAAO,EAAEA,OAAQ;UACjBgE,KAAK,EAAEvF,UAAU,GAAGuF,KAAK,GAAG;QAAE,GAFzBhE,OAAO,CAACE,UAAU;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAG1B,CACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,GACNpG,YAAY,gBACZrB,OAAA;QAAKiH,SAAS,EAAC,cAAc;QAAAE,QAAA,EACxBiD,WAAW,CAAC3D,GAAG,CAAC,CAACxB,OAAO,EAAEgE,KAAK,kBAC5BjJ,OAAA,CAACuJ,UAAU;UAEPtE,OAAO,EAAEA,OAAQ;UACjBgE,KAAK,EAAEA;QAAM,GAFRhE,OAAO,CAACE,UAAU;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAG1B,CACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,gBAENzH,OAAA,CAAAE,SAAA;QAAAiH,QAAA,gBACInH,OAAA;UAAKiH,SAAS,EAAC,iBAAiB;UAAAE,QAAA,eAC5BnH,OAAA;YAAAmH,QAAA,gBACInH,OAAA,CAACyJ,WAAW;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACfzH,OAAA;cAAAmH,QAAA,EACKiD,WAAW,CAAC3D,GAAG,CAAC,CAACxB,OAAO,EAAEgE,KAAK;gBAAA,IAAAqB,sBAAA;gBAAA,oBAC5BtK,OAAA;kBAAAmH,QAAA,gBACInH,OAAA;oBAAIiH,SAAS,EAAC,YAAY;oBAAAE,QAAA,EAAEzD,UAAU,GAAGuF,KAAK,GAAG;kBAAC;oBAAA3B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDzH,OAAA;oBAAIiH,SAAS,EAAC,cAAc;oBACxBmC,KAAK,EAAE,YAAYzB,UAAU,CAAC1C,OAAO,CAACM,UAAU,CAAC,CAACqC,IAAI,GAAG3C,OAAO,CAACqE,UAAU,GAAG,cAAc3B,UAAU,CAAC1C,OAAO,CAACqE,UAAU,CAAC,CAAC1B,IAAI,EAAE,GAAG,EAAE,EAAG;oBAAAT,QAAA,eACzInH,OAAA;sBAAKiH,SAAS,EAAC,cAAc;sBAAAE,QAAA,gBACzBnH,OAAA;wBAAKiH,SAAS,EAAC,WAAW;wBAAAE,QAAA,gBACtBnH,OAAA;0BAAMiH,SAAS,EAAC,YAAY;0BAAAE,QAAA,EAAC;wBAAK;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACzCzH,OAAA;0BAAMiH,SAAS,EAAC,cAAc;0BAAAE,QAAA,EAAEQ,UAAU,CAAC1C,OAAO,CAACM,UAAU,CAAC,CAACqD;wBAAU;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CAAC,EACLxC,OAAO,CAACqE,UAAU,iBACftJ,OAAA;wBAAKiH,SAAS,EAAC,cAAc;wBAAAE,QAAA,gBACzBnH,OAAA;0BAAMiH,SAAS,EAAC,YAAY;0BAAAE,QAAA,EAAC;wBAAI;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC,eACxCzH,OAAA;0BAAMiH,SAAS,EAAC,cAAc;0BAAAE,QAAA,EAAEQ,UAAU,CAAC1C,OAAO,CAACqE,UAAU,CAAC,CAACV;wBAAU;0BAAAtB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChF,CACR;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLzH,OAAA;oBAAIiH,SAAS,EAAC,kBAAkB;oBAAAE,QAAA,EAAElC,OAAO,CAACQ;kBAAQ;oBAAA6B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eACxDzH,OAAA;oBAAIiH,SAAS,EAAC,QAAQ;oBAAAE,QAAA,GAAC,QAAC,EAAC/D,UAAU,CAAC6B,OAAO,CAACI,MAAM,CAAC,CAACqC,cAAc,CAAC,CAAC;kBAAA;oBAAAJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC,eAC1EzH,OAAA;oBAAAmH,QAAA,eACInH,OAAA;sBAAKiH,SAAS,EAAC,qBAAqB;sBAAAE,QAAA,gBAChCnH,OAAA;wBAAKiH,SAAS,EAAC,aAAa;wBAAAE,QAAA,GAAAmD,sBAAA,GAAErF,OAAO,CAACoE,cAAc,cAAAiB,sBAAA,uBAAtBA,sBAAA,CAAwBzD;sBAAI;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjEzH,OAAA;wBAAKiH,SAAS,EAAC,gBAAgB;wBAAAE,QAAA,EAC1BjB,0BAA0B,CAACjB,OAAO,CAACoE,cAAc;sBAAC;wBAAA/B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACLzH,OAAA;oBAAAmH,QAAA,eACInH,OAAA;sBAAMiH,SAAS,EAAEY,mBAAmB,CAAC5C,OAAO,CAACO,MAAM,CAAE;sBAAA2B,QAAA,EAChDlC,OAAO,CAACO,MAAM,CAACsC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG9C,OAAO,CAACO,MAAM,CAAC7B,KAAK,CAAC,CAAC;oBAAC;sBAAA2D,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACP,CAAC,eACLzH,OAAA;oBAAAmH,QAAA,eACInH,OAAA;sBACIiH,SAAS,EAAC,gBAAgB;sBAC1BC,OAAO,EAAEA,CAAA,KAAMlC,eAAe,CAACC,OAAO,CAAE;sBACxC,cAAW,oBAAoB;sBAAAkC,QAAA,EAClC;oBAED;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC,eACLzH,OAAA;oBAAAmH,QAAA,EACKlC,OAAO,CAACO,MAAM,KAAK,SAAS,iBACzBxF,OAAA;sBAAKiH,SAAS,EAAC,gBAAgB;sBAAAE,QAAA,gBAC3BnH,OAAA;wBACIiH,SAAS,EAAC,aAAa;wBACvBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;wBACpF,cAAW,yBAAyB;wBAAA0B,QAAA,EACvC;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTzH,OAAA;wBACIiH,SAAS,EAAC,YAAY;wBACtBC,OAAO,EAAEA,CAAA,KAAMrB,kBAAkB,CAACZ,OAAO,CAACE,UAAU,EAAE,UAAU,EAAEF,OAAO,CAACQ,QAAQ,CAAE;wBACpF,cAAW,wBAAwB;wBAAA0B,QAAA,EACtC;sBAED;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR;kBACR;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC;gBAAA,GA5DAxC,OAAO,CAACE,UAAU;kBAAAmC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6DvB,CAAC;cAAA,CACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNzH,OAAA,CAAC0J,UAAU;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAChB,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,gBAENzH,OAAA;MAAKiH,SAAS,EAAC,aAAa;MAAAE,QAAA,eACxBnH,OAAA;QAAAmH,QAAA,EAAG;MAAyB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CACR,EAEA9G,cAAc,iBACXX,OAAA,CAAC8G,UAAU;MACPC,KAAK,EAAElG,aAAc;MACrBmG,OAAO,EAAEA,CAAA,KAAM;QACXpG,iBAAiB,CAAC,KAAK,CAAC;QACxBE,gBAAgB,CAAC,IAAI,CAAC;MAC1B;IAAE;MAAAwG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAACrH,EAAA,CAljBQD,qBAAqB;AAAAoK,EAAA,GAArBpK,qBAAqB;AAojB9B,eAAeA,qBAAqB;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}