import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { FaSignOutAlt, FaCog, FaBell, FaUser } from 'react-icons/fa';

function AdminHeader({ onLogout }) {
  const location = useLocation();
  const adminUsername = localStorage.getItem('adminUsername');

  const getPageTitle = () => {
    const path = location.pathname;
    if (path === '/admin' || path === '/admin/dashboard') return 'Dashboard';
    if (path.includes('challenge-system')) return 'Challenge System';
    if (path.includes('challenge-management')) return 'Challenge Management';
    if (path.includes('team-management')) return 'Team Management';
    if (path.includes('league-management')) return 'League Management';
    if (path.includes('users')) return 'User Management';
    if (path.includes('credit-user')) return 'Credit User';
    if (path.includes('debit-user')) return 'Debit User';
    if (path.includes('payment-methods')) return 'Payment Methods';
    if (path.includes('bets')) return 'Bet Management';
    if (path.includes('transactions')) return 'Transaction Management';
    if (path.includes('leaderboard')) return 'Leaderboard';
    if (path.includes('settings')) return 'System Settings';
    if (path.includes('reports')) return 'Reports & Analytics';
    return 'Admin Panel';
  };

  return (
    <header className="bg-white shadow-sm px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side - Page title */}
        <div>
          <h1 className="text-xl font-semibold text-gray-800">{getPageTitle()}</h1>
        </div>

        {/* Right side - User info and actions */}
        <div className="flex items-center space-x-4">
          {/* Notifications */}
          <button className="p-2 rounded-full hover:bg-gray-100 relative">
            <FaBell className="text-gray-700 h-5 w-5" />
            <span className="absolute top-0 right-0 block h-2.5 w-2.5 rounded-full bg-red-500 transform translate-x-1/4 -translate-y-1/4 ring-2 ring-white"></span>
          </button>

          {/* Settings */}
          <Link to="/admin/settings" className="p-2 rounded-full hover:bg-gray-100">
            <FaCog className="text-gray-700 h-5 w-5" />
          </Link>

          {/* Admin info */}
          <div className="flex items-center border-l pl-4 ml-2">
            <div className="h-9 w-9 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mr-3">
              <FaUser className="h-4 w-4" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-800">{adminUsername || 'Admin'}</div>
              <div className="text-xs text-gray-500">Administrator</div>
            </div>
          </div>

          {/* Logout button */}
          <button
            onClick={onLogout}
            className="ml-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md text-sm font-medium flex items-center shadow-sm"
          >
            <FaSignOutAlt className="mr-2 h-4 w-4" />
            <span>Logout</span>
          </button>
        </div>
      </div>
    </header>
  );
}

export default AdminHeader;