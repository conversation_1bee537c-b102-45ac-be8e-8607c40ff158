import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaSearch, Fa<PERSON>ilter, FaSort, FaEye, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';
import './LeaderboardManagement.css';

const API_BASE_URL = '/backend';

function LeaderboardManagement() {
    const [leaderboard, setLeaderboard] = useState([]);
    const [leagues, setLeagues] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Filters and pagination
    const [filters, setFilters] = useState({
        search: '',
        leagueFilter: '',
        sortBy: 'points',
        order: 'DESC',
        limit: 20
    });

    const [pagination, setPagination] = useState({
        current_page: 1,
        total_pages: 1,
        total_records: 0,
        records_per_page: 20
    });

    // Modal states
    const [showUserModal, setShowUserModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState(null);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editingUser, setEditingUser] = useState(null);

    useEffect(() => {
        fetchLeaderboard();
    }, [pagination.current_page, filters]);

    const fetchLeaderboard = async () => {
        setLoading(true);
        setError('');
        try {
            const params = new URLSearchParams({
                page: pagination.current_page,
                limit: filters.limit,
                sortBy: filters.sortBy,
                order: filters.order,
                ...(filters.search && { search: filters.search }),
                ...(filters.leagueFilter && { leagueFilter: filters.leagueFilter })
            });

            console.log('Fetching leaderboard with params:', params.toString());
            const response = await axios.get(`${API_BASE_URL}/handlers/leaderboard.php?${params}`);

            console.log('Leaderboard API Response:', response.data);

            if (response.data.success) {
                setLeaderboard(response.data.data.leaderboard || []);
                setLeagues(response.data.data.leagues || []);
                setPagination(response.data.data.pagination || pagination);
            } else {
                const errorMsg = `Failed to fetch leaderboard: ${response.data.message || 'Unknown error'}`;
                console.error('Leaderboard API Error:', response.data);
                setError(errorMsg);
            }
        } catch (err) {
            let errorMessage = 'Failed to fetch leaderboard data';

            if (err.response) {
                // Server responded with error status
                errorMessage += ` (Status: ${err.response.status})`;
                if (err.response.data) {
                    if (typeof err.response.data === 'string') {
                        errorMessage += ` - ${err.response.data}`;
                    } else if (err.response.data.message) {
                        errorMessage += ` - ${err.response.data.message}`;
                    } else {
                        errorMessage += ` - ${JSON.stringify(err.response.data)}`;
                    }
                }
            } else if (err.request) {
                // Request was made but no response received
                errorMessage += ' - No response from server. Please check your connection.';
            } else {
                // Something else happened
                errorMessage += ` - ${err.message}`;
            }

            console.error('Leaderboard fetch error details:', {
                error: err,
                message: err.message,
                response: err.response?.data,
                status: err.response?.status,
                request: err.request
            });

            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({ ...prev, [key]: value }));
        setPagination(prev => ({ ...prev, current_page: 1 }));
    };

    const handleSort = (column) => {
        const newOrder = filters.sortBy === column && filters.order === 'DESC' ? 'ASC' : 'DESC';
        setFilters(prev => ({ ...prev, sortBy: column, order: newOrder }));
    };

    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, current_page: newPage }));
    };

    const viewUserDetails = (user) => {
        setSelectedUser(user);
        setShowUserModal(true);
    };

    const editUser = (user) => {
        setEditingUser({ ...user });
        setShowEditModal(true);
    };

    const handleUpdateUser = async (e) => {
        e.preventDefault();
        try {
            const response = await axios.put(
                `${API_BASE_URL}/handlers/user_management.php?id=${editingUser.user_id}`,
                editingUser
            );

            if (response.data.success) {
                setSuccess('User updated successfully!');
                setShowEditModal(false);
                fetchLeaderboard();
            } else {
                setError(response.data.message || 'Failed to update user');
            }
        } catch (err) {
            setError('Failed to update user');
        }
    };

    const resetUserStats = async (userId) => {
        if (!window.confirm('Are you sure you want to reset this user\'s stats? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/reset_user_stats.php`, {
                user_id: userId
            });

            if (response.data.success) {
                setSuccess('User stats reset successfully!');
                fetchLeaderboard();
            } else {
                setError(response.data.message || 'Failed to reset user stats');
            }
        } catch (err) {
            setError('Failed to reset user stats');
        }
    };

    return (
        <div className="admin-container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">Leaderboard Management</h1>
                    <p className="admin-description">
                        Manage user rankings, points, and leaderboard statistics across all leagues.
                    </p>
                </div>
                <button
                    onClick={() => fetchLeaderboard()}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                >
                    <FaPlus className="mr-2" />
                    Refresh Data
                </button>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {success && (
                <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    {success}
                </div>
            )}

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Search Users</label>
                        <div className="relative">
                            <FaSearch className="absolute left-3 top-3 text-gray-400" />
                            <input
                                type="text"
                                placeholder="Search by username or name..."
                                value={filters.search}
                                onChange={(e) => handleFilterChange('search', e.target.value)}
                                className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Filter by League</label>
                        <select
                            value={filters.leagueFilter}
                            onChange={(e) => handleFilterChange('leagueFilter', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="">All Leagues</option>
                            {leagues.map(league => (
                                <option key={league.league_id} value={league.league_id}>
                                    {league.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                        <select
                            value={filters.sortBy}
                            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value="points">Points</option>
                            <option value="total_points">Total Points</option>
                            <option value="wins">Wins</option>
                            <option value="losses">Losses</option>
                            <option value="balance">Balance</option>
                            <option value="current_streak">Current Streak</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Items per Page</label>
                        <select
                            value={filters.limit}
                            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </select>
                    </div>
                </div>

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div className="flex-1 flex justify-between sm:hidden">
                            <button
                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                disabled={pagination.current_page === 1}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            <button
                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                disabled={pagination.current_page === pagination.total_pages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing{' '}
                                    <span className="font-medium">
                                        {(pagination.current_page - 1) * pagination.records_per_page + 1}
                                    </span>{' '}
                                    to{' '}
                                    <span className="font-medium">
                                        {Math.min(pagination.current_page * pagination.records_per_page, pagination.total_records)}
                                    </span>{' '}
                                    of{' '}
                                    <span className="font-medium">{pagination.total_records}</span> results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => handlePageChange(pagination.current_page - 1)}
                                        disabled={pagination.current_page === 1}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Previous
                                    </button>

                                    {/* Page numbers */}
                                    {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                                        let pageNum;
                                        if (pagination.total_pages <= 5) {
                                            pageNum = i + 1;
                                        } else if (pagination.current_page <= 3) {
                                            pageNum = i + 1;
                                        } else if (pagination.current_page >= pagination.total_pages - 2) {
                                            pageNum = pagination.total_pages - 4 + i;
                                        } else {
                                            pageNum = pagination.current_page - 2 + i;
                                        }

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                    pageNum === pagination.current_page
                                                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}

                                    <button
                                        onClick={() => handlePageChange(pagination.current_page + 1)}
                                        disabled={pagination.current_page === pagination.total_pages}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Next
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Leaderboard Table */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Rank
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('username')}
                                >
                                    <div className="flex items-center">
                                        User
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('points')}
                                >
                                    <div className="flex items-center">
                                        Points
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('wins')}
                                >
                                    <div className="flex items-center">
                                        W/L/D
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th
                                    className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                    onClick={() => handleSort('balance')}
                                >
                                    <div className="flex items-center">
                                        Balance
                                        <FaSort className="ml-1" />
                                    </div>
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    League
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {loading ? (
                                <tr>
                                    <td colSpan="7" className="px-6 py-4 text-center">
                                        <div className="flex justify-center items-center">
                                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                                            <span className="ml-2">Loading...</span>
                                        </div>
                                    </td>
                                </tr>
                            ) : leaderboard.length === 0 ? (
                                <tr>
                                    <td colSpan="7" className="px-6 py-4 text-center text-gray-500">
                                        No users found
                                    </td>
                                </tr>
                            ) : (
                                leaderboard.map((user, index) => {
                                    const rank = (pagination.current_page - 1) * pagination.records_per_page + index + 1;
                                    return (
                                        <tr key={user.user_id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <span className={`inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                                                        rank === 1 ? 'bg-yellow-100 text-yellow-800' :
                                                        rank === 2 ? 'bg-gray-100 text-gray-800' :
                                                        rank === 3 ? 'bg-orange-100 text-orange-800' :
                                                        'bg-blue-100 text-blue-800'
                                                    }`}>
                                                        {rank}
                                                    </span>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div className="text-sm font-medium text-gray-900">{user.username}</div>
                                                    <div className="text-sm text-gray-500">{user.full_name}</div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">{user.points || 0}</div>
                                                <div className="text-sm text-gray-500">Total: {user.total_points || 0}</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">
                                                    <span className="text-green-600">{user.wins || 0}</span>/
                                                    <span className="text-red-600">{user.losses || 0}</span>/
                                                    <span className="text-yellow-600">{user.draws || 0}</span>
                                                </div>
                                                <div className="text-sm text-gray-500">
                                                    Win Rate: {user.win_percentage || 0}%
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">{user.balance || 0} FC</div>
                                                <div className="text-sm text-gray-500">Streak: {user.current_streak || 0}</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm text-gray-900">{user.league_name || 'No League'}</div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div className="flex space-x-2">
                                                    <button
                                                        onClick={() => viewUserDetails(user)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                        title="View Details"
                                                    >
                                                        <FaEye />
                                                    </button>
                                                    <button
                                                        onClick={() => editUser(user)}
                                                        className="text-green-600 hover:text-green-900"
                                                        title="Edit User"
                                                    >
                                                        <FaEdit />
                                                    </button>
                                                    <button
                                                        onClick={() => resetUserStats(user.user_id)}
                                                        className="text-red-600 hover:text-red-900"
                                                        title="Reset Stats"
                                                    >
                                                        <FaTrash />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    );
                                })
                            )}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                        <div className="flex-1 flex justify-between sm:hidden">
                            <button
                                onClick={() => handlePageChange(pagination.current_page - 1)}
                                disabled={pagination.current_page === 1}
                                className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            <button
                                onClick={() => handlePageChange(pagination.current_page + 1)}
                                disabled={pagination.current_page === pagination.total_pages}
                                className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                        <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                            <div>
                                <p className="text-sm text-gray-700">
                                    Showing{' '}
                                    <span className="font-medium">
                                        {(pagination.current_page - 1) * pagination.records_per_page + 1}
                                    </span>{' '}
                                    to{' '}
                                    <span className="font-medium">
                                        {Math.min(pagination.current_page * pagination.records_per_page, pagination.total_records)}
                                    </span>{' '}
                                    of{' '}
                                    <span className="font-medium">{pagination.total_records}</span> results
                                </p>
                            </div>
                            <div>
                                <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                    <button
                                        onClick={() => handlePageChange(pagination.current_page - 1)}
                                        disabled={pagination.current_page === 1}
                                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Previous
                                    </button>

                                    {/* Page numbers */}
                                    {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                                        let pageNum;
                                        if (pagination.total_pages <= 5) {
                                            pageNum = i + 1;
                                        } else if (pagination.current_page <= 3) {
                                            pageNum = i + 1;
                                        } else if (pagination.current_page >= pagination.total_pages - 2) {
                                            pageNum = pagination.total_pages - 4 + i;
                                        } else {
                                            pageNum = pagination.current_page - 2 + i;
                                        }

                                        return (
                                            <button
                                                key={pageNum}
                                                onClick={() => handlePageChange(pageNum)}
                                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                                    pageNum === pagination.current_page
                                                        ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                }`}
                                            >
                                                {pageNum}
                                            </button>
                                        );
                                    })}

                                    <button
                                        onClick={() => handlePageChange(pagination.current_page + 1)}
                                        disabled={pagination.current_page === pagination.total_pages}
                                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                    >
                                        Next
                                    </button>
                                </nav>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* User Details Modal */}
            {showUserModal && selectedUser && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-900">User Details</h3>
                                <button
                                    onClick={() => setShowUserModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    ×
                                </button>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Username</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.username}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Full Name</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.full_name}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Points</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.points || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Total Points</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.total_points || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Balance</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.balance || 0} FC</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">League</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.league_name || 'No League'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Wins</label>
                                    <p className="mt-1 text-sm text-green-600">{selectedUser.wins || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Losses</label>
                                    <p className="mt-1 text-sm text-red-600">{selectedUser.losses || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Draws</label>
                                    <p className="mt-1 text-sm text-yellow-600">{selectedUser.draws || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Current Streak</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.current_streak || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Highest Streak</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.highest_streak || 0}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Win Rate</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedUser.win_percentage || 0}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit User Modal */}
            {showEditModal && editingUser && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <div className="flex justify-between items-center mb-4">
                                <h3 className="text-lg font-medium text-gray-900">Edit User</h3>
                                <button
                                    onClick={() => setShowEditModal(false)}
                                    className="text-gray-400 hover:text-gray-600"
                                >
                                    ×
                                </button>
                            </div>
                            <form onSubmit={handleUpdateUser}>
                                <div className="grid grid-cols-2 gap-4">
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Username</label>
                                        <input
                                            type="text"
                                            value={editingUser.username}
                                            onChange={(e) => setEditingUser({...editingUser, username: e.target.value})}
                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Full Name</label>
                                        <input
                                            type="text"
                                            value={editingUser.full_name}
                                            onChange={(e) => setEditingUser({...editingUser, full_name: e.target.value})}
                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Points</label>
                                        <input
                                            type="number"
                                            value={editingUser.points || 0}
                                            onChange={(e) => setEditingUser({...editingUser, points: parseInt(e.target.value)})}
                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700">Balance</label>
                                        <input
                                            type="number"
                                            step="0.01"
                                            value={editingUser.balance || 0}
                                            onChange={(e) => setEditingUser({...editingUser, balance: parseFloat(e.target.value)})}
                                            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        />
                                    </div>
                                </div>
                                <div className="mt-6 flex justify-end space-x-3">
                                    <button
                                        type="button"
                                        onClick={() => setShowEditModal(false)}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                                    >
                                        Update User
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default LeaderboardManagement;
