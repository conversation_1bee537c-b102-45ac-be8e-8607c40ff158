import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaTrophy, FaMedal, FaAward, FaChart<PERSON>ine, Fa<PERSON>ilter, FaSort } from 'react-icons/fa';
import './AdminStyles.css';

const API_BASE_URL = '/backend';

function AdminLeaderboard() {
    const [leaderboard, setLeaderboard] = useState([]);
    const [leagues, setLeagues] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [pagination, setPagination] = useState({
        current_page: 1,
        total_pages: 1,
        total_records: 0,
        records_per_page: 20
    });

    // Filter and sort states
    const [filters, setFilters] = useState({
        sortBy: 'points',
        order: 'DESC',
        timeFilter: 'all',
        leagueFilter: '',
        limit: 20
    });

    useEffect(() => {
        fetchLeaderboard();
    }, [filters]);

    const fetchLeaderboard = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: pagination.current_page,
                limit: filters.limit,
                sortBy: filters.sortBy,
                order: filters.order,
                timeFilter: filters.timeFilter,
                ...(filters.leagueFilter && { leagueFilter: filters.leagueFilter })
            });

            const response = await axios.get(`${API_BASE_URL}/handlers/leaderboard.php?${params}`);
            
            if (response.data.success) {
                setLeaderboard(response.data.data.leaderboard || []);
                setLeagues(response.data.data.leagues || []);
                setPagination(response.data.data.pagination || pagination);
            } else {
                setError(response.data.message || 'Failed to fetch leaderboard');
            }
        } catch (err) {
            setError('Failed to fetch leaderboard data');
            console.error('Leaderboard fetch error:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleFilterChange = (key, value) => {
        setFilters(prev => ({
            ...prev,
            [key]: value
        }));
        setPagination(prev => ({ ...prev, current_page: 1 }));
    };

    const handleSort = (column) => {
        const newOrder = filters.sortBy === column && filters.order === 'DESC' ? 'ASC' : 'DESC';
        setFilters(prev => ({
            ...prev,
            sortBy: column,
            order: newOrder
        }));
    };

    const getRankIcon = (rank) => {
        if (rank === 1) return <FaTrophy className="text-yellow-500" />;
        if (rank === 2) return <FaMedal className="text-gray-400" />;
        if (rank === 3) return <FaAward className="text-orange-500" />;
        return <span className="text-gray-600">#{rank}</span>;
    };

    const getSortIcon = (column) => {
        if (filters.sortBy !== column) return <FaSort className="text-gray-400" />;
        return filters.order === 'DESC' ? '↓' : '↑';
    };

    if (loading) {
        return (
            <div className="admin-container">
                <div className="loading-spinner">
                    <FaChartLine className="animate-spin text-4xl text-green-600" />
                    <p>Loading leaderboard...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="admin-container">
            <div className="flex justify-between items-center mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-2">Leaderboard Management</h1>
                    <p className="admin-description">
                        View and analyze user rankings, performance metrics, and competitive standings.
                    </p>
                </div>
                <div className="flex items-center space-x-2">
                    <FaTrophy className="text-yellow-500 text-2xl" />
                    <span className="text-lg font-semibold text-gray-700">
                        {pagination.total_records} Players
                    </span>
                </div>
            </div>

            {error && (
                <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    {error}
                </div>
            )}

            {/* Filters */}
            <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <div className="flex items-center mb-4">
                    <FaFilter className="text-green-600 mr-2" />
                    <h3 className="text-lg font-semibold">Filters & Sorting</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {/* Sort By */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                        <select
                            value={filters.sortBy}
                            onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                            <option value="points">Current Points</option>
                            <option value="total_points">Total Points</option>
                            <option value="wins">Wins</option>
                            <option value="total_bets">Total Bets</option>
                            <option value="balance">Balance</option>
                            <option value="current_streak">Current Streak</option>
                            <option value="highest_streak">Highest Streak</option>
                        </select>
                    </div>

                    {/* Time Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Activity Period</label>
                        <select
                            value={filters.timeFilter}
                            onChange={(e) => handleFilterChange('timeFilter', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                            <option value="all">All Time</option>
                            <option value="week">Last Week</option>
                            <option value="month">Last Month</option>
                            <option value="year">Last Year</option>
                        </select>
                    </div>

                    {/* League Filter */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">League</label>
                        <select
                            value={filters.leagueFilter}
                            onChange={(e) => handleFilterChange('leagueFilter', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                            <option value="">All Leagues</option>
                            {leagues.map(league => (
                                <option key={league.league_id} value={league.league_id}>
                                    {league.name}
                                </option>
                            ))}
                        </select>
                    </div>

                    {/* Results Per Page */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Results Per Page</label>
                        <select
                            value={filters.limit}
                            onChange={(e) => handleFilterChange('limit', parseInt(e.target.value))}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-green-500 focus:border-green-500"
                        >
                            <option value={10}>10</option>
                            <option value={20}>20</option>
                            <option value={50}>50</option>
                            <option value={100}>100</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Leaderboard Table */}
            <div className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead className="bg-green-600 text-white">
                            <tr>
                                <th className="px-4 py-3 text-left">Rank</th>
                                <th className="px-4 py-3 text-left">Player</th>
                                <th 
                                    className="px-4 py-3 text-left cursor-pointer hover:bg-green-700"
                                    onClick={() => handleSort('points')}
                                >
                                    <div className="flex items-center">
                                        Points {getSortIcon('points')}
                                    </div>
                                </th>
                                <th 
                                    className="px-4 py-3 text-left cursor-pointer hover:bg-green-700"
                                    onClick={() => handleSort('wins')}
                                >
                                    <div className="flex items-center">
                                        W/D/L {getSortIcon('wins')}
                                    </div>
                                </th>
                                <th 
                                    className="px-4 py-3 text-left cursor-pointer hover:bg-green-700"
                                    onClick={() => handleSort('total_bets')}
                                >
                                    <div className="flex items-center">
                                        Total Bets {getSortIcon('total_bets')}
                                    </div>
                                </th>
                                <th className="px-4 py-3 text-left">Win %</th>
                                <th 
                                    className="px-4 py-3 text-left cursor-pointer hover:bg-green-700"
                                    onClick={() => handleSort('current_streak')}
                                >
                                    <div className="flex items-center">
                                        Streak {getSortIcon('current_streak')}
                                    </div>
                                </th>
                                <th 
                                    className="px-4 py-3 text-left cursor-pointer hover:bg-green-700"
                                    onClick={() => handleSort('balance')}
                                >
                                    <div className="flex items-center">
                                        Balance {getSortIcon('balance')}
                                    </div>
                                </th>
                                <th className="px-4 py-3 text-left">League</th>
                            </tr>
                        </thead>
                        <tbody>
                            {leaderboard.map((player, index) => (
                                <tr key={player.user_id} className="border-b hover:bg-gray-50">
                                    <td className="px-4 py-3">
                                        <div className="flex items-center">
                                            {getRankIcon(player.rank)}
                                        </div>
                                    </td>
                                    <td className="px-4 py-3">
                                        <div>
                                            <div className="font-semibold text-gray-900">{player.username}</div>
                                            <div className="text-sm text-gray-500">{player.full_name}</div>
                                        </div>
                                    </td>
                                    <td className="px-4 py-3">
                                        <span className="font-bold text-green-600">{player.points}</span>
                                        {player.total_points !== player.points && (
                                            <div className="text-xs text-gray-500">
                                                Total: {player.total_points}
                                            </div>
                                        )}
                                    </td>
                                    <td className="px-4 py-3">
                                        <div className="text-sm">
                                            <span className="text-green-600 font-semibold">{player.wins}</span>
                                            <span className="text-gray-400 mx-1">/</span>
                                            <span className="text-yellow-600">{player.draws}</span>
                                            <span className="text-gray-400 mx-1">/</span>
                                            <span className="text-red-600">{player.losses}</span>
                                        </div>
                                    </td>
                                    <td className="px-4 py-3 font-semibold">{player.total_bets}</td>
                                    <td className="px-4 py-3">
                                        <span className={`font-semibold ${
                                            player.win_percentage >= 60 ? 'text-green-600' :
                                            player.win_percentage >= 40 ? 'text-yellow-600' : 'text-red-600'
                                        }`}>
                                            {player.win_percentage}%
                                        </span>
                                    </td>
                                    <td className="px-4 py-3">
                                        <div className="text-sm">
                                            <div className="font-semibold">{player.current_streak}</div>
                                            <div className="text-xs text-gray-500">
                                                Best: {player.highest_streak}
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-4 py-3">
                                        <span className="font-semibold text-blue-600">
                                            ${parseFloat(player.balance).toFixed(2)}
                                        </span>
                                    </td>
                                    <td className="px-4 py-3">
                                        <span className="text-sm text-gray-600">
                                            {player.league_name || 'No League'}
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                    <div className="bg-gray-50 px-4 py-3 border-t">
                        <div className="flex items-center justify-between">
                            <div className="text-sm text-gray-700">
                                Showing {((pagination.current_page - 1) * pagination.records_per_page) + 1} to{' '}
                                {Math.min(pagination.current_page * pagination.records_per_page, pagination.total_records)} of{' '}
                                {pagination.total_records} results
                            </div>
                            <div className="flex space-x-2">
                                <button
                                    onClick={() => setPagination(prev => ({ ...prev, current_page: prev.current_page - 1 }))}
                                    disabled={pagination.current_page === 1}
                                    className="px-3 py-1 border rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Previous
                                </button>
                                <span className="px-3 py-1 text-sm">
                                    Page {pagination.current_page} of {pagination.total_pages}
                                </span>
                                <button
                                    onClick={() => setPagination(prev => ({ ...prev, current_page: prev.current_page + 1 }))}
                                    disabled={pagination.current_page === pagination.total_pages}
                                    className="px-3 py-1 border rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}

export default AdminLeaderboard;
