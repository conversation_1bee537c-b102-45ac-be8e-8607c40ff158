import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import './LeagueManagement.css';
import {
    FaImage,
    FaTrophy,
    FaPlus,
    FaEdit,
    FaCalendarAlt,
    FaMoneyBillWave,
    FaClock,
    FaExclamationCircle,
    FaCheckCircle,
    FaSave,
    FaUsers,
    FaInfoCircle,
    FaTimes,
    FaFootballBall
} from 'react-icons/fa';

const API_BASE_URL = '/backend';

function LeagueManagement() {
    const navigate = useNavigate();
    const [leagues, setLeagues] = useState([]);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [loading, setLoading] = useState(true);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [selectedLeague, setSelectedLeague] = useState(null);
    // Using list view only
    const [newLeague, setNewLeague] = useState({
        name: '',
        min_bet_amount: '',
        max_bet_amount: '',
        description: '',
        season_duration: 90,
        league_rules: '',
        reward_description: '',
        status: 'upcoming'
    });

    const getMediaUrl = (path, type) => {
        if (!path) return null;
        // If path already contains 'uploads', use it as is
        if (path.includes('uploads')) {
            return `${API_BASE_URL}/${path}`;
        }
        // Otherwise, construct the full path
        return `${API_BASE_URL}/uploads/leagues/${type}s/${path}`;
    };

    const fetchLeagues = async () => {
        try {
            setLoading(true);
            setError('');
            const response = await axios.get(`${API_BASE_URL}/handlers/league_management.php`);
            if (response.data.status === 200) {
                setLeagues(response.data.data || []);
            } else {
                setError(response.data.message || 'Failed to fetch leagues');
            }
        } catch (err) {
            console.error('Error fetching leagues:', err);
            setError('Failed to fetch leagues. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchLeagues();
    }, []);

    const handleCreateLeague = async (e) => {
        e.preventDefault();
        try {
            setError('');
            const formData = new FormData();
            Object.keys(newLeague).forEach(key => {
                formData.append(key, newLeague[key]);
            });

            const response = await axios.post(`${API_BASE_URL}/handlers/league_management.php`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.status === 200 || response.data.status === 201) {
                setSuccess('League created successfully');
                setShowCreateModal(false);
                fetchLeagues();
                setNewLeague({
                    name: '',
                    min_bet_amount: '',
                    max_bet_amount: '',
                    description: '',
                    season_duration: 90,
                    league_rules: '',
                    reward_description: '',
                    status: 'upcoming'
                });
            } else {
                setError(response.data.message || 'Failed to create league');
            }
        } catch (err) {
            console.error('Error creating league:', err);
            setError('Failed to create league. Please try again.');
        }
    };

    const handleUpdateLeague = async (updatedData) => {
        try {
            setError('');
            setSuccess('');

            // For debugging
            console.log('Updated data received:', updatedData);

            const formData = new FormData();
            formData.append('action', 'update');
            formData.append('league_id', selectedLeague.league_id);

            // Required fields that must be present
            const requiredFields = ['name', 'min_bet_amount', 'max_bet_amount', 'description'];

            // First, add all existing data from selectedLeague as fallback
            requiredFields.forEach(field => {
                formData.append(field, selectedLeague[field]);
            });

            // Then add all updated fields from the form
            for (let [key, value] of updatedData.entries()) {
                formData.append(key, value);
            }

            // Validate that all required fields are present
            let missingFields = [];
            requiredFields.forEach(field => {
                if (!formData.get(field)) {
                    missingFields.push(field);
                }
            });

            if (missingFields.length > 0) {
                throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
            }

            // For debugging
            console.log('Form data being sent:');
            for (let [key, value] of formData.entries()) {
                console.log(key, ':', value);
            }

            const response = await axios.post(`${API_BASE_URL}/handlers/league_management.php`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            if (response.data.status === 200) {
                setSuccess('League updated successfully');
                setShowEditModal(false);
                await fetchLeagues(); // Re-fetch leagues to update the display
            } else {
                setError(response.data.message || 'Failed to update league');
            }
        } catch (err) {
            console.error('Error updating league:', err);
            setError(err.message || 'Failed to update league. Please try again.');
        }
    };

    const handleViewSeasons = (league) => {
        navigate(`/admin/league-management/${league.league_id}/seasons`);
    };

    return (
        <div className="league-management">
            <div className="page-header">
                <h1><FaTrophy /> LEAGUE MANAGEMENT</h1>
                <div className="header-content">
                    <button className="create-league-btn" onClick={() => setShowCreateModal(true)}>
                        <FaPlus /> Create New League
                    </button>
                </div>
                {error && <div className="error-message"><FaExclamationCircle /> {error}</div>}
                {success && <div className="success-message"><FaCheckCircle /> {success}</div>}
            </div>

            {loading ? (
                <div className="flex justify-center items-center py-8">
                    <div className="loading-spinner"></div>
                    <span className="ml-3 text-gray-600">Loading leagues...</span>
                </div>
            ) : leagues.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                    <FaTrophy className="mx-auto text-gray-400 text-4xl mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No leagues found</h3>
                    <p className="text-gray-500 mb-4">Create your first league to get started!</p>
                    <button
                        onClick={() => setShowCreateModal(true)}
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        style={{ backgroundColor: '#166534' }}
                    >
                        <FaPlus className="mr-2" /> Create New League
                    </button>
                </div>
            ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr className="bg-green-600" style={{ backgroundColor: '#166534' }}>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-table-hide-small" style={{ backgroundColor: '#166534' }}>
                                        Icon
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        League Name
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-table-hide-medium" style={{ backgroundColor: '#166534' }}>
                                        Description
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Bet Range
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-table-hide-small" style={{ backgroundColor: '#166534' }}>
                                        Duration
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {leagues.map((league) => (
                                    <tr key={league.league_id} className="hover:bg-gray-50 transition-colors">
                                        <td className="px-6 py-4 whitespace-nowrap league-table-hide-small">
                                            <div className="flex-shrink-0 h-10 w-10">
                                                {league.icon_path ? (
                                                    <img
                                                        className="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                                                        src={getMediaUrl(league.icon_path, 'icon')}
                                                        alt={`${league.name} icon`}
                                                        onError={(e) => {
                                                            e.target.onerror = null;
                                                            e.target.src = '/images/default-league-icon.png';
                                                        }}
                                                    />
                                                ) : (
                                                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                                                        <FaFootballBall className="text-green-600" />
                                                    </div>
                                                )}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">{league.name}</div>
                                            <div className="text-sm text-gray-500 league-table-show-mobile">
                                                ${league.min_bet_amount} - ${league.max_bet_amount}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 league-table-hide-medium">
                                            <div className="text-sm text-gray-900 max-w-xs truncate">
                                                {league.description || 'The most competitive league'}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm text-gray-900">
                                                ${league.min_bet_amount} - ${league.max_bet_amount}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 league-table-hide-small">
                                            <div className="flex items-center">
                                                <FaClock className="mr-1" />
                                                {league.season_duration} days
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                league.status === 'active' ? 'bg-green-100 text-green-800' :
                                                league.status === 'upcoming' ? 'bg-yellow-100 text-yellow-800' :
                                                league.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                                                'bg-blue-100 text-blue-800'
                                            }`}>
                                                {league.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <div className="flex justify-end space-x-2">
                                                <button
                                                    onClick={() => {
                                                        setSelectedLeague(league);
                                                        setShowEditModal(true);
                                                    }}
                                                    className="text-blue-600 hover:text-blue-900 hover:bg-blue-50 p-2 rounded transition-colors"
                                                    title="Edit League"
                                                >
                                                    <FaEdit />
                                                </button>
                                                <button
                                                    onClick={() => handleViewSeasons(league)}
                                                    className="text-green-600 hover:text-green-900 hover:bg-green-50 p-2 rounded transition-colors"
                                                    title="Manage Seasons"
                                                >
                                                    <FaCalendarAlt />
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {showCreateModal && (
                <CreateLeagueModal
                    league={newLeague}
                    onChange={setNewLeague}
                    onSubmit={handleCreateLeague}
                    onClose={() => setShowCreateModal(false)}
                />
            )}

            {showEditModal && selectedLeague && (
                <EditLeagueModal
                    league={selectedLeague}
                    onClose={() => {
                        setShowEditModal(false);
                        setSelectedLeague(null);
                    }}
                    onSave={handleUpdateLeague}
                />
            )}
        </div>
    );
}

function CreateLeagueModal({ league, onChange, onSubmit, onClose }) {
    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-6 rounded-t-2xl">
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="text-xl font-bold flex items-center">
                                <FaTrophy className="mr-2" /> Create New League
                            </h3>
                            <p className="text-green-100 text-sm">Set up a new competitive league</p>
                        </div>
                        <button
                            onClick={onClose}
                            className="text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all"
                        >
                            ×
                        </button>
                    </div>
                </div>
                <div className="p-6">
                    <form onSubmit={onSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">League Name</label>
                                <input
                                    type="text"
                                    value={league.name}
                                    onChange={(e) => onChange({...league, name: e.target.value})}
                                    placeholder="Enter league name"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Minimum Bet (₦)</label>
                                <input
                                    type="number"
                                    value={league.min_bet_amount}
                                    onChange={(e) => onChange({...league, min_bet_amount: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">Maximum Bet (₦)</label>
                                <input
                                    type="number"
                                    value={league.max_bet_amount}
                                    onChange={(e) => onChange({...league, max_bet_amount: e.target.value})}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                                <textarea
                                    value={league.description}
                                    onChange={(e) => onChange({...league, description: e.target.value})}
                                    rows="3"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">League Rules</label>
                                <textarea
                                    value={league.league_rules}
                                    onChange={(e) => onChange({...league, league_rules: e.target.value})}
                                    rows="3"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div className="md:col-span-2">
                                <label className="block text-sm font-medium text-gray-700 mb-2">Reward Description</label>
                                <textarea
                                    value={league.reward_description}
                                    onChange={(e) => onChange({...league, reward_description: e.target.value})}
                                    rows="3"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    required
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">League Icon</label>
                                <input
                                    type="file"
                                    id="league_icon"
                                    accept="image/*"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">League Banner</label>
                                <input
                                    type="file"
                                    id="league_banner"
                                    accept="image/*"
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                />
                            </div>
                        </div>

                        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                            <button
                                type="button"
                                onClick={onClose}
                                className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center"
                                style={{ backgroundColor: '#166534' }}
                            >
                                <FaPlus className="mr-2" /> Create League
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
}

const EditLeagueModal = ({ league, onClose, onSave }) => {
    const [formData, setFormData] = useState({
        name: league.name || '',
        description: league.description || '',
        min_bet_amount: league.min_bet_amount || '',
        max_bet_amount: league.max_bet_amount || '',
        season_duration: league.season_duration || 90,
        league_rules: league.league_rules || '',
        reward_description: league.reward_description || '',
        status: league.status || 'upcoming',
        theme_color: league.theme_color || '#007bff'
    });
    const [iconPreview, setIconPreview] = useState(league.icon_url || null);
    const [bannerPreview, setBannerPreview] = useState(league.banner_url || null);
    const [error, setError] = useState('');
    const [loading, setLoading] = useState(false);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: value
        }));
    };

    const handleFileChange = (e) => {
        const { name, files } = e.target;
        if (files && files[0]) {
            const file = files[0];
            const reader = new FileReader();

            reader.onloadend = () => {
                if (name === 'icon') {
                    setIconPreview(reader.result);
                } else if (name === 'banner') {
                    setBannerPreview(reader.result);
                }
            };

            reader.readAsDataURL(file);
            setFormData(prev => ({
                ...prev,
                [name]: file
            }));
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setError('');

        try {
            // Validate required fields
            const requiredFields = ['name', 'min_bet_amount', 'max_bet_amount', 'description'];
            for (const field of requiredFields) {
                if (!formData[field]) {
                    throw new Error(`${field} is required`);
                }
            }

            const data = new FormData();
            Object.keys(formData).forEach(key => {
                if (formData[key] !== null && formData[key] !== undefined) {
                    data.append(key, formData[key]);
                }
            });

            await onSave(data);
            onClose();
        } catch (err) {
            setError(err.message || 'Failed to update league');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-60 overflow-y-auto h-full w-full z-50 flex items-center justify-center p-4">
            <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="bg-gradient-to-r from-green-600 to-emerald-600 text-white p-6 rounded-t-2xl">
                    <div className="flex justify-between items-center">
                        <div>
                            <h3 className="text-xl font-bold flex items-center">
                                <FaEdit className="mr-2" /> Edit League
                            </h3>
                            <p className="text-green-100 text-sm">Update league settings and information</p>
                        </div>
                        <button
                            onClick={onClose}
                            className="text-white hover:text-gray-200 text-2xl font-bold w-8 h-8 flex items-center justify-center rounded-full hover:bg-white hover:bg-opacity-20 transition-all"
                        >
                            ×
                        </button>
                    </div>
                </div>

                {error && <div className="error-message"><FaExclamationCircle /> {error}</div>}

                <form onSubmit={handleSubmit} className="edit-league-form">
                    <div className="form-section">
                        <h3>League Media</h3>
                        <div className="media-preview-section">
                            <div className="icon-preview-container">
                                <label htmlFor="icon">
                                    {iconPreview ? (
                                        <img src={iconPreview} alt="League icon preview" className="preview-image" />
                                    ) : (
                                        <div className="preview-placeholder">
                                            <FaImage />
                                            <span>Upload League Icon</span>
                                            <small>Recommended: 300x300px</small>
                                        </div>
                                    )}
                                </label>
                                <input
                                    type="file"
                                    id="icon"
                                    name="icon"
                                    onChange={handleFileChange}
                                    accept="image/*"
                                    style={{ display: 'none' }}
                                />
                            </div>

                            <div className="banner-preview-container">
                                <label htmlFor="banner">
                                    {bannerPreview ? (
                                        <img src={bannerPreview} alt="League banner preview" className="preview-image" />
                                    ) : (
                                        <div className="preview-placeholder">
                                            <FaImage />
                                            <span>Upload League Banner</span>
                                            <small>Recommended: 1200x400px</small>
                                        </div>
                                    )}
                                </label>
                                <input
                                    type="file"
                                    id="banner"
                                    name="banner"
                                    onChange={handleFileChange}
                                    accept="image/*"
                                    style={{ display: 'none' }}
                                />
                            </div>
                        </div>

                        <div className="color-preview-section" style={{ '--theme-color': formData.theme_color }}>
                            <label htmlFor="theme_color">Theme Color</label>
                            <input
                                type="color"
                                id="theme_color"
                                name="theme_color"
                                value={formData.theme_color}
                                onChange={handleInputChange}
                                className="color-picker"
                            />
                            <span className="color-value">{formData.theme_color}</span>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Basic Information</h3>
                        <div className="form-group">
                            <label htmlFor="name">League Name</label>
                            <input
                                type="text"
                                id="name"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="description">Description</label>
                            <textarea
                                id="description"
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                required
                            />
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>League Settings</h3>
                        <div className="form-group">
                            <label htmlFor="min_bet_amount">Minimum Bet Amount</label>
                            <input
                                type="number"
                                id="min_bet_amount"
                                name="min_bet_amount"
                                value={formData.min_bet_amount}
                                onChange={handleInputChange}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="max_bet_amount">Maximum Bet Amount</label>
                            <input
                                type="number"
                                id="max_bet_amount"
                                name="max_bet_amount"
                                value={formData.max_bet_amount}
                                onChange={handleInputChange}
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="season_duration">Season Duration (days)</label>
                            <input
                                type="number"
                                id="season_duration"
                                name="season_duration"
                                value={formData.season_duration}
                                onChange={handleInputChange}
                                min="1"
                                required
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="status">Status</label>
                            <select
                                id="status"
                                name="status"
                                value={formData.status}
                                onChange={handleInputChange}
                            >
                                <option value="upcoming">Upcoming</option>
                                <option value="active">Active</option>
                                <option value="completed">Completed</option>
                            </select>
                        </div>
                    </div>

                    <div className="form-section">
                        <h3>Additional Information</h3>
                        <div className="form-group">
                            <label htmlFor="league_rules">League Rules</label>
                            <textarea
                                id="league_rules"
                                name="league_rules"
                                value={formData.league_rules}
                                onChange={handleInputChange}
                            />
                        </div>

                        <div className="form-group">
                            <label htmlFor="reward_description">Reward Description</label>
                            <textarea
                                id="reward_description"
                                name="reward_description"
                                value={formData.reward_description}
                                onChange={handleInputChange}
                            />
                        </div>
                    </div>

                    <div className="form-actions">
                        <button type="button" onClick={onClose} className="cancel-button" disabled={loading}>
                            Cancel
                        </button>
                        <button type="submit" className="submit-button" disabled={loading}>
                            <FaSave /> {loading ? 'Saving...' : 'Save Changes'}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
};

export default LeagueManagement;
