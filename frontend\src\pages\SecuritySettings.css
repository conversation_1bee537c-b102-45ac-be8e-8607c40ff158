/* Security Settings Styles */
.security-settings {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.security-settings h1 {
  color: #000000;
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.75rem;
}

.security-description {
  margin-bottom: 2rem;
  color: #4b5563;
  font-size: 1rem;
  line-height: 1.6;
}

/* Form Styles */
.security-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-section {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
}

.form-section h2 {
  color: #166534;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #374151;
  font-weight: 500;
  font-size: 0.95rem;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="email"],
.form-group input[type="password"],
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: #f9fafb;
  font-size: 0.95rem;
  transition: all 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #166534;
  box-shadow: 0 0 0 3px rgba(22, 101, 52, 0.1);
}

.form-group input:disabled,
.form-group select:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
  opacity: 0.7;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.checkbox-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 0.5rem;
}

.checkbox-options label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.field-description {
  margin-top: 0.25rem;
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.btn-primary {
  background-color: #166534;
  color: white;
}

.btn-primary:hover {
  background-color: #15803d;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Alert Styles */
.success-alert,
.warning-alert,
.error-alert {
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.success-alert {
  background-color: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.warning-alert {
  background-color: #fff7ed;
  color: #c2410c;
  border: 1px solid #fed7aa;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.error-alert {
  background-color: #fee2e2;
  color: #b91c1c;
  border: 1px solid #fecaca;
}

.btn-link {
  background: none;
  border: none;
  color: #166534;
  padding: 0.5rem 0;
  font-weight: 500;
  cursor: pointer;
  text-decoration: underline;
}

.btn-link:hover {
  color: #15803d;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #6b7280;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .security-settings {
    padding: 1rem;
  }
  
  .form-section {
    padding: 1.25rem;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
  }
}
