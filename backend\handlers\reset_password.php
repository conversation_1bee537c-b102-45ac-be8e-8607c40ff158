<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode([
        "success" => false,
        "message" => "Method not allowed"
    ]);
    exit;
}

require_once '../config/database.php';

try {
    $conn = getDBConnection();
    
    // Get request data
    $data = json_decode(file_get_contents("php://input"));
    
    // Validate required fields
    if (!isset($data->email) || !isset($data->new_password)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Email and new password are required"
        ]);
        exit;
    }
    
    $email = trim($data->email);
    $newPassword = $data->new_password;
    
    // Validate email format
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid email format"
        ]);
        exit;
    }
    
    // Validate password length
    if (strlen($newPassword) < 6) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Password must be at least 6 characters long"
        ]);
        exit;
    }
    
    // Check if user exists
    $stmt = $conn->prepare("SELECT user_id, email FROM users WHERE email = :email");
    $stmt->bindParam(':email', $email);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(404);
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check if there's a valid OTP verification for this user
    // This ensures the user has completed the OTP verification step
    $stmt = $conn->prepare("
        SELECT id FROM user_otp 
        WHERE user_id = :user_id 
        AND expires_at > NOW() 
        AND attempts < 5
        ORDER BY id DESC 
        LIMIT 1
    ");
    $stmt->bindParam(':user_id', $user['user_id']);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        http_response_code(400);
        echo json_encode([
            "success" => false,
            "message" => "Invalid or expired reset session. Please start the password reset process again."
        ]);
        exit;
    }
    
    // Hash the new password
    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // Begin transaction
    $conn->beginTransaction();
    
    try {
        // Update user password
        $stmt = $conn->prepare("UPDATE users SET password = :password WHERE user_id = :user_id");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':user_id', $user['user_id']);
        
        if (!$stmt->execute()) {
            throw new Exception("Failed to update password");
        }
        
        // Clear all OTP records for this user (invalidate the reset session)
        $stmt = $conn->prepare("DELETE FROM user_otp WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $user['user_id']);
        $stmt->execute();
        
        // Log the password reset (optional - for security audit)
        $stmt = $conn->prepare("
            INSERT INTO user_activity_log (user_id, activity_type, details, created_at)
            VALUES (:user_id, 'password_reset', :details, NOW())
        ");
        $details = json_encode([
            'method' => 'email_verification',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
        $stmt->bindParam(':user_id', $user['user_id']);
        $stmt->bindParam(':details', $details);
        $stmt->execute();
        
        $conn->commit();
        
        // Send confirmation email (optional)
        sendPasswordResetConfirmationEmail($user['email'], $conn);
        
        http_response_code(200);
        echo json_encode([
            "success" => true,
            "message" => "Password reset successfully"
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => "Database error: " . $e->getMessage()
    ]);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        "success" => false,
        "message" => $e->getMessage()
    ]);
}

/**
 * Send password reset confirmation email
 */
function sendPasswordResetConfirmationEmail($email, $conn) {
    try {
        // Get SMTP settings
        $stmt = $conn->prepare("SELECT * FROM smtp_settings WHERE is_active = 1 ORDER BY id DESC LIMIT 1");
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            return false;
        }
        
        $smtp = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Set up PHPMailer
        require '../vendor/autoload.php';
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        // Server settings
        $mail->isSMTP();
        $mail->Host = $smtp['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $smtp['username'];
        $mail->Password = $smtp['password'];
        
        if ($smtp['encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($smtp['encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
        }
        
        $mail->Port = $smtp['port'];
        
        // Recipients
        $mail->setFrom($smtp['from_email'], $smtp['from_name']);
        $mail->addAddress($email);
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = "FanBet247 - Password Reset Confirmation";
        $mail->Body = "
            <html>
            <head>
                <title>Password Reset Confirmation</title>
            </head>
            <body>
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;'>
                    <h2 style='color: #2c5f2d;'>Password Reset Successful</h2>
                    <p>Your FanBet247 account password has been successfully reset.</p>
                    <p><strong>Reset Details:</strong></p>
                    <ul>
                        <li>Date: " . date('Y-m-d H:i:s') . "</li>
                        <li>IP Address: " . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . "</li>
                    </ul>
                    <p>If you did not request this password reset, please contact our support team immediately.</p>
                    <p>For security reasons, we recommend:</p>
                    <ul>
                        <li>Using a strong, unique password</li>
                        <li>Enabling two-factor authentication</li>
                        <li>Not sharing your login credentials</li>
                    </ul>
                    <hr style='border: 1px solid #ddd; margin: 20px 0;'>
                    <p style='font-size: 12px; color: #666;'>
                        This is an automated message from FanBet247. Please do not reply to this email.
                    </p>
                </div>
            </body>
            </html>
        ";
        
        $mail->send();
        return true;
        
    } catch (Exception $e) {
        error_log("Failed to send password reset confirmation email: " . $e->getMessage());
        return false;
    }
}
?>
