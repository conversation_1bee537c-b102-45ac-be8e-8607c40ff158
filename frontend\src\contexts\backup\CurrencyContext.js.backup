import React, { createContext, useState, useContext, useEffect, useCallback } from 'react';
import { fetchCurrencies, fetchExchangeRates, getUserCurrencyPreference } from '../utils/currencyUtils';
import { useUser } from '../context/UserContext';

const CurrencyContext = createContext();

export const CurrencyProvider = ({ children }) => {
    const [currencies, setCurrencies] = useState([]);
    const [exchangeRates, setExchangeRates] = useState([]);
    const [userCurrency, setUserCurrency] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const { userData } = useUser();

    // Cache currencies in localStorage to reduce API calls
    const CACHE_KEY = 'fanbet247_currencies';
    const CACHE_EXPIRY = 'fanbet247_currencies_expiry';
    const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

    const getCachedCurrencies = () => {
        try {
            const cached = localStorage.getItem(CACHE_KEY);
            const expiry = localStorage.getItem(CACHE_EXPIRY);
            
            if (cached && expiry && Date.now() < parseInt(expiry)) {
                return JSON.parse(cached);
            }
        } catch (error) {
            console.error('Error reading cached currencies:', error);
        }
        return null;
    };

    const setCachedCurrencies = (data) => {
        try {
            localStorage.setItem(CACHE_KEY, JSON.stringify(data));
            localStorage.setItem(CACHE_EXPIRY, (Date.now() + CACHE_DURATION).toString());
        } catch (error) {
            console.error('Error caching currencies:', error);
        }
    };

    const loadCurrencies = useCallback(async () => {
        try {
            setError(null);
            
            // Try to get cached currencies first
            const cached = getCachedCurrencies();
            if (cached) {
                setCurrencies(cached);
                return cached;
            }

            // Fetch from API if no cache
            const currenciesData = await fetchCurrencies(true);
            setCurrencies(currenciesData);
            setCachedCurrencies(currenciesData);
            return currenciesData;
        } catch (err) {
            console.error('Error loading currencies:', err);
            setError('Failed to load currencies');
            
            // Fallback to USD if everything fails
            const fallbackCurrency = {
                id: 1,
                currency_code: 'USD',
                currency_name: 'US Dollar',
                currency_symbol: '$',
                is_active: true,
                display_name: '$ USD - US Dollar'
            };
            setCurrencies([fallbackCurrency]);
            return [fallbackCurrency];
        }
    }, []);

    const loadExchangeRates = useCallback(async () => {
        try {
            const ratesData = await fetchExchangeRates();
            setExchangeRates(ratesData);
            return ratesData;
        } catch (err) {
            console.error('Error loading exchange rates:', err);
            setError('Failed to load exchange rates');
            return [];
        }
    }, []);

    const loadUserCurrency = useCallback(async (userId) => {
        if (!userId) return null;
        
        try {
            const userCurrencyData = await getUserCurrencyPreference(userId);
            setUserCurrency(userCurrencyData.preferred_currency);
            return userCurrencyData.preferred_currency;
        } catch (err) {
            console.error('Error loading user currency:', err);
            // Fallback to USD
            const usdCurrency = currencies.find(c => c.currency_code === 'USD') || {
                id: 1,
                currency_code: 'USD',
                currency_name: 'US Dollar',
                currency_symbol: '$',
                rate_to_fancoin: 1.0
            };
            setUserCurrency(usdCurrency);
            return usdCurrency;
        }
    }, [currencies]);

    // Initialize currency data
    useEffect(() => {
        const initializeCurrencyData = async () => {
            setLoading(true);
            try {
                await loadCurrencies();
                await loadExchangeRates();
                
                // Load user currency if user is logged in
                const userId = localStorage.getItem('userId');
                if (userId) {
                    await loadUserCurrency(userId);
                }
            } catch (err) {
                console.error('Error initializing currency data:', err);
            } finally {
                setLoading(false);
            }
        };

        initializeCurrencyData();
    }, [loadCurrencies, loadExchangeRates, loadUserCurrency]);

    // Update user currency when user data changes
    useEffect(() => {
        const userId = localStorage.getItem('userId');
        if (userId && currencies.length > 0 && !userCurrency) {
            loadUserCurrency(userId);
        }
    }, [userData, currencies, userCurrency, loadUserCurrency]);

    // Convert FanCoin amount to user's preferred currency
    const convertToUserCurrency = useCallback((fanCoinAmount) => {
        if (!userCurrency || !userCurrency.rate_to_fancoin) {
            return {
                original_amount: fanCoinAmount,
                converted_amount: fanCoinAmount,
                currency_symbol: '$',
                currency_code: 'USD',
                formatted_amount: `$${fanCoinAmount.toFixed(2)}`
            };
        }

        const convertedAmount = fanCoinAmount * userCurrency.rate_to_fancoin;
        return {
            original_amount: fanCoinAmount,
            converted_amount: convertedAmount,
            currency_symbol: userCurrency.currency_symbol,
            currency_code: userCurrency.currency_code,
            formatted_amount: `${userCurrency.currency_symbol}${convertedAmount.toLocaleString('en-US', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            })}`
        };
    }, [userCurrency]);

    // Format amount for display with both FanCoin and local currency
    const formatAmountForDisplay = useCallback((fanCoinAmount, showBoth = true) => {
        const conversion = convertToUserCurrency(fanCoinAmount);
        
        if (showBoth && userCurrency && userCurrency.currency_code !== 'USD') {
            return `${conversion.formatted_amount} (${fanCoinAmount} FanCoin)`;
        }
        
        return conversion.formatted_amount;
    }, [convertToUserCurrency, userCurrency]);

    // Get currency by ID
    const getCurrencyById = useCallback((currencyId) => {
        return currencies.find(currency => currency.id === currencyId) || null;
    }, [currencies]);

    // Get currency by code
    const getCurrencyByCode = useCallback((currencyCode) => {
        return currencies.find(currency => currency.currency_code === currencyCode) || null;
    }, [currencies]);

    // Refresh currency data
    const refreshCurrencyData = useCallback(async () => {
        // Clear cache
        localStorage.removeItem(CACHE_KEY);
        localStorage.removeItem(CACHE_EXPIRY);
        
        setLoading(true);
        try {
            await Promise.all([
                loadCurrencies(),
                loadExchangeRates()
            ]);
            
            const userId = localStorage.getItem('userId');
            if (userId) {
                await loadUserCurrency(userId);
            }
        } catch (err) {
            console.error('Error refreshing currency data:', err);
        } finally {
            setLoading(false);
        }
    }, [loadCurrencies, loadExchangeRates, loadUserCurrency]);

    // Update user currency preference
    const updateUserCurrency = useCallback((newCurrency) => {
        setUserCurrency(newCurrency);
    }, []);

    const value = {
        // Data
        currencies,
        exchangeRates,
        userCurrency,
        loading,
        error,
        
        // Functions
        convertToUserCurrency,
        formatAmountForDisplay,
        getCurrencyById,
        getCurrencyByCode,
        refreshCurrencyData,
        updateUserCurrency,
        loadUserCurrency,
        
        // State setters for external updates
        setCurrencies,
        setExchangeRates,
        setUserCurrency
    };

    return (
        <CurrencyContext.Provider value={value}>
            {children}
        </CurrencyContext.Provider>
    );
};

export const useCurrency = () => {
    const context = useContext(CurrencyContext);
    if (!context) {
        throw new Error('useCurrency must be used within a CurrencyProvider');
    }
    return context;
};

export default CurrencyContext;
