-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:56 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `link`
--

-- --------------------------------------------------------

--
-- Table structure for table `active_sessions`
--

CREATE TABLE `active_sessions` (
  `session_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiry` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `active_sessions`
--

INSERT INTO `active_sessions` (`session_id`, `user_id`, `ip_address`, `expiry`) VALUES
(33, 18, '::1', '2024-09-20 16:28:38'),
(34, 18, '::1', '2024-09-20 16:28:39'),
(39, 17, '::1', '2024-10-29 09:13:59'),
(40, 17, '::1', '2024-10-29 09:13:59'),
(41, 11, '::1', '2025-05-23 09:34:53');

-- --------------------------------------------------------

--
-- Table structure for table `admin_actions`
--

CREATE TABLE `admin_actions` (
  `action_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `api_keys`
--

CREATE TABLE `api_keys` (
  `id` int(11) NOT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `cse_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `api_keys`
--

INSERT INTO `api_keys` (`id`, `api_key`, `cse_id`, `created_at`) VALUES
(1, '1111', '111', '2024-09-21 12:07:55'),
(2, '1111', '111', '2024-09-21 12:10:39'),
(3, '1111', '111', '2024-09-21 12:12:46'),
(4, 'AIzaSyB-OLLScuar8OUnH0Ge1RTjWOaeBIHbp0U', '256bf14bc674940b1', '2024-09-21 12:46:41'),
(5, 'AIzaSyB-OLLScuar8OUnH0Ge1RTjWOaeBIHbp0U', '256bf14bc674940b1', '2024-09-21 12:46:46'),
(6, 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxbp0U', 'xxxxxxxxxxxxx40b1', '2024-09-21 14:23:41'),
(7, 'AIzaSyB-OLLScuar8OUnH0Ge1RTjWOaeBIHbp0U', '256bf14bc674940b1', '2024-09-21 14:28:58'),
(8, 'AIzaSyB-OLLScuar8OUnH0Ge1RTjWOaeBIHbp0U', '256bf14bc674940b1', '2024-09-21 15:40:36'),
(9, 'xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxbp0U', 'xxxxxxxxxxxxx40b1', '2025-05-22 09:37:43'),
(10, 'AIzaSyBa9SL4B1itwDsO9X-SK1LC9qddMHp1esQ', '256bf14bc674940b1', '2025-05-22 09:41:26');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subscription_id` int(11) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `proof_of_payment` varchar(255) DEFAULT NULL,
  `admin_notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payments`
--

INSERT INTO `payments` (`id`, `user_id`, `subscription_id`, `payment_method_id`, `amount`, `status`, `proof_of_payment`, `admin_notes`, `created_at`, `updated_at`) VALUES
(1, 11, 2, 1, '0.00', 'approved', '66e09acc614a5_qrcode-ai-6ede0d02-cb58-49da-adda-1d166f8bf44d.png', NULL, '2024-09-10 19:15:24', '2024-09-10 19:25:09'),
(2, 12, 2, 1, '0.00', 'approved', '66e09fda7c594_qrcode-ai-6ede0d02-cb58-49da-adda-1d166f8bf44d.png', NULL, '2024-09-10 19:36:58', '2024-09-10 19:37:41'),
(3, 15, 2, 1, '0.00', 'approved', '66e0c6bf787cf_qrcode-ai-6ede0d02-cb58-49da-adda-1d166f8bf44d.png', NULL, '2024-09-10 22:22:55', '2024-09-10 22:23:27'),
(4, 16, 1, 3, '0.00', 'approved', '66e0c8bbbe705_qrcode-ai-6ede0d02-cb58-49da-adda-1d166f8bf44d.png', NULL, '2024-09-10 22:31:23', '2024-09-10 22:33:09'),
(5, 16, 1, 1, '0.00', 'approved', '66e0d554d4f46_cargoliftlogs-com (13).webp', NULL, '2024-09-10 23:25:08', '2024-09-10 23:25:18'),
(7, 17, 2, 1, '50.00', 'approved', '66eb2d4d0430d_ance-dashb-1-2024-09-15-18_19_19.png', NULL, '2024-09-18 19:43:09', '2024-09-18 19:43:45'),
(8, 18, 3, 1, '100.00', 'approved', '66ec353a814f2_Screenshot 2024-01-17 205022.png', NULL, '2024-09-19 14:29:14', '2024-09-19 14:30:11'),
(9, 11, 1, 1, '20.00', 'approved', '679b14909d1ca_Screenshot 2025-01-08 170832.png', NULL, '2025-01-30 05:56:32', '2025-01-30 05:57:16'),
(10, 11, 1, 1, '20.00', 'approved', '679b149c6e991_Screenshot 2025-01-08 170832.png', NULL, '2025-01-30 05:56:44', '2025-01-30 05:57:07'),
(11, 11, 1, 1, '20.00', 'approved', '682ef020a28e6_creative-modern-banking-logo-with-dark-g_skJjAA9VRdK31p09l6WNtA_68EzXCgIS5qMpiI40qol3w.jpeg', NULL, '2025-05-22 09:36:32', '2025-05-22 09:36:38');

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('bank_transfer','cryptocurrency') NOT NULL,
  `details` text NOT NULL,
  `barcode_image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `bank_name` varchar(255) DEFAULT NULL,
  `account_name` varchar(255) DEFAULT NULL,
  `account_number` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `type`, `details`, `barcode_image`, `created_at`, `updated_at`, `bank_name`, `account_name`, `account_number`) VALUES
(1, 'Bitcoin', 'cryptocurrency', 'djhdjjdjdjjdjdjdjjd', '66e08c25efe32_qrcode-ai-6ede0d02-cb58-49da-adda-1d166f8bf44d.png', '2024-09-10 18:12:53', '2024-09-10 18:12:53', NULL, NULL, NULL),
(3, 'Bank', 'bank_transfer', '', NULL, '2024-09-10 18:36:34', '2024-09-10 18:36:34', 'Fnb', 'Jamesbong', '**********');

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `subscription_id` int(11) NOT NULL,
  `plan_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `duration_days` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscriptions`
--

INSERT INTO `subscriptions` (`subscription_id`, `plan_name`, `duration_days`, `user_id`, `amount`) VALUES
(1, 'Starter', 30, NULL, '20.00'),
(2, 'Plantinum', 60, NULL, '50.00'),
(3, 'Ultimate Plan', 90, NULL, '100.00');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('user','admin') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'user',
  `subscription_expiry` datetime DEFAULT NULL,
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verification_token` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `plan_id` int(11) DEFAULT NULL,
  `status` enum('active','suspended') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `password`, `email`, `role`, `subscription_expiry`, `email_verified`, `created_at`, `verification_token`, `expiry_date`, `plan_id`, `status`) VALUES
(9, 'admin', '$2y$10$kIcjroz.zHaLE8CUcWtddOC6Mhn0e4xhdMlMe2lcKn03Gf.6ZlTcG', '<EMAIL>', 'admin', NULL, 1, '2024-09-09 21:21:53', NULL, NULL, NULL, 'active'),
(11, 'demohomexxx', '$2y$10$3AQjTrmM34EEfK45HTja1eecHKvjAbSSeiV/xzcOIydCbDccyLwDa', '<EMAIL>', 'user', '2025-06-21 09:36:38', 1, '2024-09-10 08:44:30', '6b688fd442578bb924f7369fd0755285', NULL, 1, 'active'),
(12, 'baddie', '$2y$10$tRKLmhVXO/XX4ZsCuIA3SO8tiIodcREXW/OUJyOAKdcWDeq7Jebc6', '<EMAIL>', 'user', NULL, 1, '2024-09-10 13:43:13', '03044cdd47524e6189b865ad14d04319', NULL, 2, 'active'),
(13, 'jamesbong01', '$2y$10$j/55XGnVO8h6U5Y1JHDus.hu/i6JjOsC0SKTdmMH2UkWVCcc5Luki', '<EMAIL>', 'user', '2024-10-10 23:47:29', 0, '2024-09-10 21:42:11', '1247bb0aba6dc69fc6c34154bf94b7ae', NULL, 1, 'active'),
(14, 'lunkas', '$2y$10$rIcFwqqBX8ncLmsEr39KXuxr7F6OIz1prHDNXcpuASDkGe7ypGagS', '<EMAIL>', 'user', NULL, 1, '2024-09-10 21:48:52', '8f5dcb575545d4bdb2d595ce4709ecf5', NULL, 2, 'active'),
(15, 'Toguex', '$2y$10$Gg6DZcLfVIAnG/O9KVAbs.LEgtcCjLzhjZ4WNz0qtOWsTD4KyU0wq', '<EMAIL>', 'user', NULL, 1, '2024-09-10 22:21:38', NULL, NULL, 2, 'active'),
(16, 'thegamerx', '$2y$10$Vv.WNdMytUkYQosElbsqzODzTPU0Cr9lotSSky8W.9Xl9TwvdVzyq', '<EMAIL>', 'user', '2024-10-11 01:25:18', 1, '2024-09-10 22:29:42', NULL, NULL, 1, 'active'),
(17, 'lilwayne', '$2y$10$7lE.lo0kQma8FknUgkjR1ecIaLYQYk0MGFqT7bi2RX3ZJ2Xk3qKrC', '<EMAIL>', 'user', '2024-11-17 21:43:45', 1, '2024-09-10 22:50:51', NULL, NULL, 2, 'active'),
(18, 'davido01', '$2y$10$kIzYGj4fM7SA7UEVpp5ozeKX3HhBjnJktq8jU0SYjvOPUnYh/nQj2', '<EMAIL>', 'user', '2024-12-18 16:30:11', 1, '2024-09-18 19:50:41', 'adaa16f86b5e5b5539bb79b3d2b89f47', NULL, 3, 'active');

-- --------------------------------------------------------

--
-- Table structure for table `user_searches`
--

CREATE TABLE `user_searches` (
  `search_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `search_query` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `field` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `job_title` varchar(255) DEFAULT NULL,
  `email_provider` varchar(255) DEFAULT NULL,
  `negative_terms` text,
  `country` varchar(255) DEFAULT NULL,
  `state` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `search_type` enum('comprehensive','specific','broad') DEFAULT NULL,
  `result_type` enum('image','web') DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `user_searches`
--

INSERT INTO `user_searches` (`search_id`, `user_id`, `search_query`, `created_at`, `field`, `name`, `job_title`, `email_provider`, `negative_terms`, `country`, `state`, `city`, `search_type`, `result_type`) VALUES
(20, 17, '   -intitle:\"profiles\" -inurl:\"dir/\" site:www.linkedin.com/in OR AND -site:linkedin.com/pub -site:linkedin.com/in/dir', '2024-09-21 14:07:43', '', '', '', '', '', '', '', '', 'comprehensive', 'web'),
(21, 11, '   -intitle:\"profiles\" -inurl:\"dir/\" site:www.linkedin.com/in OR AND -site:linkedin.com/pub -site:linkedin.com/in/dir', '2025-01-30 05:58:48', '', '', '', '', '', '', '', '', 'comprehensive', 'web'),
(22, 11, '   -intitle:\"profiles\" -inurl:\"dir/\" site:www.linkedin.com/in OR AND -site:linkedin.com/pub -site:linkedin.com/in/dir', '2025-05-22 09:37:07', '', '', '', '', '', '', '', '', 'comprehensive', 'web');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `active_sessions`
--
ALTER TABLE `active_sessions`
  ADD PRIMARY KEY (`session_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `admin_actions`
--
ALTER TABLE `admin_actions`
  ADD PRIMARY KEY (`action_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `api_keys`
--
ALTER TABLE `api_keys`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `subscription_id` (`subscription_id`),
  ADD KEY `payment_method_id` (`payment_method_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`subscription_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `plan_id` (`plan_id`);

--
-- Indexes for table `user_searches`
--
ALTER TABLE `user_searches`
  ADD PRIMARY KEY (`search_id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `active_sessions`
--
ALTER TABLE `active_sessions`
  MODIFY `session_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

--
-- AUTO_INCREMENT for table `admin_actions`
--
ALTER TABLE `admin_actions`
  MODIFY `action_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `api_keys`
--
ALTER TABLE `api_keys`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `subscription_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `user_searches`
--
ALTER TABLE `user_searches`
  MODIFY `search_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `active_sessions`
--
ALTER TABLE `active_sessions`
  ADD CONSTRAINT `active_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `admin_actions`
--
ALTER TABLE `admin_actions`
  ADD CONSTRAINT `admin_actions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `admin_actions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE SET NULL;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`subscription_id`) REFERENCES `subscriptions` (`subscription_id`),
  ADD CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`);

--
-- Constraints for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD CONSTRAINT `subscriptions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`plan_id`) REFERENCES `subscriptions` (`subscription_id`);

--
-- Constraints for table `user_searches`
--
ALTER TABLE `user_searches`
  ADD CONSTRAINT `user_searches_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
