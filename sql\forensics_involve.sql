-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:55 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `forensics_involve`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `last_login` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `password`, `full_name`, `email`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$DFw4Kt7jiFjtg4SDnKi0SuwfqBi3TSLzvIejylZq1WosjCdClq3zy', 'Administrator', '<EMAIL>', '2025-05-27 10:34:48', '2025-05-24 20:26:52', '2025-05-27 10:34:48');

-- --------------------------------------------------------

--
-- Table structure for table `blog_posts`
--

CREATE TABLE `blog_posts` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `excerpt` text,
  `category_id` int(11) DEFAULT NULL,
  `content` longtext NOT NULL,
  `author` varchar(100) NOT NULL,
  `category` varchar(50) NOT NULL,
  `tags` text,
  `featured_image` varchar(255) DEFAULT NULL,
  `status` enum('draft','published') DEFAULT 'draft',
  `is_featured` tinyint(1) DEFAULT '0',
  `views` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `published_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `blog_posts`
--

INSERT INTO `blog_posts` (`id`, `title`, `slug`, `excerpt`, `category_id`, `content`, `author`, `category`, `tags`, `featured_image`, `status`, `is_featured`, `views`, `created_at`, `updated_at`, `published_at`) VALUES
(1, 'Cryptocurrency Fraud Reaches Record Highs in 2025', 'cryptocurrency-fraud-reaches-record-highs-2025', 'Cryptocurrency fraud has increased by 85% this year, with investment scams being the most common type affecting victims worldwide.', NULL, '## The Growing Threat of Cryptocurrency Fraud\r\n\r\nNew data reveals that cryptocurrency-related fraud has increased by 85% this year, with investment scams being the most common type. As digital currencies become more mainstream, fraudsters are developing increasingly sophisticated methods to deceive victims.\r\n\r\n### Common Cryptocurrency Scams\r\n\r\n1. **Investment Platforms**: Fake investment websites promising guaranteed returns\r\n2. **Social Media Scams**: Fraudsters impersonating celebrities or influencers\r\n3. **Phishing Attacks**: Fake wallet applications and exchange platforms\r\n4. **Romance Scams**: Building relationships to solicit cryptocurrency investments\r\n\r\n### Protection Strategies\r\n\r\n- Always verify the legitimacy of investment platforms\r\n- Never send cryptocurrency to unknown individuals\r\n- Use hardware wallets for large holdings\r\n- Report suspicious activity immediately\r\n\r\nIf you\'ve been a victim of cryptocurrency fraud, our digital forensics team can help trace transactions and recover funds through legal channels.', 'Digital Forensics Team', 'Cryptocurrency', 'cryptocurrency,fraud,investment,scam,bitcoin', '', 'published', 0, 8, '2025-05-24 20:28:45', '2025-05-25 12:29:10', NULL),
(2, 'Romance Scams: How to Protect Yourself from Online Dating Fraud', 'romance-scams-protect-yourself-online-dating-fraud', 'Romance scams exploit emotions and trust, resulting in billions in losses. Learn the warning signs and protection strategies.', NULL, '## Understanding Romance Scams\r\n\r\nRomance scams have become one of the fastest-growing types of fraud, with victims losing billions of dollars annually. These scams exploit human emotions and the desire for companionship, making them particularly devastating.\r\n\r\n### How Romance Scams Work\r\n\r\nScammers create fake profiles on dating sites and social media platforms, often using stolen photos of attractive individuals. They spend weeks or months building emotional connections with their victims before introducing financial requests.\r\n\r\n### Warning Signs\r\n\r\n- **Professes love very quickly**\r\n- **Avoids phone calls or video chats**\r\n- **Claims to be traveling or deployed overseas**\r\n- **Asks for money for emergencies**\r\n- **Requests gift cards or cryptocurrency**\r\n- **Has very few photos or photos that seem too professional**\r\n\r\n### Protection Tips\r\n\r\n1. **Reverse image search** any photos they send\r\n2. **Insist on video calls** early in the relationship  \r\n3. **Never send money** to someone you haven\'t met in person\r\n4. **Be suspicious** of sob stories requiring financial help\r\n5. **Trust your instincts** if something feels off\r\n\r\n### If You\'ve Been Scammed\r\n\r\nDon\'t be embarrassed - these scammers are professionals who manipulate emotions expertly. Contact our team immediately to discuss recovery options and evidence preservation.', 'Fraud Prevention Team', 'Romance Scams', 'romance,scam,dating,fraud,online', '', 'published', 0, 3, '2025-05-24 20:28:45', '2025-05-24 22:43:13', NULL),
(3, 'Investment Fraud Recovery: Legal Options and Digital Evidence', 'investment-fraud-recovery-legal-options-digital-evidence', 'Investment fraud victims have legal remedies and recovery options available. Learn about digital evidence preservation and asset recovery.', NULL, '## Recovering from Investment Fraud\r\n\r\nInvestment fraud victims often feel helpless, but there are legal remedies and recovery options available. The key is acting quickly to preserve evidence and understanding your legal rights.\r\n\r\n### Types of Investment Fraud\r\n\r\n**Ponzi Schemes**: Using new investor money to pay earlier investors\r\n**Pump and Dump**: Artificially inflating stock prices then selling\r\n**Binary Options Scams**: Fake trading platforms that steal deposits\r\n**Forex Fraud**: Unregulated foreign exchange trading schemes\r\n**Cryptocurrency Scams**: Fake ICOs and investment platforms\r\n\r\n### Immediate Steps for Victims\r\n\r\n1. **Stop all contact** with the fraudsters\r\n2. **Document everything** - emails, messages, transaction records\r\n3. **Report to authorities** - SEC, CFTC, FBI, local police\r\n4. **Contact your bank** to attempt transaction reversals\r\n5. **Preserve digital evidence** before it\'s deleted\r\n\r\n### Legal Recovery Options\r\n\r\n- **Civil litigation** against perpetrators and enablers\r\n- **Criminal restitution** through prosecutor cooperation\r\n- **Insurance claims** if applicable\r\n- **Asset recovery** through forensic investigation\r\n\r\n### Digital Forensics Role\r\n\r\nOur team specializes in:\r\n- Blockchain transaction analysis\r\n- Email and communications forensics\r\n- Financial transaction tracing\r\n- Digital asset recovery\r\n- Evidence preservation for litigation\r\n\r\nTime is critical in fraud cases. Contact our emergency response team immediately to maximize your recovery chances.', 'Legal Recovery Team', 'Investment Fraud', 'investment,fraud,recovery,legal,evidence', '', 'published', 0, 0, '2025-05-24 20:28:45', '2025-05-24 20:28:45', NULL),
(4, '10 Essential Tips for Avoiding Online Fraud in 2025', '10-essential-tips-avoiding-online-fraud-2025', 'Stay protected in 2025 with these essential tips for avoiding online fraud, from password security to investment verification.', NULL, '## Staying Safe in the Digital Age\r\n\r\nAs technology evolves, so do the tactics used by fraudsters. Here are ten essential tips to protect yourself from online fraud in 2025.\r\n\r\n### 1. Use Strong, Unique Passwords\r\n\r\nCreate complex passwords for each account and use a reputable password manager. Enable two-factor authentication wherever possible.\r\n\r\n### 2. Verify Before You Trust\r\n\r\nAlways verify the identity of individuals or organizations requesting personal or financial information through independent channels.\r\n\r\n### 3. Be Cautious with Public Wi-Fi\r\n\r\nAvoid accessing sensitive accounts on public networks. Use a VPN when necessary.\r\n\r\n### 4. Keep Software Updated\r\n\r\nRegularly update your devices, browsers, and security software to protect against the latest threats.\r\n\r\n### 5. Monitor Financial Accounts\r\n\r\nCheck bank and credit card statements regularly for unauthorized transactions.\r\n\r\n### 6. Think Before You Click\r\n\r\nBe suspicious of unexpected emails, text messages, or social media messages with links or attachments.\r\n\r\n### 7. Research Investment Opportunities\r\n\r\nVerify the legitimacy of investment platforms and financial advisors through regulatory databases.\r\n\r\n### 8. Protect Personal Information\r\n\r\nBe selective about what personal information you share on social media and online platforms.\r\n\r\n### 9. Trust Your Instincts\r\n\r\nIf something seems too good to be true or feels suspicious, investigate further before proceeding.\r\n\r\n### 10. Know How to Report Fraud\r\n\r\nFamiliarize yourself with the proper channels for reporting fraud in your jurisdiction.\r\n\r\n### Emergency Response\r\n\r\nIf you suspect you\'ve been targeted or victimized by fraud, contact our 24/7 emergency response team for immediate assistance and guidance.', 'Security Team', 'Prevention', 'prevention,security,online,fraud,tips', '', 'published', 0, 0, '2025-05-24 20:28:45', '2025-05-24 20:28:45', NULL),
(5, 'Phishing Attacks Evolution: New Tactics and Defense Strategies', 'phishing-attacks-evolution-new-tactics-defense-strategies', 'Phishing attacks are evolving with AI and new tactics. Learn about modern threats and advanced defense strategies.', NULL, '## The Evolution of Phishing Attacks\r\n\r\nPhishing attacks have become increasingly sophisticated, moving beyond simple email scams to complex, multi-channel campaigns that can fool even security-conscious individuals.\r\n\r\n### Modern Phishing Techniques\r\n\r\n**Spear Phishing**: Highly targeted attacks using personal information\r\n**Smishing**: SMS-based phishing attacks\r\n**Vishing**: Voice call phishing using social engineering\r\n**Business Email Compromise (BEC)**: Targeting business communications\r\n**Social Media Phishing**: Fake messages on social platforms\r\n\r\n### AI-Enhanced Phishing\r\n\r\nCriminals now use artificial intelligence to:\r\n- Create more convincing fake communications\r\n- Generate realistic voice clones for vishing\r\n- Automate personalized attack campaigns\r\n- Bypass traditional security filters\r\n\r\n### Red Flags to Watch For\r\n\r\n- **Urgent language** creating pressure to act quickly\r\n- **Generic greetings** like \"Dear Customer\"\r\n- **Suspicious URLs** that don\'t match the claimed sender\r\n- **Requests for sensitive information** via email or text\r\n- **Unexpected attachments** or download links\r\n\r\n### Advanced Defense Strategies\r\n\r\n1. **Email Authentication**: Implement DMARC, SPF, and DKIM\r\n2. **Security Awareness Training**: Regular employee education\r\n3. **Multi-Factor Authentication**: Add extra security layers\r\n4. **Email Filtering**: Use advanced threat detection\r\n5. **Incident Response Plan**: Prepare for when attacks succeed\r\n\r\n### When Phishing Succeeds\r\n\r\nIf you\'ve fallen victim to a phishing attack:\r\n- Change all passwords immediately\r\n- Contact your financial institutions\r\n- Run comprehensive malware scans\r\n- Monitor accounts for unauthorized activity\r\n- Report the incident to authorities\r\n\r\nOur digital forensics team can help investigate phishing attacks, trace perpetrators, and recover compromised data.', 'Cybersecurity Team', 'Phishing', 'phishing,cybersecurity,email,fraud,AI', '', 'published', 0, 1, '2025-05-24 20:28:45', '2025-05-24 22:44:10', NULL),
(6, 'Blockchain Forensics: Tracing Cryptocurrency Transactions', 'blockchain-forensics-tracing-cryptocurrency-transactions', 'Blockchain forensics can trace cryptocurrency transactions to identify perpetrators and recover stolen funds through expert analysis.', NULL, '## Understanding Blockchain Forensics\r\n\r\nContrary to popular belief, cryptocurrency transactions are not anonymous but pseudonymous. Blockchain forensics leverages this transparency to trace illegal activities and recover stolen funds.\r\n\r\n### How Blockchain Analysis Works\r\n\r\nEvery cryptocurrency transaction is recorded on a public ledger. Our forensics experts use specialized tools to:\r\n\r\n- **Map transaction flows** between addresses\r\n- **Identify exchange connections** where crypto is converted to fiat\r\n- **Cluster addresses** belonging to the same entity\r\n- **Track mixing services** used to obscure trails\r\n- **Correlate with traditional evidence** like IP addresses and timestamps\r\n\r\n### Common Investigation Scenarios\r\n\r\n**Ransomware Payments**: Tracing payments to identify perpetrators\r\n**Investment Scams**: Following stolen funds to recovery points\r\n**Dark Web Transactions**: Connecting illegal marketplace activities\r\n**Money Laundering**: Identifying complex layering schemes\r\n**Theft and Fraud**: Tracking stolen cryptocurrency\r\n\r\n### Advanced Techniques\r\n\r\nOur team employs cutting-edge blockchain analysis techniques:\r\n\r\n- **Pattern Recognition**: Identifying behavioral signatures\r\n- **Cross-Chain Analysis**: Following funds across different blockchains\r\n- **DeFi Protocol Analysis**: Tracking through decentralized finance\r\n- **Privacy Coin Investigation**: Analyzing Monero and similar currencies\r\n- **NFT Forensics**: Investigating non-fungible token fraud\r\n\r\n### Legal Admissibility\r\n\r\nBlockchain evidence must meet legal standards:\r\n- **Chain of custody** documentation\r\n- **Technical accuracy** in analysis methods\r\n- **Expert testimony** to explain findings\r\n- **Correlation** with traditional evidence\r\n\r\n### Recovery Success Stories\r\n\r\nOur blockchain forensics team has successfully:\r\n- Recovered over $50 million in stolen cryptocurrency\r\n- Identified perpetrators in major fraud cases\r\n- Provided evidence leading to criminal convictions\r\n- Assisted in civil asset recovery proceedings\r\n\r\n### Working with Law Enforcement\r\n\r\nWe collaborate closely with:\r\n- Federal agencies (FBI, Secret Service, IRS-CI)\r\n- International partners (Europol, Interpol)\r\n- Local law enforcement agencies\r\n- Regulatory bodies (FinCEN, SEC)\r\n\r\nContact our blockchain forensics team for expert investigation and recovery services.', 'Blockchain Analysis Team', 'Blockchain Forensics', 'blockchain,forensics,cryptocurrency,investigation,recovery', '', 'published', 0, 0, '2025-05-24 20:28:45', '2025-05-24 20:28:45', NULL),
(7, 'Final Test Post', 'final-test-post', '', NULL, 'This is the final test to verify all database fields are working correctly.', 'admin', 'General', NULL, NULL, 'draft', 0, 0, '2025-05-25 13:14:23', '2025-05-25 13:14:23', NULL),
(8, 'Success Test Post', 'success-test-post', '', NULL, 'This post should be created successfully without any 500 errors!', 'admin', 'General', NULL, NULL, 'draft', 0, 0, '2025-05-25 13:15:21', '2025-05-25 13:15:21', NULL),
(9, '22', '22', '22', 4, '22', 'admin', 'Case Studies', NULL, 'admin/uploads/forensics-involve_011_-_Sequential_number_1748170346_6832f66a1458c.webp', 'published', 0, 1, '2025-05-25 13:16:16', '2025-05-25 13:16:51', NULL),
(11, 'Cold Cases Thawed: DNA Technology Reopens Decades-Old Investigations', 'cold-cases-thawed-dna-technology-reopens-decades-old-investigations', 'Advancements in forensic DNA analysis are breathing new life into cold cases long thought unsolvable.', 4, 'Forensic scientists are now using next-generation DNA sequencing and genetic genealogy to solve crimes once considered unsolvable. By reanalyzing degraded or partial samples from decades-old crime scenes, investigators are able to identify suspects and victims with unprecedented accuracy.\r\n\r\nOne recent case involved a 40-year-old unsolved homicide that was cracked after a DNA match was found through a public genealogy database. These breakthroughs are not only bringing justice to victims’ families but also reshaping how law enforcement approaches long-dormant investigations.', 'admin', 'Case Studies', NULL, 'uploads/Image_fx__4__1748171884_6832fc6cd2b6d.jpg', 'published', 0, 1, '2025-05-25 13:49:58', '2025-05-25 18:57:45', NULL),
(12, 'Digital Forensics Uncovers Hidden Evidence in Cybercrime Surge', 'digital-forensics-uncovers-hidden-evidence-in-cybercrime-surge', 'As cybercrime increases, digital forensic experts are playing a critical role in tracking down hackers and online fraudsters.', 1, 'With ransomware attacks and online fraud on the rise, digital forensics has become a cornerstone of modern criminal investigations. Experts can now recover deleted files, trace IP addresses, and examine metadata to uncover key evidence in cybercrime cases.\r\n\r\nLaw enforcement agencies are also collaborating with private tech firms to combat increasingly complex digital threats. The ability to extract and analyze data from smartphones, computers, and cloud storage has led to faster arrests and stronger cases in court.', 'admin', 'Cryptocurrency', NULL, 'uploads/Image_fx__10__1748173890_6833044204699.jpg', 'published', 0, 5, '2025-05-25 13:51:41', '2025-05-25 16:06:13', NULL),
(13, 'Forensic Psychology Helps Decode Criminal Behavior in Courtrooms', 'forensic-psychology-helps-decode-criminal-behavior-in-courtrooms', 'Forensic Psychology Helps Decode Criminal Behavior in Courtrooms', 2, 'Forensic psychology blends mental health expertise with legal procedures to evaluate a suspect&#039;s mental state during or after a crime. These professionals help determine whether a defendant is fit to stand trial or if mental illness played a role in their actions.\r\n\r\nIn recent trials, forensic psychologists have offered key insights into motive and behavior, influencing verdicts and sentencing. Their work bridges the gap between science and law, offering a deeper understanding of the human side of criminal behavior.', 'admin', 'Fraud Prevention', NULL, 'uploads/forensics-involve_005_-_Sequential_number_1748174147_68330543731c6.webp', 'published', 0, 13, '2025-05-25 13:56:03', '2025-05-27 09:38:36', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) DEFAULT NULL,
  `description` text,
  `color` varchar(7) DEFAULT '#1e40af',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `color`, `created_at`) VALUES
(1, 'Cryptocurrency', 'cryptocurrency', 'Articles about cryptocurrency fraud and recovery', '#1e40af', '2025-05-24 20:26:52'),
(2, 'Fraud Prevention', 'fraud-prevention', 'Tips and guides for preventing fraud', '#10b981', '2025-05-24 20:26:52'),
(3, 'Recovery Tips', 'recovery-tips', 'Advice for fraud victims', '#f59e0b', '2025-05-24 20:26:52'),
(4, 'Case Studies', 'case-studies', 'Real case studies and success stories', '#8b5cf6', '2025-05-24 20:26:52'),
(5, 'Industry News', 'industry-news', 'Latest news in forensic investigation', '#ef4444', '2025-05-24 20:26:52'),
(6, 'Technology', 'technology', 'Technology updates and digital forensics', '#6366f1', '2025-05-24 20:26:52');

-- --------------------------------------------------------

--
-- Table structure for table `media_files`
--

CREATE TABLE `media_files` (
  `id` int(11) NOT NULL,
  `original_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `uploaded_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `media_files`
--

INSERT INTO `media_files` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `description`, `uploaded_by`, `created_at`, `updated_at`) VALUES
(1, 'forensics-involve 011 - Sequential number.webp', 'forensics-involve_011_-_Sequential_number_1748170346_6832f66a1458c.webp', 'admin/uploads/forensics-involve_011_-_Sequential_number_1748170346_6832f66a1458c.webp', 59172, 'image/webp', '', 1, '2025-05-25 10:52:26', '2025-05-25 10:52:26'),
(2, 'Image_fx (4).jpg', 'Image_fx__4__1748171884_6832fc6cd2b6d.jpg', 'admin/uploads/Image_fx__4__1748171884_6832fc6cd2b6d.jpg', 1373136, 'image/png', '', 1, '2025-05-25 11:18:04', '2025-05-25 11:18:04'),
(3, 'Image_fx (10).jpg', 'Image_fx__10__1748173890_6833044204699.jpg', 'admin/uploads/Image_fx__10__1748173890_6833044204699.jpg', 1394829, 'image/png', '', 1, '2025-05-25 11:51:30', '2025-05-25 11:51:30'),
(4, 'forensics-involve 005 - Sequential number.webp', 'forensics-involve_005_-_Sequential_number_1748174147_68330543731c6.webp', 'admin/uploads/forensics-involve_005_-_Sequential_number_1748174147_68330543731c6.webp', 70754, 'image/webp', '', 1, '2025-05-25 11:55:47', '2025-05-25 11:55:47');

-- --------------------------------------------------------

--
-- Table structure for table `site_settings`
--

CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('text','email','phone','address','textarea') DEFAULT 'text',
  `setting_group` varchar(50) DEFAULT 'general',
  `setting_label` varchar(255) NOT NULL,
  `setting_description` text,
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int(11) DEFAULT '0',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `setting_group`, `setting_label`, `setting_description`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'phone_emergency', '(*************', 'phone', 'contact', 'Emergency Hotline', '24/7 emergency contact number', 1, 1, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(2, 'phone_general', '(*************', 'phone', 'contact', 'General Phone', 'Main business phone number', 1, 2, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(3, 'email_general', '<EMAIL>', 'email', 'contact', 'General Email', 'Main business email address', 1, 3, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(4, 'email_help', '<EMAIL>', 'email', 'contact', 'Help & Support Email', 'Customer support email address', 1, 4, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(5, 'email_emergency', '<EMAIL>', 'email', 'contact', 'Emergency Email', 'Emergency response email address', 1, 5, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(6, 'address_primary', '123 James Street, Demo City, DC 12345, United States', 'address', 'contact', 'Primary Office Address', 'Main business address', 1, 6, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(7, 'address_secondary', '100 Sample Avenue, Example Town, ET 67890, United Kingdom', 'address', 'contact', 'Secondary Office Address', 'International office address', 1, 7, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(8, 'business_hours', '24/7 Emergency Support Available', 'text', 'contact', 'Business Hours', 'Operating hours information', 1, 8, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(9, 'emergency_note', 'Available 24/7', 'text', 'contact', 'Emergency Availability', 'Emergency service availability note', 1, 9, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(10, 'social_facebook', 'https://forensicsinvolve.com/index.html', 'text', 'social', 'Facebook URL', 'Facebook page URL', 1, 10, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(11, 'social_twitter', 'https://forensicsinvolve.com/index.html', 'text', 'social', 'Twitter URL', 'Twitter profile URL', 1, 11, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(12, 'social_linkedin', 'https://forensicsinvolve.com/index.html', 'text', 'social', 'LinkedIn URL', 'LinkedIn company page URL', 1, 12, '2025-05-27 10:26:04', '2025-05-27 10:51:23'),
(13, 'social_instagram', 'https://forensicsinvolve.com/index.html', 'text', 'social', 'Instagram URL', 'Instagram profile URL', 1, 13, '2025-05-27 10:26:04', '2025-05-27 10:51:23');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indexes for table `blog_posts`
--
ALTER TABLE `blog_posts`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_published_at` (`published_at`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `media_files`
--
ALTER TABLE `media_files`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_uploaded_by` (`uploaded_by`),
  ADD KEY `idx_mime_type` (`mime_type`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `idx_setting_key` (`setting_key`),
  ADD KEY `idx_setting_group` (`setting_group`),
  ADD KEY `idx_is_active` (`is_active`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `blog_posts`
--
ALTER TABLE `blog_posts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `media_files`
--
ALTER TABLE `media_files`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `media_files`
--
ALTER TABLE `media_files`
  ADD CONSTRAINT `media_files_ibfk_1` FOREIGN KEY (`uploaded_by`) REFERENCES `admin_users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
