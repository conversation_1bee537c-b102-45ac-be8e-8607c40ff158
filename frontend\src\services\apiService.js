/**
 * Centralized API Service for FanBet247
 * 
 * This service provides a consistent interface for all API calls,
 * ensuring proper error handling, response formatting, and request consistency.
 */

import axios from '../utils/axiosConfig';

// Standard response format interface
class ApiResponse {
    constructor(success, data = null, message = '', error = null, meta = {}) {
        this.success = success;
        this.data = data;
        this.message = message;
        this.error = error;
        this.meta = meta;
    }
}

// API Service class
class ApiService {
    /**
     * Generic GET request handler
     * @param {string} endpoint - API endpoint (without /handlers/ prefix)
     * @param {object} params - Query parameters
     * @param {object} options - Additional axios options
     * @returns {Promise<ApiResponse>}
     */
    async get(endpoint, params = {}, options = {}) {
        try {
            const response = await axios.get(endpoint, {
                params,
                ...options
            });
            
            return this.formatResponse(response);
        } catch (error) {
            return this.handleError(error, 'GET', endpoint);
        }
    }

    /**
     * Generic POST request handler
     * @param {string} endpoint - API endpoint
     * @param {object} data - Request body data
     * @param {object} options - Additional axios options
     * @returns {Promise<ApiResponse>}
     */
    async post(endpoint, data = {}, options = {}) {
        try {
            const response = await axios.post(endpoint, data, options);
            return this.formatResponse(response);
        } catch (error) {
            return this.handleError(error, 'POST', endpoint);
        }
    }

    /**
     * Generic PUT request handler
     * @param {string} endpoint - API endpoint
     * @param {object} data - Request body data
     * @param {object} options - Additional axios options
     * @returns {Promise<ApiResponse>}
     */
    async put(endpoint, data = {}, options = {}) {
        try {
            const response = await axios.put(endpoint, data, options);
            return this.formatResponse(response);
        } catch (error) {
            return this.handleError(error, 'PUT', endpoint);
        }
    }

    /**
     * Generic DELETE request handler
     * @param {string} endpoint - API endpoint
     * @param {object} options - Additional axios options
     * @returns {Promise<ApiResponse>}
     */
    async delete(endpoint, options = {}) {
        try {
            const response = await axios.delete(endpoint, options);
            return this.formatResponse(response);
        } catch (error) {
            return this.handleError(error, 'DELETE', endpoint);
        }
    }

    /**
     * Format response to consistent structure
     * @param {object} response - Axios response object
     * @returns {ApiResponse}
     */
    formatResponse(response) {
        const { data, status, statusText } = response;
        
        // Handle different backend response formats
        if (data && typeof data === 'object') {
            // Backend returns {success: true/false, data: ..., message: ...}
            if (data.hasOwnProperty('success')) {
                return new ApiResponse(
                    data.success,
                    data.data || data,
                    data.message || statusText,
                    data.error || null,
                    { status, statusText }
                );
            }
            
            // Backend returns {status: 200, data: ..., message: ...}
            if (data.hasOwnProperty('status')) {
                return new ApiResponse(
                    data.status >= 200 && data.status < 300,
                    data.data || data,
                    data.message || statusText,
                    data.error || null,
                    { status: data.status, statusText }
                );
            }
        }
        
        // Default success response
        return new ApiResponse(
            status >= 200 && status < 300,
            data,
            statusText,
            null,
            { status, statusText }
        );
    }

    /**
     * Handle API errors consistently
     * @param {object} error - Axios error object
     * @param {string} method - HTTP method
     * @param {string} endpoint - API endpoint
     * @returns {ApiResponse}
     */
    handleError(error, method, endpoint) {
        console.error(`API Error [${method} ${endpoint}]:`, error);
        
        if (error.response) {
            // Server responded with error status
            const { data, status, statusText } = error.response;
            
            return new ApiResponse(
                false,
                null,
                data?.message || statusText || 'Server error occurred',
                {
                    type: 'server_error',
                    status,
                    statusText,
                    data: data || null
                },
                { status, statusText }
            );
        } else if (error.request) {
            // Network error
            return new ApiResponse(
                false,
                null,
                'Network error - please check your connection',
                {
                    type: 'network_error',
                    message: error.message
                }
            );
        } else {
            // Request setup error
            return new ApiResponse(
                false,
                null,
                'Request configuration error',
                {
                    type: 'config_error',
                    message: error.message
                }
            );
        }
    }

    /**
     * Upload file with progress tracking
     * @param {string} endpoint - Upload endpoint
     * @param {FormData} formData - Form data with file
     * @param {function} onProgress - Progress callback
     * @returns {Promise<ApiResponse>}
     */
    async upload(endpoint, formData, onProgress = null) {
        try {
            const response = await axios.post(endpoint, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                },
                onUploadProgress: onProgress ? (progressEvent) => {
                    const percentCompleted = Math.round(
                        (progressEvent.loaded * 100) / progressEvent.total
                    );
                    onProgress(percentCompleted);
                } : undefined
            });
            
            return this.formatResponse(response);
        } catch (error) {
            return this.handleError(error, 'UPLOAD', endpoint);
        }
    }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;
export { ApiResponse };
