<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            'PragmaRX\\Google2FA\\' => 19,
            'ParagonIE\\ConstantTime\\' => 23,
            '<PERSON><PERSON><PERSON>ail<PERSON>\\PHPMailer\\' => 20,
        ),
        'F' => 
        array (
            'FanBet247\\' => 10,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PragmaRX\\Google2FA\\' => 
        array (
            0 => __DIR__ . '/..' . '/pragmarx/google2fa/src',
        ),
        'ParagonIE\\ConstantTime\\' => 
        array (
            0 => __DIR__ . '/..' . '/paragonie/constant_time_encoding/src',
        ),
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
        'FanBet247\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInitbe14072eb65fe1eca6c06ee4c3696e51::$classMap;

        }, null, ClassLoader::class);
    }
}
