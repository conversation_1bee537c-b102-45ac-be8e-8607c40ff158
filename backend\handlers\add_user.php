<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

$data = json_decode(file_get_contents("php://input"));

if (
    !empty($data->username) &&
    !empty($data->full_name) &&
    !empty($data->email) &&
    !empty($data->password) &&
    !empty($data->favorite_team)
) {
    $username = htmlspecialchars(strip_tags($data->username));
    $full_name = htmlspecialchars(strip_tags($data->full_name));
    $email = htmlspecialchars(strip_tags($data->email));
    $password = password_hash($data->password, PASSWORD_DEFAULT);
    $favorite_team = htmlspecialchars(strip_tags($data->favorite_team));
    $balance = floatval($data->balance);

    $query = "INSERT INTO users (username, full_name, email, password_hash, favorite_team, balance) VALUES (:username, :full_name, :email, :password, :favorite_team, :balance)";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(":username", $username);
    $stmt->bindParam(":full_name", $full_name);
    $stmt->bindParam(":email", $email);
    $stmt->bindParam(":password", $password);
    $stmt->bindParam(":favorite_team", $favorite_team);
    $stmt->bindParam(":balance", $balance);

    if ($stmt->execute()) {
        http_response_code(201);
        echo json_encode(array("success" => true, "message" => "User was created."));
    } else {
        http_response_code(503);
        echo json_encode(array("success" => false, "message" => "Unable to create user."));
    }
} else {
    http_response_code(400);
    echo json_encode(array("success" => false, "message" => "Unable to create user. Data is incomplete."));
}
