-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: May 27, 2025 at 12:55 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `burger_shot`
--

-- --------------------------------------------------------

--
-- Table structure for table `access_codes`
--

CREATE TABLE `access_codes` (
  `id` int(11) NOT NULL,
  `code_hash` varchar(64) NOT NULL,
  `code_plain` varchar(12) NOT NULL,
  `expires_at` datetime NOT NULL,
  `validity_days` int(2) NOT NULL DEFAULT '1',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `description` varchar(255) DEFAULT NULL COMMENT 'Optional description of the code'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `access_codes`
--

INSERT INTO `access_codes` (`id`, `code_hash`, `code_plain`, `expires_at`, `validity_days`, `is_active`, `created_at`, `description`) VALUES
(1, '4056c5fa527df2e8c2185fc605c4e825621c7a36a74d5657775031261ec5c79d', '28E6CB', '2025-03-16 12:10:17', 1, 1, '2025-03-15 12:10:17', ''),
(2, 'df8bffb58a821e165b46b24035ea48246809427c8e2ce455014e437b807866ac', '09D417', '2025-04-04 12:12:19', 20, 1, '2025-03-15 12:12:19', 'Platinum'),
(3, '1a6cec75caef1c4ea1b5fc0bab19997bd54d4d26353079ffd2791248ded7413c', '23F962', '2025-04-04 12:21:27', 20, 1, '2025-03-15 12:21:27', ''),
(4, '501803a4eac79ad1c580dbecee6a3305cf364b6eb422efd4c238c4d592edfd46', '7B3593', '2025-04-23 09:24:46', 20, 1, '2025-04-03 09:24:46', ''),
(5, 'd4ee6f5d111090ce2986b2997a949d4add7702180d8d55a642f7e79082bf8b2e', '891128', '2025-06-05 18:13:33', 20, 1, '2025-05-16 18:13:33', '');

-- --------------------------------------------------------

--
-- Table structure for table `admin_notifications`
--

CREATE TABLE `admin_notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `description` text,
  `image_path` varchar(255) DEFAULT NULL,
  `display_order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `description`, `image_path`, `display_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Indoors', 'Delicious Indoor strain made with 100% Love', 'assets/images/categories/category_6827942846163.png', 1, 1, '2025-03-11 22:03:12', '2025-05-16 19:38:16'),
(2, 'Green House', 'Perfect companions to your daily', 'assets/images/categories/category_68279439c05d6.png', 2, 1, '2025-03-11 22:03:12', '2025-05-16 19:38:33'),
(3, 'Green Dor', 'Refreshing beverages', 'assets/images/categories/category_682794421943e.png', 3, 1, '2025-03-11 22:03:12', '2025-05-16 19:38:42'),
(4, 'Outdoor', 'Sweet treats to finish your meal', 'assets/images/categories/category_68278c63a9615.png', 4, 1, '2025-03-11 22:03:12', '2025-05-16 19:05:07'),
(5, 'Edibles', 'Fruity Lovely gummies', 'assets/images/categories/category_682794fad0d0d.png', 5, 1, '2025-05-16 19:41:46', '2025-05-16 19:41:46'),
(6, 'Accesories', 'Exclusive Accessories', 'assets/images/categories/category_682795f29a3d6.png', 6, 1, '2025-05-16 19:45:54', '2025-05-16 19:45:54'),
(7, 'Snacks', 'Healthy Cbd Snack', 'assets/images/categories/category_682824fda33dc.png', 7, 1, '2025-05-17 05:56:13', '2025-05-17 05:56:13');

-- --------------------------------------------------------

--
-- Table structure for table `claimed_rewards`
--

CREATE TABLE `claimed_rewards` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `reward_type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `redeemed` tinyint(1) DEFAULT '0',
  `redeemed_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `claimed_rewards`
--

INSERT INTO `claimed_rewards` (`id`, `customer_id`, `reward_type`, `created_at`, `redeemed`, `redeemed_at`) VALUES
(4, 6, 'free_gift', '2025-03-15 07:25:24', 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `customer_favorites`
--

CREATE TABLE `customer_favorites` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `customer_notifications`
--

CREATE TABLE `customer_notifications` (
  `id` int(11) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `message` text NOT NULL,
  `is_read` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `customer_profiles`
--

CREATE TABLE `customer_profiles` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `address` text,
  `email` varchar(100) DEFAULT NULL,
  `total_orders` int(11) DEFAULT '0',
  `last_order_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `rewards_points` int(11) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `customer_profiles`
--

INSERT INTO `customer_profiles` (`id`, `name`, `phone`, `address`, `email`, `total_orders`, `last_order_id`, `created_at`, `updated_at`, `rewards_points`) VALUES
(6, 'james Bong', '1919191', '11 Kent Road Manchester United', '<EMAIL>', 10, 28, '2025-03-12 10:54:54', '2025-05-17 05:23:49', 26);

-- --------------------------------------------------------

--
-- Table structure for table `custom_fields`
--

CREATE TABLE `custom_fields` (
  `id` int(11) NOT NULL,
  `field_name` varchar(50) NOT NULL,
  `field_type` enum('text','textarea','select','radio','checkbox') DEFAULT 'text',
  `is_required` tinyint(1) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `field_type_context` enum('order','menu_item') DEFAULT 'order'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `custom_fields`
--

INSERT INTO `custom_fields` (`id`, `field_name`, `field_type`, `is_required`, `is_active`, `display_order`, `created_at`, `updated_at`, `field_type_context`) VALUES
(1, 'Delivery Instructions', 'textarea', 0, 1, 1, '2025-03-11 22:03:12', '2025-03-11 22:03:12', 'order'),
(2, 'Date of Birth', 'text', 1, 1, 2, '2025-03-11 22:03:12', '2025-03-14 10:06:34', 'order'),
(3, 'Grams', 'text', 1, 1, 1, '2025-03-12 19:09:44', '2025-03-12 20:30:53', 'menu_item'),
(4, 'Unit Type', 'text', 0, 0, 2, '2025-03-12 19:09:44', '2025-03-12 20:30:34', 'menu_item'),
(5, 'Ingredients', 'textarea', 0, 0, 3, '2025-03-12 19:09:44', '2025-03-12 19:15:53', 'menu_item');

-- --------------------------------------------------------

--
-- Table structure for table `delivery_methods`
--

CREATE TABLE `delivery_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `cost` decimal(10,2) DEFAULT '0.00',
  `estimated_time` varchar(100) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `delivery_methods`
--

INSERT INTO `delivery_methods` (`id`, `name`, `description`, `cost`, `estimated_time`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'Standard Delivery', 'Regular delivery service', '3.50', '30-45 minutes', 1, 1, '2025-03-13 09:53:57', '2025-03-13 09:53:57'),
(2, 'Express Delivery', 'Faster delivery service with priority handling', '5.99', '15-25 minutes', 1, 2, '2025-03-13 09:53:57', '2025-03-13 09:53:57'),
(3, 'Pickup', 'Pick up your order at our restaurant', '0.00', 'Ready in 20 minutes', 1, 3, '2025-03-13 09:53:57', '2025-03-13 09:53:57'),
(4, 'Standard Delivery', 'Regular delivery service', '0.00', '30-45 minutes', 1, 1, '2025-03-13 09:54:06', '2025-03-13 11:47:40');

-- --------------------------------------------------------

--
-- Table structure for table `email_logs`
--

CREATE TABLE `email_logs` (
  `id` int(11) NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `template_key` varchar(50) DEFAULT NULL,
  `recipient` varchar(255) NOT NULL,
  `status` varchar(20) NOT NULL,
  `error` text,
  `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `email_logs`
--

INSERT INTO `email_logs` (`id`, `order_id`, `template_key`, `recipient`, `status`, `error`, `sent_at`) VALUES
(1, 48, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:22:45'),
(2, 47, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:29:41'),
(3, 46, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:33:59'),
(4, 45, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:44:56'),
(5, 43, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:49:19'),
(6, 41, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:50:28'),
(7, 40, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:55:07'),
(8, 39, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 16:58:40'),
(9, 48, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:06:00'),
(10, 39, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:07:12'),
(11, 39, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:21:09'),
(12, 39, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:21:16'),
(13, 48, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:21:19'),
(14, 43, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:49:24'),
(15, 37, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:50:56'),
(16, 41, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 17:55:48'),
(17, 49, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 19:02:30'),
(18, 48, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 19:18:07'),
(19, 47, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 19:18:53'),
(20, 50, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-13 22:27:23'),
(21, 51, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-14 15:02:17'),
(22, 35, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 03:36:14'),
(23, 46, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 03:40:27'),
(24, 33, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 03:42:46'),
(25, 52, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 03:47:06'),
(26, 37, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 03:50:00'),
(27, 41, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-03-15 05:12:39'),
(28, 62, 'order_status_update', '<EMAIL>', 'sent', NULL, '2025-04-03 09:22:34');

-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `template_key` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `subject` varchar(200) NOT NULL,
  `html_content` text NOT NULL,
  `plain_content` text,
  `variables` text,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `email_templates`
--

INSERT INTO `email_templates` (`id`, `template_key`, `name`, `subject`, `html_content`, `plain_content`, `variables`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'order_confirmation', 'Order Confirmation', 'Your Order #{order_id} Confirmation', '<!DOCTYPE html>\r\n<html>\r\n<head>\r\n    <meta charset=\"UTF-8\">\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n    <title>Order Confirmation</title>\r\n    <style>\r\n        body {\r\n            font-family: Arial, sans-serif;\r\n            line-height: 1.6;\r\n            color: #333;\r\n            margin: 0;\r\n            padding: 0;\r\n            background-color: #f0f0f0;\r\n        }\r\n        .container {\r\n            max-width: 600px;\r\n            margin: 20px auto;\r\n            background-color: #ffffff;\r\n            border-radius: 8px;\r\n            overflow: hidden;\r\n            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\r\n        }\r\n        .header {\r\n            background-color: {primary_color};\r\n            color: white;\r\n            padding: 20px;\r\n            text-align: center;\r\n        }\r\n        .content {\r\n            padding: 30px;\r\n            background-color: #ffffff;\r\n        }\r\n        .order-details {\r\n            margin-top: 20px;\r\n            border-top: 1px solid #eee;\r\n            padding-top: 20px;\r\n        }\r\n        .order-items {\r\n            width: 100%;\r\n            border-collapse: collapse;\r\n            margin: 15px 0;\r\n            background-color: #ffffff;\r\n        }\r\n        .order-items th, .order-items td {\r\n            padding: 12px 15px;\r\n            border-bottom: 1px solid #eee;\r\n            text-align: left;\r\n        }\r\n        .order-items th {\r\n            background-color: #f8f8f8;\r\n            font-weight: 600;\r\n        }\r\n        .order-items tr:last-child td {\r\n            border-bottom: none;\r\n            font-weight: bold;\r\n        }\r\n        .address-box {\r\n            background-color: #f8f8f8;\r\n            padding: 15px;\r\n            border-radius: 6px;\r\n            margin: 15px 0;\r\n        }\r\n        .tracking-button {\r\n            display: inline-block;\r\n            padding: 12px 24px;\r\n            background-color: {primary_color};\r\n            color: white;\r\n            text-decoration: none;\r\n            border-radius: 6px;\r\n            margin: 20px 0;\r\n        }\r\n        .footer {\r\n            background-color: #f8f8f8;\r\n            padding: 20px;\r\n            text-align: center;\r\n            font-size: 12px;\r\n            color: #666;\r\n        }\r\n        .total-row {\r\n            background-color: #f8f8f8;\r\n        }\r\n        .login-info {\r\n            background-color: #f8f8f8;\r\n            padding: 15px;\r\n            border-radius: 6px;\r\n            margin: 20px 0;\r\n        }\r\n        @media only screen and (max-width: 600px) {\r\n            .container {\r\n                margin: 10px;\r\n                width: auto;\r\n            }\r\n            .content {\r\n                padding: 15px;\r\n            }\r\n            .order-items th, .order-items td {\r\n                padding: 8px;\r\n            }\r\n        }\r\n    </style>\r\n</head>\r\n<body>\r\n    <div class=\"container\">\r\n        <div class=\"header\">\r\n            <h1>Order Confirmation #{order_id}</h1>\r\n        </div>\r\n        <div class=\"content\">\r\n            <p>Dear {customer_name},</p>\r\n            <p>Thank you for your order! We\'re excited to prepare your delicious meal.</p>\r\n            \r\n            <div class=\"order-status\">Status: New Order</div>\r\n            \r\n            <p>Your order has been received and is being processed. Here are your order details:</p>\r\n            \r\n            <div class=\"order-details\">\r\n                <p><strong>Order Number:</strong> {order_id}</p>\r\n                <p><strong>Order Date:</strong> {order_date}</p>\r\n                \r\n                <div class=\"address-box\">\r\n                    <strong>Delivery Address:</strong><br>\r\n                    {customer_address}\r\n                </div>\r\n                \r\n                <h3>Order Items:</h3>\r\n                <table class=\"order-items\">\r\n                    <thead>\r\n                        <tr>\r\n                            <th>Item</th>\r\n                            <th>Quantity</th>\r\n                            <th>Price</th>\r\n                            <th>Total</th>\r\n                        </tr>\r\n                    </thead>\r\n                    <tbody>\r\n                        {order_items}\r\n                        <tr>\r\n                            <td colspan=\"3\" style=\"text-align: right;\"><strong>Subtotal:</strong></td>\r\n                            <td><strong>{subtotal}</strong></td>\r\n                        </tr>\r\n                        {delivery_fee}\r\n                        <tr class=\"total-row\">\r\n                            <td colspan=\"3\" style=\"text-align: right;\"><strong>Total:</strong></td>\r\n                            <td><strong>{total_amount}</strong></td>\r\n                        </tr>\r\n                    </tbody>\r\n                </table>\r\n                \r\n                <div style=\"text-align: center;\">\r\n                    <a href=\"{tracking_url}\" class=\"tracking-button\">Track Your Order</a>\r\n                </div>\r\n                \r\n                <div class=\"login-info\">\r\n                    <strong>Want to view all your orders?</strong>\r\n                    <p>You can log in to your account using your phone number to view your order history and track all your orders.</p>\r\n                    <p><a href=\"{profile_url}\">Visit your profile page</a> and enter your phone number: {customer_phone}</p>\r\n                </div>\r\n                \r\n                <p>If you have any questions about your order, please contact us.</p>\r\n                <p>Thank you for choosing us!</p>\r\n            </div>\r\n        </div>\r\n        <div class=\"footer\">\r\n            <p>&copy; {year} {site_name}. All rights reserved.</p>\r\n            <p>This is an automated email, please do not reply to this message.</p>\r\n        </div>\r\n    </div>\r\n</body>\r\n</html>', 'Hello {customer_name},\r\n\r\nThank you for your order! We have received your order #{order_id} and it is being processed.\r\n\r\nOrder Date: {order_date}\r\nTotal Amount: {total_amount}\r\n\r\nDelivery Address:\r\n{customer_address}\r\n\r\nWe will notify you when your order is ready for pickup or delivery.\r\n\r\nTo track your order, visit: {tracking_url}\r\n\r\nThank you for choosing {site_name}!', '{\r\n  \"order_id\": \"Order ID number\",\r\n  \"customer_name\": \"Customer\'s full name\",\r\n  \"order_date\": \"Date and time of order\",\r\n  \"total_amount\": \"Total order amount with currency symbol\",\r\n  \"subtotal\": \"Subtotal amount with currency symbol\",\r\n  \"customer_address\": \"Customer\'s delivery address\",\r\n  \"customer_phone\": \"Customer\'s phone number\",\r\n  \"tracking_url\": \"URL to track the order\",\r\n  \"profile_url\": \"URL to customer profile\",\r\n  \"order_items\": \"HTML table rows with order items\",\r\n  \"delivery_fee\": \"HTML table row with delivery fee\",\r\n  \"site_name\": \"Name of the site\",\r\n  \"year\": \"Current year\",\r\n  \"primary_color\": \"Primary color for styling\"\r\n}', 1, '2025-03-13 15:57:29', '2025-03-13 15:57:29'),
(2, 'order_status_update', 'Order Status Update', 'Your Order #{order_id} Status Update', '<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>Order Status Update</title>\n    <link href=\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap\" rel=\"stylesheet\">\n</head>\n<body style=\"font-family: \'Poppins\', Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5;\">\n    <div style=\"max-width: 600px; margin: 20px auto; background-color: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\">\n        <div style=\"background-color: {primary_color}; color: white; padding: 30px 20px; text-align: center;\">\n            <h1 style=\"margin: 0; font-size: 24px; font-weight: 600;\">Order Status Update</h1>\n        </div>\n        <div style=\"padding: 40px 30px; background-color: #ffffff;\">\n            <p>Dear {customer_name},</p>\n            <p>Your order #{order_id} has been updated.</p>\n            \n            <div style=\"display: inline-block; padding: 8px 16px; background-color: {primary_color}; color: white; border-radius: 20px; font-size: 14px; font-weight: 500; margin: 15px 0;\">\n                Status: {status}\n            </div>\n            \n            <div style=\"background-color: #f8f9fa; border-radius: 8px; padding: 20px; margin: 20px 0;\">\n                <table style=\"width: 100%; border-collapse: collapse;\">\n                    <tr>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee; font-weight: 500;\">Order Number:</td>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee;\">{order_id}</td>\n                    </tr>\n                    <tr>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee; font-weight: 500;\">Order Date:</td>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee;\">{order_date}</td>\n                    </tr>\n                    <tr>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee; font-weight: 500;\">Total Amount:</td>\n                        <td style=\"padding: 10px 0; border-bottom: 1px solid #eee;\">{total_amount}</td>\n                    </tr>\n                </table>\n            </div>\n            \n            <p>{status_message}</p>\n            \n            <div style=\"text-align: center; margin: 30px 0;\">\n                <a href=\"{tracking_url}\" style=\"display: inline-block; padding: 12px 24px; background-color: {primary_color}; color: white; text-decoration: none; border-radius: 6px; font-weight: 500;\">Track Your Order</a>\n            </div>\n            \n            <p>If you have any questions about your order, please don\'t hesitate to contact us.</p>\n            <p>Thank you for choosing {site_name}!</p>\n        </div>\n        <div style=\"background-color: #f8f9fa; padding: 20px; text-align: center; font-size: 12px; color: #666;\">\n            <p style=\"margin: 5px 0;\">&copy; {year} {site_name}. All rights reserved.</p>\n            <p style=\"margin: 5px 0;\">This is an automated email, please do not reply to this message.</p>\n        </div>\n    </div>\n</body>\n</html>', 'Plain text template has been updated - please edit in the email templates section', '{\r\n  \"order_id\": \"Order ID number\",\r\n  \"customer_name\": \"Customer\'s full name\",\r\n  \"status\": \"New order status\",\r\n  \"notes\": \"Additional notes regarding the status change\",\r\n  \"site_name\": \"Name of the site\",\r\n  \"primary_color\": \"Primary color for styling\"\r\n}', 1, '2025-03-13 15:57:29', '2025-03-13 17:18:58');

-- --------------------------------------------------------

--
-- Table structure for table `items`
--

CREATE TABLE `items` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `price` decimal(10,2) NOT NULL,
  `image_path` varchar(255) DEFAULT NULL,
  `is_available` tinyint(1) DEFAULT '1',
  `stock` int(11) DEFAULT '0',
  `display_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `has_bulk_discount` tinyint(1) NOT NULL DEFAULT '0',
  `bulk_discount_quantity` int(11) DEFAULT NULL,
  `bulk_discount_price` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `items`
--

INSERT INTO `items` (`id`, `category_id`, `name`, `description`, `price`, `image_path`, `is_available`, `stock`, `display_order`, `created_at`, `updated_at`, `has_bulk_discount`, `bulk_discount_quantity`, `bulk_discount_price`) VALUES
(1, 1, 'Pink Guava', 'Our signature burger with lettuce, tomato, and special sauce', '150.00', 'assets/images/items/item_67d2a06cad108.jpg', 1, 20, 0, '2025-03-11 22:03:12', '2025-03-15 06:19:50', 1, 5, '100.00'),
(2, 1, 'Cheese Burger', 'Classic burger with American cheese', '7.99', 'assets/images/items/item_67d1a6bbd1a32.jpg', 1, 6, 2, '2025-03-11 22:03:12', '2025-03-13 20:28:07', 0, NULL, NULL),
(3, 1, 'Jamaican Wedding', 'Jamaican refers to the indigenous varieties of cannabis (or landraces) that grow natively in this region of the world.', '150.00', 'assets/images/items/item_67d29c3d8ef27.jpg', 1, 0, 0, '2025-03-11 22:03:12', '2025-03-15 10:00:49', 1, 3, '100.00'),
(4, 2, 'French Fries', 'Crispy golden fries', '3.49', 'assets/images/items/item_67d29b63ebfc9.jpg', 1, 0, 1, '2025-03-11 22:03:12', '2025-03-13 08:46:27', 0, NULL, NULL),
(5, 2, 'Onion Rings', 'Crispy battered onion rings', '3.99', 'assets/images/items/onion_rings.jpg', 1, 0, 2, '2025-03-11 22:03:12', '2025-03-11 22:03:12', 0, NULL, NULL),
(6, 3, 'Soda', 'Choice of soft drinks', '2.49', 'assets/images/items/soda.jpg', 1, 0, 1, '2025-03-11 22:03:12', '2025-03-11 22:03:12', 0, NULL, NULL),
(7, 3, 'Milkshake', 'Creamy vanilla milkshake', '4.99', 'assets/images/items/milkshake.jpg', 1, 0, 2, '2025-03-11 22:03:12', '2025-03-11 22:03:12', 0, NULL, NULL),
(8, 4, 'Ice Cream', 'Soft serve ice cream cone', '2.99', 'assets/images/items/ice_cream.jpg', 0, 0, 1, '2025-03-11 22:03:12', '2025-03-13 08:46:12', 0, NULL, NULL),
(9, 3, 'Mimosa', 'Mimosa, also known as \"Purple Mimosa,\" is a hybrid made by crossing Clementine with Purple Punch.', '60.00', 'assets/images/items/item_67d5422c187a2.jpg', 1, 50, 0, '2025-03-15 09:02:36', '2025-03-15 09:02:36', 1, 5, '50.00');

-- --------------------------------------------------------

--
-- Table structure for table `item_custom_fields`
--

CREATE TABLE `item_custom_fields` (
  `id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `field_value` text,
  `order_item_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `item_custom_fields`
--

INSERT INTO `item_custom_fields` (`id`, `item_id`, `custom_field_id`, `field_value`, `order_item_id`, `created_at`, `updated_at`) VALUES
(1, 1, 3, '5G,3G,1G', 2, '2025-03-12 19:12:26', '2025-03-15 06:18:41'),
(9, 3, 3, '100', NULL, '2025-03-13 08:50:05', '2025-03-13 08:50:05'),
(10, 9, 3, '1 G, 2G, 5G', NULL, '2025-03-15 09:02:36', '2025-03-15 09:03:33');

-- --------------------------------------------------------

--
-- Table structure for table `item_custom_field_map`
--

CREATE TABLE `item_custom_field_map` (
  `id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `item_custom_field_map`
--

INSERT INTO `item_custom_field_map` (`id`, `item_id`, `custom_field_id`) VALUES
(12, 1, 3),
(16, 3, 3),
(14, 9, 3);

-- --------------------------------------------------------

--
-- Table structure for table `item_tags`
--

CREATE TABLE `item_tags` (
  `id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `tag_category_id` int(11) NOT NULL,
  `tag_value` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `item_tags`
--

INSERT INTO `item_tags` (`id`, `item_id`, `tag_category_id`, `tag_value`, `created_at`, `updated_at`) VALUES
(10, 1, 1, 'Giggly,Creative,Happy', '2025-03-15 06:19:50', '2025-03-15 06:19:50'),
(12, 9, 1, 'Focused,Energetic,Uplifted', '2025-03-15 09:03:33', '2025-03-15 09:03:33');

-- --------------------------------------------------------

--
-- Table structure for table `orders`
--

CREATE TABLE `orders` (
  `id` int(11) NOT NULL,
  `customer_name` varchar(100) NOT NULL,
  `customer_address` text NOT NULL,
  `customer_phone` varchar(20) NOT NULL,
  `customer_email` varchar(100) DEFAULT NULL,
  `status` enum('new','pending','approved','preparing','ready','on_the_way','delivered','rejected') DEFAULT 'new',
  `total_amount` decimal(10,2) DEFAULT '0.00',
  `notes` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `customer_id` int(11) DEFAULT NULL,
  `delivery_method_id` int(11) DEFAULT NULL,
  `payment_method_id` int(11) DEFAULT NULL,
  `email_sent` tinyint(1) NOT NULL DEFAULT '0',
  `payment_status` enum('pending','completed') DEFAULT 'pending'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `orders`
--

INSERT INTO `orders` (`id`, `customer_name`, `customer_address`, `customer_phone`, `customer_email`, `status`, `total_amount`, `notes`, `created_at`, `updated_at`, `customer_id`, `delivery_method_id`, `payment_method_id`, `email_sent`, `payment_status`) VALUES
(1, '1', '1', '1', NULL, 'new', '14.98', '', '2025-03-11 22:19:17', '2025-03-11 23:24:14', NULL, NULL, NULL, 0, 'pending'),
(2, 'John Doe', '123 Main St, Anytown, USA', '************', NULL, 'rejected', '25.98', 'Please deliver to the side door.', '2025-03-11 23:13:15', '2025-03-11 23:28:23', NULL, NULL, NULL, 0, 'pending'),
(3, 'John Doe', '123 Main St', '************', NULL, 'new', '25.98', 'Test order', '2025-03-11 23:13:22', '2025-03-11 23:13:22', NULL, NULL, NULL, 0, 'pending'),
(4, 'we', 'we', 'we', NULL, 'new', '41.94', NULL, '2025-03-11 23:59:14', '2025-03-11 23:59:14', NULL, NULL, NULL, 0, 'pending'),
(5, 'Paw Paw', '11 Paw Paw Road\r\nGta Lifestyle', '1982', NULL, 'on_the_way', '31.45', '', '2025-03-12 07:41:33', '2025-03-12 08:29:14', NULL, NULL, NULL, 0, 'pending'),
(6, 'Jane Smith', '123 Main St, Anytown', '************', NULL, 'approved', '18.50', 'Another demo order for display', '2025-03-12 07:56:55', '2025-03-12 07:56:55', NULL, NULL, NULL, 0, 'pending'),
(7, 'Test Customer', '123 Test Street', '************', NULL, 'on_the_way', '25.99', 'Test order with preparing status', '2025-03-12 08:23:11', '2025-03-12 08:30:49', NULL, NULL, NULL, 0, 'pending'),
(8, 'Test Customer', '123 Test Street', '************', NULL, 'preparing', '25.99', NULL, '2025-03-12 08:23:24', '2025-03-12 08:23:24', NULL, NULL, NULL, 0, 'pending'),
(9, 'Ready Customer', '456 Ready Street', '555-456-7890', NULL, 'ready', '32.50', NULL, '2025-03-12 08:23:38', '2025-03-12 08:23:38', NULL, NULL, NULL, 0, 'pending'),
(10, 'Delivered Customer', '789 Delivered Ave', '555-789-0123', NULL, 'delivered', '45.75', NULL, '2025-03-12 08:23:44', '2025-03-12 08:23:44', NULL, NULL, NULL, 0, 'pending'),
(11, 'Delivery Customer', '789 Road Avenue', '555-321-0987', NULL, 'on_the_way', '37.25', NULL, '2025-03-12 08:30:35', '2025-03-12 08:30:35', NULL, NULL, NULL, 0, 'pending'),
(19, 'James Bong', '11 kent', '1919191', NULL, 'delivered', '14.98', '', '2025-03-12 10:54:54', '2025-03-12 10:55:51', 6, NULL, NULL, 0, 'pending'),
(20, 'James Bong', '11 kent', '1919191', NULL, 'delivered', '28.45', '', '2025-03-12 10:59:48', '2025-03-15 04:34:35', 6, NULL, NULL, 0, 'completed'),
(21, 'james Bong', 'leave at the door', '1919191', NULL, 'delivered', '12.47', '', '2025-03-12 11:00:59', '2025-03-15 05:00:14', 6, NULL, NULL, 0, 'completed'),
(22, 'james Bong', 'leave at the door', '1919191', NULL, 'delivered', '42.44', '', '2025-03-12 11:24:47', '2025-03-15 04:49:48', 6, NULL, NULL, 0, 'completed'),
(23, 'james Bong', 'leave at the door', '1919191', NULL, 'delivered', '14.98', '', '2025-03-12 15:24:14', '2025-03-15 04:30:27', 6, NULL, NULL, 0, 'completed'),
(24, 'james Bong', '11 Kent Road Manchester United', '1919191', NULL, 'delivered', '31.96', '', '2025-03-12 20:06:21', '2025-03-15 04:25:42', 6, NULL, NULL, 0, 'completed'),
(25, 'james Bong', '11 Kent Road Manchester United', '1919191', NULL, 'delivered', '14.98', '', '2025-03-12 20:28:28', '2025-03-15 04:03:42', 6, NULL, NULL, 0, 'completed'),
(26, 'james Bong', '11 Kent Road Manchester United', '1919191', NULL, 'delivered', '34.95', '', '2025-03-12 21:38:33', '2025-03-15 04:00:07', 6, NULL, NULL, 0, 'completed'),
(27, 'james Bong', '11 Kent Road Manchester United', '1919191', NULL, 'delivered', '42.94', '', '2025-03-12 21:52:04', '2025-03-15 03:55:04', 6, NULL, NULL, 0, 'completed'),
(28, 'james Bong', '11 Kent Road Manchester United', '1919191', NULL, 'delivered', '31.45', 'ok thank you for the order', '2025-03-12 22:02:58', '2025-03-15 04:46:11', 6, NULL, NULL, 0, 'completed'),
(33, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '637.92', '', '2025-03-13 12:23:47', '2025-03-15 03:42:43', 6, 3, 4, 0, 'completed'),
(35, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '641.42', 'thank you for ordering with us', '2025-03-13 12:27:05', '2025-03-15 03:36:11', 6, 1, 1, 0, 'completed'),
(37, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '1387.92', '', '2025-03-13 12:34:52', '2025-03-15 03:49:57', 6, 4, 3, 0, 'completed'),
(39, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '1387.92', '', '2025-03-13 12:37:14', '2025-03-13 17:21:13', 6, 3, 4, 0, 'pending'),
(40, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '150.00', '', '2025-03-13 12:39:05', '2025-03-13 16:55:05', 6, 3, 3, 0, 'pending'),
(41, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '157.99', '', '2025-03-13 12:41:17', '2025-03-15 05:12:36', 6, 3, 4, 0, 'completed'),
(43, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '750.00', '', '2025-03-13 13:03:05', '2025-03-13 17:49:19', 6, 3, 3, 0, 'pending'),
(45, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '157.99', '', '2025-03-13 13:05:58', '2025-03-13 16:44:54', 6, 4, 3, 1, 'pending'),
(46, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '453.50', '', '2025-03-13 13:19:32', '2025-03-15 03:40:25', 6, 1, 1, 1, 'completed'),
(47, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '20.95', '', '2025-03-13 13:39:04', '2025-03-13 19:18:51', 6, 1, 2, 1, 'completed'),
(48, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '157.99', '', '2025-03-13 13:44:25', '2025-03-13 19:18:04', 6, 4, 1, 1, 'completed'),
(49, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '153.50', '', '2025-03-13 15:31:53', '2025-03-13 19:02:27', 6, 1, 3, 1, 'completed'),
(50, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '923.97', '', '2025-03-13 21:14:25', '2025-03-13 22:27:21', 6, 4, 1, 1, 'completed'),
(51, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '312.98', '', '2025-03-14 12:55:46', '2025-03-14 15:02:11', 6, 3, 3, 1, 'completed'),
(52, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '150.00', '', '2025-03-15 03:46:43', '2025-03-15 03:47:04', 6, 3, 3, 1, 'completed'),
(53, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'new', '325.94', NULL, '2025-03-15 08:42:38', '2025-03-15 08:42:40', 6, 2, 1, 1, 'pending'),
(59, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'new', '217.99', NULL, '2025-03-15 17:11:37', '2025-03-15 17:11:37', 6, 3, 3, 0, 'pending'),
(60, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'new', '763.98', NULL, '2025-03-15 17:13:42', '2025-03-15 17:13:42', 6, 2, 2, 0, 'pending'),
(61, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'new', '771.97', NULL, '2025-03-15 17:20:29', '2025-03-15 17:20:29', 6, 2, 1, 0, 'pending'),
(62, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'delivered', '503.50', '', '2025-03-15 19:04:51', '2025-04-03 09:22:29', 6, 1, 1, 0, 'completed'),
(63, 'james Bong', '11 Kent Road Manchester United', '1919191', '<EMAIL>', 'new', '453.50', NULL, '2025-05-17 05:23:49', '2025-05-17 05:23:49', 6, 1, 1, 0, 'pending');

-- --------------------------------------------------------

--
-- Table structure for table `order_custom_fields`
--

CREATE TABLE `order_custom_fields` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `custom_field_id` int(11) NOT NULL,
  `field_value` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `order_custom_fields`
--

INSERT INTO `order_custom_fields` (`id`, `order_id`, `custom_field_id`, `field_value`) VALUES
(1, 1, 1, '1'),
(2, 1, 2, '1'),
(3, 4, 1, 'we'),
(4, 4, 2, 'we'),
(5, 5, 1, 'drop it at the casino'),
(6, 5, 2, '11:11'),
(7, 19, 1, 'i2ii2'),
(8, 19, 2, '11:11'),
(9, 20, 1, '11:11'),
(10, 20, 2, '11:11'),
(11, 21, 1, '11:11'),
(12, 21, 2, '11:11'),
(13, 24, 2, '11:11'),
(14, 25, 1, 'Drop it at my Door'),
(15, 25, 2, '11:11'),
(16, 26, 1, 'Leave it at my door'),
(17, 26, 2, '11:11'),
(18, 28, 1, 'leave for Door'),
(27, 35, 1, '11'),
(28, 35, 2, '11'),
(31, 37, 1, 'Door'),
(33, 39, 1, '11'),
(34, 40, 1, '11'),
(35, 40, 2, '11'),
(36, 41, 1, '11'),
(37, 41, 2, '11'),
(40, 46, 1, '11'),
(41, 46, 2, '11'),
(42, 47, 1, '11'),
(43, 47, 2, '11'),
(44, 48, 1, '11'),
(45, 48, 2, '11'),
(46, 50, 1, 'Keep By the Security'),
(47, 50, 2, '11:11'),
(48, 51, 1, 'Drop at Door'),
(49, 51, 2, '11/11/1976'),
(50, 52, 1, 'at the door'),
(51, 52, 2, '11/11/1965'),
(52, 53, 1, 'Drop at the Door'),
(53, 53, 2, '11/11/1987'),
(54, 59, 2, '11/11/1989'),
(55, 60, 1, 'Drop at Door'),
(56, 60, 2, '11/11/1976'),
(57, 61, 1, 'Door'),
(58, 61, 2, '11/11/1197'),
(59, 62, 2, '11/11/1198'),
(60, 63, 1, 'uber '),
(61, 63, 2, '11/11/1986');

-- --------------------------------------------------------

--
-- Table structure for table `order_items`
--

CREATE TABLE `order_items` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `item_id` int(11) NOT NULL,
  `quantity` int(11) NOT NULL DEFAULT '1',
  `price_at_time` decimal(10,2) NOT NULL,
  `special_instructions` text,
  `original_price` decimal(10,2) DEFAULT NULL,
  `discount_applied` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `order_items`
--

INSERT INTO `order_items` (`id`, `order_id`, `item_id`, `quantity`, `price_at_time`, `special_instructions`, `original_price`, `discount_applied`) VALUES
(1, 1, 2, 1, '7.99', NULL, NULL, 0),
(2, 1, 1, 1, '6.99', NULL, NULL, 0),
(3, 2, 1, 2, '9.99', 'Extra cheese', NULL, 0),
(4, 2, 2, 1, '5.99', NULL, NULL, 0),
(5, 2, 1, 2, '9.99', NULL, NULL, 0),
(6, 4, 2, 3, '7.99', NULL, NULL, 0),
(7, 4, 1, 2, '6.99', NULL, NULL, 0),
(8, 4, 5, 1, '3.99', NULL, NULL, 0),
(9, 5, 4, 1, '3.49', NULL, NULL, 0),
(10, 5, 1, 2, '6.99', NULL, NULL, 0),
(11, 5, 1, 2, '6.99', NULL, NULL, 0),
(12, 19, 1, 1, '6.99', NULL, NULL, 0),
(13, 19, 2, 1, '7.99', NULL, NULL, 0),
(14, 20, 1, 1, '6.99', NULL, NULL, 0),
(15, 20, 2, 2, '7.99', NULL, NULL, 0),
(16, 20, 6, 1, '2.49', NULL, NULL, 0),
(17, 20, 8, 1, '2.99', NULL, NULL, 0),
(18, 21, 4, 1, '3.49', NULL, NULL, 0),
(19, 21, 5, 1, '3.99', NULL, NULL, 0),
(20, 21, 7, 1, '4.99', NULL, NULL, 0),
(21, 22, 1, 3, '6.99', NULL, NULL, 0),
(22, 22, 2, 1, '7.99', NULL, NULL, 0),
(23, 22, 3, 1, '9.99', NULL, NULL, 0),
(24, 22, 4, 1, '3.49', NULL, NULL, 0),
(25, 23, 1, 1, '6.99', NULL, NULL, 0),
(26, 23, 2, 1, '7.99', NULL, NULL, 0),
(27, 24, 1, 2, '6.99', NULL, NULL, 0),
(28, 24, 2, 1, '7.99', NULL, NULL, 0),
(29, 24, 3, 1, '9.99', NULL, NULL, 0),
(30, 25, 1, 1, '6.99', NULL, NULL, 0),
(31, 25, 2, 1, '7.99', NULL, NULL, 0),
(32, 26, 1, 5, '6.99', NULL, NULL, 0),
(33, 27, 2, 1, '7.99', NULL, NULL, 0),
(34, 27, 1, 5, '6.99', NULL, NULL, 0),
(35, 28, 4, 1, '3.49', NULL, NULL, 0),
(36, 28, 1, 1, '6.99', NULL, NULL, 0),
(37, 28, 1, 3, '6.99', NULL, NULL, 0),
(62, 33, 1, 2, '150.00', NULL, NULL, 0),
(63, 33, 2, 2, '7.99', NULL, NULL, 0),
(64, 33, 3, 2, '150.00', NULL, NULL, 0),
(65, 33, 7, 2, '4.99', NULL, NULL, 0),
(66, 33, 6, 2, '2.49', NULL, NULL, 0),
(67, 33, 4, 2, '3.49', NULL, NULL, 0),
(74, 35, 1, 2, '150.00', NULL, NULL, 0),
(75, 35, 2, 2, '7.99', NULL, NULL, 0),
(76, 35, 3, 2, '150.00', NULL, NULL, 0),
(77, 35, 7, 2, '4.99', NULL, NULL, 0),
(78, 35, 6, 2, '2.49', NULL, NULL, 0),
(79, 35, 4, 2, '3.49', NULL, NULL, 0),
(86, 37, 1, 2, '150.00', NULL, NULL, 0),
(87, 37, 2, 2, '7.99', NULL, NULL, 0),
(88, 37, 3, 2, '150.00', NULL, NULL, 0),
(89, 37, 7, 2, '4.99', NULL, NULL, 0),
(90, 37, 6, 2, '2.49', NULL, NULL, 0),
(91, 37, 4, 2, '3.49', NULL, NULL, 0),
(92, 37, 1, 5, '150.00', NULL, NULL, 0),
(100, 39, 1, 2, '150.00', NULL, NULL, 0),
(101, 39, 2, 2, '7.99', NULL, NULL, 0),
(102, 39, 3, 2, '150.00', NULL, NULL, 0),
(103, 39, 7, 2, '4.99', NULL, NULL, 0),
(104, 39, 6, 2, '2.49', NULL, NULL, 0),
(105, 39, 4, 2, '3.49', NULL, NULL, 0),
(106, 39, 1, 5, '150.00', NULL, NULL, 0),
(107, 40, 1, 1, '150.00', NULL, NULL, 0),
(108, 41, 1, 1, '150.00', NULL, NULL, 0),
(109, 41, 2, 1, '7.99', NULL, NULL, 0),
(112, 43, 1, 5, '150.00', NULL, NULL, 0),
(114, 45, 1, 1, '150.00', NULL, NULL, 0),
(115, 45, 2, 1, '7.99', NULL, NULL, 0),
(116, 46, 1, 3, '150.00', NULL, NULL, 0),
(117, 47, 4, 5, '3.49', NULL, NULL, 0),
(118, 48, 2, 1, '7.99', NULL, NULL, 0),
(119, 48, 3, 1, '150.00', NULL, NULL, 0),
(120, 49, 1, 1, '150.00', NULL, NULL, 0),
(121, 50, 1, 6, '150.00', NULL, NULL, 0),
(122, 50, 2, 3, '7.99', NULL, NULL, 0),
(123, 51, 7, 1, '4.99', NULL, NULL, 0),
(124, 51, 1, 1, '150.00', NULL, NULL, 0),
(125, 51, 2, 1, '7.99', NULL, NULL, 0),
(126, 51, 3, 1, '150.00', NULL, NULL, 0),
(127, 52, 1, 1, '150.00', NULL, NULL, 0),
(128, 53, 1, 1, '150.00', NULL, NULL, 0),
(129, 53, 3, 1, '150.00', NULL, NULL, 0),
(130, 53, 4, 1, '3.49', NULL, NULL, 0),
(131, 53, 6, 1, '2.49', NULL, NULL, 0),
(132, 53, 5, 1, '3.99', NULL, NULL, 0),
(133, 53, 7, 2, '4.99', NULL, NULL, 0),
(139, 59, 1, 1, '150.00', NULL, '150.00', 0),
(140, 59, 2, 1, '7.99', NULL, '7.99', 0),
(141, 59, 9, 1, '60.00', NULL, '60.00', 0),
(142, 60, 1, 5, '100.00', NULL, '150.00', 1),
(143, 60, 2, 1, '7.99', NULL, '7.99', 0),
(144, 60, 9, 5, '50.00', NULL, '60.00', 1),
(145, 61, 1, 5, '100.00', NULL, '150.00', 1),
(146, 61, 2, 2, '7.99', NULL, '7.99', 0),
(147, 61, 9, 5, '50.00', NULL, '60.00', 1),
(148, 62, 1, 5, '100.00', NULL, '150.00', 1),
(149, 63, 1, 3, '150.00', NULL, '150.00', 0);

-- --------------------------------------------------------

--
-- Table structure for table `order_status_history`
--

CREATE TABLE `order_status_history` (
  `id` int(11) NOT NULL,
  `order_id` int(11) NOT NULL,
  `status` varchar(50) NOT NULL,
  `changed_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `instructions` text,
  `is_active` tinyint(1) DEFAULT '1',
  `display_order` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `description`, `instructions`, `is_active`, `display_order`, `created_at`, `updated_at`) VALUES
(1, 'Cash on Delivery', 'Give the Driver Cash on Delivery', 'Please Always keep your change', 1, 0, '2025-03-13 09:53:26', '2025-03-13 09:53:26'),
(2, 'Cash on Delivery', 'Pay with cash when your order arrives', 'Please have exact change ready for the delivery person.', 1, 1, '2025-03-13 09:54:13', '2025-03-13 09:54:13'),
(3, 'Card on Delivery', 'Pay with card when your order arrives', 'We accept Visa, Mastercard, and American Express. Our delivery personnel carry mobile card terminals.', 1, 2, '2025-03-13 09:54:21', '2025-03-13 09:54:21'),
(4, 'E-Wallet', 'Pay with your favorite e-wallet app', 'Scan the QR code provided by our delivery person or use our app to complete payment.', 1, 3, '2025-03-13 09:54:29', '2025-03-13 09:54:29');

-- --------------------------------------------------------

--
-- Table structure for table `site_lockdown`
--

CREATE TABLE `site_lockdown` (
  `id` int(11) NOT NULL,
  `lockdown_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `access_code` varchar(64) DEFAULT NULL,
  `code_plain` varchar(12) DEFAULT NULL,
  `code_expires_at` datetime DEFAULT NULL,
  `code_validity_days` int(2) NOT NULL DEFAULT '1',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `site_lockdown`
--

INSERT INTO `site_lockdown` (`id`, `lockdown_enabled`, `access_code`, `code_plain`, `code_expires_at`, `code_validity_days`, `last_updated`) VALUES
(1, 0, 'd4ee6f5d111090ce2986b2997a949d4add7702180d8d55a642f7e79082bf8b2e', '891128', '2025-06-05 18:13:33', 20, '2025-05-16 18:13:33');

-- --------------------------------------------------------

--
-- Table structure for table `site_settings`
--

CREATE TABLE `site_settings` (
  `id` int(11) NOT NULL,
  `setting_name` varchar(50) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_group` varchar(50) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_name`, `setting_value`, `setting_group`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'Medical+', 'general', '2025-03-12 12:04:39', '2025-03-15 07:45:52'),
(2, 'primary_color', '#254b74', 'general', '2025-03-12 12:04:39', '2025-03-15 07:45:52'),
(3, 'currency', 'ZAR', 'general', '2025-03-12 12:04:39', '2025-03-15 07:45:52'),
(4, 'currency_symbol', 'R', 'general', '2025-03-12 12:04:39', '2025-03-15 07:45:52'),
(5, 'smtp_host', 'smtp.hostinger.com', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(6, 'smtp_port', '465', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(7, 'smtp_encryption', 'ssl', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(8, 'smtp_username', '<EMAIL>', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(9, 'smtp_password', 'Money2024@Demo#', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(10, 'sender_email', '<EMAIL>', 'email', '2025-03-12 12:04:39', '2025-03-12 12:04:39'),
(11, 'sender_name', 'Joint End', 'email', '2025-03-12 12:04:39', '2025-03-14 15:01:21'),
(12, 'decimal_separator', '.', 'general', '2025-03-12 13:18:11', '2025-03-15 07:45:52'),
(13, 'thousands_separator', ',', 'general', '2025-03-12 13:18:11', '2025-03-15 07:45:52'),
(14, 'smtp_from_name', 'Joint End', 'email', '2025-03-12 13:18:11', '2025-03-14 15:01:21'),
(15, 'hover_color', '#6484f7', 'general', '2025-03-12 15:43:43', '2025-03-15 07:45:52'),
(16, 'hero_heading', 'Delicious Cannabis<br>Delivered to Your Door', 'general', '2025-03-12 16:08:07', '2025-03-15 07:45:52'),
(17, 'hero_subheading', 'Fast, fresh and flavorful.', 'general', '2025-03-12 16:08:07', '2025-03-15 07:45:52'),
(18, 'search_placeholder', 'Search for burgers, sides, drinks...', 'general', '2025-03-12 16:08:07', '2025-03-15 07:45:52'),
(19, 'site_title', 'Delicious Kush Delivered to Your Door', 'general', '2025-03-13 17:48:26', '2025-03-15 07:45:52'),
(20, 'favicon', 'assets/images/favicon.png', 'general', '2025-03-13 17:48:26', '2025-03-15 07:45:52'),
(21, 'store_address', '123 Main Street', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(22, 'store_city', 'New York', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(23, 'store_state', 'NY', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(24, 'store_zip', '10001', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(25, 'store_phone', '+27846114757', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(26, 'store_email', '<EMAIL>', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(27, 'show_address_on_invoice', '1', 'general', '2025-03-13 18:06:50', '2025-03-15 07:45:52'),
(28, 'splash_screen_delay', '2500', 'general', '2025-03-14 07:37:27', '2025-03-15 07:45:52'),
(29, 'show_splash_screen', '1', 'general', '2025-03-14 07:37:27', '2025-03-15 07:45:52'),
(30, 'splash_returning_delay', '1000', 'general', '2025-03-14 08:31:23', '2025-03-15 07:45:52'),
(31, 'splash_persistence_mode', 'cookie', 'general', '2025-03-14 08:31:23', '2025-03-15 07:45:52'),
(32, 'splash_logo', 'assets/images/splash_logo.png', 'general', '2025-03-14 08:31:40', '2025-03-15 07:31:33'),
(33, 'show_payment_methods', '0', 'general', '2025-03-14 09:00:38', '2025-03-15 07:45:52'),
(34, 'payment_methods_count', '4', 'general', '2025-03-14 09:00:38', '2025-03-15 07:45:52'),
(35, 'payment_method_image_1', 'assets/images/payments/payment_method_1_1741944160.svg', 'general', '2025-03-14 09:03:11', '2025-03-14 09:22:40'),
(36, 'show_age_disclaimer', '1', 'general', '2025-03-14 09:14:11', '2025-03-15 07:45:52'),
(37, 'payment_method_image_2', 'assets/images/payments/payment_method_2_1741944088.svg', 'general', '2025-03-14 09:21:28', '2025-03-14 09:21:28'),
(38, 'payment_method_image_3', 'assets/images/payments/payment_method_3_1741944226.svg', 'general', '2025-03-14 09:21:28', '2025-03-14 09:23:46'),
(39, 'payment_method_image_4', 'assets/images/payments/payment_method_4_1741944396.svg', 'general', '2025-03-14 09:21:28', '2025-03-14 09:26:36'),
(40, 'splash_logo_size', '190', 'general', '2025-03-14 09:33:40', '2025-03-15 07:45:52'),
(41, 'show_pixel_banner', '1', 'general', '2025-03-14 12:57:46', '2025-03-15 07:45:52'),
(42, 'status_new_label', 'New Order', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(43, 'status_new_description', 'Order has been received', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(44, 'status_pending_label', 'Pending Approval', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(45, 'status_pending_description', 'Order is awaiting confirmation', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(46, 'status_approved_label', 'Order Approved', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(47, 'status_approved_description', 'Order has been confirmed', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(48, 'status_preparing_label', 'Preparing Order', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(49, 'status_preparing_description', 'Order is being prepared', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(50, 'status_ready_label', 'Ready for Delivery', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(51, 'status_ready_description', 'Products is ready to be shipped', 'general', '2025-03-14 14:31:01', '2025-03-14 14:39:05'),
(52, 'status_on_the_way_label', 'On The Way', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(53, 'status_on_the_way_description', 'Order is on its way to you', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(54, 'status_delivered_label', 'Delivered', 'general', '2025-03-14 14:31:01', '2025-03-14 14:31:01'),
(55, 'status_delivered_description', 'Products has been delivered', 'general', '2025-03-14 14:31:01', '2025-03-14 14:44:02'),
(56, 'loyalty_points_threshold', '5', 'general', '2025-03-15 05:45:59', '2025-03-15 07:45:52'),
(57, 'lockdown_redirect_url', '/enter_code.php', 'security', '2025-03-15 12:11:25', '2025-03-15 12:11:25'),
(58, 'enable_site_lockdown', '0', 'security', '2025-03-15 12:11:25', '2025-03-15 12:11:25'),
(59, 'lockdown_validity_days', '20', 'security', '2025-03-15 12:11:25', '2025-04-03 09:23:49');

-- --------------------------------------------------------

--
-- Table structure for table `tag_categories`
--

CREATE TABLE `tag_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `display_order` int(11) DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `tag_categories`
--

INSERT INTO `tag_categories` (`id`, `name`, `display_order`, `is_active`, `created_at`) VALUES
(1, 'STRAIN HIGHLIGHTS', 1, 1, '2025-03-13 09:11:39');

-- --------------------------------------------------------

--
-- Table structure for table `updates`
--

CREATE TABLE `updates` (
  `id` int(11) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `updates`
--

INSERT INTO `updates` (`id`, `title`, `content`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'Grand Opening!', 'We are excited to announce that Burger Shot will be opening its doors on April 15th. Come join us for special offers and discounts!', 1, '2025-03-11 23:44:07', '2025-03-11 23:44:07'),
(2, 'Free Pre Roll', 'Free Preroll for Everyone if you Spend R1000', 1, '2025-03-13 22:46:23', '2025-03-13 22:46:23');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` varchar(20) NOT NULL DEFAULT 'admin',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `role`, `created_at`, `updated_at`, `last_login`) VALUES
(4, 'admin', '$2y$10$cu.VV60k/eXTWwE3xYONH./gGIttBDxUJfqiytUHn/Xw7w/e/rn8O', '<EMAIL>', 'admin', '2025-03-11 22:34:45', '2025-05-18 11:28:15', '2025-05-18 11:28:15'),
(5, 'superadmin', '$2y$10$t0qDnCwZj69zNSqQw6XDmOqO6dzpJwFZidbKTQ2CtzZ1/lT4DsLjS', '<EMAIL>', 'admin', '2025-03-13 22:45:11', '2025-03-13 22:45:11', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `access_codes`
--
ALTER TABLE `access_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `code_hash` (`code_hash`,`expires_at`,`is_active`);

--
-- Indexes for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `claimed_rewards`
--
ALTER TABLE `claimed_rewards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `customer_favorites`
--
ALTER TABLE `customer_favorites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customer_id_2` (`customer_id`,`item_id`),
  ADD KEY `customer_id` (`customer_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `customer_notifications`
--
ALTER TABLE `customer_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `customer_id` (`customer_id`);

--
-- Indexes for table `customer_profiles`
--
ALTER TABLE `customer_profiles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name_phone` (`name`,`phone`),
  ADD UNIQUE KEY `phone_unique` (`phone`);

--
-- Indexes for table `custom_fields`
--
ALTER TABLE `custom_fields`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `delivery_methods`
--
ALTER TABLE `delivery_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `email_logs`
--
ALTER TABLE `email_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_order_id` (`order_id`),
  ADD KEY `idx_template_key` (`template_key`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `template_key` (`template_key`);

--
-- Indexes for table `items`
--
ALTER TABLE `items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `item_custom_fields`
--
ALTER TABLE `item_custom_fields`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_item_field` (`item_id`,`custom_field_id`),
  ADD KEY `custom_field_id` (`custom_field_id`),
  ADD KEY `idx_order_item_id` (`order_item_id`);

--
-- Indexes for table `item_custom_field_map`
--
ALTER TABLE `item_custom_field_map`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `item_id` (`item_id`,`custom_field_id`),
  ADD KEY `custom_field_id` (`custom_field_id`);

--
-- Indexes for table `item_tags`
--
ALTER TABLE `item_tags`
  ADD PRIMARY KEY (`id`),
  ADD KEY `item_id` (`item_id`),
  ADD KEY `tag_category_id` (`tag_category_id`);

--
-- Indexes for table `orders`
--
ALTER TABLE `orders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `fk_customer_profile` (`customer_id`),
  ADD KEY `delivery_method_id` (`delivery_method_id`),
  ADD KEY `payment_method_id` (`payment_method_id`);

--
-- Indexes for table `order_custom_fields`
--
ALTER TABLE `order_custom_fields`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `custom_field_id` (`custom_field_id`);

--
-- Indexes for table `order_items`
--
ALTER TABLE `order_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id` (`order_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `order_status_history`
--
ALTER TABLE `order_status_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `order_id_idx` (`order_id`),
  ADD KEY `status_idx` (`status`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `site_lockdown`
--
ALTER TABLE `site_lockdown`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`);

--
-- Indexes for table `tag_categories`
--
ALTER TABLE `tag_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `updates`
--
ALTER TABLE `updates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `access_codes`
--
ALTER TABLE `access_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `admin_notifications`
--
ALTER TABLE `admin_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `claimed_rewards`
--
ALTER TABLE `claimed_rewards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `customer_favorites`
--
ALTER TABLE `customer_favorites`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customer_notifications`
--
ALTER TABLE `customer_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `customer_profiles`
--
ALTER TABLE `customer_profiles`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `custom_fields`
--
ALTER TABLE `custom_fields`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `delivery_methods`
--
ALTER TABLE `delivery_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `email_logs`
--
ALTER TABLE `email_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `items`
--
ALTER TABLE `items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `item_custom_fields`
--
ALTER TABLE `item_custom_fields`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `item_custom_field_map`
--
ALTER TABLE `item_custom_field_map`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `item_tags`
--
ALTER TABLE `item_tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `orders`
--
ALTER TABLE `orders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=64;

--
-- AUTO_INCREMENT for table `order_custom_fields`
--
ALTER TABLE `order_custom_fields`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=62;

--
-- AUTO_INCREMENT for table `order_items`
--
ALTER TABLE `order_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=150;

--
-- AUTO_INCREMENT for table `order_status_history`
--
ALTER TABLE `order_status_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `site_lockdown`
--
ALTER TABLE `site_lockdown`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=60;

--
-- AUTO_INCREMENT for table `tag_categories`
--
ALTER TABLE `tag_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `updates`
--
ALTER TABLE `updates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `claimed_rewards`
--
ALTER TABLE `claimed_rewards`
  ADD CONSTRAINT `claimed_rewards_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `customer_profiles` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `items`
--
ALTER TABLE `items`
  ADD CONSTRAINT `items_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `item_custom_fields`
--
ALTER TABLE `item_custom_fields`
  ADD CONSTRAINT `item_custom_fields_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `item_custom_fields_ibfk_2` FOREIGN KEY (`custom_field_id`) REFERENCES `custom_fields` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `item_custom_field_map`
--
ALTER TABLE `item_custom_field_map`
  ADD CONSTRAINT `item_custom_field_map_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `item_custom_field_map_ibfk_2` FOREIGN KEY (`custom_field_id`) REFERENCES `custom_fields` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `item_tags`
--
ALTER TABLE `item_tags`
  ADD CONSTRAINT `item_tags_ibfk_1` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `item_tags_ibfk_2` FOREIGN KEY (`tag_category_id`) REFERENCES `tag_categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `orders`
--
ALTER TABLE `orders`
  ADD CONSTRAINT `fk_customer_profile` FOREIGN KEY (`customer_id`) REFERENCES `customer_profiles` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_1` FOREIGN KEY (`delivery_method_id`) REFERENCES `delivery_methods` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `orders_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `order_custom_fields`
--
ALTER TABLE `order_custom_fields`
  ADD CONSTRAINT `order_custom_fields_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_custom_fields_ibfk_2` FOREIGN KEY (`custom_field_id`) REFERENCES `custom_fields` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `order_items`
--
ALTER TABLE `order_items`
  ADD CONSTRAINT `order_items_ibfk_1` FOREIGN KEY (`order_id`) REFERENCES `orders` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `order_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
