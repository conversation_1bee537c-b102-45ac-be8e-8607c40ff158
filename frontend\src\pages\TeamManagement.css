body {
      font-family: '<PERSON>o', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
}

.layout {
      display: flex;
}

.main-content {
      flex: 1;
      margin-left: 250px;
      padding: 20px;
      overflow-y: auto;
      min-height: 100vh;
      box-sizing: border-box;
}

.header-bar {
      background-color: #166534;
      color: white;
      padding: 10px 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: sticky;
      top: 0;
      z-index: 999;
}

/* Team Management Page Styles */

.team-management-container {
    padding: 1.5rem;
    background-color: #f9fafb;
    min-height: 100vh;
}

.team-management-content {
    max-width: 1200px;
    margin: 0 auto;
}

/* Card Styles */
.team-management-card {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.team-management-card h2 {
    color: #166534;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
}

/* Form Styles */
.team-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1.25rem;
    width: 100%;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #374151;
    font-weight: 500;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background-color: #f9fafb;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.form-input:focus {
    border-color: #166534;
    outline: none;
    box-shadow: 0 0 0 2px rgba(22, 101, 52, 0.1);
}

.team-management form button[type="submit"] {
    padding: 10px 20px;
    background-color: #4CAF50; /* Green button */
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 15px;
}

/* Existing Teams Table Styles */
.team-management table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.team-management th,
.team-management td {
    text-align: left;
    padding: 12px;
    border-bottom: 1px solid #ddd;
}

.team-management th {
    background-color: #f5f5f5;
    font-weight: 500;
}

.team-management .team-logo {
    width: 30px;
    height: 30px;
    object-fit: contain; /* Ensure logo fits within the cell */
    margin-right: 10px;
}

.team-management button {
    background-color: #f44336; /* Red for delete */
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 5px; /* Space between buttons */
}

/* Error and Success Message Styles */
.team-management .error-message,
.team-management .success-message {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
}

.team-management .error-message {
    background-color: #f44336; /* Red for error */
    color: white;
}

.team-management .success-message {
    background-color: #4CAF50; /* Green for success */
    color: white;
}

@media (max-width: 768px) {
      .main-content {
          margin-left: 0;
      }
}

/* Base Container Styles */
.team-management-container {
  width: 100%;
  padding: 20px;
  box-sizing: border-box;
  background-color: white;
}

.team-management-content {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.team-management-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
  width: 100%;
}

/* Form Styles */
.team-form {
  width: 100%;
}

.form-group {
  margin-bottom: 20px;
  width: 100%;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.file-hint {
  display: block;
  margin-top: 5px;
  color: #666;
  font-size: 12px;
}

/* Button Styles - Increased Specificity */
.team-management-container .submit-button,
.team-management-container .update-button,
.team-management-container .edit-button {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: #166534;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.team-management-container .cancel-button,
.team-management-container .delete-button {
  width: 100%;
  padding: 0.75rem 1rem;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.team-management-container .submit-button:hover,
.team-management-container .update-button:hover,
.team-management-container .edit-button:hover {
  background-color: #15803d;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.team-management-container .delete-button {
  background-color: #dc2626;
}

.team-management-container .delete-button:hover {
  background-color: #b91c1c;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.team-management-container .cancel-button {
  background-color: #6b7280;
}

.team-management-container .cancel-button:hover {
  background-color: #4b5563;
  transform: translateY(-2px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Table Styles */
.teams-table-container {
  width: 100%;
  overflow-x: auto;
}

.teams-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 600px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.teams-table th,
.teams-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.teams-table th {
  background-color: #166534;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.05em;
}

.teams-table tr:hover {
  background-color: #f9fafb;
}

/* Team Logo Style */
.team-logo-image {
  width: 50px;
  height: 50px;
  object-fit: contain;
  display: block;
}

/* Action Buttons Container */
.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
  align-items: center;
}

.team-management-container .action-buttons button {
  flex: 1;
  min-width: 80px;
  max-width: 120px;
}

/* Message Styles */
.error-message,
.success-message {
  padding: 0.75rem 1rem;
  border-radius: 6px;
  margin-bottom: 1.25rem;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  border-left: 3px solid #dc2626;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  border-left: 3px solid #16a34a;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 20px;
}

.team-management-container .modal-buttons button {
  flex: 1;
  max-width: none;
}

/* Responsive Styles */
@media (max-width: 1366px) {
  .team-management-container {
    padding: 15px;
  }

  .team-management-card {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .team-management-container {
    padding: 10px;
  }

  .action-buttons {
    gap: 8px;
  }

  .team-management-container .action-buttons button {
    padding: 0.8rem;
    font-size: 0.9rem;
  }

  .modal-content {
    width: 95%;
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .team-management-card {
    padding: 10px;
  }

  .teams-table {
    min-width: 400px;
  }

  .action-buttons {
    gap: 6px;
  }

  .team-management-container .action-buttons button {
    padding: 0.7rem;
    font-size: 0.85rem;
  }
}