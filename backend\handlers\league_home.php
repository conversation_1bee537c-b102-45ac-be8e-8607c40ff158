<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

include_once '../includes/db_connect.php';

function jsonResponse($status, $message, $data = []) {
    http_response_code($status);
    echo json_encode(['status' => $status, 'message' => $message, 'data' => $data]);
    exit();
}

try {
    // Get database connection
    $conn = getDBConnection();

    // Get user from token or session
    session_start();
    $userId = null;
    
    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];
    } else if (isset($_SERVER['HTTP_AUTHORIZATION'])) {
        $token = str_replace('Bearer ', '', $_SERVER['HTTP_AUTHORIZATION']);
        $stmt = $conn->prepare("SELECT user_id FROM user_sessions WHERE token = ? AND expires_at > NOW()");
        $stmt->execute([$token]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result) {
            $userId = $result['user_id'];
        }
    }

    if (!$userId) {
        jsonResponse(401, 'User not authenticated');
    }

    // Get user's active league membership
    $activeMembershipQuery = "SELECT league_id FROM league_memberships WHERE user_id = ? AND status = 'active' LIMIT 1";
    $stmt = $conn->prepare($activeMembershipQuery);
    $stmt->execute([$userId]);
    $activeMembership = $stmt->fetch(PDO::FETCH_ASSOC);
    $activeLeagueId = $activeMembership ? $activeMembership['league_id'] : null;

    // Get leagues with membership status and prevent duplicates
    $leaguesQuery = "SELECT DISTINCT
        l.*,
        COALESCE(m.member_count, 0) as member_count,
        CASE WHEN lm.user_id IS NOT NULL THEN 1 ELSE 0 END as is_member,
        CASE WHEN ? IS NOT NULL THEN 1 ELSE 0 END as has_active_membership,
        FORMAT(l.min_bet_amount, 0) as min_bet_formatted,
        FORMAT(l.max_bet_amount, 0) as max_bet_formatted
    FROM leagues l
    LEFT JOIN (
        SELECT league_id, COUNT(*) as member_count 
        FROM league_memberships 
        WHERE status = 'active'
        GROUP BY league_id
    ) m ON m.league_id = l.league_id
    LEFT JOIN league_memberships lm ON lm.league_id = l.league_id 
        AND lm.user_id = ? 
        AND lm.status = 'active'
    WHERE l.status IN ('active', 'upcoming')
    GROUP BY l.name
    ORDER BY l.created_at DESC";

    $stmt = $conn->prepare($leaguesQuery);
    $stmt->execute([$activeLeagueId, $userId]);
    $leagues = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Add full URLs for media files
    foreach ($leagues as &$league) {
        if ($league['icon_path']) {
            $league['league_icon'] = '/backend/uploads/leagues/icons/' . $league['icon_path'];
        }
        if ($league['banner_path']) {
            $league['league_banner'] = '/backend/uploads/leagues/banners/' . $league['banner_path'];
        }
    }

    // Get user stats
    $statsQuery = "SELECT 
        u.points,
        u.current_streak,
        u.highest_streak,
        u.balance,
        u.total_points,
        u.current_league_id,
        COUNT(DISTINCT lm.league_id) as leagues_joined
    FROM users u
    LEFT JOIN league_memberships lm ON lm.user_id = u.user_id AND lm.status = 'active'
    WHERE u.user_id = ?
    GROUP BY u.user_id";

    $statsStmt = $conn->prepare($statsQuery);
    $statsStmt->execute([$userId]);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

    // Get current season
    $seasonQuery = "SELECT 
        season_id,
        name,
        start_date,
        end_date,
        status,
        DATEDIFF(end_date, CURRENT_DATE()) as days_remaining
    FROM seasons 
    WHERE status = 'active' 
    LIMIT 1";
    
    $season = $conn->query($seasonQuery)->fetch(PDO::FETCH_ASSOC);

    // Debug log
    error_log("Leagues found: " . count($leagues));
    error_log("User ID: " . $userId);

    jsonResponse(200, 'Success', [
        'leagues' => $leagues,
        'user_stats' => $stats,
        'current_season' => $season
    ]);

} catch (Exception $e) {
    error_log("Error in league_home.php: " . $e->getMessage());
    jsonResponse(500, 'An error occurred while fetching leagues');
}
