import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
    <PERSON>aCog,
    FaShieldAlt,
    FaEnvelope,
    FaCreditCard,
    FaUsers,
    FaGamepad,
    FaTrophy,
    FaBell
} from 'react-icons/fa';
import './AdminStyles.css';

function SystemSettings() {
    return (
        <div className="admin-container">
            <div className="page-header">
                <h1><FaCog /> System Settings</h1>
                <p className="admin-description">
                    Configure various system settings to customize the behavior and appearance of the FanBet247 platform.
                </p>
            </div>

            <div className="settings-grid">
                <div className="settings-card">
                    <h3><FaCog /> General Settings</h3>
                    <p>Configure basic system settings like site name, logo, and contact information.</p>
                    <Link to="/admin/general-settings" className="btn btn-primary">
                        <FaCog /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaShieldAlt /> Security Settings</h3>
                    <p>Configure two-factor authentication (2FA) options and other security features.</p>
                    <Link to="/admin/security-settings" className="btn btn-primary">
                        <FaShieldAlt /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaEnvelope /> SMTP Settings</h3>
                    <p>Configure email server settings for sending notifications and OTP codes.</p>
                    <Link to="/admin/smtp-settings" className="btn btn-primary">
                        <FaEnvelope /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaCreditCard /> Payment Settings</h3>
                    <p>Manage payment gateways, transaction fees, and withdrawal limits.</p>
                    <Link to="/admin/payment-methods" className="btn btn-primary">
                        <FaCreditCard /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaUsers /> User Settings</h3>
                    <p>Manage user registration options, verification requirements, and permissions.</p>
                    <Link to="/admin/users" className="btn btn-primary">
                        <FaUsers /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaGamepad /> Challenge Settings</h3>
                    <p>Configure challenge types, odds calculation, and betting limits.</p>
                    <Link to="/admin/challenge-management" className="btn btn-primary">
                        <FaGamepad /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaTrophy /> League Settings</h3>
                    <p>Manage league configurations, seasons, and promotion/relegation rules.</p>
                    <Link to="/admin/league-management" className="btn btn-primary">
                        <FaTrophy /> Configure
                    </Link>
                </div>

                <div className="settings-card">
                    <h3><FaBell /> Notification Settings</h3>
                    <p>Configure email and push notification templates and settings.</p>
                    <Link to="/admin/notification-settings" className="btn btn-primary">
                        <FaBell /> Configure
                    </Link>
                </div>
            </div>
        </div>
    );
}

export default SystemSettings;
