<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Update the password for the test user
    $username = 'demohomexx';
    $email = '<EMAIL>';
    $new_password = 'loving12';
    $password_hash = password_hash($new_password, PASSWORD_DEFAULT);

    // Also update testuser password
    $test_username = 'testuser';
    $test_email = '<EMAIL>';

    // Update both users' passwords
    $query = "UPDATE users SET password_hash = :password_hash WHERE username IN (:username, :test_username) OR email IN (:email, :test_email)";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':password_hash', $password_hash);
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':test_username', $test_username);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':test_email', $test_email);
    
    if ($stmt->execute()) {
        $affected_rows = $stmt->rowCount();
        
        if ($affected_rows > 0) {
            echo json_encode([
                'success' => true,
                'message' => "Password updated successfully for user: $username",
                'affected_rows' => $affected_rows
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => "User not found: $username"
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => "Failed to update password"
        ]);
    }

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Database error: " . $e->getMessage()
    ]);
}
?>
