import React from 'react';

// Function to truncate team names to a reasonable length
const truncateTeamName = (name) => {
  const maxLength = 12; // Maximum characters to display
  if (name.length <= maxLength) return name;
  return name.substring(0, maxLength) + '...';
};

const EmptyState = ({ message }) => (
  <div className="empty-state">
    <img
      src="/empty-challenges.png"
      alt="No data"
      className="empty-state__image"
      onError={(e) => e.target.style.display = 'none'}
    />
    <p>{message}</p>
  </div>
);

const RecentBets = ({ recentBets, loading, API_BASE_URL }) => {
  if (loading) {
    return <div className="loading">Loading recent bets...</div>;
  }

  if (recentBets.length === 0) {
    return <EmptyState message="No recent bets available. Start betting now!" />;
  }

  return (
    <div className="recent-bets-grid">
      {recentBets.slice(0, 6).map((bet, index) => (
        <div key={bet.bet_id || index} className="recent-bet-card">
          <div className="bet-header">
            <span className="bet-ref">REF: {bet.unique_code}</span>
          </div>

          <div className="bet-match-container">
            <div className="bet-team-column">
              <div className="bet-team">
                <img
                  src={`${API_BASE_URL}/${bet.team_a_logo}`}
                  alt={bet.team_a}
                  className="bet-team-logo"
                  onError={(e) => {
                    e.target.src = '/default-team-logo.png';
                  }}
                />
                <span className="bet-team-name">{truncateTeamName(bet.team_a)}</span>
              </div>

              <div className="bet-odds-item">
                <span className="bet-odds-value">{Number(bet.odds_team_a).toFixed(2)}</span>
                <span className="bet-odds-label">Win</span>
              </div>

              <div className="bet-user-simple">
                <span className="bet-username-simple">{bet.user1_name.toUpperCase()}</span>
                <span className="bet-user-amount">{Number(bet.amount_user1).toFixed(2)} FC</span>
              </div>
            </div>

            <div className="bet-vs-column">
              <div className="bet-vs">VS</div>
              <div className="bet-odds-item">
                <span className="bet-odds-value">{Number(bet.odds_draw).toFixed(2)}</span>
                <span className="bet-odds-label">Draw</span>
              </div>
            </div>

            <div className="bet-team-column">
              <div className="bet-team">
                <img
                  src={`${API_BASE_URL}/${bet.team_b_logo}`}
                  alt={bet.team_b}
                  className="bet-team-logo"
                  onError={(e) => {
                    e.target.src = '/default-team-logo.png';
                  }}
                />
                <span className="bet-team-name">{truncateTeamName(bet.team_b)}</span>
              </div>

              <div className="bet-odds-item">
                <span className="bet-odds-value">{Number(bet.odds_team_b).toFixed(2)}</span>
                <span className="bet-odds-label">Win</span>
              </div>

              <div className="bet-user-simple">
                <span className="bet-username-simple">{bet.user2_name.toUpperCase()}</span>
                <span className="bet-user-amount">{Number(bet.amount_user2).toFixed(2)} FC</span>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default RecentBets;
