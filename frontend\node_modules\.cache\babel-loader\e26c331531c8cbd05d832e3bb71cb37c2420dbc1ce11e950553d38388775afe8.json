{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * Custom hook for API service usage\n * \n * Provides a consistent interface for making API calls with loading states,\n * error handling, and automatic retries.\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport { apiService } from '../services';\n\n/**\n * Custom hook for API operations\n * @param {object} options - Hook configuration options\n * @returns {object} API operation utilities\n */\nconst useApiService = (options = {}) => {\n  _s();\n  const {\n    autoRetry = true,\n    maxRetries = 3,\n    retryDelay = 1000,\n    onError = null,\n    onSuccess = null\n  } = options;\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [data, setData] = useState(null);\n  const abortControllerRef = useRef(null);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n    };\n  }, []);\n\n  /**\n   * Execute API operation with error handling and retries\n   * @param {function} operation - API operation function\n   * @param {object} operationOptions - Operation-specific options\n   * @returns {Promise<any>} Operation result\n   */\n  const execute = useCallback(async (operation, operationOptions = {}) => {\n    const {\n      retries = autoRetry ? maxRetries : 0,\n      delay = retryDelay,\n      abortable = true\n    } = operationOptions;\n\n    // Create abort controller for cancellable requests\n    if (abortable) {\n      abortControllerRef.current = new AbortController();\n    }\n    setLoading(true);\n    setError(null);\n    let lastError = null;\n    let attempt = 0;\n    while (attempt <= retries) {\n      try {\n        var _abortControllerRef$c;\n        const result = await operation((_abortControllerRef$c = abortControllerRef.current) === null || _abortControllerRef$c === void 0 ? void 0 : _abortControllerRef$c.signal);\n        if (result.success) {\n          setData(result.data);\n          if (onSuccess) {\n            onSuccess(result);\n          }\n          return result;\n        } else {\n          throw new Error(result.message || 'Operation failed');\n        }\n      } catch (err) {\n        var _err$response, _err$response2;\n        lastError = err;\n\n        // Don't retry if request was aborted\n        if (err.name === 'AbortError') {\n          break;\n        }\n\n        // Don't retry on certain error types\n        if (((_err$response = err.response) === null || _err$response === void 0 ? void 0 : _err$response.status) === 401 || ((_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : _err$response2.status) === 403) {\n          break;\n        }\n        attempt++;\n\n        // Wait before retry (except on last attempt)\n        if (attempt <= retries) {\n          await new Promise(resolve => setTimeout(resolve, delay * attempt));\n        }\n      }\n    }\n\n    // Handle final error\n    setError(lastError);\n    if (onError) {\n      onError(lastError);\n    }\n    throw lastError;\n  }, [autoRetry, maxRetries, retryDelay, onError, onSuccess]);\n\n  /**\n   * Cancel ongoing request\n   */\n  const cancel = useCallback(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n  }, []);\n\n  /**\n   * Reset hook state\n   */\n  const reset = useCallback(() => {\n    setLoading(false);\n    setError(null);\n    setData(null);\n  }, []);\n\n  /**\n   * GET request wrapper\n   */\n  const get = useCallback((endpoint, params = {}, options = {}) => {\n    return execute(signal => apiService.get(endpoint, params, {\n      ...options,\n      signal\n    }), options);\n  }, [execute]);\n\n  /**\n   * POST request wrapper\n   */\n  const post = useCallback((endpoint, data = {}, options = {}) => {\n    return execute(signal => apiService.post(endpoint, data, {\n      ...options,\n      signal\n    }), options);\n  }, [execute]);\n\n  /**\n   * PUT request wrapper\n   */\n  const put = useCallback((endpoint, data = {}, options = {}) => {\n    return execute(signal => apiService.put(endpoint, data, {\n      ...options,\n      signal\n    }), options);\n  }, [execute]);\n\n  /**\n   * DELETE request wrapper\n   */\n  const del = useCallback((endpoint, options = {}) => {\n    return execute(signal => apiService.delete(endpoint, {\n      ...options,\n      signal\n    }), options);\n  }, [execute]);\n\n  /**\n   * Upload request wrapper\n   */\n  const upload = useCallback((endpoint, formData, onProgress = null, options = {}) => {\n    return execute(signal => apiService.upload(endpoint, formData, onProgress, {\n      ...options,\n      signal\n    }), options);\n  }, [execute]);\n\n  // Finally block to always set loading to false\n  useEffect(() => {\n    const originalExecute = execute;\n    return () => {\n      setLoading(false);\n    };\n  }, [execute]);\n\n  // Set loading to false when operation completes\n  const wrappedExecute = useCallback(async (...args) => {\n    try {\n      return await execute(...args);\n    } finally {\n      setLoading(false);\n    }\n  }, [execute]);\n  return {\n    // State\n    loading,\n    error,\n    data,\n    // Actions\n    execute: wrappedExecute,\n    cancel,\n    reset,\n    // HTTP methods\n    get,\n    post,\n    put,\n    delete: del,\n    upload,\n    // Utilities\n    isLoading: loading,\n    hasError: !!error,\n    hasData: !!data\n  };\n};\n_s(useApiService, \"UJQIz82/ALKtKGjKo7+DNKu7Chs=\");\nexport default useApiService;", "map": {"version": 3, "names": ["useState", "useCallback", "useRef", "useEffect", "apiService", "useApiService", "options", "_s", "autoRetry", "maxRetries", "retry<PERSON><PERSON><PERSON>", "onError", "onSuccess", "loading", "setLoading", "error", "setError", "data", "setData", "abortControllerRef", "current", "abort", "execute", "operation", "operationOptions", "retries", "delay", "abortable", "AbortController", "lastError", "attempt", "_abortControllerRef$c", "result", "signal", "success", "Error", "message", "err", "_err$response", "_err$response2", "name", "response", "status", "Promise", "resolve", "setTimeout", "cancel", "reset", "get", "endpoint", "params", "post", "put", "del", "delete", "upload", "formData", "onProgress", "originalExecute", "wrappedExecute", "args", "isLoading", "<PERSON><PERSON><PERSON><PERSON>", "hasData"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/hooks/useApiService.js"], "sourcesContent": ["/**\n * Custom hook for API service usage\n * \n * Provides a consistent interface for making API calls with loading states,\n * error handling, and automatic retries.\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport { apiService } from '../services';\n\n/**\n * Custom hook for API operations\n * @param {object} options - Hook configuration options\n * @returns {object} API operation utilities\n */\nconst useApiService = (options = {}) => {\n    const {\n        autoRetry = true,\n        maxRetries = 3,\n        retryDelay = 1000,\n        onError = null,\n        onSuccess = null\n    } = options;\n\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState(null);\n    const [data, setData] = useState(null);\n    const abortControllerRef = useRef(null);\n\n    // Cleanup on unmount\n    useEffect(() => {\n        return () => {\n            if (abortControllerRef.current) {\n                abortControllerRef.current.abort();\n            }\n        };\n    }, []);\n\n    /**\n     * Execute API operation with error handling and retries\n     * @param {function} operation - API operation function\n     * @param {object} operationOptions - Operation-specific options\n     * @returns {Promise<any>} Operation result\n     */\n    const execute = useCallback(async (operation, operationOptions = {}) => {\n        const {\n            retries = autoRetry ? maxRetries : 0,\n            delay = retryDelay,\n            abortable = true\n        } = operationOptions;\n\n        // Create abort controller for cancellable requests\n        if (abortable) {\n            abortControllerRef.current = new AbortController();\n        }\n\n        setLoading(true);\n        setError(null);\n\n        let lastError = null;\n        let attempt = 0;\n\n        while (attempt <= retries) {\n            try {\n                const result = await operation(abortControllerRef.current?.signal);\n                \n                if (result.success) {\n                    setData(result.data);\n                    if (onSuccess) {\n                        onSuccess(result);\n                    }\n                    return result;\n                } else {\n                    throw new Error(result.message || 'Operation failed');\n                }\n            } catch (err) {\n                lastError = err;\n                \n                // Don't retry if request was aborted\n                if (err.name === 'AbortError') {\n                    break;\n                }\n                \n                // Don't retry on certain error types\n                if (err.response?.status === 401 || err.response?.status === 403) {\n                    break;\n                }\n                \n                attempt++;\n                \n                // Wait before retry (except on last attempt)\n                if (attempt <= retries) {\n                    await new Promise(resolve => setTimeout(resolve, delay * attempt));\n                }\n            }\n        }\n\n        // Handle final error\n        setError(lastError);\n        if (onError) {\n            onError(lastError);\n        }\n        \n        throw lastError;\n    }, [autoRetry, maxRetries, retryDelay, onError, onSuccess]);\n\n    /**\n     * Cancel ongoing request\n     */\n    const cancel = useCallback(() => {\n        if (abortControllerRef.current) {\n            abortControllerRef.current.abort();\n        }\n    }, []);\n\n    /**\n     * Reset hook state\n     */\n    const reset = useCallback(() => {\n        setLoading(false);\n        setError(null);\n        setData(null);\n    }, []);\n\n    /**\n     * GET request wrapper\n     */\n    const get = useCallback((endpoint, params = {}, options = {}) => {\n        return execute(\n            (signal) => apiService.get(endpoint, params, { ...options, signal }),\n            options\n        );\n    }, [execute]);\n\n    /**\n     * POST request wrapper\n     */\n    const post = useCallback((endpoint, data = {}, options = {}) => {\n        return execute(\n            (signal) => apiService.post(endpoint, data, { ...options, signal }),\n            options\n        );\n    }, [execute]);\n\n    /**\n     * PUT request wrapper\n     */\n    const put = useCallback((endpoint, data = {}, options = {}) => {\n        return execute(\n            (signal) => apiService.put(endpoint, data, { ...options, signal }),\n            options\n        );\n    }, [execute]);\n\n    /**\n     * DELETE request wrapper\n     */\n    const del = useCallback((endpoint, options = {}) => {\n        return execute(\n            (signal) => apiService.delete(endpoint, { ...options, signal }),\n            options\n        );\n    }, [execute]);\n\n    /**\n     * Upload request wrapper\n     */\n    const upload = useCallback((endpoint, formData, onProgress = null, options = {}) => {\n        return execute(\n            (signal) => apiService.upload(endpoint, formData, onProgress, { ...options, signal }),\n            options\n        );\n    }, [execute]);\n\n    // Finally block to always set loading to false\n    useEffect(() => {\n        const originalExecute = execute;\n        return () => {\n            setLoading(false);\n        };\n    }, [execute]);\n\n    // Set loading to false when operation completes\n    const wrappedExecute = useCallback(async (...args) => {\n        try {\n            return await execute(...args);\n        } finally {\n            setLoading(false);\n        }\n    }, [execute]);\n\n    return {\n        // State\n        loading,\n        error,\n        data,\n        \n        // Actions\n        execute: wrappedExecute,\n        cancel,\n        reset,\n        \n        // HTTP methods\n        get,\n        post,\n        put,\n        delete: del,\n        upload,\n        \n        // Utilities\n        isLoading: loading,\n        hasError: !!error,\n        hasData: !!data\n    };\n};\n\nexport default useApiService;\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChE,SAASC,UAAU,QAAQ,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,OAAO,GAAG,CAAC,CAAC,KAAK;EAAAC,EAAA;EACpC,MAAM;IACFC,SAAS,GAAG,IAAI;IAChBC,UAAU,GAAG,CAAC;IACdC,UAAU,GAAG,IAAI;IACjBC,OAAO,GAAG,IAAI;IACdC,SAAS,GAAG;EAChB,CAAC,GAAGN,OAAO;EAEX,MAAM,CAACO,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACiB,IAAI,EAAEC,OAAO,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAMmB,kBAAkB,GAAGjB,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACAC,SAAS,CAAC,MAAM;IACZ,OAAO,MAAM;MACT,IAAIgB,kBAAkB,CAACC,OAAO,EAAE;QAC5BD,kBAAkB,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;MACtC;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMC,OAAO,GAAGrB,WAAW,CAAC,OAAOsB,SAAS,EAAEC,gBAAgB,GAAG,CAAC,CAAC,KAAK;IACpE,MAAM;MACFC,OAAO,GAAGjB,SAAS,GAAGC,UAAU,GAAG,CAAC;MACpCiB,KAAK,GAAGhB,UAAU;MAClBiB,SAAS,GAAG;IAChB,CAAC,GAAGH,gBAAgB;;IAEpB;IACA,IAAIG,SAAS,EAAE;MACXR,kBAAkB,CAACC,OAAO,GAAG,IAAIQ,eAAe,CAAC,CAAC;IACtD;IAEAd,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAIa,SAAS,GAAG,IAAI;IACpB,IAAIC,OAAO,GAAG,CAAC;IAEf,OAAOA,OAAO,IAAIL,OAAO,EAAE;MACvB,IAAI;QAAA,IAAAM,qBAAA;QACA,MAAMC,MAAM,GAAG,MAAMT,SAAS,EAAAQ,qBAAA,GAACZ,kBAAkB,CAACC,OAAO,cAAAW,qBAAA,uBAA1BA,qBAAA,CAA4BE,MAAM,CAAC;QAElE,IAAID,MAAM,CAACE,OAAO,EAAE;UAChBhB,OAAO,CAACc,MAAM,CAACf,IAAI,CAAC;UACpB,IAAIL,SAAS,EAAE;YACXA,SAAS,CAACoB,MAAM,CAAC;UACrB;UACA,OAAOA,MAAM;QACjB,CAAC,MAAM;UACH,MAAM,IAAIG,KAAK,CAACH,MAAM,CAACI,OAAO,IAAI,kBAAkB,CAAC;QACzD;MACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;QAAA,IAAAC,aAAA,EAAAC,cAAA;QACVV,SAAS,GAAGQ,GAAG;;QAEf;QACA,IAAIA,GAAG,CAACG,IAAI,KAAK,YAAY,EAAE;UAC3B;QACJ;;QAEA;QACA,IAAI,EAAAF,aAAA,GAAAD,GAAG,CAACI,QAAQ,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,MAAM,MAAK,GAAG,IAAI,EAAAH,cAAA,GAAAF,GAAG,CAACI,QAAQ,cAAAF,cAAA,uBAAZA,cAAA,CAAcG,MAAM,MAAK,GAAG,EAAE;UAC9D;QACJ;QAEAZ,OAAO,EAAE;;QAET;QACA,IAAIA,OAAO,IAAIL,OAAO,EAAE;UACpB,MAAM,IAAIkB,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAElB,KAAK,GAAGI,OAAO,CAAC,CAAC;QACtE;MACJ;IACJ;;IAEA;IACAd,QAAQ,CAACa,SAAS,CAAC;IACnB,IAAIlB,OAAO,EAAE;MACTA,OAAO,CAACkB,SAAS,CAAC;IACtB;IAEA,MAAMA,SAAS;EACnB,CAAC,EAAE,CAACrB,SAAS,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,EAAEC,SAAS,CAAC,CAAC;;EAE3D;AACJ;AACA;EACI,MAAMkC,MAAM,GAAG7C,WAAW,CAAC,MAAM;IAC7B,IAAIkB,kBAAkB,CAACC,OAAO,EAAE;MAC5BD,kBAAkB,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IACtC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;AACJ;AACA;EACI,MAAM0B,KAAK,GAAG9C,WAAW,CAAC,MAAM;IAC5Ba,UAAU,CAAC,KAAK,CAAC;IACjBE,QAAQ,CAAC,IAAI,CAAC;IACdE,OAAO,CAAC,IAAI,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACJ;AACA;EACI,MAAM8B,GAAG,GAAG/C,WAAW,CAAC,CAACgD,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAE5C,OAAO,GAAG,CAAC,CAAC,KAAK;IAC7D,OAAOgB,OAAO,CACTW,MAAM,IAAK7B,UAAU,CAAC4C,GAAG,CAACC,QAAQ,EAAEC,MAAM,EAAE;MAAE,GAAG5C,OAAO;MAAE2B;IAAO,CAAC,CAAC,EACpE3B,OACJ,CAAC;EACL,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;;EAEb;AACJ;AACA;EACI,MAAM6B,IAAI,GAAGlD,WAAW,CAAC,CAACgD,QAAQ,EAAEhC,IAAI,GAAG,CAAC,CAAC,EAAEX,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,OAAOgB,OAAO,CACTW,MAAM,IAAK7B,UAAU,CAAC+C,IAAI,CAACF,QAAQ,EAAEhC,IAAI,EAAE;MAAE,GAAGX,OAAO;MAAE2B;IAAO,CAAC,CAAC,EACnE3B,OACJ,CAAC;EACL,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;;EAEb;AACJ;AACA;EACI,MAAM8B,GAAG,GAAGnD,WAAW,CAAC,CAACgD,QAAQ,EAAEhC,IAAI,GAAG,CAAC,CAAC,EAAEX,OAAO,GAAG,CAAC,CAAC,KAAK;IAC3D,OAAOgB,OAAO,CACTW,MAAM,IAAK7B,UAAU,CAACgD,GAAG,CAACH,QAAQ,EAAEhC,IAAI,EAAE;MAAE,GAAGX,OAAO;MAAE2B;IAAO,CAAC,CAAC,EAClE3B,OACJ,CAAC;EACL,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;;EAEb;AACJ;AACA;EACI,MAAM+B,GAAG,GAAGpD,WAAW,CAAC,CAACgD,QAAQ,EAAE3C,OAAO,GAAG,CAAC,CAAC,KAAK;IAChD,OAAOgB,OAAO,CACTW,MAAM,IAAK7B,UAAU,CAACkD,MAAM,CAACL,QAAQ,EAAE;MAAE,GAAG3C,OAAO;MAAE2B;IAAO,CAAC,CAAC,EAC/D3B,OACJ,CAAC;EACL,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;;EAEb;AACJ;AACA;EACI,MAAMiC,MAAM,GAAGtD,WAAW,CAAC,CAACgD,QAAQ,EAAEO,QAAQ,EAAEC,UAAU,GAAG,IAAI,EAAEnD,OAAO,GAAG,CAAC,CAAC,KAAK;IAChF,OAAOgB,OAAO,CACTW,MAAM,IAAK7B,UAAU,CAACmD,MAAM,CAACN,QAAQ,EAAEO,QAAQ,EAAEC,UAAU,EAAE;MAAE,GAAGnD,OAAO;MAAE2B;IAAO,CAAC,CAAC,EACrF3B,OACJ,CAAC;EACL,CAAC,EAAE,CAACgB,OAAO,CAAC,CAAC;;EAEb;EACAnB,SAAS,CAAC,MAAM;IACZ,MAAMuD,eAAe,GAAGpC,OAAO;IAC/B,OAAO,MAAM;MACTR,UAAU,CAAC,KAAK,CAAC;IACrB,CAAC;EACL,CAAC,EAAE,CAACQ,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMqC,cAAc,GAAG1D,WAAW,CAAC,OAAO,GAAG2D,IAAI,KAAK;IAClD,IAAI;MACA,OAAO,MAAMtC,OAAO,CAAC,GAAGsC,IAAI,CAAC;IACjC,CAAC,SAAS;MACN9C,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACQ,OAAO,CAAC,CAAC;EAEb,OAAO;IACH;IACAT,OAAO;IACPE,KAAK;IACLE,IAAI;IAEJ;IACAK,OAAO,EAAEqC,cAAc;IACvBb,MAAM;IACNC,KAAK;IAEL;IACAC,GAAG;IACHG,IAAI;IACJC,GAAG;IACHE,MAAM,EAAED,GAAG;IACXE,MAAM;IAEN;IACAM,SAAS,EAAEhD,OAAO;IAClBiD,QAAQ,EAAE,CAAC,CAAC/C,KAAK;IACjBgD,OAAO,EAAE,CAAC,CAAC9C;EACf,CAAC;AACL,CAAC;AAACV,EAAA,CAvMIF,aAAa;AAyMnB,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}