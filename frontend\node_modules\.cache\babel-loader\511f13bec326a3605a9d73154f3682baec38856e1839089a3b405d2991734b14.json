{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction CurrencyManagement() {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stats\n  const [stats, setStats] = useState({\n    totalCurrencies: 0,\n    activeCurrencies: 0,\n    inactiveCurrencies: 0,\n    lastUpdated: null\n  });\n\n  // Modal states\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [editingRate, setEditingRate] = useState(null);\n  const [editingCurrency, setEditingCurrency] = useState(null);\n\n  // Form states\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  useEffect(() => {\n    loadCurrencies();\n    loadExchangeRates();\n  }, []);\n  const loadCurrencies = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n      if (response.data.success) {\n        const currencyData = response.data.data.currencies || [];\n        setCurrencies(currencyData);\n\n        // Calculate stats\n        const totalCurrencies = currencyData.length;\n        const activeCurrencies = currencyData.filter(c => c.is_active).length;\n        const inactiveCurrencies = totalCurrencies - activeCurrencies;\n        setStats({\n          totalCurrencies,\n          activeCurrencies,\n          inactiveCurrencies,\n          lastUpdated: new Date().toISOString()\n        });\n      } else {\n        setError(response.data.message || 'Failed to load currencies');\n      }\n    } catch (err) {\n      console.error('Error loading currencies:', err);\n      setError('Failed to load currencies. Please check your network connection.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadExchangeRates = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n      if (response.data.success) {\n        setExchangeRates(response.data.data.exchange_rates || []);\n      }\n    } catch (err) {\n      console.error('Error loading exchange rates:', err);\n    }\n  };\n\n  // Update exchange rate\n  const handleUpdateRate = useCallback(async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      alert('Please enter a valid exchange rate');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        alert('Exchange rate updated successfully!');\n      } else {\n        alert(response.message || 'Failed to update exchange rate');\n      }\n    } catch (error) {\n      alert('Error updating exchange rate: ' + error.message);\n    }\n  }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n  // Toggle currency active status\n  const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('toggle', {\n        currency_id: currencyId\n      }));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n      } else {\n        alert(response.message || 'Failed to toggle currency status');\n      }\n    } catch (error) {\n      alert('Error toggling currency: ' + error.message);\n    }\n  }, [execute, loadData, refreshCurrencyData]);\n\n  // Add new currency\n  const handleAddCurrency = useCallback(async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('create', newCurrency));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        alert('Currency added successfully!');\n      } else {\n        alert(response.message || 'Failed to add currency');\n      }\n    } catch (error) {\n      alert('Error adding currency: ' + error.message);\n    }\n  }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n  // Get exchange rate for currency\n  const getExchangeRate = useCallback(currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  }, [exchangeRates]);\n\n  // Format date\n  const formatDate = useCallback(dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }, []);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"currency-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Currency Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage supported currencies and exchange rates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 192,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddCurrency(true),\n        children: \"Add New Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\u26A0\\uFE0F \", error.message || 'An error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-secondary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 17\n    }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"currencies-grid\",\n      children: currencies.map(currency => {\n        const rate = getExchangeRate(currency.id);\n        const isEditing = editingRate === currency.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `currency-card ${!currency.is_active ? 'inactive' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: currency.currency_code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 225,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: currency.currency_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"currency-symbol\",\n                children: currency.currency_symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${currency.is_active ? 'active' : 'inactive'}`,\n                children: currency.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exchange-rate-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Exchange Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 37\n            }, this), rate ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rate-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-rate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rate-value\",\n                  children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Updated: \", formatDate(rate.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 45\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rate-edit-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.0001\",\n                  value: newRate,\n                  onChange: e => setNewRate(e.target.value),\n                  placeholder: \"New rate\",\n                  className: \"rate-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: notes,\n                  onChange: e => setNotes(e.target.value),\n                  placeholder: \"Update notes (optional)\",\n                  className: \"notes-input\",\n                  rows: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"edit-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleUpdateRate(currency.id),\n                    className: \"btn btn-success btn-sm\",\n                    disabled: loading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRate(null);\n                      setNewRate('');\n                      setNotes('');\n                    },\n                    className: \"btn btn-secondary btn-sm\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 272,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate(rate.rate_to_fancoin.toString());\n                },\n                className: \"btn btn-outline btn-sm\",\n                children: \"Update Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-rate\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No exchange rate set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate('');\n                },\n                className: \"btn btn-primary btn-sm\",\n                children: \"Set Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n              className: `btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`,\n              disabled: loading,\n              children: currency.is_active ? 'Deactivate' : 'Activate'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 312,\n            columnNumber: 33\n          }, this)]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 216,\n      columnNumber: 17\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 331,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 362,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 361,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 329,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 189,\n    columnNumber: 9\n  }, this);\n}\n_s(CurrencyManagement, \"+W8TnmakR5xH+vR0kjF2XfBizwY=\");\n_c = CurrencyManagement;\n;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "editingRate", "setEditingRate", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadCurrencies", "loadExchangeRates", "response", "get", "data", "currencyData", "length", "filter", "c", "Date", "toISOString", "message", "err", "console", "exchange_rates", "handleUpdateRate", "useCallback", "currencyId", "isNaN", "parseFloat", "alert", "execute", "currencyService", "updateExchangeRate", "loadData", "refreshCurrencyData", "handleToggleCurrency", "currentStatus", "manageCurrencies", "currency_id", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "currency", "id", "isEditing", "rate_to_fancoin", "updated_at", "type", "step", "value", "onChange", "e", "target", "placeholder", "rows", "disabled", "toString", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [editingRate, setEditingRate] = useState(null);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    useEffect(() => {\n        loadCurrencies();\n        loadExchangeRates();\n    }, []);\n\n    const loadCurrencies = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n            if (response.data.success) {\n                const currencyData = response.data.data.currencies || [];\n                setCurrencies(currencyData);\n\n                // Calculate stats\n                const totalCurrencies = currencyData.length;\n                const activeCurrencies = currencyData.filter(c => c.is_active).length;\n                const inactiveCurrencies = totalCurrencies - activeCurrencies;\n\n                setStats({\n                    totalCurrencies,\n                    activeCurrencies,\n                    inactiveCurrencies,\n                    lastUpdated: new Date().toISOString()\n                });\n            } else {\n                setError(response.data.message || 'Failed to load currencies');\n            }\n        } catch (err) {\n            console.error('Error loading currencies:', err);\n            setError('Failed to load currencies. Please check your network connection.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const loadExchangeRates = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n            if (response.data.success) {\n                setExchangeRates(response.data.data.exchange_rates || []);\n            }\n        } catch (err) {\n            console.error('Error loading exchange rates:', err);\n        }\n    };\n\n    // Update exchange rate\n    const handleUpdateRate = useCallback(async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            alert('Please enter a valid exchange rate');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                alert('Exchange rate updated successfully!');\n            } else {\n                alert(response.message || 'Failed to update exchange rate');\n            }\n        } catch (error) {\n            alert('Error updating exchange rate: ' + error.message);\n        }\n    }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n    // Toggle currency active status\n    const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('toggle', { currency_id: currencyId })\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n            } else {\n                alert(response.message || 'Failed to toggle currency status');\n            }\n        } catch (error) {\n            alert('Error toggling currency: ' + error.message);\n        }\n    }, [execute, loadData, refreshCurrencyData]);\n\n    // Add new currency\n    const handleAddCurrency = useCallback(async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            alert('Please fill in all required fields');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('create', newCurrency)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                alert('Currency added successfully!');\n            } else {\n                alert(response.message || 'Failed to add currency');\n            }\n        } catch (error) {\n            alert('Error adding currency: ' + error.message);\n        }\n    }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n    // Get exchange rate for currency\n    const getExchangeRate = useCallback((currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    }, [exchangeRates]);\n\n    // Format date\n    const formatDate = useCallback((dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }, []);\n\n    // Load data on component mount\n    useEffect(() => {\n        loadData();\n    }, [loadData]);\n\n    return (\n        <div className=\"currency-management\">\n            <div className=\"page-header\">\n                <h1>Currency Management</h1>\n                <p>Manage supported currencies and exchange rates</p>\n                <button \n                    className=\"btn btn-primary\"\n                    onClick={() => setShowAddCurrency(true)}\n                >\n                    Add New Currency\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <p>⚠️ {error.message || 'An error occurred'}</p>\n                    <button onClick={loadData} className=\"btn btn-secondary\">\n                        Retry\n                    </button>\n                </div>\n            )}\n\n            {loading && currencies.length === 0 ? (\n                <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                    <p>Loading currencies...</p>\n                </div>\n            ) : (\n                <div className=\"currencies-grid\">\n                    {currencies.map((currency) => {\n                        const rate = getExchangeRate(currency.id);\n                        const isEditing = editingRate === currency.id;\n\n                        return (\n                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>\n                                <div className=\"currency-header\">\n                                    <div className=\"currency-info\">\n                                        <h3>{currency.currency_code}</h3>\n                                        <p>{currency.currency_name}</p>\n                                        <span className=\"currency-symbol\">{currency.currency_symbol}</span>\n                                    </div>\n                                    <div className=\"currency-status\">\n                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>\n                                            {currency.is_active ? 'Active' : 'Inactive'}\n                                        </span>\n                                    </div>\n                                </div>\n\n                                <div className=\"exchange-rate-section\">\n                                    <h4>Exchange Rate</h4>\n                                    {rate ? (\n                                        <div className=\"rate-info\">\n                                            <div className=\"current-rate\">\n                                                <span className=\"rate-value\">\n                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                </span>\n                                                <small>Updated: {formatDate(rate.updated_at)}</small>\n                                            </div>\n\n                                            {isEditing ? (\n                                                <div className=\"rate-edit-form\">\n                                                    <input\n                                                        type=\"number\"\n                                                        step=\"0.0001\"\n                                                        value={newRate}\n                                                        onChange={(e) => setNewRate(e.target.value)}\n                                                        placeholder=\"New rate\"\n                                                        className=\"rate-input\"\n                                                    />\n                                                    <textarea\n                                                        value={notes}\n                                                        onChange={(e) => setNotes(e.target.value)}\n                                                        placeholder=\"Update notes (optional)\"\n                                                        className=\"notes-input\"\n                                                        rows=\"2\"\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button \n                                                            onClick={() => handleUpdateRate(currency.id)}\n                                                            className=\"btn btn-success btn-sm\"\n                                                            disabled={loading}\n                                                        >\n                                                            Save\n                                                        </button>\n                                                        <button \n                                                            onClick={() => {\n                                                                setEditingRate(null);\n                                                                setNewRate('');\n                                                                setNotes('');\n                                                            }}\n                                                            className=\"btn btn-secondary btn-sm\"\n                                                        >\n                                                            Cancel\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <button \n                                                    onClick={() => {\n                                                        setEditingRate(currency.id);\n                                                        setNewRate(rate.rate_to_fancoin.toString());\n                                                    }}\n                                                    className=\"btn btn-outline btn-sm\"\n                                                >\n                                                    Update Rate\n                                                </button>\n                                            )}\n                                        </div>\n                                    ) : (\n                                        <div className=\"no-rate\">\n                                            <p>No exchange rate set</p>\n                                            <button \n                                                onClick={() => {\n                                                    setEditingRate(currency.id);\n                                                    setNewRate('');\n                                                }}\n                                                className=\"btn btn-primary btn-sm\"\n                                            >\n                                                Set Rate\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"currency-actions\">\n                                    <button \n                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}\n                                        disabled={loading}\n                                    >\n                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                    </button>\n                                </div>\n                            </div>\n                        );\n                    })}\n                </div>\n            )}\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC;IAC3CwC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACZ+C,cAAc,CAAC,CAAC;IAChBC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B3B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,8BAA8B,CAAC;MAC/E,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvB,MAAM6B,YAAY,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACpC,UAAU,IAAI,EAAE;QACxDC,aAAa,CAACoC,YAAY,CAAC;;QAE3B;QACA,MAAMzB,eAAe,GAAGyB,YAAY,CAACC,MAAM;QAC3C,MAAMzB,gBAAgB,GAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,SAAS,CAAC,CAACW,MAAM;QACrE,MAAMxB,kBAAkB,GAAGF,eAAe,GAAGC,gBAAgB;QAE7DF,QAAQ,CAAC;UACLC,eAAe;UACfC,gBAAgB;UAChBC,kBAAkB;UAClBC,WAAW,EAAE,IAAI0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACxC,CAAC,CAAC;MACN,CAAC,MAAM;QACHnC,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,2BAA2B,EAAEsC,GAAG,CAAC;MAC/CrC,QAAQ,CAAC,kEAAkE,CAAC;IAChF,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,kCAAkC,CAAC;MACnF,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBL,gBAAgB,CAAC+B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;IACvD;EACJ,CAAC;;EAED;EACA,MAAMG,gBAAgB,GAAGC,WAAW,CAAC,MAAOC,UAAU,IAAK;IACvD,IAAI,CAACrB,OAAO,IAAIsB,KAAK,CAACC,UAAU,CAACvB,OAAO,CAAC,CAAC,EAAE;MACxCwB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMlB,QAAQ,GAAG,MAAMmB,OAAO,CAAC,MAC3BC,eAAe,CAACC,kBAAkB,CAACN,UAAU,EAAEE,UAAU,CAACvB,OAAO,CAAC,EAAEE,KAAK,CAC7E,CAAC;MAED,IAAII,QAAQ,CAAC1B,OAAO,EAAE;QAClB,MAAMgD,QAAQ,CAAC,CAAC;QAChB,MAAMC,mBAAmB,CAAC,CAAC;QAC3BtC,cAAc,CAAC,IAAI,CAAC;QACpBU,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZqB,KAAK,CAAC,qCAAqC,CAAC;MAChD,CAAC,MAAM;QACHA,KAAK,CAAClB,QAAQ,CAACS,OAAO,IAAI,gCAAgC,CAAC;MAC/D;IACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACZ8C,KAAK,CAAC,gCAAgC,GAAG9C,KAAK,CAACqC,OAAO,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACf,OAAO,EAAEE,KAAK,EAAEuB,OAAO,EAAEG,QAAQ,EAAEC,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAMC,oBAAoB,GAAGV,WAAW,CAAC,OAAOC,UAAU,EAAEU,aAAa,KAAK;IAC1E,IAAI;MACA,MAAMzB,QAAQ,GAAG,MAAMmB,OAAO,CAAC,MAC3BC,eAAe,CAACM,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,WAAW,EAAEZ;MAAW,CAAC,CAC1E,CAAC;MAED,IAAIf,QAAQ,CAAC1B,OAAO,EAAE;QAClB,MAAMgD,QAAQ,CAAC,CAAC;QAChB,MAAMC,mBAAmB,CAAC,CAAC;QAC3BL,KAAK,CAAC,YAAYO,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;MAClF,CAAC,MAAM;QACHP,KAAK,CAAClB,QAAQ,CAACS,OAAO,IAAI,kCAAkC,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACZ8C,KAAK,CAAC,2BAA2B,GAAG9C,KAAK,CAACqC,OAAO,CAAC;IACtD;EACJ,CAAC,EAAE,CAACU,OAAO,EAAEG,QAAQ,EAAEC,mBAAmB,CAAC,CAAC;;EAE5C;EACA,MAAMK,iBAAiB,GAAGd,WAAW,CAAC,YAAY;IAC9C,IAAI,CAAC1B,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1F0B,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMlB,QAAQ,GAAG,MAAMmB,OAAO,CAAC,MAC3BC,eAAe,CAACM,gBAAgB,CAAC,QAAQ,EAAEtC,WAAW,CAC1D,CAAC;MAED,IAAIY,QAAQ,CAAC1B,OAAO,EAAE;QAClB,MAAMgD,QAAQ,CAAC,CAAC;QAChB,MAAMC,mBAAmB,CAAC,CAAC;QAC3BxC,kBAAkB,CAAC,KAAK,CAAC;QACzBM,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACFyB,KAAK,CAAC,8BAA8B,CAAC;MACzC,CAAC,MAAM;QACHA,KAAK,CAAClB,QAAQ,CAACS,OAAO,IAAI,wBAAwB,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACZ8C,KAAK,CAAC,yBAAyB,GAAG9C,KAAK,CAACqC,OAAO,CAAC;IACpD;EACJ,CAAC,EAAE,CAACrB,WAAW,EAAE+B,OAAO,EAAEG,QAAQ,EAAEC,mBAAmB,CAAC,CAAC;;EAEzD;EACA,MAAMM,eAAe,GAAGf,WAAW,CAAEC,UAAU,IAAK;IAChD,OAAO/C,aAAa,CAAC8D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,WAAW,KAAKZ,UAAU,CAAC;EACtE,CAAC,EAAE,CAAC/C,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMgE,UAAU,GAAGlB,WAAW,CAAEmB,UAAU,IAAK;IAC3C,OAAO,IAAI1B,IAAI,CAAC0B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAxF,SAAS,CAAC,MAAM;IACZuE,QAAQ,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACI5D,OAAA;IAAK8E,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChC/E,OAAA;MAAK8E,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB/E,OAAA;QAAA+E,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnF,OAAA;QAAA+E,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDnF,OAAA;QACI8E,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,IAAI,CAAE;QAAA0D,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAELzE,KAAK,iBACFV,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B/E,OAAA;QAAA+E,QAAA,GAAG,eAAG,EAACrE,KAAK,CAACqC,OAAO,IAAI,mBAAmB;MAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDnF,OAAA;QAAQoF,OAAO,EAAExB,QAAS;QAACkB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEA3E,OAAO,IAAIJ,UAAU,CAACsC,MAAM,KAAK,CAAC,gBAC/B1C,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B/E,OAAA;QAAK8E,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BnF,OAAA;QAAA+E,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,gBAENnF,OAAA;MAAK8E,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3B3E,UAAU,CAACiF,GAAG,CAAEC,QAAQ,IAAK;QAC1B,MAAMjB,IAAI,GAAGF,eAAe,CAACmB,QAAQ,CAACC,EAAE,CAAC;QACzC,MAAMC,SAAS,GAAGlE,WAAW,KAAKgE,QAAQ,CAACC,EAAE;QAE7C,oBACIvF,OAAA;UAAuB8E,SAAS,EAAE,iBAAiB,CAACQ,QAAQ,CAACvD,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;UAAAgD,QAAA,gBACvF/E,OAAA;YAAK8E,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B/E,OAAA;cAAK8E,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1B/E,OAAA;gBAAA+E,QAAA,EAAKO,QAAQ,CAAC1D;cAAa;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCnF,OAAA;gBAAA+E,QAAA,EAAIO,QAAQ,CAACzD;cAAa;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BnF,OAAA;gBAAM8E,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEO,QAAQ,CAACxD;cAAe;gBAAAkD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNnF,OAAA;cAAK8E,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B/E,OAAA;gBAAM8E,SAAS,EAAE,gBAAgBQ,QAAQ,CAACvD,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAAgD,QAAA,EACzEO,QAAQ,CAACvD,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClC/E,OAAA;cAAA+E,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBd,IAAI,gBACDrE,OAAA;cAAK8E,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB/E,OAAA;gBAAK8E,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB/E,OAAA;kBAAM8E,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,SAClB,EAACO,QAAQ,CAACxD,eAAe,EAAEuC,IAAI,CAACoB,eAAe;gBAAA;kBAAAT,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACPnF,OAAA;kBAAA+E,QAAA,GAAO,WAAS,EAACT,UAAU,CAACD,IAAI,CAACqB,UAAU,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAELK,SAAS,gBACNxF,OAAA;gBAAK8E,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3B/E,OAAA;kBACI2F,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAE7D,OAAQ;kBACf8D,QAAQ,EAAGC,CAAC,IAAK9D,UAAU,CAAC8D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5CI,WAAW,EAAC,UAAU;kBACtBnB,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFnF,OAAA;kBACI6F,KAAK,EAAE3D,KAAM;kBACb4D,QAAQ,EAAGC,CAAC,IAAK5D,QAAQ,CAAC4D,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1CI,WAAW,EAAC,yBAAyB;kBACrCnB,SAAS,EAAC,aAAa;kBACvBoB,IAAI,EAAC;gBAAG;kBAAAlB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFnF,OAAA;kBAAK8E,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB/E,OAAA;oBACIoF,OAAO,EAAEA,CAAA,KAAMjC,gBAAgB,CAACmC,QAAQ,CAACC,EAAE,CAAE;oBAC7CT,SAAS,EAAC,wBAAwB;oBAClCqB,QAAQ,EAAE3F,OAAQ;oBAAAuE,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnF,OAAA;oBACIoF,OAAO,EAAEA,CAAA,KAAM;sBACX7D,cAAc,CAAC,IAAI,CAAC;sBACpBU,UAAU,CAAC,EAAE,CAAC;sBACdE,QAAQ,CAAC,EAAE,CAAC;oBAChB,CAAE;oBACF2C,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENnF,OAAA;gBACIoF,OAAO,EAAEA,CAAA,KAAM;kBACX7D,cAAc,CAAC+D,QAAQ,CAACC,EAAE,CAAC;kBAC3BtD,UAAU,CAACoC,IAAI,CAACoB,eAAe,CAACW,QAAQ,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFtB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENnF,OAAA;cAAK8E,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACpB/E,OAAA;gBAAA+E,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3BnF,OAAA;gBACIoF,OAAO,EAAEA,CAAA,KAAM;kBACX7D,cAAc,CAAC+D,QAAQ,CAACC,EAAE,CAAC;kBAC3BtD,UAAU,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACF6C,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENnF,OAAA;YAAK8E,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7B/E,OAAA;cACIoF,OAAO,EAAEA,CAAA,KAAMtB,oBAAoB,CAACwB,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAACvD,SAAS,CAAE;cACrE+C,SAAS,EAAE,cAAcQ,QAAQ,CAACvD,SAAS,GAAG,aAAa,GAAG,aAAa,EAAG;cAC9EoE,QAAQ,EAAE3F,OAAQ;cAAAuE,QAAA,EAEjBO,QAAQ,CAACvD,SAAS,GAAG,YAAY,GAAG;YAAU;cAAAiD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlGAG,QAAQ,CAACC,EAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGhB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGA/D,eAAe,iBACZpB,OAAA;MAAK8E,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;MAAA0D,QAAA,eACpE/E,OAAA;QAAK8E,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGW,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;QAAAtB,QAAA,gBAC/D/E,OAAA;UAAA+E,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBnF,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/E,OAAA;YAAA+E,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CnF,OAAA;YACI2F,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACE,aAAc;YACjCkE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAEmE,CAAC,CAACC,MAAM,CAACH,KAAK,CAACS,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/E,OAAA;YAAA+E,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BnF,OAAA;YACI2F,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACG,aAAc;YACjCiE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAEkE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACjFI,WAAW,EAAC;UAAW;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/E,OAAA;YAAA+E,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BnF,OAAA;YACI2F,IAAI,EAAC,MAAM;YACXE,KAAK,EAAEnE,WAAW,CAACI,eAAgB;YACnCgE,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAEiE,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnFI,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB/E,OAAA;YAAA+E,QAAA,gBACI/E,OAAA;cACI2F,IAAI,EAAC,UAAU;cACfa,OAAO,EAAE9E,WAAW,CAACK,SAAU;cAC/B+D,QAAQ,EAAGC,CAAC,IAAKpE,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAEgE,CAAC,CAACC,MAAM,CAACQ;cAAO,CAAC;YAAE;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNnF,OAAA;UAAK8E,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B/E,OAAA;YACIoF,OAAO,EAAElB,iBAAkB;YAC3BY,SAAS,EAAC,iBAAiB;YAC3BqB,QAAQ,EAAE3F,OAAQ;YAAAuE,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnF,OAAA;YACIoF,OAAO,EAAEA,CAAA,KAAM/D,kBAAkB,CAAC,KAAK,CAAE;YACzCyD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAChF,EAAA,CAzXQD,kBAAkB;AAAAuG,EAAA,GAAlBvG,kBAAkB;AAyX1B;AAED,eAAeA,kBAAkB;AAAC,IAAAuG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}