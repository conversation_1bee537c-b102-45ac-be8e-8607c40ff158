import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaShieldAlt, FaKey, FaQrcode, FaCopy, FaCheck, FaSpinner, FaExclamationTriangle, FaDownload, FaTrash } from 'react-icons/fa';
import { Admin2FASetup, AdminAuthPreferences } from '../components/Admin';

const API_BASE_URL = '/backend';

const Admin2FASettings = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [adminId, setAdminId] = useState(null);
    const [adminData, setAdminData] = useState(null);
    const [authPreferences, setAuthPreferences] = useState(null);
    const [tfaSetup, setTfaSetup] = useState(null);
    const [backupCodes, setBackupCodes] = useState([]);
    const [showSetup, setShowSetup] = useState(false);
    const [globalSettings, setGlobalSettings] = useState({});

    useEffect(() => {
        // Get admin ID from localStorage
        const storedAdminId = localStorage.getItem('adminId');
        if (storedAdminId) {
            setAdminId(storedAdminId);
            fetchAdminData(storedAdminId);
        } else {
            setError('Admin session not found. Please log in again.');
            setLoading(false);
        }
    }, []);

    const fetchAdminData = async (id) => {
        try {
            setLoading(true);
            setError('');

            // Get admin auth preferences
            const response = await axios.get(`${API_BASE_URL}/handlers/admin_auth_preferences.php?adminId=${id}`);

            if (response.data.success) {
                setAdminData(response.data.admin);
                setAuthPreferences(response.data.auth_settings);
                setTfaSetup(response.data.two_factor_setup);
                setGlobalSettings(response.data.global_settings);

                // Get backup codes if 2FA is set up
                if (response.data.two_factor_setup && response.data.two_factor_setup.setup_completed) {
                    fetchBackupCodes(id);
                }
            } else {
                setError(response.data.message || 'Failed to load admin data');
            }
        } catch (err) {
            setError('Failed to load admin data');
            console.error('Error fetching admin data:', err);
        } finally {
            setLoading(false);
        }
    };

    const fetchBackupCodes = async (id) => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${id}`);
            if (response.data.success) {
                setBackupCodes(response.data.backup_codes || []);
            }
        } catch (err) {
            console.error('Error fetching backup codes:', err);
        }
    };

    const handleSetupComplete = (setupData) => {
        setShowSetup(false);
        setSuccess('2FA has been set up successfully!');
        
        // Refresh admin data
        fetchAdminData(adminId);
        
        setTimeout(() => setSuccess(''), 5000);
    };

    const handleDisable2FA = async () => {
        if (!window.confirm('Are you sure you want to disable 2FA? This will reduce your account security.')) {
            return;
        }

        const currentPassword = window.prompt('Please enter your current password to confirm:');
        if (!currentPassword) {
            return;
        }

        try {
            setLoading(true);
            const response = await axios.post(`${API_BASE_URL}/handlers/admin_disable_2fa.php`, {
                admin_id: adminId,
                current_password: currentPassword
            });

            if (response.data.success) {
                setSuccess('2FA has been disabled successfully');
                fetchAdminData(adminId);
            } else {
                setError(response.data.message || 'Failed to disable 2FA');
            }
        } catch (err) {
            setError('Failed to disable 2FA');
            console.error('Error disabling 2FA:', err);
        } finally {
            setLoading(false);
        }
    };

    const handleRegenerateBackupCodes = async () => {
        if (!window.confirm('Are you sure you want to regenerate backup codes? Your current codes will no longer work.')) {
            return;
        }

        try {
            setLoading(true);
            const response = await axios.post(`${API_BASE_URL}/handlers/admin_backup_codes.php?adminId=${adminId}`, {
                require_verification: false
            });

            if (response.data.success) {
                setBackupCodes(response.data.backup_codes);
                setSuccess('Backup codes regenerated successfully');
            } else {
                setError(response.data.message || 'Failed to regenerate backup codes');
            }
        } catch (err) {
            setError('Failed to regenerate backup codes');
            console.error('Error regenerating backup codes:', err);
        } finally {
            setLoading(false);
        }
    };

    const downloadBackupCodes = () => {
        const content = `FanBet247 Admin 2FA Backup Codes\nGenerated: ${new Date().toLocaleString()}\nAdmin: ${adminData?.username}\n\n${backupCodes.join('\n')}\n\nKeep these codes safe and secure. Each code can only be used once.`;
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `fanbet247-backup-codes-${adminData?.username}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    if (loading) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <FaSpinner className="animate-spin text-4xl text-blue-600 mx-auto mb-4" />
                    <p className="text-gray-600">Loading 2FA settings...</p>
                </div>
            </div>
        );
    }

    if (showSetup) {
        return (
            <Admin2FASetup
                adminId={adminId}
                username={adminData?.username}
                onSuccess={handleSetupComplete}
                onBack={() => setShowSetup(false)}
            />
        );
    }

    return (
        <div className="p-6">
            <div className="max-w-7xl mx-auto">
                {/* Page Header */}
                <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-800">Two-Factor Authentication</h1>
                    <p className="text-gray-600 mt-1">Secure your admin account with Google Authenticator</p>
                </div>

                {/* Admin Info Card */}
                <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
                    <div className="flex items-center gap-3 mb-4">
                        <FaShieldAlt className="text-green-500 text-xl" />
                        <div>
                            <h2 className="text-lg font-semibold text-gray-800">Admin Information</h2>
                            <p className="text-gray-600 text-sm">Current admin account details</p>
                        </div>
                    </div>

                    {/* Admin Info */}
                    <div className="bg-gray-50 rounded-lg p-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span className="text-gray-600">Admin:</span>
                                <span className="ml-2 font-medium">{adminData?.username}</span>
                            </div>
                            <div>
                                <span className="text-gray-600">Email:</span>
                                <span className="ml-2 font-medium">{adminData?.email}</span>
                            </div>
                            <div>
                                <span className="text-gray-600">Current Method:</span>
                                <span className="ml-2 font-medium capitalize">
                                    {authPreferences?.auth_method?.replace('_', ' ') || 'Password Only'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Error/Success Messages */}
                {error && (
                    <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
                        <FaExclamationTriangle className="text-red-500" />
                        <span className="text-red-700">{error}</span>
                    </div>
                )}

                {success && (
                    <div className="mb-6 bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
                        <FaCheck className="text-green-500" />
                        <span className="text-green-700">{success}</span>
                    </div>
                )}

                {/* Global 2FA Status */}
                {globalSettings.admin_2fa_enabled !== 'true' && (
                    <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-center gap-3">
                        <FaExclamationTriangle className="text-yellow-500" />
                        <div>
                            <p className="text-yellow-800 font-medium">2FA Not Enabled Globally</p>
                            <p className="text-yellow-700 text-sm mt-1">
                                2FA is not enabled globally. Contact your system administrator to enable 2FA.
                            </p>
                        </div>
                    </div>
                )}

                {/* Authentication Preferences */}
                <div className="mb-6">
                    <AdminAuthPreferences
                        adminId={adminId}
                        onUpdate={(preferences) => {
                            setAuthPreferences(preferences);
                            if (preferences.auth_method === '2fa' && !tfaSetup?.setup_completed) {
                                setShowSetup(true);
                            }
                        }}
                    />
                </div>

                {/* 2FA Setup Section */}
                {globalSettings.admin_2fa_enabled === 'true' && (
                    <div className="bg-white rounded-lg shadow-sm border p-6">
                        <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                            <FaKey className="text-blue-500" />
                            2FA Configuration
                        </h2>

                        {!tfaSetup?.setup_completed ? (
                            <div className="text-center py-8">
                                <FaQrcode className="text-6xl text-gray-300 mx-auto mb-4" />
                                <h3 className="text-lg font-medium text-gray-800 mb-2">2FA Not Set Up</h3>
                                <p className="text-gray-600 mb-6">
                                    Set up Google Authenticator to secure your account with 2FA
                                </p>
                                <button
                                    onClick={() => setShowSetup(true)}
                                    style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                        gap: '0.5rem',
                                        padding: '0.75rem 1.5rem',
                                        backgroundColor: '#3b82f6',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '0.5rem',
                                        fontSize: '0.875rem',
                                        fontWeight: '600',
                                        cursor: 'pointer',
                                        transition: 'all 0.2s ease',
                                        boxShadow: '0 2px 4px rgba(59, 130, 246, 0.3)',
                                        margin: '0 auto'
                                    }}
                                    onMouseEnter={(e) => {
                                        e.target.style.backgroundColor = '#2563eb';
                                        e.target.style.transform = 'translateY(-1px)';
                                        e.target.style.boxShadow = '0 4px 8px rgba(59, 130, 246, 0.4)';
                                    }}
                                    onMouseLeave={(e) => {
                                        e.target.style.backgroundColor = '#3b82f6';
                                        e.target.style.transform = 'translateY(0)';
                                        e.target.style.boxShadow = '0 2px 4px rgba(59, 130, 246, 0.3)';
                                    }}
                                >
                                    <FaShieldAlt />
                                    Set Up 2FA
                                </button>
                            </div>
                        ) : (
                            <div className="space-y-6">
                                {/* 2FA Status */}
                                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <div className="flex items-center gap-3">
                                        <FaCheck className="text-green-500" />
                                        <div>
                                            <p className="text-green-800 font-medium">2FA is Active</p>
                                            <p className="text-green-700 text-sm">
                                                Your account is protected with Google Authenticator
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                {/* Backup Codes */}
                                <div>
                                    <h3 className="text-md font-medium text-gray-800 mb-3">Backup Codes</h3>
                                    <p className="text-sm text-gray-600 mb-4">
                                        Use these codes if you lose access to your authenticator app. Each code can only be used once.
                                    </p>

                                    {backupCodes.length > 0 ? (
                                        <div className="bg-gray-50 rounded-lg p-4 mb-4">
                                            <div className="grid grid-cols-2 gap-2 font-mono text-sm mb-4">
                                                {backupCodes.map((code, index) => (
                                                    <div key={index} className="bg-white p-2 rounded border text-center">
                                                        {code}
                                                    </div>
                                                ))}
                                            </div>
                                            
                                            <div className="flex gap-3">
                                                <button
                                                    onClick={downloadBackupCodes}
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        gap: '0.5rem',
                                                        padding: '0.5rem 1rem',
                                                        backgroundColor: '#3b82f6',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '0.375rem',
                                                        fontSize: '0.875rem',
                                                        fontWeight: '500',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        boxShadow: '0 1px 3px rgba(59, 130, 246, 0.3)'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = '#2563eb';
                                                        e.target.style.transform = 'translateY(-1px)';
                                                        e.target.style.boxShadow = '0 2px 6px rgba(59, 130, 246, 0.4)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = '#3b82f6';
                                                        e.target.style.transform = 'translateY(0)';
                                                        e.target.style.boxShadow = '0 1px 3px rgba(59, 130, 246, 0.3)';
                                                    }}
                                                >
                                                    <FaDownload />
                                                    Download Codes
                                                </button>
                                                <button
                                                    onClick={handleRegenerateBackupCodes}
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        gap: '0.5rem',
                                                        padding: '0.5rem 1rem',
                                                        backgroundColor: '#f59e0b',
                                                        color: 'white',
                                                        border: 'none',
                                                        borderRadius: '0.375rem',
                                                        fontSize: '0.875rem',
                                                        fontWeight: '500',
                                                        cursor: 'pointer',
                                                        transition: 'all 0.2s ease',
                                                        boxShadow: '0 1px 3px rgba(245, 158, 11, 0.3)'
                                                    }}
                                                    onMouseEnter={(e) => {
                                                        e.target.style.backgroundColor = '#d97706';
                                                        e.target.style.transform = 'translateY(-1px)';
                                                        e.target.style.boxShadow = '0 2px 6px rgba(245, 158, 11, 0.4)';
                                                    }}
                                                    onMouseLeave={(e) => {
                                                        e.target.style.backgroundColor = '#f59e0b';
                                                        e.target.style.transform = 'translateY(0)';
                                                        e.target.style.boxShadow = '0 1px 3px rgba(245, 158, 11, 0.3)';
                                                    }}
                                                >
                                                    <FaKey />
                                                    Regenerate Codes
                                                </button>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                            <p className="text-yellow-800">No backup codes available</p>
                                            <button
                                                onClick={handleRegenerateBackupCodes}
                                                style={{
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    gap: '0.5rem',
                                                    padding: '0.5rem 1rem',
                                                    backgroundColor: '#f59e0b',
                                                    color: 'white',
                                                    border: 'none',
                                                    borderRadius: '0.375rem',
                                                    fontSize: '0.875rem',
                                                    fontWeight: '500',
                                                    cursor: 'pointer',
                                                    transition: 'all 0.2s ease',
                                                    boxShadow: '0 1px 3px rgba(245, 158, 11, 0.3)',
                                                    marginTop: '0.5rem'
                                                }}
                                                onMouseEnter={(e) => {
                                                    e.target.style.backgroundColor = '#d97706';
                                                    e.target.style.transform = 'translateY(-1px)';
                                                    e.target.style.boxShadow = '0 2px 6px rgba(245, 158, 11, 0.4)';
                                                }}
                                                onMouseLeave={(e) => {
                                                    e.target.style.backgroundColor = '#f59e0b';
                                                    e.target.style.transform = 'translateY(0)';
                                                    e.target.style.boxShadow = '0 1px 3px rgba(245, 158, 11, 0.3)';
                                                }}
                                            >
                                                Generate Backup Codes
                                            </button>
                                        </div>
                                    )}
                                </div>

                                {/* Disable 2FA */}
                                <div className="border-t pt-6">
                                    <h3 className="text-md font-medium text-gray-800 mb-3 text-red-600">Danger Zone</h3>
                                    <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <p className="text-red-800 text-sm mb-3">
                                            Disabling 2FA will reduce your account security. Only disable if absolutely necessary.
                                        </p>
                                        <button
                                            onClick={handleDisable2FA}
                                            style={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                gap: '0.5rem',
                                                padding: '0.5rem 1rem',
                                                backgroundColor: '#dc2626',
                                                color: 'white',
                                                border: 'none',
                                                borderRadius: '0.375rem',
                                                fontSize: '0.875rem',
                                                fontWeight: '500',
                                                cursor: 'pointer',
                                                transition: 'all 0.2s ease',
                                                boxShadow: '0 1px 3px rgba(220, 38, 38, 0.3)'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.target.style.backgroundColor = '#b91c1c';
                                                e.target.style.transform = 'translateY(-1px)';
                                                e.target.style.boxShadow = '0 2px 6px rgba(220, 38, 38, 0.4)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.target.style.backgroundColor = '#dc2626';
                                                e.target.style.transform = 'translateY(0)';
                                                e.target.style.boxShadow = '0 1px 3px rgba(220, 38, 38, 0.3)';
                                            }}
                                        >
                                            <FaTrash />
                                            Disable 2FA
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default Admin2FASettings;
