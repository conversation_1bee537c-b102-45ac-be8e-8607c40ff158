import React, { useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';
import { useNavigate, Link } from 'react-router-dom';
import UserAuthLayout from '../components/UserAuthLayout';
import '../styles/UserAuth.css';
import '../styles/AuthAnimations.css';

function UserLogin() {
    const [credentials, setCredentials] = useState({ usernameOrEmail: '', password: '' });
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [showOtpVerification, setShowOtpVerification] = useState(false);
    const [showGoogleAuthVerification, setShowGoogleAuthVerification] = useState(false);
    const [otpCode, setOtpCode] = useState('');
    const [googleAuthCode, setGoogleAuthCode] = useState('');
    const [userEmail, setUserEmail] = useState('');
    const [otpExpiry, setOtpExpiry] = useState(null);
    const [otpCountdown, setOtpCountdown] = useState(0);
    const [otpResending, setOtpResending] = useState(false);
    const navigate = useNavigate();

    // Redirect if already logged in
    useEffect(() => {
        const userId = localStorage.getItem('userId');
        if (userId) {
            navigate('/user/dashboard');
        }
    }, [navigate]);

    // OTP countdown timer
    useEffect(() => {
        let timer;
        if (otpCountdown > 0) {
            timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [otpCountdown]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setCredentials(prev => ({ ...prev, [name]: value }));
        if (error) setError('');
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post(
                'user_login.php',
                credentials
            );

            if (response.data.success) {
                // Check if 2FA is required
                if (response.data.requires2FA) {
                    // Store user email for 2FA verification
                    setUserEmail(response.data.email);

                    if (response.data.authType === 'email_otp') {
                        // Show OTP verification form
                        setShowOtpVerification(true);
                        setOtpExpiry(response.data.otpExpiry);
                        setOtpCountdown(response.data.otpExpiry);
                    } else if (response.data.authType === 'google_auth') {
                        // Show Google Authenticator verification form
                        setShowGoogleAuthVerification(true);
                    }
                } else {
                    // No 2FA required, proceed with login
                    localStorage.setItem('userId', response.data.userId);
                    localStorage.setItem('username', response.data.username);

                    // Get redirect path or default to dashboard
                    const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';
                    sessionStorage.removeItem('redirectAfterLogin');
                    navigate(redirectPath);
                }
            } else {
                setError(response.data.message || 'Login failed. Please try again.');
            }
        } catch (err) {
            console.error('Login error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else if (err.response?.status === 401) {
                setError('Invalid username/email or password');
            } else if (err.response?.status === 400) {
                setError('Please enter both username/email and password');
            } else {
                setError('An error occurred. Please try again later.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleOtpVerification = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post(
                'verify_otp.php',
                {
                    email: userEmail,
                    otp: otpCode
                }
            );

            if (response.data.success) {
                localStorage.setItem('userId', response.data.userId);
                localStorage.setItem('username', response.data.username);

                // Get redirect path or default to dashboard
                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';
                sessionStorage.removeItem('redirectAfterLogin');
                navigate(redirectPath);
            } else {
                setError(response.data.message || 'OTP verification failed. Please try again.');
            }
        } catch (err) {
            console.error('OTP verification error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred during OTP verification. Please try again.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleGoogleAuthVerification = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post(
                'verify_google_auth.php',
                {
                    email: userEmail,
                    code: googleAuthCode
                }
            );

            if (response.data.success) {
                localStorage.setItem('userId', response.data.userId);
                localStorage.setItem('username', response.data.username);

                // Get redirect path or default to dashboard
                const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/user/dashboard';
                sessionStorage.removeItem('redirectAfterLogin');
                navigate(redirectPath);
            } else {
                setError(response.data.message || 'Verification failed. Please try again.');
            }
        } catch (err) {
            console.error('Google Auth verification error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred during verification. Please try again.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleResendOtp = async () => {
        setError('');
        setOtpResending(true);

        try {
            const response = await axios.post(
                'send_otp.php',
                {
                    email: userEmail
                }
            );

            if (response.data.success) {
                setOtpExpiry(response.data.expiresIn);
                setOtpCountdown(response.data.expiresIn);
                setError(''); // Clear any previous errors
            } else {
                setError(response.data.message || 'Failed to resend OTP. Please try again.');
            }
        } catch (err) {
            console.error('Resend OTP error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred while resending OTP. Please try again.');
            }
        } finally {
            setOtpResending(false);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
    };

    // Render OTP verification form
    if (showOtpVerification) {
        return (
            <UserAuthLayout
                title="OTP Verification"
                subtitle="Enter the one-time password sent to your email"
            >
                {error && <div className="user-auth-error-message">{error}</div>}
                <form onSubmit={handleOtpVerification} className="user-auth-form">
                    <div className="user-auth-form-group">
                        <label htmlFor="otpCode">OTP Code</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                type="text"
                                id="otpCode"
                                name="otpCode"
                                value={otpCode}
                                onChange={(e) => setOtpCode(e.target.value)}
                                placeholder="Enter 6-digit OTP code"
                                required
                                autoComplete="one-time-code"
                                className="user-auth-otp-input"
                                maxLength="6"
                            />
                            <i className="fas fa-key"></i>
                        </div>
                    </div>
                    {otpCountdown > 0 && (
                        <div className="user-auth-countdown-timer">
                            OTP expires in: {formatTime(otpCountdown)}
                        </div>
                    )}
                    <button type="submit" className="user-auth-button" disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <span className="user-auth-loading-spinner"></span>
                                Verifying...
                            </>
                        ) : (
                            'Verify OTP'
                        )}
                    </button>
                    <div className="user-auth-form-options center">
                        <button
                            type="button"
                            className="user-auth-resend-button"
                            onClick={handleResendOtp}
                            disabled={otpResending || otpCountdown > 0}
                        >
                            {otpResending ? (
                                <>
                                    <span className="user-auth-loading-spinner"></span>
                                    Resending...
                                </>
                            ) : (
                                'Resend OTP'
                            )}
                        </button>
                    </div>
                    <div className="user-auth-footer">
                        <button
                            type="button"
                            className="user-auth-button-secondary"
                            onClick={() => {
                                setShowOtpVerification(false);
                                setOtpCode('');
                                setError('');
                            }}
                        >
                            Back to Login
                        </button>
                    </div>
                </form>
            </UserAuthLayout>
        );
    }

    // Render Google Authenticator verification form
    if (showGoogleAuthVerification) {
        return (
            <UserAuthLayout
                title="Two-Factor Authentication"
                subtitle="Enter the code from your Google Authenticator app"
            >
                {error && <div className="user-auth-error-message">{error}</div>}
                <form onSubmit={handleGoogleAuthVerification} className="user-auth-form">
                    <div className="user-auth-form-group">
                        <label htmlFor="googleAuthCode">Authentication Code</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                type="text"
                                id="googleAuthCode"
                                name="googleAuthCode"
                                value={googleAuthCode}
                                onChange={(e) => setGoogleAuthCode(e.target.value)}
                                placeholder="Enter 6-digit code"
                                required
                                autoComplete="one-time-code"
                                className="user-auth-otp-input"
                                maxLength="6"
                            />
                            <i className="fas fa-shield-alt"></i>
                        </div>
                    </div>
                    <div className="user-auth-form-group">
                        <div className="user-auth-countdown-timer">
                            Open your Google Authenticator app and enter the 6-digit code for FanBet247
                        </div>
                    </div>
                    <button type="submit" className="user-auth-button" disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <span className="user-auth-loading-spinner"></span>
                                Verifying...
                            </>
                        ) : (
                            'Verify Code'
                        )}
                    </button>
                    <div className="user-auth-footer">
                        <button
                            type="button"
                            className="user-auth-button-secondary"
                            onClick={() => {
                                setShowGoogleAuthVerification(false);
                                setGoogleAuthCode('');
                                setError('');
                            }}
                        >
                            Back to Login
                        </button>
                    </div>
                </form>
            </UserAuthLayout>
        );
    }

    // Render main login form
    return (
        <UserAuthLayout
            title="Welcome Back"
            subtitle="Sign in to your account"
            variant="login"
        >
            {error && <div className="user-auth-error-message">{error}</div>}
            <form onSubmit={handleSubmit} className="user-auth-form">
                <div className="user-auth-form-group">
                    <label htmlFor="usernameOrEmail">Email or Username</label>
                    <div className="user-auth-input-wrapper">
                        <input
                            type="text"
                            id="usernameOrEmail"
                            name="usernameOrEmail"
                            value={credentials.usernameOrEmail}
                            onChange={handleInputChange}
                            placeholder="Enter your email or username"
                            required
                        />
                        <i className="fas fa-user"></i>
                    </div>
                </div>
                <div className="user-auth-form-group">
                    <label htmlFor="password">Password</label>
                    <div className="user-auth-input-wrapper">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            value={credentials.password}
                            onChange={handleInputChange}
                            placeholder="Enter your password"
                            required
                        />
                        <i className="fas fa-lock"></i>
                    </div>
                </div>
                <div className="user-auth-form-options end">
                    <Link to="/forgot-password" className="user-auth-forgot-password">
                        Forgot Password?
                    </Link>
                </div>
                <button type="submit" className="user-auth-button" disabled={isLoading}>
                    {isLoading ? (
                        <>
                            <span className="user-auth-loading-spinner"></span>
                            Signing in...
                        </>
                    ) : (
                        'Sign In'
                    )}
                </button>
                <div className="user-auth-footer">
                    <p>Don't have an account? <Link to="/register" className="user-auth-register-link">Create one here</Link></p>
                </div>
            </form>
        </UserAuthLayout>
    );
}

export default UserLogin;