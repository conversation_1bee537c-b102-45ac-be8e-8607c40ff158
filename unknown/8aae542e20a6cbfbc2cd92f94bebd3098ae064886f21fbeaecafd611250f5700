<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // Check if user exists by email or username
    $email = '<EMAIL>';
    $username = 'demohomexx';
    
    $query = "SELECT user_id, username, email, password, status FROM users WHERE email = :email OR username = :username";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':email', $email);
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        // Don't return the actual password, just check if it exists
        $response = [
            'success' => true,
            'user_found' => true,
            'user_id' => $user['user_id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'status' => $user['status'],
            'password_exists' => !empty($user['password']),
            'password_length' => strlen($user['password']),
            'password_hash_check' => password_verify('loving12', $user['password']) ? 'MATCH' : 'NO_MATCH'
        ];
    } else {
        $response = [
            'success' => true,
            'user_found' => false,
            'message' => 'User not found with email or username'
        ];
    }
    
    echo json_encode($response);

} catch(PDOException $e) {
    echo json_encode([
        'success' => false,
        'message' => "Database error: " . $e->getMessage()
    ]);
}
?>
