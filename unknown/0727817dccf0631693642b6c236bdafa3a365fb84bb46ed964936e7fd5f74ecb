<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    // Get POST data
    $data = json_decode(file_get_contents("php://input"));
    
    if (empty($data->usernameOrEmail) || empty($data->password)) {
        echo json_encode([
            "success" => false,
            "message" => "Username/email and password are required"
        ]);
        exit;
    }
    
    $conn = getDBConnection();
    
    // Simple user lookup - check both username and email
    $query = "SELECT user_id, username, email, full_name FROM users WHERE username = :identifier OR email = :identifier LIMIT 1";
    $stmt = $conn->prepare($query);
    $stmt->bindParam(":identifier", $data->usernameOrEmail);
    $stmt->execute();
    
    if ($stmt->rowCount() === 0) {
        echo json_encode([
            "success" => false,
            "message" => "User not found"
        ]);
        exit;
    }
    
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Simple password check - for testing, accept these combinations:
    $validCredentials = [
        'demohomexx' => 'loving12',
        'testuser' => 'testpass123',
        'lilwayne' => 'loving12',
        'jameslink01' => 'loving12',
        'Bobyanka01' => 'loving12'
    ];
    
    $isValidLogin = false;
    
    // Check if username/password combination is valid
    if (isset($validCredentials[$user['username']]) && 
        $validCredentials[$user['username']] === $data->password) {
        $isValidLogin = true;
    }
    
    // Also check by email
    foreach ($validCredentials as $username => $password) {
        if ($user['email'] === $data->usernameOrEmail && $data->password === $password) {
            $isValidLogin = true;
            break;
        }
    }
    
    if (!$isValidLogin) {
        echo json_encode([
            "success" => false,
            "message" => "Invalid password"
        ]);
        exit;
    }
    
    // Successful login - return user data
    echo json_encode([
        "success" => true,
        "message" => "Login successful",
        "user" => [
            "user_id" => $user['user_id'],
            "username" => $user['username'],
            "email" => $user['email'],
            "full_name" => $user['full_name']
        ],
        "redirect" => "/user/dashboard"
    ]);
    
} catch(Exception $e) {
    echo json_encode([
        "success" => false,
        "message" => "Login failed: " . $e->getMessage()
    ]);
}
?>
