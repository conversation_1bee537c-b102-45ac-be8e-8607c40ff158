<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");

// Test common passwords against the existing hash
$existingHash = '$2y$10$Tq.WGMvqcPthx/WepKxfpuw2D4mbrEXK0JF4jkLss8zJAms6PDMEC';

$commonPasswords = [
    'loving12',
    'password',
    'admin',
    'demohomexx',
    '123456',
    'password123',
    'admin123',
    'test',
    'test123',
    'fanbet247',
    'demo',
    'demo123',
    'user',
    'user123',
    '12345678',
    'qwerty',
    'abc123'
];

$results = [];
$foundPassword = null;

foreach ($commonPasswords as $password) {
    $isMatch = password_verify($password, $existingHash);
    $results[] = [
        'password' => $password,
        'matches' => $isMatch
    ];
    
    if ($isMatch) {
        $foundPassword = $password;
        break;
    }
}

echo json_encode([
    'success' => true,
    'existing_hash' => $existingHash,
    'found_password' => $foundPassword,
    'test_results' => $results,
    'message' => $foundPassword ? "Found matching password: $foundPassword" : "No matching password found in common list"
]);
?>
