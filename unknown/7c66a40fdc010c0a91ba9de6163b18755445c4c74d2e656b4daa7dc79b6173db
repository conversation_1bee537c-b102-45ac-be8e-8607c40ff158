-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.1.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jun 12, 2025 at 02:07 PM
-- Server version: 5.7.24
-- PHP Version: 8.3.1

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `fanbet247`
--

-- --------------------------------------------------------

--
-- Table structure for table `achievements`
--

CREATE TABLE `achievements` (
  `achievement_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `icon` varchar(50) DEFAULT NULL,
  `points_required` int(11) DEFAULT '0',
  `streak_required` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `achievements`
--

INSERT INTO `achievements` (`achievement_id`, `name`, `description`, `icon`, `points_required`, `streak_required`, `created_at`) VALUES
(1, 'Rookie Bettor', 'Earn your first 10 points', '', 10, 0, '2024-12-15 15:07:53'),
(2, 'Rising Star', 'Accumulate 50 points', '', 50, 0, '2024-12-15 15:07:53'),
(3, 'Betting Pro', 'Reach 100 points', '', 100, 0, '2024-12-15 15:07:53'),
(4, 'Master Predictor', 'Achieve 500 points', '', 500, 0, '2024-12-15 15:07:53'),
(5, 'Hot Streak', 'Win 5 bets in a row', '', 0, 5, '2024-12-15 15:07:53'),
(6, 'Unstoppable', 'Win 10 bets in a row', '', 0, 10, '2024-12-15 15:07:53'),
(7, 'Perfect Season', 'Finish a season at rank #1', '', 0, 0, '2024-12-15 15:07:53');

-- --------------------------------------------------------

--
-- Table structure for table `adminactions`
--

CREATE TABLE `adminactions` (
  `action_id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `action_type` enum('challenge_created','challenge_settled','user_banned','points_awarded','points_deducted','balance_adjusted') DEFAULT NULL,
  `details` text,
  `action_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `adminactions`
--

INSERT INTO `adminactions` (`action_id`, `admin_id`, `action_type`, `details`, `action_date`) VALUES
(1, 1, 'points_awarded', 'Credited user ID: 3 with amount: 500 FC', '2024-10-29 21:17:02'),
(2, 1, 'points_awarded', 'Credited user ID: 3 with amount: 50000 FC', '2024-10-30 10:57:44'),
(3, 1, 'points_awarded', 'Credited user ID: 6 with amount: 5000 FC', '2024-10-30 11:51:40'),
(4, 1, 'points_awarded', 'Credited user ID: 4 with amount: 16398 FC', '2024-11-03 12:12:15'),
(5, 1, 'points_awarded', 'Credited user ID: 2 with amount: 50000999 FC', '2025-02-08 15:07:56'),
(6, 1, 'points_awarded', 'Credited user ID: 9 with amount: 10000 FC', '2025-05-17 11:42:43');

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `admin_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('super_admin','moderator','support') NOT NULL,
  `last_login` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `auth_method` enum('password_only','otp','2fa') NOT NULL DEFAULT 'password_only',
  `last_2fa_setup` timestamp NULL DEFAULT NULL,
  `account_locked_until` datetime DEFAULT NULL,
  `failed_login_attempts` int(11) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`admin_id`, `username`, `password_hash`, `email`, `role`, `last_login`, `created_at`, `updated_at`, `two_factor_enabled`, `auth_method`, `last_2fa_setup`, `account_locked_until`, `failed_login_attempts`) VALUES
(1, 'superadmin', '$2y$10$a0gOq9SFukkVOQBrw.3peuneANGcw2OXxXgxq1ikK77ppuKiaHx4y', '<EMAIL>', 'super_admin', '2025-06-09 07:51:26', '2024-09-06 08:47:14', '2025-06-09 07:51:26', 1, '2fa', '2025-06-06 19:18:49', NULL, 0),
(2, 'jamesbong', '$2y$10$J4gUr26QS.gnQIzzK2Hr7.tBIdy4v3/qBRODgylocvIq3.r4ZL4cu', '<EMAIL>', 'super_admin', '2025-06-04 22:32:52', '2024-09-06 20:07:14', '2025-06-04 22:32:52', 0, 'password_only', NULL, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `admin_2fa`
--

CREATE TABLE `admin_2fa` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `secret_key` varchar(255) DEFAULT NULL,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `auth_type` enum('email_otp','google_auth') NOT NULL DEFAULT 'email_otp',
  `backup_codes` text,
  `setup_completed` tinyint(1) NOT NULL DEFAULT '0',
  `last_used` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admin_2fa`
--

INSERT INTO `admin_2fa` (`id`, `admin_id`, `secret_key`, `is_enabled`, `auth_type`, `backup_codes`, `setup_completed`, `last_used`, `created_at`, `updated_at`) VALUES
(1, 1, 'J7YM7KZMGWLU2NHR', 1, 'google_auth', '[\"41E7D3EA\",\"E7FC99BE\",\"3AD5746A\",\"030443B7\",\"7D7BC835\",\"133B9050\",\"F5F7049C\",\"80F0F36B\",\"CC7BF5B8\",\"490A0EE9\"]', 1, '2025-06-09 07:51:26', '2025-06-06 18:10:32', '2025-06-09 07:51:26');

-- --------------------------------------------------------

--
-- Table structure for table `admin_auth_logs`
--

CREATE TABLE `admin_auth_logs` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `auth_type` enum('password','otp','2fa','backup_code','recovery','config','security','test','system') NOT NULL,
  `action` varchar(50) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `details` text,
  `log_level` enum('INFO','WARNING','ERROR','CRITICAL') NOT NULL DEFAULT 'INFO',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admin_auth_logs`
--

INSERT INTO `admin_auth_logs` (`id`, `admin_id`, `auth_type`, `action`, `ip_address`, `user_agent`, `details`, `log_level`, `created_at`) VALUES
(1, 1, 'security', 'incident', '::1', 'unknown', '{\"incident_type\":\"unauthorized_otp_request\",\"description\":\"OTP request when OTP is disabled\",\"timestamp\":\"2025-06-06 18:09:47\",\"ip_address\":\"::1\",\"user_agent\":\"unknown\"}', 'WARNING', '2025-06-06 18:09:47'),
(2, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:10:11\",\"expires_at\":\"2025-06-06 18:15:08\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:10:11'),
(3, 1, '2fa', '2fa_setup', '::1', 'unknown', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 18:10:32'),
(4, 1, 'password', 'preferences_updated', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"new_auth_method\":\"otp\",\"two_factor_enabled\":false}', 'INFO', '2025-06-06 18:16:37'),
(5, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 18:19:17'),
(6, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:19:19\",\"expires_at\":\"2025-06-06 18:24:17\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:19:19'),
(7, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:19:22\",\"expires_at\":\"2025-06-06 18:24:17\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:19:22'),
(8, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:20:00'),
(9, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:20:08'),
(10, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:20:18'),
(11, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:25:16\",\"expires_at\":\"2025-06-06 18:30:09\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:25:16'),
(12, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:26:02'),
(13, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:28:08\",\"expires_at\":\"2025-06-06 18:33:06\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:28:08'),
(14, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:28:08'),
(15, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:28:08'),
(16, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:29:27\",\"expires_at\":\"2025-06-06 18:34:25\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:29:27'),
(17, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:29:27'),
(18, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:29:27'),
(19, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:29:27'),
(20, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:29:42'),
(21, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:29:56'),
(22, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:30:02\",\"expires_at\":\"2025-06-06 18:34:59\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:30:02'),
(23, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"invalid_otp\",\"attempts\":1,\"max_attempts\":\"3\"}', 'INFO', '2025-06-06 18:30:14'),
(24, 1, 'otp', 'otp_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:31:32'),
(25, 1, 'otp', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:31:32'),
(26, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 18:36:31'),
(27, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:36:34\",\"expires_at\":\"2025-06-06 18:41:31\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:36:34'),
(28, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:36:36\",\"expires_at\":\"2025-06-06 18:41:32\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:36:36'),
(29, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"invalid_otp\",\"attempts\":1,\"max_attempts\":\"3\"}', 'INFO', '2025-06-06 18:36:50'),
(30, 1, 'otp', 'otp_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:37:02'),
(31, 1, 'otp', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:37:02'),
(32, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 18:37:58'),
(33, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:42:06\",\"expires_at\":\"2025-06-06 18:47:04\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:42:06'),
(34, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:42:06'),
(35, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:42:06'),
(36, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:42:06'),
(37, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:48:45\",\"expires_at\":\"2025-06-06 18:53:42\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:48:45'),
(38, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:48:45'),
(39, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:48:45'),
(40, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:48:45'),
(41, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 18:49:32'),
(42, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:49:34\",\"expires_at\":\"2025-06-06 18:54:32\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:49:34'),
(43, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:49:37\",\"expires_at\":\"2025-06-06 18:54:32\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:49:37'),
(44, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 18:53:00'),
(45, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:53:16\",\"expires_at\":\"2025-06-06 18:58:14\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:53:16'),
(46, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:53:16'),
(47, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:53:16'),
(48, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:53:16'),
(49, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:55:39'),
(50, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:56:57\",\"expires_at\":\"2025-06-06 19:01:55\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:56:57'),
(51, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:56:57'),
(52, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:56:57'),
(53, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:56:57'),
(54, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:57:33\",\"expires_at\":\"2025-06-06 19:02:31\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:57:33'),
(55, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 18:57:35\",\"expires_at\":\"2025-06-06 19:02:31\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:57:35'),
(56, 1, 'otp', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"invalid_otp\",\"attempts\":1,\"max_attempts\":\"3\"}', 'INFO', '2025-06-06 18:57:45'),
(57, 1, 'otp', 'otp_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:58:08'),
(58, 1, 'otp', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:58:08'),
(59, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 18:59:11\",\"expires_at\":\"2025-06-06 19:04:08\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 18:59:11'),
(60, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 18:59:11'),
(61, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 18:59:11'),
(62, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 18:59:11'),
(63, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 19:00:25'),
(64, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 19:00:27\",\"expires_at\":\"2025-06-06 19:05:25\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:00:27'),
(65, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 19:00:29\",\"expires_at\":\"2025-06-06 19:05:25\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:00:29'),
(66, 1, 'otp', 'otp_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"login_successful\":true}', 'INFO', '2025-06-06 19:00:50'),
(67, 1, 'otp', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 19:00:50'),
(68, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 19:03:52\",\"expires_at\":\"2025-06-06 19:08:49\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:03:52'),
(69, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 19:03:52'),
(70, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 19:03:52'),
(71, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 19:03:52'),
(72, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"otp\"}', 'INFO', '2025-06-06 19:05:52'),
(73, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 19:05:54\",\"expires_at\":\"2025-06-06 19:10:52\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:05:54'),
(74, 1, 'otp', 'otp_sent', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"success\":true,\"timestamp\":\"2025-06-06 19:05:57\",\"expires_at\":\"2025-06-06 19:10:52\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:05:57'),
(75, 1, 'otp', 'otp_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"login_successful\":true}', 'INFO', '2025-06-06 19:08:19'),
(76, 1, 'otp', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 19:08:19'),
(77, 1, 'otp', 'otp_sent', '::1', 'unknown', '{\"success\":true,\"timestamp\":\"2025-06-06 19:08:47\",\"expires_at\":\"2025-06-06 19:13:45\",\"email\":\"<EMAIL>\"}', 'INFO', '2025-06-06 19:08:47'),
(78, 1, 'otp', 'otp_verified', '::1', 'unknown', '{\"login_successful\":true}', 'INFO', '2025-06-06 19:08:47'),
(79, 1, 'otp', 'login_success', '::1', 'unknown', '{\"auth_method\":\"otp\"}', 'INFO', '2025-06-06 19:08:47'),
(80, 1, 'otp', 'login_failed', '::1', 'unknown', '{\"reason\":\"otp_expired_or_not_found\"}', 'INFO', '2025-06-06 19:08:47'),
(81, 1, 'password', 'preferences_updated', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"new_auth_method\":\"2fa\",\"two_factor_enabled\":true}', 'INFO', '2025-06-06 19:09:04'),
(82, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:09:04'),
(83, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:09:04'),
(84, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_completed\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:11:14'),
(85, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:13:07'),
(86, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:13:28'),
(87, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:13:38'),
(88, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:13:39'),
(89, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:13:44'),
(90, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:14:08'),
(91, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:14:49'),
(92, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:14:49'),
(93, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:07'),
(94, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:07'),
(95, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:22'),
(96, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:22'),
(97, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:31'),
(98, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:31'),
(99, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:37'),
(100, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:37'),
(101, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:56'),
(102, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:15:56'),
(103, 1, '2fa', 'login_failed', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"reason\":\"invalid_2fa_setup_code\"}', 'INFO', '2025-06-06 19:18:15'),
(104, 1, 'password', 'preferences_updated', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"new_auth_method\":\"2fa\",\"two_factor_enabled\":1}', 'INFO', '2025-06-06 19:18:27'),
(105, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:18:27'),
(106, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_initiated\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:18:27'),
(107, 1, '2fa', '2fa_setup', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"setup_completed\":true,\"auth_type\":\"google_auth\"}', 'INFO', '2025-06-06 19:18:49'),
(108, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"2fa\"}', 'INFO', '2025-06-06 19:19:28'),
(109, 1, '2fa', '2fa_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"is_backup_code\":false,\"used_backup_code\":null}', 'INFO', '2025-06-06 19:20:55'),
(110, 1, '2fa', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"2fa\",\"used_backup_code\":false}', 'INFO', '2025-06-06 19:20:55'),
(111, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"2fa\"}', 'INFO', '2025-06-06 19:26:45'),
(112, 1, '2fa', '2fa_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"is_backup_code\":false,\"used_backup_code\":null}', 'INFO', '2025-06-06 19:53:59'),
(113, 1, '2fa', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"2fa\",\"used_backup_code\":false}', 'INFO', '2025-06-06 19:53:59'),
(114, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"2fa\"}', 'INFO', '2025-06-06 20:03:08'),
(115, 1, '2fa', '2fa_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"is_backup_code\":false,\"used_backup_code\":null}', 'INFO', '2025-06-06 20:03:29'),
(116, 1, '2fa', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"2fa\",\"used_backup_code\":false}', 'INFO', '2025-06-06 20:03:29'),
(117, 1, 'password', 'login_attempt', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"password_verified\":true,\"next_step\":\"2fa\"}', 'INFO', '2025-06-09 07:50:27'),
(118, 1, '2fa', '2fa_verified', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"is_backup_code\":false,\"used_backup_code\":null}', 'INFO', '2025-06-09 07:51:26'),
(119, 1, '2fa', 'login_success', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '{\"auth_method\":\"2fa\",\"used_backup_code\":false}', 'INFO', '2025-06-09 07:51:26');

-- --------------------------------------------------------

--
-- Table structure for table `admin_auth_settings`
--

CREATE TABLE `admin_auth_settings` (
  `id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admin_auth_settings`
--

INSERT INTO `admin_auth_settings` (`id`, `setting_name`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'admin_auth_method', 'password_only', 'Admin authentication method: password_only, otp, 2fa', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(2, 'admin_otp_enabled', 'true', 'Enable OTP authentication for admins', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(3, 'admin_2fa_enabled', 'true', 'Enable 2FA authentication for admins', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(4, 'admin_otp_expiry_time', '300', 'Admin OTP expiry time in seconds (default: 5 minutes)', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(5, 'admin_max_otp_attempts', '3', 'Maximum OTP verification attempts before lockout', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(6, 'admin_max_login_attempts', '5', 'Maximum login attempts before account lockout', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(7, 'admin_lockout_time', '1800', 'Admin account lockout time in seconds (default: 30 minutes)', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(8, 'admin_require_2fa_for', 'login,password_change', 'Actions requiring 2FA verification (comma-separated)', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(9, 'admin_backup_codes_count', '10', 'Number of backup codes to generate for 2FA', '2025-06-06 17:49:40', '2025-06-06 19:08:55'),
(10, 'admin_session_timeout', '3600', 'Admin session timeout in seconds (default: 1 hour)', '2025-06-06 17:49:40', '2025-06-06 19:08:55');

-- --------------------------------------------------------

--
-- Stand-in structure for view `admin_auth_status`
-- (See below for the actual view)
--
CREATE TABLE `admin_auth_status` (
`admin_id` int(11)
,`username` varchar(50)
,`email` varchar(100)
,`role` enum('super_admin','moderator','support')
,`auth_method` enum('password_only','otp','2fa')
,`two_factor_enabled` tinyint(1)
,`account_locked_until` datetime
,`failed_login_attempts` int(11)
,`has_2fa_setup` tinyint(1)
,`preferred_2fa_type` enum('email_otp','google_auth')
,`is_2fa_setup_complete` tinyint(1)
,`security_status` varchar(14)
);

-- --------------------------------------------------------

--
-- Table structure for table `admin_login_attempts`
--

CREATE TABLE `admin_login_attempts` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `attempt_type` enum('password','otp','2fa','login','critical_incident') NOT NULL,
  `attempts` int(11) NOT NULL DEFAULT '1',
  `locked_until` datetime DEFAULT NULL,
  `last_attempt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `admin_otp`
--

CREATE TABLE `admin_otp` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `otp` varchar(10) NOT NULL,
  `expires_at` datetime NOT NULL,
  `attempts` int(11) NOT NULL DEFAULT '0',
  `locked_until` datetime DEFAULT NULL,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `admin_otp`
--

INSERT INTO `admin_otp` (`id`, `admin_id`, `otp`, `expires_at`, `attempts`, `locked_until`, `used`, `created_at`) VALUES
(24, 1, '896295', '2025-06-06 19:13:45', 0, NULL, 1, '2025-06-06 19:08:45');

-- --------------------------------------------------------

--
-- Table structure for table `admin_recovery_codes`
--

CREATE TABLE `admin_recovery_codes` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `recovery_code` varchar(20) NOT NULL,
  `expires_at` datetime NOT NULL,
  `used` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `admin_recovery_tokens`
--

CREATE TABLE `admin_recovery_tokens` (
  `id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `access_token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `bets`
--

CREATE TABLE `bets` (
  `bet_id` int(11) NOT NULL,
  `challenge_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `outcome` enum('team_a_win','team_b_win','draw','pending') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `user1_id` int(11) DEFAULT NULL,
  `user2_id` int(11) DEFAULT NULL,
  `team1_id` int(11) DEFAULT NULL,
  `team2_id` int(11) DEFAULT NULL,
  `amount_user1` decimal(10,2) DEFAULT NULL,
  `amount_user2` decimal(10,2) DEFAULT NULL,
  `bet_choice_user1` varchar(50) DEFAULT NULL,
  `bet_choice_user2` varchar(50) DEFAULT NULL,
  `odds_user1` decimal(5,2) DEFAULT NULL,
  `odds_user2` decimal(5,2) DEFAULT NULL,
  `bet_status` enum('open','joined','completed') DEFAULT 'open',
  `potential_return_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_user2` decimal(10,2) DEFAULT NULL,
  `unique_code` varchar(255) DEFAULT NULL,
  `potential_return_win_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_draw_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_loss_user1` decimal(10,2) DEFAULT NULL,
  `potential_return_win_user2` decimal(10,2) DEFAULT NULL,
  `potential_return_draw_user2` decimal(10,2) DEFAULT NULL,
  `potential_return_loss_user2` decimal(10,2) DEFAULT NULL,
  `user1_outcome` enum('win','loss','pending') DEFAULT 'pending',
  `user2_outcome` enum('win','loss','pending') DEFAULT 'pending',
  `final_winnings_user1` decimal(10,2) DEFAULT NULL,
  `final_winnings_user2` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `bets`
--

INSERT INTO `bets` (`bet_id`, `challenge_id`, `amount`, `outcome`, `created_at`, `user1_id`, `user2_id`, `team1_id`, `team2_id`, `amount_user1`, `amount_user2`, `bet_choice_user1`, `bet_choice_user2`, `odds_user1`, `odds_user2`, `bet_status`, `potential_return_user1`, `potential_return_user2`, `unique_code`, `potential_return_win_user1`, `potential_return_draw_user1`, `potential_return_loss_user1`, `potential_return_win_user2`, `potential_return_draw_user2`, `potential_return_loss_user2`, `user1_outcome`, `user2_outcome`, `final_winnings_user1`, `final_winnings_user2`) VALUES
(73, 8, NULL, NULL, '2025-01-26 16:03:53', 2, 1, NULL, NULL, '5000.00', '5000.00', 'team_a_win', 'team_b_win', '1.00', '1.00', 'completed', '1000.00', '5000.00', '739a5c56', '5000.00', '4000.00', '1000.00', '5000.00', '4000.00', '1000.00', 'pending', 'pending', NULL, NULL),
(75, 9, NULL, NULL, '2025-01-27 13:37:02', 2, 1, NULL, NULL, '7500.00', '7500.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '1500.00', '13500.00', '75f2dc7d', '13500.00', '6000.00', '1500.00', '13500.00', '6000.00', '1500.00', 'pending', 'pending', NULL, NULL),
(76, 11, NULL, NULL, '2025-01-27 13:39:18', 2, 1, NULL, NULL, '8500.00', '8500.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '15300.00', '1700.00', '766b41aa', '15300.00', '6800.00', '1700.00', '15300.00', '6800.00', '1700.00', 'pending', 'pending', NULL, NULL),
(77, 12, NULL, NULL, '2025-01-27 14:42:51', 1, 2, NULL, NULL, '8500.00', '8500.00', 'team_a_win', 'team_b_win', '1.00', '1.00', 'completed', '8500.00', '1700.00', '77b62ad4', '8500.00', '6800.00', '1700.00', '8500.00', '6800.00', '1700.00', 'pending', 'pending', NULL, NULL),
(78, 14, NULL, NULL, '2025-01-27 19:44:52', 2, NULL, NULL, NULL, '7800.00', NULL, 'team_b_win', NULL, '1.80', NULL, 'open', '14040.00', NULL, '78496d2e', '14040.00', '6240.00', '1560.00', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(79, 15, NULL, 'team_a_win', '2025-01-27 21:35:35', 2, 1, NULL, NULL, '8700.00', '8700.00', 'team_b_win', 'team_a_win', '1.80', '1.80', 'completed', '15660.00', '15660.00', '7975dc74', '15660.00', '6960.00', '1740.00', '15660.00', '6960.00', '1740.00', 'pending', 'pending', NULL, NULL),
(80, 16, NULL, NULL, '2025-01-30 13:35:20', 2, NULL, NULL, NULL, '5000.00', NULL, 'team_a_win', NULL, '1.80', NULL, 'open', '9000.00', NULL, '80935a5b', '9000.00', '4000.00', '1000.00', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(81, 16, NULL, 'team_b_win', '2025-02-01 03:06:20', 2, 8, NULL, NULL, '5000.00', '5000.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '9000.00', '9000.00', '81c65008', '9000.00', '4000.00', '1000.00', '9000.00', '4000.00', '1000.00', 'loss', 'win', '1000.00', '9000.00'),
(82, 15, NULL, NULL, '2025-02-01 03:13:33', 2, NULL, NULL, NULL, '5553.00', NULL, 'team_a_win', NULL, '1.80', NULL, 'open', '9995.40', NULL, '82d4f44e', '9995.40', '4442.40', '1110.60', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(83, 17, NULL, NULL, '2025-02-01 03:20:46', 2, NULL, NULL, NULL, '666.00', NULL, 'team_a_win', NULL, '1.00', NULL, 'open', '666.00', NULL, '83e0ae88', '666.00', '532.80', '133.20', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(84, 17, NULL, NULL, '2025-02-01 03:22:36', 8, NULL, NULL, NULL, '8975.00', NULL, 'team_b_win', NULL, '1.00', NULL, 'open', '8975.00', NULL, '84c39648', '8975.00', '7180.00', '1795.00', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(85, 15, NULL, 'team_a_win', '2025-02-01 03:37:48', 8, 2, NULL, NULL, '4998.00', '4998.00', 'draw', 'team_a_win', '0.80', '0.80', 'completed', '3998.40', '3998.40', '85cb3cf5', '3998.40', '3998.40', '999.60', '3998.40', '3998.40', '999.60', 'pending', 'pending', NULL, NULL),
(86, 23, NULL, 'team_b_win', '2025-02-09 04:15:00', 2, 8, NULL, NULL, '5000.00', '5000.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '9000.00', '9000.00', '864b9a8a', '9000.00', '4000.00', '1000.00', '9000.00', '4000.00', '1000.00', 'loss', 'win', '1000.00', '9000.00'),
(87, 25, NULL, 'pending', '2025-02-09 05:53:45', 2, NULL, NULL, NULL, '10000.00', NULL, 'team_a_win', NULL, '1.80', NULL, 'open', '18000.00', NULL, '879df7bc', '18000.00', '8000.00', '2000.00', NULL, NULL, NULL, 'pending', 'pending', NULL, NULL),
(88, 27, NULL, 'team_a_win', '2025-02-09 06:13:20', 2, 8, NULL, NULL, '8000.00', '8000.00', 'team_b_win', 'team_a_win', '1.80', '1.80', 'completed', '14400.00', '14400.00', '8805a1b5', '14400.00', '6400.00', '1600.00', '14400.00', '6400.00', '1600.00', 'loss', 'win', '1600.00', '14400.00'),
(89, 25, NULL, 'team_a_win', '2025-02-09 06:23:21', 6, 4, NULL, NULL, '8000.00', '8000.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '14400.00', '14400.00', '899a72ec', '14400.00', '6400.00', '1600.00', '14400.00', '6400.00', '1600.00', 'win', 'loss', '14400.00', '1600.00'),
(90, 25, NULL, 'team_a_win', '2025-02-09 06:25:24', 5, 3, NULL, NULL, '11000.00', '11000.00', 'team_a_win', 'team_b_win', '1.80', '1.80', 'completed', '19800.00', '19800.00', '9047f4b2', '19800.00', '8800.00', '2200.00', '19800.00', '8800.00', '2200.00', 'win', 'loss', '19800.00', '2200.00');

-- --------------------------------------------------------

--
-- Table structure for table `challenges`
--

CREATE TABLE `challenges` (
  `challenge_id` int(11) NOT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `team_a` varchar(100) DEFAULT NULL,
  `logo1` varchar(255) DEFAULT NULL,
  `team_b` varchar(100) DEFAULT NULL,
  `logo2` varchar(255) DEFAULT NULL,
  `odds_team_a` decimal(5,2) DEFAULT NULL,
  `odds_team_b` decimal(5,2) DEFAULT NULL,
  `odds_draw` decimal(5,2) DEFAULT NULL,
  `team_a_goal_advantage` int(11) DEFAULT '0',
  `team_b_goal_advantage` int(11) DEFAULT '0',
  `challenge_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `start_time` datetime DEFAULT NULL,
  `end_time` datetime DEFAULT NULL,
  `match_date` datetime NOT NULL,
  `result` enum('team_a_win','team_b_win','draw') DEFAULT NULL,
  `status` enum('Open','Closed','Settled') DEFAULT 'Open',
  `team_a_score` int(11) DEFAULT NULL,
  `team_b_score` int(11) DEFAULT NULL,
  `match_type` enum('half_time','full_time') DEFAULT 'full_time',
  `odds_lost` decimal(5,2) DEFAULT '0.20',
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `challenges`
--

INSERT INTO `challenges` (`challenge_id`, `admin_id`, `team_a`, `logo1`, `team_b`, `logo2`, `odds_team_a`, `odds_team_b`, `odds_draw`, `team_a_goal_advantage`, `team_b_goal_advantage`, `challenge_date`, `start_time`, `end_time`, `match_date`, `result`, `status`, `team_a_score`, `team_b_score`, `match_type`, `odds_lost`, `completed_at`) VALUES
(8, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Everton', '/backend/uploads/Everton Logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-24 11:10:00', '2025-01-24 13:10:00', '2025-01-27 13:10:00', '2025-01-28 13:10:00', 'team_b_win', 'Settled', 1, 2, 'half_time', '0.20', NULL),
(9, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Chelsea Fc', '/backend/uploads/image.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 13:10:00', '2025-01-27 15:10:00', '2025-01-29 16:10:00', '2025-02-01 15:11:00', 'team_a_win', 'Settled', 2, 1, 'full_time', '0.20', NULL),
(10, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Everton', '/backend/uploads/Everton Logo.png', '1.80', '1.80', '0.80', 2, 1, '2025-01-27 13:13:00', '2025-01-27 15:13:00', '2025-01-28 15:13:00', '2025-01-29 15:13:00', 'team_a_win', 'Settled', 3, 1, 'full_time', '0.20', NULL),
(11, NULL, 'Manchester United', '/backend/uploads/Manchester United Logo.png', 'Liverpool Fc', '/backend/uploads/pngwing.com.png', '1.80', '1.80', '0.80', 0, 0, '2025-01-27 13:18:00', '2025-01-27 15:18:00', '2025-01-28 15:18:00', '2025-01-29 15:18:00', 'draw', 'Settled', 1, 1, 'full_time', '0.20', NULL),
(12, NULL, 'Manchester City', '/backend/uploads/Manchester City.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-28 13:18:00', '2025-01-28 15:18:00', '2025-01-28 15:18:00', '2025-01-29 15:19:00', 'team_a_win', 'Settled', 2, 1, 'full_time', '0.20', NULL),
(13, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', '1.00', '1.00', '0.80', 0, 0, '2025-02-01 13:27:00', '2025-02-01 15:27:00', '2025-02-02 15:27:00', '2025-02-03 15:27:00', 'team_b_win', 'Closed', 1, 2, 'full_time', '0.20', NULL),
(14, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 19:35:00', '2025-01-27 21:35:00', '2025-01-27 22:35:00', '2025-01-27 23:35:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL),
(15, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Liverpool Fc', '/backend/uploads/pngwing.com.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 21:12:00', '2025-01-27 23:12:00', '2025-01-28 12:12:00', '2025-01-30 23:12:00', 'team_a_win', 'Closed', 1, 2, 'half_time', '0.20', NULL),
(16, NULL, 'Brentford FC', '/backend/uploads/Brentford FC.png', 'Manchester City', '/backend/uploads/Manchester City.png', '1.80', '1.80', '0.80', 1, 1, '2025-01-27 21:19:00', '2025-01-27 23:19:00', '2025-01-28 23:19:00', '2025-01-29 23:19:00', 'team_b_win', 'Settled', 1, 1, 'full_time', '0.20', '2025-02-08 17:49:18'),
(17, NULL, 'Manchester City', '/backend/uploads/Manchester City.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.00', '1.00', '0.80', 0, 0, '2025-01-27 21:20:00', '2025-01-27 23:20:00', '2025-01-28 23:20:00', '2025-01-30 23:20:00', 'team_a_win', 'Closed', 3, 2, 'full_time', '0.20', NULL),
(18, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', '1.80', '1.80', '0.80', 2, 1, '2025-01-27 21:21:00', '2025-01-27 23:21:00', '2025-01-28 23:21:00', '2025-01-31 23:21:00', NULL, 'Closed', NULL, NULL, 'half_time', '0.20', NULL),
(22, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.80', '1.80', '0.80', 1, 2, '2025-02-07 07:22:00', '2025-02-07 09:22:00', '2025-02-07 10:22:00', '2025-02-10 09:22:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL),
(23, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 0, 0, '2025-02-08 07:36:00', '2025-02-08 09:36:00', '2025-02-09 09:36:00', '2025-02-10 09:36:00', 'team_b_win', 'Settled', 1, 2, 'full_time', '0.20', '2025-02-09 07:40:56'),
(25, NULL, 'Everton', '/backend/uploads/Everton Logo.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 0, '2025-02-08 07:42:00', '2025-02-08 09:42:00', '2025-02-09 09:42:00', '2025-02-10 09:42:00', 'team_a_win', 'Settled', 2, 1, 'half_time', '0.20', '2025-02-09 06:31:04'),
(26, NULL, 'Chelsea Fc', '/backend/uploads/image.png', 'Brentford FC', '/backend/uploads/Brentford FC.png', '1.80', '1.80', '0.80', 0, 0, '2025-02-08 07:42:00', '2025-02-08 09:42:00', '2025-02-10 09:42:00', '2025-02-10 10:42:00', 'team_a_win', 'Settled', 1, 1, 'half_time', '0.20', '2025-02-09 01:38:55'),
(27, NULL, 'Everton', '/backend/uploads/Everton Logo.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 1, 1, '2025-02-08 11:40:00', '2025-02-08 13:40:00', '2025-02-09 13:40:00', '2025-02-11 13:40:00', 'team_a_win', 'Settled', 3, 2, 'half_time', '0.20', '2025-02-09 06:17:53'),
(28, NULL, 'Brentford FC', '/backend/uploads/Brentford FC.png', 'Manchester United', '/backend/uploads/Manchester United Logo.png', '1.80', '1.80', '0.80', 0, 1, '2025-02-09 12:02:00', '2025-02-09 14:02:00', '2025-02-10 04:03:00', '2025-02-11 14:03:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL),
(29, NULL, 'Arsenal', '/backend/uploads/Arsenal-FC-logo.png', 'Everton', '/backend/uploads/Everton Logo.png', '1.80', '1.80', '0.80', 0, 0, '2025-05-19 14:40:00', '2025-05-19 16:40:00', '2025-05-19 16:40:00', '2025-05-20 16:40:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL),
(30, NULL, 'Liverpool Fc', '/backend/uploads/pngwing.com.png', 'Chelsea Fc', '/backend/uploads/image.png', '1.80', '1.80', '0.80', 1, 0, '2025-05-20 18:04:00', '2025-05-20 20:04:00', '2025-05-22 20:04:00', '2025-05-24 20:04:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL),
(31, NULL, 'Brentford FC', '/backend/uploads/Brentford FC.png', 'Liverpool Fc', '/backend/uploads/pngwing.com.png', '1.80', '1.80', '0.80', 0, 0, '2025-05-20 20:09:00', '2025-05-20 22:09:00', '2025-05-22 22:09:00', '2025-05-24 22:09:00', NULL, 'Closed', NULL, NULL, 'full_time', '0.20', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `credit_requests`
--

CREATE TABLE `credit_requests` (
  `request_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `proof_image` varchar(255) NOT NULL,
  `status` enum('pending','approved','rejected','expired') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `expires_at` timestamp NULL DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `approved_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `credit_requests`
--

INSERT INTO `credit_requests` (`request_id`, `user_id`, `amount`, `payment_method_id`, `proof_image`, `status`, `created_at`, `expires_at`, `approved_at`, `approved_by`) VALUES
(1, 4, '1000.00', 3, 'uploads/payment_proofs/6780c417c15ef_1736492055.jpg', 'approved', '2025-01-10 06:54:15', '2025-01-11 06:54:15', NULL, NULL),
(2, 5, '10000.00', 3, 'uploads/payment_proofs/6780daa8e4014_1736497832.png', 'approved', '2025-01-10 08:30:32', '2025-01-11 08:30:32', NULL, NULL),
(3, 4, '47000.00', 2, 'uploads/payment_proofs/6780dae8ee0b0_1736497896.png', 'approved', '2025-01-10 08:31:36', '2025-01-11 08:31:36', NULL, NULL),
(4, 4, '6000.00', 1, 'uploads/payment_proofs/67866e0c01568_1736863244.jpg', 'approved', '2025-01-14 14:00:44', '2025-01-15 14:00:44', NULL, NULL),
(5, 4, '19999.00', 3, 'uploads/payment_proofs/678bdd7d2c345_1737219453.png', 'approved', '2025-01-18 16:57:33', '2025-01-19 16:57:33', NULL, NULL),
(6, 2, '5000.00', 2, 'uploads/payment_proofs/67a991c833b5e_1739166152.jpeg', 'approved', '2025-02-10 05:42:32', '2025-02-11 05:42:32', NULL, NULL),
(7, 2, '11.00', 1, 'uploads/payment_proofs/67a99ac251f5d_1739168450.jpeg', 'approved', '2025-02-10 06:20:50', '2025-02-11 06:20:50', NULL, NULL),
(8, 2, '5666.00', 1, 'uploads/payment_proofs/67a9a0525e0c1_1739169874.jpeg', 'approved', '2025-02-10 06:44:34', '2025-02-11 06:44:34', NULL, NULL),
(9, 2, '5000.00', 1, 'uploads/payment_proofs/67aa4f189fe48_1739214616.jpg', 'pending', '2025-02-10 19:10:16', '2025-02-11 19:10:16', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` int(11) NOT NULL,
  `currency_code` varchar(3) NOT NULL COMMENT 'ISO currency code (USD, ZAR, etc.)',
  `currency_name` varchar(50) NOT NULL COMMENT 'Full currency name',
  `currency_symbol` varchar(10) NOT NULL COMMENT 'Currency symbol ($, R, etc.)',
  `is_active` tinyint(1) DEFAULT '1' COMMENT 'Whether currency is available for selection',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Available currencies for user selection';

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`id`, `currency_code`, `currency_name`, `currency_symbol`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'USD', 'US Dollar', '$', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(2, 'ZAR', 'South African Rand', 'R', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(3, 'EUR', 'Euro', '€', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(4, 'GBP', 'British Pound', '£', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(5, 'CAD', 'Canadian Dollar', 'C$', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(6, 'AUD', 'Australian Dollar', 'A$', 1, '2025-06-09 09:19:58', '2025-06-09 09:19:58');

-- --------------------------------------------------------

--
-- Table structure for table `exchange_rates`
--

CREATE TABLE `exchange_rates` (
  `id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `rate_to_fancoin` decimal(10,4) NOT NULL COMMENT 'How many units of this currency = 1 FanCoin',
  `updated_by_admin_id` int(11) NOT NULL COMMENT 'Admin who last updated this rate',
  `notes` text COMMENT 'Optional notes about the rate update',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Exchange rates for converting FanCoin to other currencies';

--
-- Dumping data for table `exchange_rates`
--

INSERT INTO `exchange_rates` (`id`, `currency_id`, `rate_to_fancoin`, `updated_by_admin_id`, `notes`, `created_at`, `updated_at`) VALUES
(1, 1, '1.0000', 1, 'Base rate - 1 FanCoin = 1 USD', '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(2, 2, '18.0000', 1, 'Initial rate - 1 FanCoin = 18 ZAR', '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(3, 3, '0.9200', 1, 'Initial rate - 1 FanCoin = 0.92 EUR', '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(4, 4, '0.8000', 1, 'Initial rate - 1 FanCoin = 0.80 GBP', '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(5, 5, '1.3500', 1, 'Initial rate - 1 FanCoin = 1.35 CAD', '2025-06-09 09:19:58', '2025-06-09 09:19:58'),
(6, 6, '1.5000', 1, 'Initial rate - 1 FanCoin = 1.50 AUD', '2025-06-09 09:19:58', '2025-06-09 09:19:58');

-- --------------------------------------------------------

--
-- Table structure for table `general_settings`
--

CREATE TABLE `general_settings` (
  `id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `general_settings`
--

INSERT INTO `general_settings` (`id`, `setting_name`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'site_name', 'FanBet247', 'The name of the website', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(2, 'site_logo', 'uploads/logo/logo_1749285304.png', 'Path to the site logo', '2025-06-05 08:25:28', '2025-06-07 08:35:05'),
(3, 'contact_email', '<EMAIL>', 'Contact email address', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(4, 'contact_phone', '+1234567890', 'Contact phone number', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(5, 'facebook_url', '', 'Facebook page URL', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(6, 'twitter_url', '', 'Twitter profile URL', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(7, 'instagram_url', '', 'Instagram profile URL', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(8, 'about_text', 'FanBet247 is a sports betting platform.', 'About us text', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(9, 'terms_conditions', 'Terms and conditions text goes here.', 'Terms and conditions text', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(10, 'privacy_policy', 'Privacy policy text goes here.', 'Privacy policy text', '2025-06-05 08:25:28', '2025-06-05 08:25:28'),
(11, 'footer_text', '© 2024 FanBet247. All rights reserved.', 'Footer text', '2025-06-05 08:25:28', '2025-06-05 08:25:28');

-- --------------------------------------------------------

--
-- Table structure for table `leaderboards`
--

CREATE TABLE `leaderboards` (
  `leaderboard_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `division` int(11) DEFAULT '5',
  `season_start` date DEFAULT NULL,
  `season_end` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `leagues`
--

CREATE TABLE `leagues` (
  `league_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `min_bet_amount` decimal(20,2) NOT NULL,
  `max_bet_amount` decimal(20,2) NOT NULL,
  `description` text,
  `icon_path` varchar(255) DEFAULT NULL,
  `banner_path` varchar(255) DEFAULT NULL,
  `theme_color` varchar(7) DEFAULT '#007bff',
  `created_by` int(11) NOT NULL,
  `season_duration` int(11) DEFAULT '90',
  `league_icon` varchar(255) DEFAULT NULL,
  `league_banner` varchar(255) DEFAULT NULL,
  `league_rules` text,
  `reward_description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` enum('active','inactive','upcoming','archived') DEFAULT 'upcoming'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `leagues`
--

INSERT INTO `leagues` (`league_id`, `name`, `min_bet_amount`, `max_bet_amount`, `description`, `icon_path`, `banner_path`, `theme_color`, `created_by`, `season_duration`, `league_icon`, `league_banner`, `league_rules`, `reward_description`, `created_at`, `updated_at`, `status`) VALUES
(1, 'Katsina Leaguex', '1000.00', '10000.00', 'Entry level league for beginners', 'league_icon_675ee6dabdaea.png', 'league_banner_675ee6dabdeee.jpg', '#3700B3', 1, 90, 'premier_league.png', 'premier_league_banner.jpg', 'null', 'null', '2024-12-14 18:33:27', '2025-01-30 14:02:01', 'upcoming'),
(2, 'Calabar League', '10000.00', '100000.00', 'Intermediate league for regular players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(3, 'Rivers League', '100000.00', '1000000.00', 'Advanced league for experienced players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(4, 'Delta League', '1000000.00', '10000000.00', 'Professional league for high-stakes players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(5, 'Lagos League', '10000000.00', '100000000.00', 'Elite league for expert players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(6, 'Abuja League', '100000000.00', '1000000000.00', 'Premium league for master players', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2024-12-14 18:33:27', '2024-12-14 18:33:27', 'upcoming'),
(7, 'UEFA Europa League', '1000.00', '10000.00', 'The English Europa League ', 'league_icon_675e893646abe.png', 'league_banner_675e893646eee.jpeg', '#007bff', 1, 90, NULL, NULL, 'Rules and Guideline', 'Structure', '2024-12-15 07:45:58', '2024-12-15 07:45:58', 'upcoming'),
(8, 'Abujax', '500.00', '1000.00', 'describe', NULL, NULL, '#007bff', 1, 90, NULL, NULL, 'jhghgg', 'hjhghgh', '2024-12-15 21:38:45', '2024-12-15 21:38:45', 'upcoming'),
(9, 'Premier League', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:02', '2025-01-30 13:57:02', 'active'),
(10, 'Premier League 2023', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:15', '2025-01-30 15:32:30', 'active'),
(11, 'Premier League 2024', '100.00', '10000.00', 'The most competitive league', NULL, NULL, '#007bff', 1, 90, NULL, NULL, NULL, NULL, '2025-01-30 13:57:50', '2025-01-30 15:32:30', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `league_audit_log`
--

CREATE TABLE `league_audit_log` (
  `log_id` int(11) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `action_type` enum('create_league','edit_league','start_season','end_season','approve_member','suspend_member','verify_results','other') NOT NULL,
  `league_id` int(11) DEFAULT NULL,
  `season_id` int(11) DEFAULT NULL,
  `membership_id` int(11) DEFAULT NULL,
  `action_details` text NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `league_history`
--

CREATE TABLE `league_history` (
  `history_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `final_points` int(11) NOT NULL,
  `final_rank` int(11) NOT NULL,
  `total_bets` int(11) NOT NULL,
  `wins` int(11) NOT NULL,
  `draws` int(11) NOT NULL,
  `losses` int(11) NOT NULL,
  `rewards_earned` text,
  `verified_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `league_memberships`
--

CREATE TABLE `league_memberships` (
  `membership_id` int(11) NOT NULL,
  `user_league_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `deposit_amount` decimal(20,2) NOT NULL DEFAULT '0.00',
  `current_points` int(11) DEFAULT '0',
  `total_bets` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `approved_by` int(11) DEFAULT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active_membership` int(11) GENERATED ALWAYS AS (if((`status` = 'active'),`user_id`,NULL)) VIRTUAL,
  `last_bet_date` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_memberships`
--

INSERT INTO `league_memberships` (`membership_id`, `user_league_id`, `user_id`, `league_id`, `season_id`, `deposit_amount`, `current_points`, `total_bets`, `wins`, `draws`, `losses`, `approved_by`, `join_date`, `status`, `created_at`, `updated_at`, `last_bet_date`) VALUES
(2, 19, 2, 11, 19, '100.00', 3, 3, 1, 0, 4, NULL, '2025-02-01 09:22:35', 'active', '2025-02-01 11:22:35', '2025-02-09 07:40:56', '2025-02-09 07:40:56'),
(3, 20, 5, 11, 19, '100.00', 3, 0, 1, 0, 0, NULL, '2025-02-01 09:31:54', 'active', '2025-02-01 11:31:55', '2025-02-09 06:31:04', '2025-02-09 06:31:04'),
(4, 21, 3, 11, 19, '1200.00', 0, 0, 0, 0, 1, NULL, '2025-02-01 09:33:27', 'active', '2025-02-01 11:33:27', '2025-02-09 06:31:04', '2025-02-09 06:31:04'),
(5, 22, 4, 11, 19, '10000.00', 0, 0, 0, 0, 1, NULL, '2025-02-01 09:35:30', 'active', '2025-02-01 11:35:31', '2025-02-09 06:31:04', '2025-02-09 06:31:04'),
(8, 23, 1, 7, 4, '10000.00', 3, 1, 1, 0, 0, NULL, '2025-02-01 12:44:07', 'active', '2025-02-01 14:44:07', '2025-02-01 15:30:20', NULL),
(9, 24, 6, 11, 19, '1000.00', 3, 0, 1, 0, 0, NULL, '2025-02-01 12:45:30', 'active', '2025-02-01 14:45:30', '2025-02-09 06:31:04', '2025-02-09 06:31:04'),
(10, 25, 8, 11, 19, '100.00', 9, 1, 3, 0, 0, NULL, '2025-02-01 13:31:13', 'active', '2025-02-01 15:31:13', '2025-02-09 07:40:56', '2025-02-09 07:40:56');

-- --------------------------------------------------------

--
-- Table structure for table `league_memberships_backup`
--

CREATE TABLE `league_memberships_backup` (
  `membership_id` int(11) NOT NULL DEFAULT '0',
  `user_league_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `deposit_amount` decimal(20,2) NOT NULL,
  `current_points` int(11) DEFAULT '0',
  `total_bets` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `approved_by` int(11) DEFAULT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `active_membership` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_memberships_backup`
--

INSERT INTO `league_memberships_backup` (`membership_id`, `user_league_id`, `user_id`, `league_id`, `season_id`, `deposit_amount`, `current_points`, `total_bets`, `wins`, `draws`, `losses`, `approved_by`, `join_date`, `status`, `created_at`, `updated_at`, `active_membership`) VALUES
(15, 20, 4, 7, 4, '1000.00', 0, 7, 5, 0, 2, NULL, '2025-01-09 16:30:51', 'completed', '2025-01-09 18:30:51', '2025-02-01 09:26:08', NULL),
(16, 21, 2, 7, 4, '1000.00', 0, 7, 4, 0, 3, NULL, '2025-01-09 16:31:09', 'completed', '2025-01-09 18:31:09', '2025-02-01 09:26:08', NULL),
(17, 22, 3, 7, 4, '10000.00', 0, 7, 3, 0, 4, NULL, '2025-01-10 02:19:35', 'completed', '2025-01-10 04:19:35', '2025-02-01 09:26:08', NULL),
(18, 23, 5, 7, 4, '10000.00', 0, 7, 2, 0, 5, NULL, '2025-01-10 02:22:25', 'completed', '2025-01-10 04:22:25', '2025-02-01 09:26:08', NULL),
(39, 29, 1, 1, 5, '10000.00', 0, 0, 0, 0, 0, NULL, '2025-01-30 14:27:55', 'completed', '2025-01-30 16:27:55', '2025-02-01 09:26:08', NULL),
(40, 30, 8, 1, 5, '1000.00', 0, 0, 0, 0, 0, NULL, '2025-01-30 14:31:07', 'completed', '2025-01-30 16:31:08', '2025-02-01 09:26:08', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `league_seasons`
--

CREATE TABLE `league_seasons` (
  `season_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `season_name` varchar(100) NOT NULL,
  `start_date` timestamp NULL DEFAULT NULL,
  `end_date` timestamp NULL DEFAULT NULL,
  `created_by` int(11) NOT NULL,
  `total_participants` int(11) DEFAULT '0',
  `prize_pool` decimal(20,2) DEFAULT '0.00',
  `status` enum('upcoming','active','completed','cancelled') DEFAULT 'upcoming',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `league_seasons`
--

INSERT INTO `league_seasons` (`season_id`, `league_id`, `season_name`, `start_date`, `end_date`, `created_by`, `total_participants`, `prize_pool`, `status`, `created_at`, `updated_at`) VALUES
(4, 7, 'Season 2025', '2025-01-09 16:48:17', '2025-02-08 16:48:17', 2, 0, '0.00', 'active', '2025-01-09 16:48:17', '2025-01-09 16:48:17'),
(5, 1, 'Season 1', '2025-01-30 13:57:50', '2025-04-30 13:57:50', 1, 0, '0.00', 'active', '2025-01-30 13:57:50', '2025-01-30 13:57:50'),
(6, 1, 'Season 1', '2025-01-30 13:58:16', '2025-04-30 13:58:16', 1, 0, '0.00', 'active', '2025-01-30 13:58:16', '2025-01-30 13:58:16'),
(19, 11, 'Season 2025', '2025-02-01 11:22:35', '2025-03-03 11:22:35', 2, 0, '0.00', 'active', '2025-02-01 11:22:35', '2025-02-01 11:22:35');

-- --------------------------------------------------------

--
-- Table structure for table `messages`
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `sender_id` int(11) NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `content` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `deleted_by_sender` tinyint(1) DEFAULT '0',
  `deleted_by_recipient` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `messages`
--

INSERT INTO `messages` (`id`, `sender_id`, `recipient_id`, `content`, `created_at`, `is_read`, `deleted_by_sender`, `deleted_by_recipient`) VALUES
(1, 2, 4, 'how arer you /?', '2024-11-29 16:32:36', 1, 0, 0),
(2, 2, 4, 'are you there?', '2024-11-29 16:34:34', 1, 0, 0),
(3, 4, 2, 'yes i am there', '2024-11-29 16:44:58', 1, 0, 0),
(4, 2, 4, 'i see am like that', '2024-11-29 16:45:10', 1, 0, 0),
(5, 4, 2, 'this is from lil wayne', '2024-11-29 17:05:36', 1, 0, 0),
(6, 2, 4, 'this lil wayne you are not lili wayne werytin make you dey lie na me be the lliwayne andf you are demohomex', '2024-11-29 19:03:44', 1, 0, 0),
(7, 4, 2, 'bas guy how you take catch me lol', '2024-11-29 19:04:07', 1, 0, 0),
(8, 2, 3, 'jameslink how far', '2024-11-30 10:09:08', 0, 0, 0),
(9, 2, 1, 'testingther mic', '2024-11-30 11:16:06', 1, 0, 0),
(10, 2, 8, 'how far blood', '2024-11-30 11:18:03', 0, 0, 0),
(11, 2, 6, 'baba Blue King wad up', '2024-11-30 11:20:28', 1, 0, 0),
(12, 6, 2, 'how far Liwayen this one you write me so hope all is well', '2024-11-30 11:21:41', 1, 0, 0),
(13, 2, 6, 'My bloda all is well i just de y check which game you play', '2024-11-30 11:22:02', 1, 0, 0),
(14, 2, 4, 'baba demhomex you dey play with me i go win your team this week ', '2024-11-30 17:40:42', 1, 0, 0),
(15, 2, 4, 'demohomex  let me know if you got this message', '2024-11-30 17:56:03', 1, 0, 0),
(16, 4, 2, 'lilwayne i got your message', '2024-11-30 18:03:35', 1, 0, 0),
(17, 2, 4, 'demohomex i feel say you dont lost', '2024-11-30 18:05:52', 1, 0, 0),
(18, 4, 2, 'how i won take lose i full ground', '2024-11-30 18:06:38', 1, 0, 0),
(19, 4, 2, 'make you sure you play the odss i tell you', '2024-11-30 18:07:15', 1, 0, 0),
(20, 6, 2, 'lil wayne na man you be ', '2024-11-30 18:44:50', 1, 0, 0),
(21, 2, 6, 'baba blue', '2024-11-30 18:45:22', 1, 0, 0),
(22, 6, 2, 'lol', '2024-11-30 18:45:34', 1, 0, 0),
(23, 2, 4, 'hi', '2024-12-15 21:39:46', 1, 0, 0),
(24, 4, 2, 'i see you', '2024-12-15 21:40:15', 1, 0, 0),
(25, 2, 4, 'hu', '2024-12-15 21:41:56', 1, 0, 0),
(26, 4, 5, 'thanks for the money my broda i apprciate ', '2025-01-10 05:47:33', 1, 0, 0),
(27, 5, 4, 'you welcome', '2025-01-10 05:48:05', 1, 0, 0),
(28, 1, 2, 'Hello', '2025-01-14 13:54:49', 1, 0, 0),
(29, 1, 4, 'wad up thanks for friend rerquest ', '2025-01-14 13:55:08', 1, 0, 0),
(30, 4, 1, 'thanks my broda', '2025-01-14 13:56:10', 1, 0, 0),
(31, 4, 1, 'Hello', '2025-01-18 16:53:28', 0, 0, 0),
(32, 2, 1, 'its working now', '2025-01-31 14:47:00', 0, 0, 0),
(33, 2, 1, 'thank you', '2025-01-31 14:53:14', 0, 0, 0);

-- --------------------------------------------------------

--
-- Table structure for table `paymentverifications`
--

CREATE TABLE `paymentverifications` (
  `verification_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_slip_url` varchar(255) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `submitted_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `verified_at` timestamp NULL DEFAULT NULL,
  `verified_by` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('bank','crypto','paypal','mobile_money') NOT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `fields` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `type`, `active`, `fields`, `created_at`, `updated_at`) VALUES
(1, 'crypto Payment Method', 'crypto', 1, '[{\"fieldName\": \"Bitcoin Address\", \"fieldValue\": \"1001jdhdjidhjdhuh4y7578457857875857hugfjhjf\"}]', '2024-09-23 14:31:17', '2025-02-10 04:46:55'),
(2, 'Bank', 'bank', 1, '[{\"fieldName\": \"Bank Name\", \"fieldValue\": \"Loko P Bank\"}, {\"fieldName\": \"Account Number\", \"fieldValue\": \"**************\"}, {\"fieldName\": \"Account  Name\", \"fieldValue\": \"Mr Fanbet247\"}]', '2024-09-23 14:32:25', '2025-02-10 04:48:24'),
(3, 'Courier Bank', 'bank', 1, '[{\"fieldName\": \"Account Number\", \"fieldValue\": \"110100101010018935\"}]', '2024-09-23 14:44:02', '2025-02-10 04:49:12'),
(7, 'Poko Money', 'mobile_money', 1, '[{\"fieldName\": \"Phone Number\", \"fieldValue\": \"**************\"}]', '2025-02-10 04:23:24', '2025-02-10 04:23:24'),
(8, 'Paypal', 'paypal', 1, '[{\"fieldName\": \"Paypal Email\", \"fieldValue\": \"<EMAIL>\"}]', '2025-02-10 04:49:47', '2025-02-10 04:49:47'),
(9, 'ETherium', 'crypto', 1, '[{\"fieldName\": \"etherium\", \"fieldValue\": \"81818181818181oyooyoyoy\"}]', '2025-02-10 05:13:37', '2025-02-10 05:13:37');

-- --------------------------------------------------------

--
-- Table structure for table `seasons`
--

CREATE TABLE `seasons` (
  `season_id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('upcoming','active','completed') DEFAULT 'upcoming',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `season_history`
--

CREATE TABLE `season_history` (
  `history_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `season_id` int(11) NOT NULL,
  `final_points` int(11) DEFAULT '0',
  `final_rank` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- --------------------------------------------------------

--
-- Table structure for table `security_settings`
--

CREATE TABLE `security_settings` (
  `id` int(11) NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `description` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `security_settings`
--

INSERT INTO `security_settings` (`id`, `setting_name`, `setting_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 'enable_2fa', 'true', 'Enable two-factor authentication for users', '2025-06-05 07:25:19', '2025-06-06 19:08:53'),
(2, 'allowed_auth_methods', 'email_otp,google_auth', 'Allowed authentication methods (comma-separated)', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(3, 'otp_expiry_time', '300', 'OTP expiry time in seconds', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(4, 'max_otp_attempts', '3', 'Maximum number of OTP verification attempts before lockout', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(5, 'lockout_time', '1800', 'Account lockout time in seconds after max failed attempts', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(6, 'password_min_length', '8', 'Minimum password length requirement', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(7, 'require_special_chars', 'true', 'Require special characters in passwords', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(8, 'session_timeout', '3600', 'Session timeout in seconds', '2025-06-05 07:25:19', '2025-06-05 07:25:19'),
(9, 'max_login_attempts', '5', 'Maximum login attempts before account lockout', '2025-06-05 07:25:19', '2025-06-05 07:25:19');

-- --------------------------------------------------------

--
-- Table structure for table `smtp_settings`
--

CREATE TABLE `smtp_settings` (
  `id` int(11) NOT NULL,
  `host` varchar(255) NOT NULL DEFAULT 'smtp.gmail.com',
  `port` int(11) NOT NULL DEFAULT '587',
  `username` varchar(255) NOT NULL DEFAULT '',
  `password` varchar(255) NOT NULL DEFAULT '',
  `encryption` enum('none','ssl','tls') NOT NULL DEFAULT 'tls',
  `from_email` varchar(255) NOT NULL DEFAULT '<EMAIL>',
  `from_name` varchar(255) NOT NULL DEFAULT 'FanBet247',
  `is_active` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `smtp_settings`
--

INSERT INTO `smtp_settings` (`id`, `host`, `port`, `username`, `password`, `encryption`, `from_email`, `from_name`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'smtp.hostinger.com', 465, '<EMAIL>', 'Money2025@Demo#', 'ssl', '<EMAIL>', 'FanBet247', 1, '2025-06-05 07:22:43', '2025-06-05 08:45:12');

-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--

CREATE TABLE `system_settings` (
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `teams`
--

CREATE TABLE `teams` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `logo` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `teams`
--

INSERT INTO `teams` (`id`, `name`, `logo`, `created_at`, `updated_at`) VALUES
(11, 'Arsenal', 'uploads/Arsenal-FC-logo.png', '2024-09-06 21:35:14', '2024-09-06 21:35:14'),
(12, 'Chelsea Fc', 'uploads/image.png', '2024-09-06 21:36:07', '2024-09-06 21:36:07'),
(13, 'Everton', 'uploads/Everton Logo.png', '2024-09-06 21:46:02', '2024-09-06 21:46:02'),
(14, 'Liverpool Fc', 'uploads/pngwing.com.png', '2024-09-06 21:46:38', '2024-09-06 21:51:05'),
(15, 'Manchester United', 'uploads/Manchester United Logo.png', '2024-09-06 21:50:36', '2024-09-06 21:50:36'),
(16, 'Brentford FC', 'uploads/Brentford FC.png', '2024-11-01 16:18:58', '2024-11-01 16:18:58'),
(17, 'Manchester City', 'uploads/Manchester City.png', '2024-11-03 10:12:26', '2024-11-03 10:12:26'),
(22, 'Sunderland AFC', 'uploads/6843d9198bcdc_pngegg.png', '2025-06-07 06:15:53', '2025-06-07 06:15:53'),
(24, 'PSG', 'uploads/6843d9df46662_pngegg (1).png', '2025-06-07 06:19:11', '2025-06-07 06:19:11'),
(25, 'Fc Barcelona', 'uploads/6843da313d28f_pngegg (2).png', '2025-06-07 06:20:33', '2025-06-07 06:20:33'),
(26, 'Aston Villa', 'uploads/6843ecf30d06d_pngegg (3).png', '2025-06-07 07:40:35', '2025-06-07 07:40:35'),
(27, 'Burnley', 'uploads/6843f2541cf70_t90.png', '2025-06-07 08:03:32', '2025-06-07 08:03:32'),
(28, 'Fulham', 'uploads/6843f99cbc9ea_t54.png', '2025-06-07 08:34:36', '2025-06-07 08:34:36');

-- --------------------------------------------------------

--
-- Table structure for table `transactions`
--

CREATE TABLE `transactions` (
  `transaction_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL,
  `type` enum('deposit','withdrawal','bet','win','refund','admin_credit','admin_debit','transfer_sent','transfer_received','bet_settlement') NOT NULL,
  `status` enum('pending','completed','failed') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `related_bet_id` int(11) DEFAULT NULL,
  `admin_id` int(11) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `related_user_id` int(11) DEFAULT NULL,
  `related_challenge_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `transactions`
--

INSERT INTO `transactions` (`transaction_id`, `user_id`, `amount`, `type`, `status`, `created_at`, `related_bet_id`, `admin_id`, `description`, `related_user_id`, `related_challenge_id`) VALUES
(1, 4, '55553.00', 'bet', 'completed', '2024-10-28 15:55:27', 55, NULL, NULL, NULL, NULL),
(2, 4, '100999.00', 'bet', 'completed', '2024-10-28 16:01:53', 56, NULL, NULL, NULL, NULL),
(3, 2, '8998.20', 'win', 'completed', '2024-10-29 18:51:05', 52, NULL, NULL, NULL, NULL),
(5, 2, '14173.20', 'win', 'completed', '2024-10-29 20:53:40', 53, NULL, NULL, NULL, NULL),
(6, 4, '2344.00', 'win', 'completed', '2024-10-29 20:53:40', 53, NULL, NULL, NULL, NULL),
(7, 2, '99995.40', 'win', 'completed', '2024-10-29 20:53:40', 55, NULL, NULL, NULL, NULL),
(8, 4, '11110.60', 'win', 'completed', '2024-10-29 20:53:40', 55, NULL, NULL, NULL, NULL),
(9, 3, '500.00', 'admin_credit', 'completed', '2024-10-29 21:17:02', NULL, 1, NULL, NULL, NULL),
(10, 3, '50000.00', 'admin_credit', 'completed', '2024-10-30 10:57:44', NULL, 1, NULL, NULL, NULL),
(11, 6, '5000.00', 'admin_credit', 'completed', '2024-10-30 11:51:40', NULL, 1, NULL, NULL, NULL),
(12, 4, '5000.00', 'bet', 'completed', '2024-10-30 11:59:41', 57, NULL, NULL, NULL, NULL),
(13, 6, '9000.00', 'win', 'completed', '2024-10-30 12:03:17', 57, NULL, NULL, NULL, NULL),
(14, 4, '1000.00', 'win', 'completed', '2024-10-30 12:03:17', 57, NULL, NULL, NULL, NULL),
(15, 8, '5000.00', 'bet', 'completed', '2024-11-01 17:37:22', 59, NULL, NULL, NULL, NULL),
(16, 2, '8000.00', 'bet', 'completed', '2024-11-01 18:00:29', 60, NULL, NULL, NULL, NULL),
(17, 8, '14400.00', 'win', 'completed', '2024-11-03 11:28:39', 60, NULL, NULL, NULL, NULL),
(18, 2, '1600.00', 'win', 'completed', '2024-11-03 11:28:39', 60, NULL, NULL, NULL, NULL),
(19, 2, '9000.00', 'win', 'completed', '2024-11-03 12:02:24', 59, NULL, NULL, NULL, NULL),
(20, 8, '1000.00', 'win', 'completed', '2024-11-03 12:02:24', 59, NULL, NULL, NULL, NULL),
(21, 4, '16398.00', 'admin_credit', 'completed', '2024-11-03 12:12:15', NULL, 1, NULL, NULL, NULL),
(22, 4, '16398.00', 'bet', 'completed', '2024-11-03 12:12:22', 62, NULL, NULL, NULL, NULL),
(23, 3, '1000.00', 'bet', 'completed', '2024-11-03 12:18:28', 63, NULL, NULL, NULL, NULL),
(24, 8, '29516.40', 'win', 'completed', '2024-11-03 12:24:21', 62, NULL, NULL, NULL, NULL),
(25, 4, '3279.60', 'win', 'completed', '2024-11-03 12:24:21', 62, NULL, NULL, NULL, NULL),
(26, 5, '200.00', 'win', 'completed', '2024-11-03 12:24:21', 63, NULL, NULL, NULL, NULL),
(27, 3, '1800.00', 'win', 'completed', '2024-11-03 12:24:21', 63, NULL, NULL, NULL, NULL),
(28, 3, '40000.00', 'bet', 'completed', '2024-11-03 12:43:54', 64, NULL, NULL, NULL, NULL),
(29, 5, '40000.00', 'win', 'completed', '2024-11-03 12:45:58', 64, NULL, NULL, NULL, NULL),
(30, 3, '8000.00', 'win', 'completed', '2024-11-03 12:45:58', 64, NULL, NULL, NULL, NULL),
(31, 3, '5000.00', 'bet', 'completed', '2024-11-03 13:20:58', 65, NULL, NULL, NULL, NULL),
(32, 5, '9000.00', 'win', 'completed', '2024-11-03 13:27:10', 65, NULL, NULL, NULL, NULL),
(33, 3, '1000.00', 'win', 'completed', '2024-11-03 13:27:10', 65, NULL, NULL, NULL, NULL),
(34, 3, '3998.00', 'bet', 'completed', '2024-11-03 13:32:15', 66, NULL, NULL, NULL, NULL),
(35, 5, '7196.40', 'win', 'completed', '2024-11-03 13:33:27', 66, NULL, NULL, NULL, NULL),
(36, 3, '799.60', 'win', 'completed', '2024-11-03 13:33:27', 66, NULL, NULL, NULL, NULL),
(37, 3, '4998.00', 'bet', 'completed', '2024-11-03 13:37:59', 67, NULL, NULL, NULL, NULL),
(38, 5, '8996.40', 'win', 'completed', '2024-11-03 13:39:00', 67, NULL, NULL, NULL, NULL),
(39, 3, '999.60', 'win', 'completed', '2024-11-03 13:39:00', 67, NULL, NULL, NULL, NULL),
(40, 2, '1000.00', 'admin_debit', 'completed', '2024-12-15 20:05:26', NULL, NULL, NULL, NULL, NULL),
(41, 2, '3000.00', 'admin_debit', 'completed', '2024-12-15 20:07:29', NULL, NULL, NULL, NULL, NULL),
(42, 2, '10000.00', 'admin_debit', 'completed', '2024-12-15 20:18:01', NULL, NULL, NULL, NULL, NULL),
(43, 4, '1000.00', 'admin_debit', 'completed', '2024-12-15 22:13:31', NULL, NULL, NULL, NULL, NULL),
(44, 4, '1000.00', 'admin_debit', 'completed', '2024-12-15 22:14:01', NULL, NULL, NULL, NULL, NULL),
(45, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 16:48:18', NULL, NULL, NULL, NULL, NULL),
(46, 2, '10000.00', 'admin_debit', 'completed', '2025-01-09 17:34:02', NULL, NULL, NULL, NULL, NULL),
(47, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 17:45:47', NULL, NULL, NULL, NULL, NULL),
(48, 2, '10000.00', 'admin_debit', 'completed', '2025-01-09 17:46:03', NULL, NULL, NULL, NULL, NULL),
(49, 4, '1000.00', 'admin_debit', 'completed', '2025-01-09 18:07:35', NULL, NULL, NULL, NULL, NULL),
(50, 2, '1000.00', 'admin_debit', 'completed', '2025-01-09 18:07:47', NULL, NULL, NULL, NULL, NULL),
(51, 4, '1000.00', 'bet', 'completed', '2025-01-09 18:30:51', NULL, NULL, NULL, NULL, NULL),
(52, 2, '1000.00', 'bet', 'completed', '2025-01-09 18:31:09', NULL, NULL, NULL, NULL, NULL),
(53, 3, '10000.00', 'admin_debit', 'completed', '2025-01-10 04:19:35', NULL, NULL, NULL, NULL, NULL),
(54, 5, '10000.00', 'admin_debit', 'completed', '2025-01-10 04:22:25', NULL, NULL, NULL, NULL, NULL),
(55, 4, '-500.00', 'transfer_sent', 'completed', '2025-01-10 05:45:24', NULL, NULL, 'Transfer between users', 5, NULL),
(56, 5, '500.00', 'transfer_received', 'completed', '2025-01-10 05:45:24', NULL, NULL, 'Transfer between users', 4, NULL),
(57, 4, '-234.00', 'transfer_sent', 'completed', '2025-01-10 05:46:08', NULL, NULL, 'Transfer between users', 5, NULL),
(58, 5, '234.00', 'transfer_received', 'completed', '2025-01-10 05:46:08', NULL, NULL, 'Transfer between users', 4, NULL),
(59, 5, '-40000.00', 'transfer_sent', 'completed', '2025-01-10 05:46:51', NULL, NULL, 'Transfer between users', 4, NULL),
(60, 4, '40000.00', 'transfer_received', 'completed', '2025-01-10 05:46:51', NULL, NULL, 'Transfer between users', 5, NULL),
(61, 4, '1000.00', 'admin_credit', 'completed', '2025-01-10 08:28:36', NULL, 1, NULL, NULL, NULL),
(62, 5, '10000.00', 'admin_credit', 'completed', '2025-01-10 08:31:54', NULL, 1, NULL, NULL, NULL),
(63, 4, '47000.00', 'admin_credit', 'completed', '2025-01-10 08:33:52', NULL, 1, NULL, NULL, NULL),
(64, 4, '-500.00', 'transfer_sent', 'completed', '2025-01-14 13:56:45', NULL, NULL, 'Transfer between users', 1, NULL),
(65, 1, '500.00', 'transfer_received', 'completed', '2025-01-14 13:56:45', NULL, NULL, 'Transfer between users', 4, NULL),
(66, 4, '6000.00', 'admin_credit', 'completed', '2025-01-14 14:08:17', NULL, 1, NULL, NULL, NULL),
(67, 4, '-100.00', 'transfer_sent', 'completed', '2025-01-18 16:54:08', NULL, NULL, 'Transfer between users', 1, NULL),
(68, 1, '100.00', 'transfer_received', 'completed', '2025-01-18 16:54:08', NULL, NULL, 'Transfer between users', 4, NULL),
(69, 4, '19999.00', 'admin_credit', 'completed', '2025-01-18 17:08:54', NULL, 1, NULL, NULL, NULL),
(70, 1, '5000.00', 'bet', 'completed', '2025-01-26 16:57:46', 73, NULL, NULL, NULL, NULL),
(71, 1, '7500.00', 'bet', 'completed', '2025-01-27 13:40:04', 75, NULL, NULL, NULL, NULL),
(72, 2, '8500.00', 'bet', 'completed', '2025-01-27 14:44:17', 77, NULL, NULL, NULL, NULL),
(74, 1, '8500.00', 'bet', 'completed', '2025-01-27 18:56:02', 77, NULL, 'Bet outcome: Win - Points earned: 3', NULL, NULL),
(75, 2, '1700.00', 'bet', 'completed', '2025-01-27 18:56:02', 77, NULL, 'Bet outcome: Loss - Points earned: 0', NULL, NULL),
(76, 2, '1500.00', 'bet', 'completed', '2025-01-27 19:02:30', 75, NULL, 'Bet outcome: Loss - Points earned: 0', NULL, NULL),
(77, 1, '13500.00', 'bet', 'completed', '2025-01-27 19:02:30', 75, NULL, 'Bet outcome: Win - Points earned: 3', NULL, NULL),
(78, 2, '1000.00', 'bet', 'completed', '2025-01-27 19:06:50', 73, NULL, 'Bet outcome: Loss - Points earned: 0', NULL, NULL),
(79, 1, '5000.00', 'bet', 'completed', '2025-01-27 19:06:50', 73, NULL, 'Bet outcome: Win - Points earned: 3', NULL, NULL),
(80, 1, '8500.00', 'bet', 'completed', '2025-01-27 19:13:19', 76, NULL, NULL, NULL, NULL),
(81, 2, '15300.00', 'bet', 'completed', '2025-01-27 19:14:38', 76, NULL, 'Bet outcome: Win - Points earned: 3', NULL, NULL),
(82, 1, '1700.00', 'bet', 'completed', '2025-01-27 19:14:38', 76, NULL, 'Bet outcome: Loss - Points earned: 0', NULL, NULL),
(83, 1, '8700.00', 'bet', 'completed', '2025-01-27 21:37:14', 79, NULL, NULL, NULL, NULL),
(84, 1, '10000.00', 'admin_debit', 'completed', '2025-01-30 16:27:55', NULL, NULL, NULL, NULL, NULL),
(85, 8, '1000.00', 'admin_debit', 'completed', '2025-01-30 16:31:08', NULL, NULL, NULL, NULL, NULL),
(86, 8, '5000.00', 'bet', 'completed', '2025-02-01 03:22:07', 81, NULL, NULL, NULL, NULL),
(87, 2, '4998.00', 'bet', 'completed', '2025-02-01 03:38:18', 85, NULL, NULL, NULL, NULL),
(88, 2, '100.00', 'admin_debit', 'completed', '2025-02-01 11:22:35', NULL, NULL, 'League join fee', NULL, NULL),
(89, 5, '100.00', 'admin_debit', 'completed', '2025-02-01 11:31:55', NULL, NULL, 'League join fee', NULL, NULL),
(90, 3, '1200.00', 'admin_debit', 'completed', '2025-02-01 11:33:27', NULL, NULL, 'League join fee', NULL, NULL),
(91, 4, '10000.00', 'admin_debit', 'completed', '2025-02-01 11:35:31', NULL, NULL, 'League join fee', NULL, NULL),
(92, 1, '10000.00', 'admin_debit', 'completed', '2025-02-01 14:44:07', NULL, NULL, 'League join fee', NULL, NULL),
(93, 6, '1000.00', 'admin_debit', 'completed', '2025-02-01 14:45:30', NULL, NULL, 'League join fee', NULL, NULL),
(101, 2, '-8700.00', 'bet', 'completed', '2025-02-01 15:30:20', 79, NULL, 'Bet outcome: Loss - Amount: -8700', NULL, NULL),
(102, 1, '15660.00', 'bet', 'completed', '2025-02-01 15:30:20', 79, NULL, 'Bet outcome: Win - Amount: +15660.00', NULL, NULL),
(103, 8, '-4998.00', 'bet', 'completed', '2025-02-01 15:30:20', 85, NULL, 'Bet outcome: Loss - Amount: -4998', NULL, NULL),
(104, 2, '3998.40', 'bet', 'completed', '2025-02-01 15:30:20', 85, NULL, 'Bet outcome: Win - Amount: +3998.40', NULL, NULL),
(105, 8, '100.00', 'admin_debit', 'completed', '2025-02-01 15:31:13', NULL, NULL, 'League join fee', NULL, NULL),
(106, 2, '50000999.00', 'admin_credit', 'completed', '2025-02-08 15:07:56', NULL, 1, NULL, NULL, NULL),
(115, 2, '1000.00', 'bet_settlement', 'completed', '2025-02-08 17:49:18', 81, NULL, 'Bet outcome: Loss - Amount: +1000', NULL, 16),
(116, 8, '9000.00', 'bet_settlement', 'completed', '2025-02-08 17:49:18', 81, NULL, 'Bet outcome: Win - Amount: +9000.00', NULL, 16),
(117, 8, '5000.00', 'bet', 'completed', '2025-02-09 05:19:21', 86, NULL, NULL, NULL, NULL),
(118, 8, '8000.00', 'bet', 'completed', '2025-02-09 06:15:37', 88, NULL, NULL, NULL, NULL),
(119, 2, '1600.00', 'bet_settlement', 'completed', '2025-02-09 06:17:53', 88, NULL, 'Bet outcome: Loss - Amount: +1600', NULL, 27),
(120, 8, '14400.00', 'bet_settlement', 'completed', '2025-02-09 06:17:53', 88, NULL, 'Bet outcome: Win - Amount: +14400.00', NULL, 27),
(121, 4, '8000.00', 'bet', 'completed', '2025-02-09 06:23:49', 89, NULL, NULL, NULL, NULL),
(122, 3, '11000.00', 'bet', 'completed', '2025-02-09 06:27:21', 90, NULL, NULL, NULL, NULL),
(123, 6, '14400.00', 'bet_settlement', 'completed', '2025-02-09 06:31:04', 89, NULL, 'Bet outcome: Win - Amount: +14400.00', NULL, 25),
(124, 4, '1600.00', 'bet_settlement', 'completed', '2025-02-09 06:31:04', 89, NULL, 'Bet outcome: Loss - Amount: +1600', NULL, 25),
(125, 5, '19800.00', 'bet_settlement', 'completed', '2025-02-09 06:31:04', 90, NULL, 'Bet outcome: Win - Amount: +19800.00', NULL, 25),
(126, 3, '2200.00', 'bet_settlement', 'completed', '2025-02-09 06:31:04', 90, NULL, 'Bet outcome: Loss - Amount: +2200', NULL, 25),
(127, 2, '1000.00', 'bet_settlement', 'completed', '2025-02-09 07:40:56', 86, NULL, 'Bet outcome: Loss - Amount: +1000', NULL, 23),
(128, 8, '9000.00', 'bet_settlement', 'completed', '2025-02-09 07:40:56', 86, NULL, 'Bet outcome: Win - Amount: +9000.00', NULL, 23),
(129, 2, '5000.00', 'admin_credit', 'completed', '2025-02-10 06:32:26', NULL, 1, NULL, NULL, NULL),
(130, 2, '11.00', 'admin_credit', 'completed', '2025-02-10 06:33:18', NULL, 1, NULL, NULL, NULL),
(131, 2, '-49984262.00', 'transfer_sent', 'completed', '2025-02-10 06:42:20', NULL, NULL, 'Transfer between users', 4, NULL),
(132, 4, '49984262.00', 'transfer_received', 'completed', '2025-02-10 06:42:20', NULL, NULL, 'Transfer between users', 2, NULL),
(133, 2, '5666.00', 'admin_credit', 'completed', '2025-02-10 18:45:16', NULL, 1, NULL, NULL, NULL),
(134, 9, '10000.00', 'admin_credit', 'completed', '2025-05-17 11:42:43', NULL, 1, NULL, NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `favorite_team` varchar(100) DEFAULT NULL,
  `balance` decimal(10,2) DEFAULT '0.00',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `points` int(11) DEFAULT '0',
  `current_league_id` int(11) DEFAULT NULL,
  `total_points` int(11) DEFAULT '0',
  `current_streak` int(11) DEFAULT '0',
  `highest_streak` int(11) DEFAULT '0',
  `current_season_id` int(11) DEFAULT NULL,
  `last_active` timestamp NULL DEFAULT NULL,
  `role` enum('user','admin') NOT NULL DEFAULT 'user',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `total_bets` int(11) DEFAULT '0',
  `last_bet_date` timestamp NULL DEFAULT NULL,
  `status` enum('active','suspended','banned') DEFAULT 'active',
  `preferred_currency_id` int(11) DEFAULT '1' COMMENT 'User preferred currency for display'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `full_name`, `password_hash`, `email`, `favorite_team`, `balance`, `created_at`, `updated_at`, `points`, `current_league_id`, `total_points`, `current_streak`, `highest_streak`, `current_season_id`, `last_active`, `role`, `wins`, `draws`, `losses`, `total_bets`, `last_bet_date`, `status`, `preferred_currency_id`) VALUES
(1, 'Jamesbong01', 'James Bellengi', '$2y$10$cRil26laQt9KepdLzZwDp.xhM.zntH2yLsBMeAuqaJPfi4dymDh7i', '<EMAIL>', 'Manchester United', '5923206.00', '2024-09-23 15:25:21', '2025-06-07 08:01:25', 9, 7, 9, 0, 0, NULL, NULL, 'admin', 1, 0, 0, 0, NULL, 'suspended', 1),
(2, 'lilwayne', 'Lil Wayne', '$2y$10$FYcSfZpqua0E/NXqcCBzoe38AfqcEeW.EROWgVffLub0Pnui1UDTK', '<EMAIL>', 'Everton', '5666.20', '2024-09-23 15:44:57', '2025-02-10 18:45:16', 3, 11, 3, 0, 0, NULL, NULL, 'user', 1, 0, 4, 3, '2025-02-09 07:40:56', 'active', 1),
(3, 'jameslink01', 'James Link', '$2y$10$upz956oskiIMk07QUxDYXO5i9D/R8mSX0DXcHoC/pybrHKaOWMt66', '<EMAIL>', 'Manchester United', '8103.20', '2024-09-24 17:28:36', '2025-06-07 08:01:40', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 1, 1, '2025-02-09 06:31:04', 'banned', 1),
(4, 'demohomexx', 'Mr Owner', '$2y$10$Tq.WGMvqcPthx/WepKxfpuw2D4mbrEXK0JF4jkLss8zJAms6PDMEC', '<EMAIL>', 'Arsenal', '50087261.20', '2024-10-22 17:22:47', '2025-02-10 06:42:20', 0, 11, 0, 0, 0, NULL, NULL, 'user', 0, 0, 1, 1, '2025-02-09 06:31:04', 'active', 1),
(5, 'Bobyanka01', 'Bobby Yanky', '$2y$10$XPWcZna1yubnBHFwZXDT.OlufpWuXBncqLlvktEg.PP8FuTYPETkq', '<EMAIL>', 'Arsenal', '20630.80', '2024-10-30 11:05:24', '2025-02-09 06:31:04', 3, 11, 3, 0, 0, NULL, NULL, 'user', 1, 0, 0, 1, '2025-02-09 06:31:04', 'active', 1),
(6, 'BlueeyeKing', 'Blue King', '$2y$10$QX9Q8B5nLBxkclgjz/CmnuS03m4xICojFifwkWwcyG4RMKgDFp7Sq', '<EMAIL>', 'Liverpool Fc', '14400.00', '2024-10-30 11:07:25', '2025-02-09 06:31:03', 3, 11, 3, 0, 0, NULL, NULL, 'user', 1, 0, 0, 1, '2025-02-09 06:31:03', 'active', 1),
(7, 'jesm', 'look', '$2y$10$2s9g1YuO8H/4ta9dwZ8UQuF7PopxCreXnnTV.Zu9TgvQuf3xQuEqW', '<EMAIL>', 'Arsenal', '1999.00', '2024-10-31 12:49:30', '2024-10-31 12:49:30', 0, NULL, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0, 0, NULL, 'active', 1),
(8, 'Tegowolo', 'Teg Tega', '$2y$10$NZCrsp4JDQQbVfBbnuBZGOxdrgxuYHI53ES8K4ED.PZaWhpRgYVoq', '<EMAIL>', 'Brentford FC', '23547.40', '2024-11-01 16:24:32', '2025-06-07 09:31:19', 6, 11, 6, 0, 0, NULL, NULL, 'user', 3, 0, 1, 3, '2025-02-09 07:40:56', 'banned', 1),
(9, 'Jamesbong101', 'James Walker', '$2y$10$OT0WGZQ5t8RxV/mQPpn6Juqqqo92EY/aUxrT2bVI2CcSPXqL0IDUi', '<EMAIL>', 'Everton', '11000.00', '2025-05-17 11:42:19', '2025-06-07 09:31:12', 0, NULL, 0, 0, 0, NULL, NULL, 'user', 0, 0, 0, 0, NULL, 'suspended', 1);

--
-- Triggers `users`
--
DELIMITER $$
CREATE TRIGGER `check_achievements` AFTER UPDATE ON `users` FOR EACH ROW BEGIN
    -- Check points-based achievements
    INSERT IGNORE INTO user_achievements (user_id, achievement_id)
    SELECT NEW.user_id, achievement_id
    FROM achievements
    WHERE points_required > 0 
    AND points_required <= NEW.total_points
    AND achievement_id NOT IN (
        SELECT achievement_id 
        FROM user_achievements 
        WHERE user_id = NEW.user_id
    );

    -- Check streak-based achievements
    IF NEW.current_streak > OLD.current_streak THEN
        INSERT IGNORE INTO user_achievements (user_id, achievement_id)
        SELECT NEW.user_id, achievement_id
        FROM achievements
        WHERE streak_required > 0 
        AND streak_required <= NEW.current_streak
        AND achievement_id NOT IN (
            SELECT achievement_id 
            FROM user_achievements 
            WHERE user_id = NEW.user_id
        );
    END IF;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `user_achievements`
--

CREATE TABLE `user_achievements` (
  `user_id` int(11) NOT NULL,
  `achievement_id` int(11) NOT NULL,
  `earned_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_activity_log`
--

CREATE TABLE `user_activity_log` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `activity_type` varchar(50) NOT NULL,
  `details` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Dumping data for table `user_activity_log`
--

INSERT INTO `user_activity_log` (`log_id`, `user_id`, `activity_type`, `details`, `created_at`) VALUES
(1, 2, 'league_join', 'Joined league: UEFA Europa League', '2024-12-15 15:48:49'),
(2, 2, 'league_join', 'Joined league: Katsina Leaguex', '2024-12-15 15:54:29'),
(3, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\"}', '2024-12-15 16:02:50'),
(4, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\"}', '2024-12-15 16:02:53'),
(5, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\"}', '2024-12-15 16:19:27'),
(6, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 18:23:35'),
(7, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 18:25:14'),
(8, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\",\"min_bet\":\"10000.00\"}', '2024-12-15 18:27:52'),
(9, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 20:05:26'),
(10, 2, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 20:07:29'),
(11, 2, 'league_join', '{\"league_id\":2,\"league_name\":\"Calabar League\",\"min_bet\":\"10000.00\"}', '2024-12-15 20:18:01'),
(12, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2024-12-15 22:13:31'),
(13, 4, 'league_join', '{\"league_id\":1,\"league_name\":\"Katsina Leaguex\",\"min_bet\":\"1000.00\"}', '2024-12-15 22:14:01'),
(14, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 16:48:18'),
(15, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:34:02'),
(16, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:45:47'),
(17, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 17:46:03'),
(18, 4, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 18:07:35'),
(19, 2, 'league_join', '{\"league_id\":7,\"league_name\":\"UEFA Europa League\",\"min_bet\":\"1000.00\"}', '2025-01-09 18:07:47');

-- --------------------------------------------------------

--
-- Table structure for table `user_friends`
--

CREATE TABLE `user_friends` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `friend_id` int(11) NOT NULL,
  `status` enum('pending','accepted','rejected') DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_friends`
--

INSERT INTO `user_friends` (`id`, `user_id`, `friend_id`, `status`, `created_at`) VALUES
(2, 2, 4, 'accepted', '2024-11-30 16:20:42'),
(3, 4, 1, 'accepted', '2024-11-30 18:07:44'),
(4, 2, 1, 'accepted', '2024-11-30 18:38:05'),
(5, 2, 6, 'accepted', '2024-11-30 18:44:06'),
(6, 2, 8, 'pending', '2024-11-30 18:46:57'),
(7, 4, 5, 'accepted', '2025-01-10 04:47:38');

-- --------------------------------------------------------

--
-- Table structure for table `user_leagues`
--

CREATE TABLE `user_leagues` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_leagues`
--

INSERT INTO `user_leagues` (`id`, `user_id`, `league_id`, `join_date`, `status`, `created_at`) VALUES
(19, 2, 11, '2025-02-01 09:22:35', 'active', '2025-02-01 11:22:35'),
(20, 5, 11, '2025-02-01 09:31:54', 'active', '2025-02-01 11:31:54'),
(21, 3, 11, '2025-02-01 09:33:27', 'active', '2025-02-01 11:33:27'),
(22, 4, 11, '2025-02-01 09:35:30', 'active', '2025-02-01 11:35:30'),
(23, 1, 7, '2025-02-01 12:44:07', 'active', '2025-02-01 14:44:07'),
(24, 6, 11, '2025-02-01 12:45:30', 'active', '2025-02-01 14:45:30'),
(25, 8, 11, '2025-02-01 13:31:13', 'active', '2025-02-01 15:31:13');

-- --------------------------------------------------------

--
-- Table structure for table `user_leagues_backup`
--

CREATE TABLE `user_leagues_backup` (
  `id` int(11) NOT NULL DEFAULT '0',
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `join_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `status` enum('pending','active','suspended','completed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `user_leagues_backup`
--

INSERT INTO `user_leagues_backup` (`id`, `user_id`, `league_id`, `join_date`, `status`, `created_at`) VALUES
(20, 4, 7, '2025-01-09 16:30:51', 'active', '2025-01-09 18:30:51'),
(21, 2, 7, '2025-01-09 16:31:09', 'active', '2025-01-09 18:31:09'),
(22, 3, 7, '2025-01-10 02:19:35', 'active', '2025-01-10 04:19:35'),
(23, 5, 7, '2025-01-10 02:22:25', 'active', '2025-01-10 04:22:25'),
(29, 1, 1, '2025-01-30 14:27:55', 'active', '2025-01-30 16:27:55'),
(30, 8, 1, '2025-01-30 14:31:07', 'active', '2025-01-30 16:31:07');

-- --------------------------------------------------------

--
-- Table structure for table `user_league_stats`
--

CREATE TABLE `user_league_stats` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `streak` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_league_stats`
--

INSERT INTO `user_league_stats` (`id`, `user_id`, `league_id`, `points`, `wins`, `draws`, `losses`, `streak`, `created_at`, `updated_at`) VALUES
(2, 2, 11, 3, 1, 0, 4, 0, '2025-02-01 11:22:35', '2025-02-09 07:40:56'),
(3, 5, 11, 3, 1, 0, 0, 0, '2025-02-01 11:31:55', '2025-02-09 06:31:04'),
(4, 3, 11, 0, 0, 0, 1, 0, '2025-02-01 11:33:27', '2025-02-09 06:31:04'),
(5, 4, 11, 0, 0, 0, 1, 0, '2025-02-01 11:35:31', '2025-02-09 06:31:04'),
(6, 1, 7, 3, 1, 0, 0, 0, '2025-02-01 14:44:07', '2025-02-01 15:30:20'),
(7, 6, 11, 3, 1, 0, 0, 0, '2025-02-01 14:45:30', '2025-02-09 06:31:04'),
(18, 8, 11, 9, 3, 0, 0, 0, '2025-02-01 15:31:13', '2025-02-09 07:40:56');

-- --------------------------------------------------------

--
-- Table structure for table `user_league_stats_backup`
--

CREATE TABLE `user_league_stats_backup` (
  `id` int(11) NOT NULL DEFAULT '0',
  `user_id` int(11) NOT NULL,
  `league_id` int(11) NOT NULL,
  `points` int(11) DEFAULT '0',
  `wins` int(11) DEFAULT '0',
  `draws` int(11) DEFAULT '0',
  `losses` int(11) DEFAULT '0',
  `streak` int(11) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_otp`
--

CREATE TABLE `user_otp` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `otp` varchar(6) NOT NULL,
  `expiry` datetime NOT NULL,
  `attempts` int(11) DEFAULT '0',
  `used` tinyint(1) DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- --------------------------------------------------------

--
-- Structure for view `admin_auth_status`
--
DROP TABLE IF EXISTS `admin_auth_status`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `admin_auth_status`  AS SELECT `a`.`admin_id` AS `admin_id`, `a`.`username` AS `username`, `a`.`email` AS `email`, `a`.`role` AS `role`, `a`.`auth_method` AS `auth_method`, `a`.`two_factor_enabled` AS `two_factor_enabled`, `a`.`account_locked_until` AS `account_locked_until`, `a`.`failed_login_attempts` AS `failed_login_attempts`, `a2fa`.`is_enabled` AS `has_2fa_setup`, `a2fa`.`auth_type` AS `preferred_2fa_type`, `a2fa`.`setup_completed` AS `is_2fa_setup_complete`, (case when ((`a`.`account_locked_until` is not null) and (`a`.`account_locked_until` > now())) then 'locked' when ((`a`.`two_factor_enabled` = 1) and (`a2fa`.`setup_completed` = 1)) then 'secure' when (`a`.`auth_method` = 'password_only') then 'basic' else 'setup_required' end) AS `security_status` FROM (`admins` `a` left join `admin_2fa` `a2fa` on((`a`.`admin_id` = `a2fa`.`admin_id`)))  ;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `achievements`
--
ALTER TABLE `achievements`
  ADD PRIMARY KEY (`achievement_id`);

--
-- Indexes for table `adminactions`
--
ALTER TABLE `adminactions`
  ADD PRIMARY KEY (`action_id`),
  ADD KEY `admin_id` (`admin_id`);

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`admin_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_admin_username` (`username`),
  ADD KEY `idx_admin_email` (`email`);

--
-- Indexes for table `admin_2fa`
--
ALTER TABLE `admin_2fa`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_admin_2fa` (`admin_id`);

--
-- Indexes for table `admin_auth_logs`
--
ALTER TABLE `admin_auth_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_auth_logs_admin_id` (`admin_id`),
  ADD KEY `idx_admin_auth_logs_created_at` (`created_at`);

--
-- Indexes for table `admin_auth_settings`
--
ALTER TABLE `admin_auth_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`);

--
-- Indexes for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_login_attempts_ip` (`ip_address`),
  ADD KEY `idx_admin_login_attempts_admin_id` (`admin_id`);

--
-- Indexes for table `admin_otp`
--
ALTER TABLE `admin_otp`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_otp_admin_id` (`admin_id`),
  ADD KEY `idx_admin_otp_expires` (`expires_at`);

--
-- Indexes for table `admin_recovery_codes`
--
ALTER TABLE `admin_recovery_codes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_admin_recovery_admin_id` (`admin_id`),
  ADD KEY `idx_admin_recovery_code` (`recovery_code`),
  ADD KEY `idx_admin_recovery_expires` (`expires_at`);

--
-- Indexes for table `admin_recovery_tokens`
--
ALTER TABLE `admin_recovery_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_admin_token` (`admin_id`),
  ADD KEY `idx_admin_recovery_token` (`access_token`),
  ADD KEY `idx_admin_recovery_token_expires` (`expires_at`);

--
-- Indexes for table `bets`
--
ALTER TABLE `bets`
  ADD PRIMARY KEY (`bet_id`),
  ADD KEY `idx_bet_challenge_id` (`challenge_id`),
  ADD KEY `user1_id` (`user1_id`),
  ADD KEY `user2_id` (`user2_id`),
  ADD KEY `team1_id` (`team1_id`),
  ADD KEY `team2_id` (`team2_id`),
  ADD KEY `idx_bets_user1` (`user1_id`),
  ADD KEY `idx_bets_user2` (`user2_id`),
  ADD KEY `idx_bets_challenge` (`challenge_id`);

--
-- Indexes for table `challenges`
--
ALTER TABLE `challenges`
  ADD PRIMARY KEY (`challenge_id`),
  ADD KEY `idx_challenge_admin_id` (`admin_id`),
  ADD KEY `idx_challenge_status` (`status`);

--
-- Indexes for table `credit_requests`
--
ALTER TABLE `credit_requests`
  ADD PRIMARY KEY (`request_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `payment_method_id` (`payment_method_id`),
  ADD KEY `approved_by` (`approved_by`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `currency_code` (`currency_code`),
  ADD KEY `idx_currency_code` (`currency_code`),
  ADD KEY `idx_active` (`is_active`);

--
-- Indexes for table `exchange_rates`
--
ALTER TABLE `exchange_rates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `updated_by_admin_id` (`updated_by_admin_id`),
  ADD KEY `idx_currency_id` (`currency_id`),
  ADD KEY `idx_updated_at` (`updated_at`);

--
-- Indexes for table `general_settings`
--
ALTER TABLE `general_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`);

--
-- Indexes for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD PRIMARY KEY (`leaderboard_id`),
  ADD KEY `idx_leaderboard_user_id` (`user_id`),
  ADD KEY `idx_leaderboard_points` (`points`);

--
-- Indexes for table `leagues`
--
ALTER TABLE `leagues`
  ADD PRIMARY KEY (`league_id`),
  ADD UNIQUE KEY `idx_league_name` (`name`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `season_id` (`season_id`),
  ADD KEY `membership_id` (`membership_id`),
  ADD KEY `idx_league_audit_admin` (`admin_id`),
  ADD KEY `idx_league_audit_league` (`league_id`);

--
-- Indexes for table `league_history`
--
ALTER TABLE `league_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `league_id` (`league_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_league_history_season` (`season_id`),
  ADD KEY `idx_league_history_user` (`user_id`);

--
-- Indexes for table `league_memberships`
--
ALTER TABLE `league_memberships`
  ADD PRIMARY KEY (`membership_id`),
  ADD UNIQUE KEY `idx_unique_active_membership` (`user_id`,`league_id`,`status`),
  ADD UNIQUE KEY `unique_active_membership` (`league_id`,`active_membership`),
  ADD KEY `approved_by` (`approved_by`),
  ADD KEY `idx_league_memberships_user` (`user_id`),
  ADD KEY `idx_league_memberships_league` (`league_id`),
  ADD KEY `idx_league_memberships_season` (`season_id`),
  ADD KEY `fk_league_memberships_user_league` (`user_league_id`);

--
-- Indexes for table `league_seasons`
--
ALTER TABLE `league_seasons`
  ADD PRIMARY KEY (`season_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_league_seasons_league` (`league_id`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_sender` (`sender_id`),
  ADD KEY `idx_recipient` (`recipient_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  ADD PRIMARY KEY (`verification_id`),
  ADD KEY `verified_by` (`verified_by`),
  ADD KEY `idx_payment_verification_user_id` (`user_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `seasons`
--
ALTER TABLE `seasons`
  ADD PRIMARY KEY (`season_id`);

--
-- Indexes for table `season_history`
--
ALTER TABLE `season_history`
  ADD PRIMARY KEY (`history_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `season_id` (`season_id`);

--
-- Indexes for table `security_settings`
--
ALTER TABLE `security_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`);

--
-- Indexes for table `smtp_settings`
--
ALTER TABLE `smtp_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`setting_key`);

--
-- Indexes for table `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `transactions`
--
ALTER TABLE `transactions`
  ADD PRIMARY KEY (`transaction_id`),
  ADD KEY `related_bet_id` (`related_bet_id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `idx_transaction_user_id` (`user_id`),
  ADD KEY `related_user_id` (`related_user_id`),
  ADD KEY `related_challenge_id` (`related_challenge_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`user_id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_user_username` (`username`),
  ADD KEY `idx_user_email` (`email`),
  ADD KEY `fk_user_current_season` (`current_season_id`),
  ADD KEY `idx_last_active` (`last_active`),
  ADD KEY `idx_users_league` (`current_league_id`),
  ADD KEY `idx_preferred_currency` (`preferred_currency_id`);

--
-- Indexes for table `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD PRIMARY KEY (`user_id`,`achievement_id`),
  ADD KEY `achievement_id` (`achievement_id`);

--
-- Indexes for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD PRIMARY KEY (`log_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_friends`
--
ALTER TABLE `user_friends`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_friendship` (`user_id`,`friend_id`),
  ADD KEY `friend_id` (`friend_id`);

--
-- Indexes for table `user_leagues`
--
ALTER TABLE `user_leagues`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_league` (`user_id`,`league_id`),
  ADD KEY `fk_user_leagues_league` (`league_id`);

--
-- Indexes for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_league_stats` (`user_id`,`league_id`),
  ADD UNIQUE KEY `user_league_unique` (`user_id`,`league_id`),
  ADD KEY `fk_user_league_stats_league` (`league_id`);

--
-- Indexes for table `user_otp`
--
ALTER TABLE `user_otp`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `achievements`
--
ALTER TABLE `achievements`
  MODIFY `achievement_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `adminactions`
--
ALTER TABLE `adminactions`
  MODIFY `action_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `admin_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `admin_2fa`
--
ALTER TABLE `admin_2fa`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admin_auth_logs`
--
ALTER TABLE `admin_auth_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=120;

--
-- AUTO_INCREMENT for table `admin_auth_settings`
--
ALTER TABLE `admin_auth_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_otp`
--
ALTER TABLE `admin_otp`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `admin_recovery_codes`
--
ALTER TABLE `admin_recovery_codes`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_recovery_tokens`
--
ALTER TABLE `admin_recovery_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `bets`
--
ALTER TABLE `bets`
  MODIFY `bet_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=91;

--
-- AUTO_INCREMENT for table `challenges`
--
ALTER TABLE `challenges`
  MODIFY `challenge_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=32;

--
-- AUTO_INCREMENT for table `credit_requests`
--
ALTER TABLE `credit_requests`
  MODIFY `request_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `exchange_rates`
--
ALTER TABLE `exchange_rates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `general_settings`
--
ALTER TABLE `general_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `leaderboards`
--
ALTER TABLE `leaderboards`
  MODIFY `leaderboard_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `leagues`
--
ALTER TABLE `leagues`
  MODIFY `league_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `league_history`
--
ALTER TABLE `league_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `league_memberships`
--
ALTER TABLE `league_memberships`
  MODIFY `membership_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `league_seasons`
--
ALTER TABLE `league_seasons`
  MODIFY `season_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- AUTO_INCREMENT for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  MODIFY `verification_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `seasons`
--
ALTER TABLE `seasons`
  MODIFY `season_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `season_history`
--
ALTER TABLE `season_history`
  MODIFY `history_id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `security_settings`
--
ALTER TABLE `security_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `smtp_settings`
--
ALTER TABLE `smtp_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `teams`
--
ALTER TABLE `teams`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=29;

--
-- AUTO_INCREMENT for table `transactions`
--
ALTER TABLE `transactions`
  MODIFY `transaction_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=135;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  MODIFY `log_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `user_friends`
--
ALTER TABLE `user_friends`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `user_leagues`
--
ALTER TABLE `user_leagues`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `user_otp`
--
ALTER TABLE `user_otp`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `adminactions`
--
ALTER TABLE `adminactions`
  ADD CONSTRAINT `adminactions_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `admin_2fa`
--
ALTER TABLE `admin_2fa`
  ADD CONSTRAINT `admin_2fa_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_auth_logs`
--
ALTER TABLE `admin_auth_logs`
  ADD CONSTRAINT `admin_auth_logs_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_login_attempts`
--
ALTER TABLE `admin_login_attempts`
  ADD CONSTRAINT `admin_login_attempts_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_otp`
--
ALTER TABLE `admin_otp`
  ADD CONSTRAINT `admin_otp_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_recovery_codes`
--
ALTER TABLE `admin_recovery_codes`
  ADD CONSTRAINT `admin_recovery_codes_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_recovery_tokens`
--
ALTER TABLE `admin_recovery_tokens`
  ADD CONSTRAINT `admin_recovery_tokens_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`) ON DELETE CASCADE;

--
-- Constraints for table `bets`
--
ALTER TABLE `bets`
  ADD CONSTRAINT `bets_ibfk_1` FOREIGN KEY (`user1_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `bets_ibfk_2` FOREIGN KEY (`user2_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `bets_ibfk_3` FOREIGN KEY (`team1_id`) REFERENCES `teams` (`id`),
  ADD CONSTRAINT `bets_ibfk_4` FOREIGN KEY (`team2_id`) REFERENCES `teams` (`id`);

--
-- Constraints for table `challenges`
--
ALTER TABLE `challenges`
  ADD CONSTRAINT `challenges_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `credit_requests`
--
ALTER TABLE `credit_requests`
  ADD CONSTRAINT `credit_requests_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `credit_requests_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`),
  ADD CONSTRAINT `credit_requests_ibfk_3` FOREIGN KEY (`approved_by`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `exchange_rates`
--
ALTER TABLE `exchange_rates`
  ADD CONSTRAINT `exchange_rates_ibfk_1` FOREIGN KEY (`currency_id`) REFERENCES `currencies` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `exchange_rates_ibfk_2` FOREIGN KEY (`updated_by_admin_id`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `leaderboards`
--
ALTER TABLE `leaderboards`
  ADD CONSTRAINT `leaderboards_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `leagues`
--
ALTER TABLE `leagues`
  ADD CONSTRAINT `leagues_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_audit_log`
--
ALTER TABLE `league_audit_log`
  ADD CONSTRAINT `league_audit_log_ibfk_1` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`),
  ADD CONSTRAINT `league_audit_log_ibfk_3` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_audit_log_ibfk_4` FOREIGN KEY (`membership_id`) REFERENCES `league_memberships` (`membership_id`);

--
-- Constraints for table `league_history`
--
ALTER TABLE `league_history`
  ADD CONSTRAINT `league_history_ibfk_1` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_history_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `league_history_ibfk_3` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_history_ibfk_4` FOREIGN KEY (`verified_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_memberships`
--
ALTER TABLE `league_memberships`
  ADD CONSTRAINT `fk_league_memberships_user_league` FOREIGN KEY (`user_league_id`) REFERENCES `user_leagues` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `league_memberships_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `league_memberships_ibfk_2` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_memberships_ibfk_3` FOREIGN KEY (`season_id`) REFERENCES `league_seasons` (`season_id`),
  ADD CONSTRAINT `league_memberships_ibfk_4` FOREIGN KEY (`approved_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `league_seasons`
--
ALTER TABLE `league_seasons`
  ADD CONSTRAINT `league_seasons_ibfk_1` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`),
  ADD CONSTRAINT `league_seasons_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`sender_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `messages_ibfk_2` FOREIGN KEY (`recipient_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `paymentverifications`
--
ALTER TABLE `paymentverifications`
  ADD CONSTRAINT `paymentverifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `paymentverifications_ibfk_2` FOREIGN KEY (`verified_by`) REFERENCES `admins` (`admin_id`);

--
-- Constraints for table `season_history`
--
ALTER TABLE `season_history`
  ADD CONSTRAINT `season_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `season_history_ibfk_2` FOREIGN KEY (`season_id`) REFERENCES `seasons` (`season_id`) ON DELETE CASCADE;

--
-- Constraints for table `transactions`
--
ALTER TABLE `transactions`
  ADD CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `transactions_ibfk_3` FOREIGN KEY (`admin_id`) REFERENCES `admins` (`admin_id`),
  ADD CONSTRAINT `transactions_ibfk_4` FOREIGN KEY (`related_user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `transactions_ibfk_5` FOREIGN KEY (`related_challenge_id`) REFERENCES `challenges` (`challenge_id`);

--
-- Constraints for table `users`
--
ALTER TABLE `users`
  ADD CONSTRAINT `fk_current_league` FOREIGN KEY (`current_league_id`) REFERENCES `leagues` (`league_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_current_league` FOREIGN KEY (`current_league_id`) REFERENCES `leagues` (`league_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_current_season` FOREIGN KEY (`current_season_id`) REFERENCES `seasons` (`season_id`) ON DELETE SET NULL,
  ADD CONSTRAINT `users_ibfk_1` FOREIGN KEY (`preferred_currency_id`) REFERENCES `currencies` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_achievements`
--
ALTER TABLE `user_achievements`
  ADD CONSTRAINT `user_achievements_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `user_achievements_ibfk_2` FOREIGN KEY (`achievement_id`) REFERENCES `achievements` (`achievement_id`);

--
-- Constraints for table `user_activity_log`
--
ALTER TABLE `user_activity_log`
  ADD CONSTRAINT `user_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `user_friends`
--
ALTER TABLE `user_friends`
  ADD CONSTRAINT `user_friends_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`),
  ADD CONSTRAINT `user_friends_ibfk_2` FOREIGN KEY (`friend_id`) REFERENCES `users` (`user_id`);

--
-- Constraints for table `user_leagues`
--
ALTER TABLE `user_leagues`
  ADD CONSTRAINT `fk_user_leagues_league` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_leagues_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_league_stats`
--
ALTER TABLE `user_league_stats`
  ADD CONSTRAINT `fk_user_league_stats_league` FOREIGN KEY (`league_id`) REFERENCES `leagues` (`league_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  ADD CONSTRAINT `fk_user_league_stats_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE;

--
-- Constraints for table `user_otp`
--
ALTER TABLE `user_otp`
  ADD CONSTRAINT `user_otp_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
