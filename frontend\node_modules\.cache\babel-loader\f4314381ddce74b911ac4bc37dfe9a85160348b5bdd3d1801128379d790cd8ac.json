{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n * \n * Provides admin interface for managing currencies and exchange rates\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport './CurrencyManagement.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CurrencyManagement = () => {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [editingRate, setEditingRate] = useState(null);\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const {\n    loading,\n    error,\n    execute\n  } = useApiService({\n    onError: error => {\n      console.error('Currency Management Error:', error);\n    }\n  });\n  const {\n    refreshCurrencyData\n  } = useCurrency();\n\n  // Load currencies and exchange rates\n  const loadData = useCallback(async () => {\n    try {\n      const [currenciesResponse, ratesResponse] = await Promise.all([execute(() => currencyService.getCurrencies(false)),\n      // Get all currencies including inactive\n      execute(() => currencyService.getExchangeRates())]);\n      if (currenciesResponse.success) {\n        setCurrencies(currenciesResponse.data.currencies || []);\n      }\n      if (ratesResponse.success) {\n        setExchangeRates(ratesResponse.data.exchange_rates || []);\n      }\n    } catch (error) {\n      console.error('Failed to load currency data:', error);\n    }\n  }, [execute]);\n\n  // Update exchange rate\n  const handleUpdateRate = useCallback(async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      alert('Please enter a valid exchange rate');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        alert('Exchange rate updated successfully!');\n      } else {\n        alert(response.message || 'Failed to update exchange rate');\n      }\n    } catch (error) {\n      alert('Error updating exchange rate: ' + error.message);\n    }\n  }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n  // Toggle currency active status\n  const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('toggle', {\n        currency_id: currencyId\n      }));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n      } else {\n        alert(response.message || 'Failed to toggle currency status');\n      }\n    } catch (error) {\n      alert('Error toggling currency: ' + error.message);\n    }\n  }, [execute, loadData, refreshCurrencyData]);\n\n  // Add new currency\n  const handleAddCurrency = useCallback(async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      alert('Please fill in all required fields');\n      return;\n    }\n    try {\n      const response = await execute(() => currencyService.manageCurrencies('create', newCurrency));\n      if (response.success) {\n        await loadData();\n        await refreshCurrencyData();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        alert('Currency added successfully!');\n      } else {\n        alert(response.message || 'Failed to add currency');\n      }\n    } catch (error) {\n      alert('Error adding currency: ' + error.message);\n    }\n  }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n  // Get exchange rate for currency\n  const getExchangeRate = useCallback(currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  }, [exchangeRates]);\n\n  // Format date\n  const formatDate = useCallback(dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }, []);\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, [loadData]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"currency-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Currency Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage supported currencies and exchange rates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddCurrency(true),\n        children: \"Add New Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\u26A0\\uFE0F \", error.message || 'An error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-secondary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 17\n    }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"currencies-grid\",\n      children: currencies.map(currency => {\n        const rate = getExchangeRate(currency.id);\n        const isEditing = editingRate === currency.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `currency-card ${!currency.is_active ? 'inactive' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: currency.currency_code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: currency.currency_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"currency-symbol\",\n                children: currency.currency_symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${currency.is_active ? 'active' : 'inactive'}`,\n                children: currency.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exchange-rate-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Exchange Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 37\n            }, this), rate ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rate-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-rate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rate-value\",\n                  children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Updated: \", formatDate(rate.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 209,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 45\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rate-edit-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.0001\",\n                  value: newRate,\n                  onChange: e => setNewRate(e.target.value),\n                  placeholder: \"New rate\",\n                  className: \"rate-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: notes,\n                  onChange: e => setNotes(e.target.value),\n                  placeholder: \"Update notes (optional)\",\n                  className: \"notes-input\",\n                  rows: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"edit-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleUpdateRate(currency.id),\n                    className: \"btn btn-success btn-sm\",\n                    disabled: loading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRate(null);\n                      setNewRate('');\n                      setNotes('');\n                    },\n                    className: \"btn btn-secondary btn-sm\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate(rate.rate_to_fancoin.toString());\n                },\n                className: \"btn btn-outline btn-sm\",\n                children: \"Update Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 250,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 204,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-rate\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No exchange rate set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate('');\n                },\n                className: \"btn btn-primary btn-sm\",\n                children: \"Set Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n              className: `btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`,\n              disabled: loading,\n              children: currency.is_active ? 'Deactivate' : 'Activate'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 278,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 33\n          }, this)]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 17\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 316,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 294,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 154,\n    columnNumber: 9\n  }, this);\n};\n_s(CurrencyManagement, \"R7YIZMxmWKkUgEBgHtABNmrqBZ8=\", false, function () {\n  return [useApiService, useCurrency];\n});\n_c = CurrencyManagement;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "currencyService", "useApiService", "useCurrency", "jsxDEV", "_jsxDEV", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "editingRate", "setEditingRate", "newRate", "setNewRate", "notes", "setNotes", "showAddCurrency", "setShowAddCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "loading", "error", "execute", "onError", "console", "refreshCurrencyData", "loadData", "currenciesResponse", "ratesResponse", "Promise", "all", "getCurrencies", "getExchangeRates", "success", "data", "exchange_rates", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "alert", "response", "updateExchangeRate", "message", "handleToggleCurrency", "currentStatus", "manageCurrencies", "currency_id", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "length", "map", "currency", "id", "isEditing", "rate_to_fancoin", "updated_at", "type", "step", "value", "onChange", "e", "target", "placeholder", "rows", "disabled", "toString", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n * \n * Provides admin interface for managing currencies and exchange rates\n */\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport './CurrencyManagement.css';\n\nconst CurrencyManagement = () => {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [editingRate, setEditingRate] = useState(null);\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n\n    const { loading, error, execute } = useApiService({\n        onError: (error) => {\n            console.error('Currency Management Error:', error);\n        }\n    });\n\n    const { refreshCurrencyData } = useCurrency();\n\n    // Load currencies and exchange rates\n    const loadData = useCallback(async () => {\n        try {\n            const [currenciesResponse, ratesResponse] = await Promise.all([\n                execute(() => currencyService.getCurrencies(false)), // Get all currencies including inactive\n                execute(() => currencyService.getExchangeRates())\n            ]);\n\n            if (currenciesResponse.success) {\n                setCurrencies(currenciesResponse.data.currencies || []);\n            }\n\n            if (ratesResponse.success) {\n                setExchangeRates(ratesResponse.data.exchange_rates || []);\n            }\n        } catch (error) {\n            console.error('Failed to load currency data:', error);\n        }\n    }, [execute]);\n\n    // Update exchange rate\n    const handleUpdateRate = useCallback(async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            alert('Please enter a valid exchange rate');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                alert('Exchange rate updated successfully!');\n            } else {\n                alert(response.message || 'Failed to update exchange rate');\n            }\n        } catch (error) {\n            alert('Error updating exchange rate: ' + error.message);\n        }\n    }, [newRate, notes, execute, loadData, refreshCurrencyData]);\n\n    // Toggle currency active status\n    const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('toggle', { currency_id: currencyId })\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n            } else {\n                alert(response.message || 'Failed to toggle currency status');\n            }\n        } catch (error) {\n            alert('Error toggling currency: ' + error.message);\n        }\n    }, [execute, loadData, refreshCurrencyData]);\n\n    // Add new currency\n    const handleAddCurrency = useCallback(async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            alert('Please fill in all required fields');\n            return;\n        }\n\n        try {\n            const response = await execute(() => \n                currencyService.manageCurrencies('create', newCurrency)\n            );\n\n            if (response.success) {\n                await loadData();\n                await refreshCurrencyData();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                alert('Currency added successfully!');\n            } else {\n                alert(response.message || 'Failed to add currency');\n            }\n        } catch (error) {\n            alert('Error adding currency: ' + error.message);\n        }\n    }, [newCurrency, execute, loadData, refreshCurrencyData]);\n\n    // Get exchange rate for currency\n    const getExchangeRate = useCallback((currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    }, [exchangeRates]);\n\n    // Format date\n    const formatDate = useCallback((dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    }, []);\n\n    // Load data on component mount\n    useEffect(() => {\n        loadData();\n    }, [loadData]);\n\n    return (\n        <div className=\"currency-management\">\n            <div className=\"page-header\">\n                <h1>Currency Management</h1>\n                <p>Manage supported currencies and exchange rates</p>\n                <button \n                    className=\"btn btn-primary\"\n                    onClick={() => setShowAddCurrency(true)}\n                >\n                    Add New Currency\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <p>⚠️ {error.message || 'An error occurred'}</p>\n                    <button onClick={loadData} className=\"btn btn-secondary\">\n                        Retry\n                    </button>\n                </div>\n            )}\n\n            {loading && currencies.length === 0 ? (\n                <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                    <p>Loading currencies...</p>\n                </div>\n            ) : (\n                <div className=\"currencies-grid\">\n                    {currencies.map((currency) => {\n                        const rate = getExchangeRate(currency.id);\n                        const isEditing = editingRate === currency.id;\n\n                        return (\n                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>\n                                <div className=\"currency-header\">\n                                    <div className=\"currency-info\">\n                                        <h3>{currency.currency_code}</h3>\n                                        <p>{currency.currency_name}</p>\n                                        <span className=\"currency-symbol\">{currency.currency_symbol}</span>\n                                    </div>\n                                    <div className=\"currency-status\">\n                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>\n                                            {currency.is_active ? 'Active' : 'Inactive'}\n                                        </span>\n                                    </div>\n                                </div>\n\n                                <div className=\"exchange-rate-section\">\n                                    <h4>Exchange Rate</h4>\n                                    {rate ? (\n                                        <div className=\"rate-info\">\n                                            <div className=\"current-rate\">\n                                                <span className=\"rate-value\">\n                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                </span>\n                                                <small>Updated: {formatDate(rate.updated_at)}</small>\n                                            </div>\n\n                                            {isEditing ? (\n                                                <div className=\"rate-edit-form\">\n                                                    <input\n                                                        type=\"number\"\n                                                        step=\"0.0001\"\n                                                        value={newRate}\n                                                        onChange={(e) => setNewRate(e.target.value)}\n                                                        placeholder=\"New rate\"\n                                                        className=\"rate-input\"\n                                                    />\n                                                    <textarea\n                                                        value={notes}\n                                                        onChange={(e) => setNotes(e.target.value)}\n                                                        placeholder=\"Update notes (optional)\"\n                                                        className=\"notes-input\"\n                                                        rows=\"2\"\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button \n                                                            onClick={() => handleUpdateRate(currency.id)}\n                                                            className=\"btn btn-success btn-sm\"\n                                                            disabled={loading}\n                                                        >\n                                                            Save\n                                                        </button>\n                                                        <button \n                                                            onClick={() => {\n                                                                setEditingRate(null);\n                                                                setNewRate('');\n                                                                setNotes('');\n                                                            }}\n                                                            className=\"btn btn-secondary btn-sm\"\n                                                        >\n                                                            Cancel\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <button \n                                                    onClick={() => {\n                                                        setEditingRate(currency.id);\n                                                        setNewRate(rate.rate_to_fancoin.toString());\n                                                    }}\n                                                    className=\"btn btn-outline btn-sm\"\n                                                >\n                                                    Update Rate\n                                                </button>\n                                            )}\n                                        </div>\n                                    ) : (\n                                        <div className=\"no-rate\">\n                                            <p>No exchange rate set</p>\n                                            <button \n                                                onClick={() => {\n                                                    setEditingRate(currency.id);\n                                                    setNewRate('');\n                                                }}\n                                                className=\"btn btn-primary btn-sm\"\n                                            >\n                                                Set Rate\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"currency-actions\">\n                                    <button \n                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}\n                                        disabled={loading}\n                                    >\n                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                    </button>\n                                </div>\n                            </div>\n                        );\n                    })}\n                </div>\n            )}\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,eAAe,QAAQ,aAAa;AAC7C,OAAOC,aAAa,MAAM,wBAAwB;AAClD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACoB,eAAe,EAAEC,kBAAkB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC;IAC3CwB,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EAEF,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC;EAAQ,CAAC,GAAG1B,aAAa,CAAC;IAC9C2B,OAAO,EAAGF,KAAK,IAAK;MAChBG,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACtD;EACJ,CAAC,CAAC;EAEF,MAAM;IAAEI;EAAoB,CAAC,GAAG5B,WAAW,CAAC,CAAC;;EAE7C;EACA,MAAM6B,QAAQ,GAAGhC,WAAW,CAAC,YAAY;IACrC,IAAI;MACA,MAAM,CAACiC,kBAAkB,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CAC1DR,OAAO,CAAC,MAAM3B,eAAe,CAACoC,aAAa,CAAC,KAAK,CAAC,CAAC;MAAE;MACrDT,OAAO,CAAC,MAAM3B,eAAe,CAACqC,gBAAgB,CAAC,CAAC,CAAC,CACpD,CAAC;MAEF,IAAIL,kBAAkB,CAACM,OAAO,EAAE;QAC5B9B,aAAa,CAACwB,kBAAkB,CAACO,IAAI,CAAChC,UAAU,IAAI,EAAE,CAAC;MAC3D;MAEA,IAAI0B,aAAa,CAACK,OAAO,EAAE;QACvB5B,gBAAgB,CAACuB,aAAa,CAACM,IAAI,CAACC,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOd,KAAK,EAAE;MACZG,OAAO,CAACH,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD;EACJ,CAAC,EAAE,CAACC,OAAO,CAAC,CAAC;;EAEb;EACA,MAAMc,gBAAgB,GAAG1C,WAAW,CAAC,MAAO2C,UAAU,IAAK;IACvD,IAAI,CAAC7B,OAAO,IAAI8B,KAAK,CAACC,UAAU,CAAC/B,OAAO,CAAC,CAAC,EAAE;MACxCgC,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMnB,OAAO,CAAC,MAC3B3B,eAAe,CAAC+C,kBAAkB,CAACL,UAAU,EAAEE,UAAU,CAAC/B,OAAO,CAAC,EAAEE,KAAK,CAC7E,CAAC;MAED,IAAI+B,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMP,QAAQ,CAAC,CAAC;QAChB,MAAMD,mBAAmB,CAAC,CAAC;QAC3BlB,cAAc,CAAC,IAAI,CAAC;QACpBE,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZ6B,KAAK,CAAC,qCAAqC,CAAC;MAChD,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,gCAAgC,CAAC;MAC/D;IACJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACZmB,KAAK,CAAC,gCAAgC,GAAGnB,KAAK,CAACsB,OAAO,CAAC;IAC3D;EACJ,CAAC,EAAE,CAACnC,OAAO,EAAEE,KAAK,EAAEY,OAAO,EAAEI,QAAQ,EAAED,mBAAmB,CAAC,CAAC;;EAE5D;EACA,MAAMmB,oBAAoB,GAAGlD,WAAW,CAAC,OAAO2C,UAAU,EAAEQ,aAAa,KAAK;IAC1E,IAAI;MACA,MAAMJ,QAAQ,GAAG,MAAMnB,OAAO,CAAC,MAC3B3B,eAAe,CAACmD,gBAAgB,CAAC,QAAQ,EAAE;QAAEC,WAAW,EAAEV;MAAW,CAAC,CAC1E,CAAC;MAED,IAAII,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMP,QAAQ,CAAC,CAAC;QAChB,MAAMD,mBAAmB,CAAC,CAAC;QAC3Be,KAAK,CAAC,YAAYK,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;MAClF,CAAC,MAAM;QACHL,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,kCAAkC,CAAC;MACjE;IACJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACZmB,KAAK,CAAC,2BAA2B,GAAGnB,KAAK,CAACsB,OAAO,CAAC;IACtD;EACJ,CAAC,EAAE,CAACrB,OAAO,EAAEI,QAAQ,EAAED,mBAAmB,CAAC,CAAC;;EAE5C;EACA,MAAMuB,iBAAiB,GAAGtD,WAAW,CAAC,YAAY;IAC9C,IAAI,CAACoB,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1FsB,KAAK,CAAC,oCAAoC,CAAC;MAC3C;IACJ;IAEA,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMnB,OAAO,CAAC,MAC3B3B,eAAe,CAACmD,gBAAgB,CAAC,QAAQ,EAAEhC,WAAW,CAC1D,CAAC;MAED,IAAI2B,QAAQ,CAACR,OAAO,EAAE;QAClB,MAAMP,QAAQ,CAAC,CAAC;QAChB,MAAMD,mBAAmB,CAAC,CAAC;QAC3BZ,kBAAkB,CAAC,KAAK,CAAC;QACzBE,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACFqB,KAAK,CAAC,8BAA8B,CAAC;MACzC,CAAC,MAAM;QACHA,KAAK,CAACC,QAAQ,CAACE,OAAO,IAAI,wBAAwB,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOtB,KAAK,EAAE;MACZmB,KAAK,CAAC,yBAAyB,GAAGnB,KAAK,CAACsB,OAAO,CAAC;IACpD;EACJ,CAAC,EAAE,CAAC7B,WAAW,EAAEQ,OAAO,EAAEI,QAAQ,EAAED,mBAAmB,CAAC,CAAC;;EAEzD;EACA,MAAMwB,eAAe,GAAGvD,WAAW,CAAE2C,UAAU,IAAK;IAChD,OAAOjC,aAAa,CAAC8C,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACJ,WAAW,KAAKV,UAAU,CAAC;EACtE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMgD,UAAU,GAAG1D,WAAW,CAAE2D,UAAU,IAAK;IAC3C,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnE,SAAS,CAAC,MAAM;IACZiC,QAAQ,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EAEd,oBACI3B,OAAA;IAAK8D,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChC/D,OAAA;MAAK8D,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxB/D,OAAA;QAAA+D,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BnE,OAAA;QAAA+D,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDnE,OAAA;QACI8D,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMtD,kBAAkB,CAAC,IAAI,CAAE;QAAAiD,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEL7C,KAAK,iBACFtB,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1B/D,OAAA;QAAA+D,QAAA,GAAG,eAAG,EAACzC,KAAK,CAACsB,OAAO,IAAI,mBAAmB;MAAA;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDnE,OAAA;QAAQoE,OAAO,EAAEzC,QAAS;QAACmC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEA9C,OAAO,IAAIlB,UAAU,CAACkE,MAAM,KAAK,CAAC,gBAC/BrE,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5B/D,OAAA;QAAK8D,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BnE,OAAA;QAAA+D,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,gBAENnE,OAAA;MAAK8D,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3B5D,UAAU,CAACmE,GAAG,CAAEC,QAAQ,IAAK;QAC1B,MAAMnB,IAAI,GAAGF,eAAe,CAACqB,QAAQ,CAACC,EAAE,CAAC;QACzC,MAAMC,SAAS,GAAGlE,WAAW,KAAKgE,QAAQ,CAACC,EAAE;QAE7C,oBACIxE,OAAA;UAAuB8D,SAAS,EAAE,iBAAiB,CAACS,QAAQ,CAACnD,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;UAAA2C,QAAA,gBACvF/D,OAAA;YAAK8D,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5B/D,OAAA;cAAK8D,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1B/D,OAAA;gBAAA+D,QAAA,EAAKQ,QAAQ,CAACtD;cAAa;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCnE,OAAA;gBAAA+D,QAAA,EAAIQ,QAAQ,CAACrD;cAAa;gBAAA8C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BnE,OAAA;gBAAM8D,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEQ,QAAQ,CAACpD;cAAe;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNnE,OAAA;cAAK8D,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5B/D,OAAA;gBAAM8D,SAAS,EAAE,gBAAgBS,QAAQ,CAACnD,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAA2C,QAAA,EACzEQ,QAAQ,CAACnD,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAA4C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENnE,OAAA;YAAK8D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClC/D,OAAA;cAAA+D,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBf,IAAI,gBACDpD,OAAA;cAAK8D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtB/D,OAAA;gBAAK8D,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzB/D,OAAA;kBAAM8D,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,SAClB,EAACQ,QAAQ,CAACpD,eAAe,EAAEiC,IAAI,CAACsB,eAAe;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACPnE,OAAA;kBAAA+D,QAAA,GAAO,WAAS,EAACV,UAAU,CAACD,IAAI,CAACuB,UAAU,CAAC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAELM,SAAS,gBACNzE,OAAA;gBAAK8D,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3B/D,OAAA;kBACI4E,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,QAAQ;kBACbC,KAAK,EAAErE,OAAQ;kBACfsE,QAAQ,EAAGC,CAAC,IAAKtE,UAAU,CAACsE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC5CI,WAAW,EAAC,UAAU;kBACtBpB,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFnE,OAAA;kBACI8E,KAAK,EAAEnE,KAAM;kBACboE,QAAQ,EAAGC,CAAC,IAAKpE,QAAQ,CAACoE,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC1CI,WAAW,EAAC,yBAAyB;kBACrCpB,SAAS,EAAC,aAAa;kBACvBqB,IAAI,EAAC;gBAAG;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFnE,OAAA;kBAAK8D,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzB/D,OAAA;oBACIoE,OAAO,EAAEA,CAAA,KAAM/B,gBAAgB,CAACkC,QAAQ,CAACC,EAAE,CAAE;oBAC7CV,SAAS,EAAC,wBAAwB;oBAClCsB,QAAQ,EAAE/D,OAAQ;oBAAA0C,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTnE,OAAA;oBACIoE,OAAO,EAAEA,CAAA,KAAM;sBACX5D,cAAc,CAAC,IAAI,CAAC;sBACpBE,UAAU,CAAC,EAAE,CAAC;sBACdE,QAAQ,CAAC,EAAE,CAAC;oBAChB,CAAE;oBACFkD,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENnE,OAAA;gBACIoE,OAAO,EAAEA,CAAA,KAAM;kBACX5D,cAAc,CAAC+D,QAAQ,CAACC,EAAE,CAAC;kBAC3B9D,UAAU,CAAC0C,IAAI,CAACsB,eAAe,CAACW,QAAQ,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFvB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENnE,OAAA;cAAK8D,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACpB/D,OAAA;gBAAA+D,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3BnE,OAAA;gBACIoE,OAAO,EAAEA,CAAA,KAAM;kBACX5D,cAAc,CAAC+D,QAAQ,CAACC,EAAE,CAAC;kBAC3B9D,UAAU,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFoD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENnE,OAAA;YAAK8D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7B/D,OAAA;cACIoE,OAAO,EAAEA,CAAA,KAAMvB,oBAAoB,CAAC0B,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAACnD,SAAS,CAAE;cACrE0C,SAAS,EAAE,cAAcS,QAAQ,CAACnD,SAAS,GAAG,aAAa,GAAG,aAAa,EAAG;cAC9EgE,QAAQ,EAAE/D,OAAQ;cAAA0C,QAAA,EAEjBQ,QAAQ,CAACnD,SAAS,GAAG,YAAY,GAAG;YAAU;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlGAI,QAAQ,CAACC,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGhB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAtD,eAAe,iBACZb,OAAA;MAAK8D,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAMtD,kBAAkB,CAAC,KAAK,CAAE;MAAAiD,QAAA,eACpE/D,OAAA;QAAK8D,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGY,CAAC,IAAKA,CAAC,CAACM,eAAe,CAAC,CAAE;QAAAvB,QAAA,gBAC/D/D,OAAA;UAAA+D,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBnE,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/D,OAAA;YAAA+D,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CnE,OAAA;YACI4E,IAAI,EAAC,MAAM;YACXE,KAAK,EAAE/D,WAAW,CAACE,aAAc;YACjC8D,QAAQ,EAAGC,CAAC,IAAKhE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAE+D,CAAC,CAACC,MAAM,CAACH,KAAK,CAACS,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnE,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/D,OAAA;YAAA+D,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BnE,OAAA;YACI4E,IAAI,EAAC,MAAM;YACXE,KAAK,EAAE/D,WAAW,CAACG,aAAc;YACjC6D,QAAQ,EAAGC,CAAC,IAAKhE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAE8D,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACjFI,WAAW,EAAC;UAAW;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnE,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvB/D,OAAA;YAAA+D,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BnE,OAAA;YACI4E,IAAI,EAAC,MAAM;YACXE,KAAK,EAAE/D,WAAW,CAACI,eAAgB;YACnC4D,QAAQ,EAAGC,CAAC,IAAKhE,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAE6D,CAAC,CAACC,MAAM,CAACH;YAAK,CAAC,CAAE;YACnFI,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNnE,OAAA;UAAK8D,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvB/D,OAAA;YAAA+D,QAAA,gBACI/D,OAAA;cACI4E,IAAI,EAAC,UAAU;cACfa,OAAO,EAAE1E,WAAW,CAACK,SAAU;cAC/B2D,QAAQ,EAAGC,CAAC,IAAKhE,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAE4D,CAAC,CAACC,MAAM,CAACQ;cAAO,CAAC;YAAE;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNnE,OAAA;UAAK8D,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1B/D,OAAA;YACIoE,OAAO,EAAEnB,iBAAkB;YAC3Ba,SAAS,EAAC,iBAAiB;YAC3BsB,QAAQ,EAAE/D,OAAQ;YAAA0C,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnE,OAAA;YACIoE,OAAO,EAAEA,CAAA,KAAMtD,kBAAkB,CAAC,KAAK,CAAE;YACzCgD,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd,CAAC;AAACjE,EAAA,CAvVID,kBAAkB;EAAA,QAcgBJ,aAAa,EAMjBC,WAAW;AAAA;AAAA4F,EAAA,GApBzCzF,kBAAkB;AAyVxB,eAAeA,kBAAkB;AAAC,IAAAyF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}