# Enable rewrite engine
Options -MultiViews
RewriteEngine On

# Handle Authorization Header
SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1

# If the request is for an actual file or directory, serve it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Handle /backend requests - maintain the fanbet247 prefix
RewriteRule ^backend/(.*)$ /fanbet247/backend/$1 [L,QSA]

# For API calls - maintain the fanbet247 prefix
RewriteRule ^api/(.*)$ /fanbet247/backend/api/$1 [L,QSA]

# CORS Headers
Header set Access-Control-Allow-Origin "*"
Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization"

# Force HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent directory listing
Options -Indexes

# Set security headers
Header set X-Content-Type-Options "nosniff"
Header set X-XSS-Protection "1; mode=block"
Header set X-Frame-Options "SAMEORIGIN"

# For all other routes, serve index.html
RewriteRule ^ index.html [QSA,L] 