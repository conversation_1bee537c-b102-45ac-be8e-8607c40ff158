import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import '../styles/league.css';

const LeagueSelection = () => {
    const { leagueId } = useParams();
    const navigate = useNavigate();
    const [divisions, setDivisions] = useState([]);
    const [selectedDivision, setSelectedDivision] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchDivisions = async () => {
            try {
                const response = await axios.get(`/backend/handlers/league_divisions.php?league_id=${leagueId}`);
                setDivisions(response.data.data);
            } catch (error) {
                console.error('Error fetching divisions:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchDivisions();
    }, [leagueId]);

    const handleDivisionSelect = (divisionId) => {
        setSelectedDivision(divisionId);
    };

    const handleJoinDivision = async () => {
        if (!selectedDivision) return;

        try {
            await axios.post('/backend/handlers/league_membership.php', {
                league_id: leagueId,
                division_id: selectedDivision
            });
            navigate(`/league/${leagueId}/leaderboard`);
        } catch (error) {
            console.error('Error joining division:', error);
            alert('Failed to join division. Please try again.');
        }
    };

    if (loading) {
        return <div className="loading">Loading...</div>;
    }

    return (
        <div className="league-selection">
            <header>
                <h1>Select Division</h1>
                <p>Choose a division to compete in</p>
            </header>

            <div className="divisions-grid">
                {divisions.map(division => (
                    <div 
                        key={division.division_id} 
                        className={`division-card ${selectedDivision === division.division_id ? 'selected' : ''}`}
                        onClick={() => handleDivisionSelect(division.division_id)}
                    >
                        <h3>{division.name}</h3>
                        <div className="division-stats">
                            <p>Members: {division.member_count}</p>
                            <p>Skill Level: {division.skill_level}</p>
                            <p>Entry Fee: {division.entry_fee} credits</p>
                        </div>
                        <div className="division-description">
                            <p>{division.description}</p>
                        </div>
                    </div>
                ))}
            </div>

            <div className="selection-actions">
                <button 
                    className="btn primary"
                    disabled={!selectedDivision}
                    onClick={handleJoinDivision}
                >
                    Join Division
                </button>
                <button 
                    className="btn secondary"
                    onClick={() => navigate('/league')}
                >
                    Back to Leagues
                </button>
            </div>
        </div>
    );
};

export default LeagueSelection;
