{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction CurrencyManagement() {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stats\n  const [stats, setStats] = useState({\n    totalCurrencies: 0,\n    activeCurrencies: 0,\n    inactiveCurrencies: 0,\n    lastUpdated: null\n  });\n\n  // Modal states\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [editingRate, setEditingRate] = useState(null);\n  const [editingCurrency, setEditingCurrency] = useState(null);\n\n  // Form states\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  useEffect(() => {\n    loadCurrencies();\n    loadExchangeRates();\n  }, []);\n  const loadCurrencies = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n      if (response.data.success) {\n        const currencyData = response.data.data.currencies || [];\n        setCurrencies(currencyData);\n\n        // Calculate stats\n        const totalCurrencies = currencyData.length;\n        const activeCurrencies = currencyData.filter(c => c.is_active).length;\n        const inactiveCurrencies = totalCurrencies - activeCurrencies;\n        setStats({\n          totalCurrencies,\n          activeCurrencies,\n          inactiveCurrencies,\n          lastUpdated: new Date().toISOString()\n        });\n      } else {\n        setError(response.data.message || 'Failed to load currencies');\n      }\n    } catch (err) {\n      console.error('Error loading currencies:', err);\n      setError('Failed to load currencies. Please check your network connection.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadExchangeRates = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n      if (response.data.success) {\n        setExchangeRates(response.data.data.exchange_rates || []);\n      }\n    } catch (err) {\n      console.error('Error loading exchange rates:', err);\n    }\n  };\n  const handleUpdateRate = async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      setError('Please enter a valid exchange rate');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n        currency_id: currencyId,\n        rate_to_fancoin: parseFloat(newRate),\n        notes: notes\n      });\n      if (response.data.success) {\n        setSuccess('Exchange rate updated successfully!');\n        loadExchangeRates();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update exchange rate');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error updating exchange rate:', err);\n      setError('Failed to update exchange rate. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleToggleCurrency = async (currencyId, currentStatus) => {\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'toggle',\n        currency_id: currencyId\n      });\n      if (response.data.success) {\n        setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n        loadCurrencies();\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to toggle currency status');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error toggling currency:', err);\n      setError('Failed to toggle currency status. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCurrency = async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'create',\n        ...newCurrency\n      });\n      if (response.data.success) {\n        setSuccess('Currency added successfully!');\n        loadCurrencies();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to add currency');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error adding currency:', err);\n      setError('Failed to add currency. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getExchangeRate = currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCurrency(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: \"Currency Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage supported currencies and exchange rates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n          onClick: () => setShowAddCurrency(true),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 25\n          }, this), \"Add New Currency\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 232,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-blue-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaCoins, {\n            className: \"text-blue-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Total Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.totalCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaToggleOn, {\n            className: \"text-green-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 252,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Active Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.activeCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-red-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaToggleOff, {\n            className: \"text-red-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Inactive Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.inactiveCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-yellow-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaExchangeAlt, {\n            className: \"text-yellow-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Exchange Rates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: exchangeRates.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: \"Currencies & Exchange Rates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 17\n      }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-gray-600\",\n          children: \"Loading currencies...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Exchange Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 301,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Last Updated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: currencies.map(currency => {\n              const rate = getExchangeRate(currency.id);\n              const isEditingRate = editingRate === currency.id;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: `${!currency.is_active ? 'opacity-60' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 h-10 w-10\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-600 font-semibold\",\n                          children: currency.currency_symbol\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 318,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 317,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: currency.currency_code\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 322,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: currency.currency_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 323,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 321,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: rate ? isEditingRate ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      step: \"0.0001\",\n                      value: newRate,\n                      onChange: e => setNewRate(e.target.value),\n                      placeholder: \"New rate\",\n                      className: \"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 332,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: notes,\n                      onChange: e => setNotes(e.target.value),\n                      placeholder: \"Update notes (optional)\",\n                      className: \"w-full px-3 py-1 border border-gray-300 rounded text-sm\",\n                      rows: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 340,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleUpdateRate(currency.id),\n                        className: \"bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\",\n                        disabled: loading,\n                        children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 353,\n                          columnNumber: 69\n                        }, this), \"Save\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 348,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setEditingRate(null);\n                          setNewRate('');\n                          setNotes('');\n                        },\n                        className: \"bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 364,\n                          columnNumber: 69\n                        }, this), \"Cancel\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 331,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setEditingRate(currency.id);\n                        setNewRate(rate.rate_to_fancoin.toString());\n                      },\n                      className: \"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 381,\n                        columnNumber: 65\n                      }, this), \"Update\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"No rate set\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setEditingRate(currency.id);\n                        setNewRate('');\n                      },\n                      className: \"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 61\n                      }, this), \"Set Rate\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 389,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${currency.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: currency.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 403,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: rate ? formatDate(rate.updated_at) : 'Never'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 411,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n                    className: `inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${currency.is_active ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-green-100 text-green-700 hover:bg-green-200'}`,\n                    disabled: loading,\n                    children: [currency.is_active ? /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 75\n                    }, this) : /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 424,\n                      columnNumber: 93\n                    }, this), currency.is_active ? 'Deactivate' : 'Activate']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 415,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 45\n                }, this)]\n              }, currency.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 41\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 307,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 13\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 454,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 452,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 463,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 9\n  }, this);\n}\n_s(CurrencyManagement, \"teJRouVhEaHhxEDt1jb4Qd4Sz2g=\");\n_c = CurrencyManagement;\n;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "editingRate", "setEditingRate", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadCurrencies", "loadExchangeRates", "response", "get", "data", "currencyData", "length", "filter", "c", "Date", "toISOString", "message", "err", "console", "exchange_rates", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "post", "currency_id", "rate_to_fancoin", "setTimeout", "handleToggleCurrency", "currentStatus", "action", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "handleInputChange", "e", "name", "value", "target", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "role", "map", "currency", "id", "isEditingRate", "type", "step", "onChange", "placeholder", "rows", "toString", "updated_at", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [editingRate, setEditingRate] = useState(null);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    useEffect(() => {\n        loadCurrencies();\n        loadExchangeRates();\n    }, []);\n\n    const loadCurrencies = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n            if (response.data.success) {\n                const currencyData = response.data.data.currencies || [];\n                setCurrencies(currencyData);\n\n                // Calculate stats\n                const totalCurrencies = currencyData.length;\n                const activeCurrencies = currencyData.filter(c => c.is_active).length;\n                const inactiveCurrencies = totalCurrencies - activeCurrencies;\n\n                setStats({\n                    totalCurrencies,\n                    activeCurrencies,\n                    inactiveCurrencies,\n                    lastUpdated: new Date().toISOString()\n                });\n            } else {\n                setError(response.data.message || 'Failed to load currencies');\n            }\n        } catch (err) {\n            console.error('Error loading currencies:', err);\n            setError('Failed to load currencies. Please check your network connection.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const loadExchangeRates = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n            if (response.data.success) {\n                setExchangeRates(response.data.data.exchange_rates || []);\n            }\n        } catch (err) {\n            console.error('Error loading exchange rates:', err);\n        }\n    };\n\n    const handleUpdateRate = async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            setError('Please enter a valid exchange rate');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n                currency_id: currencyId,\n                rate_to_fancoin: parseFloat(newRate),\n                notes: notes\n            });\n\n            if (response.data.success) {\n                setSuccess('Exchange rate updated successfully!');\n                loadExchangeRates();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update exchange rate');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error updating exchange rate:', err);\n            setError('Failed to update exchange rate. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleToggleCurrency = async (currencyId, currentStatus) => {\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'toggle',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n                loadCurrencies();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to toggle currency status');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error toggling currency:', err);\n            setError('Failed to toggle currency status. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleAddCurrency = async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            setError('Please fill in all required fields');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'create',\n                ...newCurrency\n            });\n\n            if (response.data.success) {\n                setSuccess('Currency added successfully!');\n                loadCurrencies();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to add currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error adding currency:', err);\n            setError('Failed to add currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getExchangeRate = (currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewCurrency(prev => ({ ...prev, [name]: value }));\n    };\n\n    return (\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\n            {/* Page Header */}\n            <div className=\"mb-8\">\n                <div className=\"flex justify-between items-center\">\n                    <div>\n                        <h1 className=\"text-2xl font-bold text-gray-800\">Currency Management</h1>\n                        <p className=\"text-gray-600\">Manage supported currencies and exchange rates</p>\n                    </div>\n                    <button\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\"\n                        onClick={() => setShowAddCurrency(true)}\n                        disabled={loading}\n                    >\n                        <FaPlus />\n                        Add New Currency\n                    </button>\n                </div>\n            </div>\n\n            {/* Notification Messages */}\n            {error && (\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{error}</span>\n                </div>\n            )}\n            {success && (\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{success}</span>\n                </div>\n            )}\n\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                {/* Total Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\n                        <FaCoins className=\"text-blue-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Active Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\n                        <FaToggleOn className=\"text-green-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Inactive Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-red-100 p-3 mr-4\">\n                        <FaToggleOff className=\"text-red-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Inactive Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.inactiveCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Exchange Rates */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\n                        <FaExchangeAlt className=\"text-yellow-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Exchange Rates</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{exchangeRates.length}</h3>\n                    </div>\n                </div>\n            </div>\n\n            {/* Currencies Table */}\n            <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                    <h2 className=\"text-lg font-semibold text-gray-800\">Currencies & Exchange Rates</h2>\n                </div>\n\n                {loading && currencies.length === 0 ? (\n                    <div className=\"p-8 text-center\">\n                        <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n                        <p className=\"mt-2 text-gray-600\">Loading currencies...</p>\n                    </div>\n                ) : (\n                    <div className=\"overflow-x-auto\">\n                        <table className=\"min-w-full divide-y divide-gray-200\">\n                            <thead className=\"bg-gray-50\">\n                                <tr>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Currency</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Exchange Rate</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Last Updated</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                                </tr>\n                            </thead>\n                            <tbody className=\"bg-white divide-y divide-gray-200\">\n                                {currencies.map((currency) => {\n                                    const rate = getExchangeRate(currency.id);\n                                    const isEditingRate = editingRate === currency.id;\n\n                                    return (\n                                        <tr key={currency.id} className={`${!currency.is_active ? 'opacity-60' : ''}`}>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <div className=\"flex items-center\">\n                                                    <div className=\"flex-shrink-0 h-10 w-10\">\n                                                        <div className=\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\">\n                                                            <span className=\"text-blue-600 font-semibold\">{currency.currency_symbol}</span>\n                                                        </div>\n                                                    </div>\n                                                    <div className=\"ml-4\">\n                                                        <div className=\"text-sm font-medium text-gray-900\">{currency.currency_code}</div>\n                                                        <div className=\"text-sm text-gray-500\">{currency.currency_name}</div>\n                                                    </div>\n                                                </div>\n                                            </td>\n\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                {rate ? (\n                                                    isEditingRate ? (\n                                                        <div className=\"space-y-2\">\n                                                            <input\n                                                                type=\"number\"\n                                                                step=\"0.0001\"\n                                                                value={newRate}\n                                                                onChange={(e) => setNewRate(e.target.value)}\n                                                                placeholder=\"New rate\"\n                                                                className=\"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                                                            />\n                                                            <textarea\n                                                                value={notes}\n                                                                onChange={(e) => setNotes(e.target.value)}\n                                                                placeholder=\"Update notes (optional)\"\n                                                                className=\"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                                                                rows=\"2\"\n                                                            />\n                                                            <div className=\"flex gap-2\">\n                                                                <button\n                                                                    onClick={() => handleUpdateRate(currency.id)}\n                                                                    className=\"bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\"\n                                                                    disabled={loading}\n                                                                >\n                                                                    <FaSave />\n                                                                    Save\n                                                                </button>\n                                                                <button\n                                                                    onClick={() => {\n                                                                        setEditingRate(null);\n                                                                        setNewRate('');\n                                                                        setNotes('');\n                                                                    }}\n                                                                    className=\"bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\"\n                                                                >\n                                                                    <FaTimes />\n                                                                    Cancel\n                                                                </button>\n                                                            </div>\n                                                        </div>\n                                                    ) : (\n                                                        <div>\n                                                            <div className=\"text-sm font-medium text-gray-900\">\n                                                                1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                            </div>\n                                                            <button\n                                                                onClick={() => {\n                                                                    setEditingRate(currency.id);\n                                                                    setNewRate(rate.rate_to_fancoin.toString());\n                                                                }}\n                                                                className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                            >\n                                                                <FaEdit />\n                                                                Update\n                                                            </button>\n                                                        </div>\n                                                    )\n                                                ) : (\n                                                    <div>\n                                                        <span className=\"text-sm text-gray-500\">No rate set</span>\n                                                        <button\n                                                            onClick={() => {\n                                                                setEditingRate(currency.id);\n                                                                setNewRate('');\n                                                            }}\n                                                            className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                        >\n                                                            <FaPlus />\n                                                            Set Rate\n                                                        </button>\n                                                    </div>\n                                                )}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                                                    currency.is_active\n                                                        ? 'bg-green-100 text-green-800'\n                                                        : 'bg-red-100 text-red-800'\n                                                }`}>\n                                                    {currency.is_active ? 'Active' : 'Inactive'}\n                                                </span>\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                                {rate ? formatDate(rate.updated_at) : 'Never'}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                                <button\n                                                    onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                                    className={`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${\n                                                        currency.is_active\n                                                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                                                            : 'bg-green-100 text-green-700 hover:bg-green-200'\n                                                    }`}\n                                                    disabled={loading}\n                                                >\n                                                    {currency.is_active ? <FaToggleOff /> : <FaToggleOn />}\n                                                    {currency.is_active ? 'Deactivate' : 'Activate'}\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    );\n                                })}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC;IAC3CwC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACZ+C,cAAc,CAAC,CAAC;IAChBC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B3B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,8BAA8B,CAAC;MAC/E,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvB,MAAM6B,YAAY,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACpC,UAAU,IAAI,EAAE;QACxDC,aAAa,CAACoC,YAAY,CAAC;;QAE3B;QACA,MAAMzB,eAAe,GAAGyB,YAAY,CAACC,MAAM;QAC3C,MAAMzB,gBAAgB,GAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,SAAS,CAAC,CAACW,MAAM;QACrE,MAAMxB,kBAAkB,GAAGF,eAAe,GAAGC,gBAAgB;QAE7DF,QAAQ,CAAC;UACLC,eAAe;UACfC,gBAAgB;UAChBC,kBAAkB;UAClBC,WAAW,EAAE,IAAI0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACxC,CAAC,CAAC;MACN,CAAC,MAAM;QACHnC,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,2BAA2B,EAAEsC,GAAG,CAAC;MAC/CrC,QAAQ,CAAC,kEAAkE,CAAC;IAChF,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,kCAAkC,CAAC;MACnF,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBL,gBAAgB,CAAC+B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;IACvD;EACJ,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC3C,IAAI,CAACpB,OAAO,IAAIqB,KAAK,CAACC,UAAU,CAACtB,OAAO,CAAC,CAAC,EAAE;MACxCrB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,oCAAoC,EAAE;QACnFuD,WAAW,EAAEJ,UAAU;QACvBK,eAAe,EAAEH,UAAU,CAACtB,OAAO,CAAC;QACpCE,KAAK,EAAEA;MACX,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,qCAAqC,CAAC;QACjDwB,iBAAiB,CAAC,CAAC;QACnBd,cAAc,CAAC,IAAI,CAAC;QACpBU,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZuB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,gCAAgC,CAAC;QACnEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;MACnDrC,QAAQ,CAAC,mDAAmD,CAAC;MAC7D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkD,oBAAoB,GAAG,MAAAA,CAAOP,UAAU,EAAEQ,aAAa,KAAK;IAC9DnD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,iCAAiC,EAAE;QAChF4D,MAAM,EAAE,QAAQ;QAChBL,WAAW,EAAEJ;MACjB,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,YAAY+C,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;QACnFxB,cAAc,CAAC,CAAC;QAChBsB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,kCAAkC,CAAC;QACrEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEsC,GAAG,CAAC;MAC9CrC,QAAQ,CAAC,qDAAqD,CAAC;MAC/D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACpC,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1FnB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,iCAAiC,EAAE;QAChF4D,MAAM,EAAE,QAAQ;QAChB,GAAGnC;MACP,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CuB,cAAc,CAAC,CAAC;QAChBf,kBAAkB,CAAC,KAAK,CAAC;QACzBM,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACF2B,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,wBAAwB,CAAC;QAC3DW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,wBAAwB,EAAEsC,GAAG,CAAC;MAC5CrC,QAAQ,CAAC,2CAA2C,CAAC;MACrD+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsD,eAAe,GAAIX,UAAU,IAAK;IACpC,OAAO9C,aAAa,CAAC0D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACT,WAAW,KAAKJ,UAAU,CAAC;EACtE,CAAC;EAED,MAAMc,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAItB,IAAI,CAACsB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnD,cAAc,CAACoD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,oBACI7E,OAAA;IAAKgF,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExCjF,OAAA;MAAKgF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACjBjF,OAAA;QAAKgF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CjF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YAAIgF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzErF,OAAA;YAAGgF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNrF,OAAA;UACIgF,SAAS,EAAC,uFAAuF;UACjGM,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;UACxCkE,QAAQ,EAAE/E,OAAQ;UAAAyE,QAAA,gBAElBjF,OAAA,CAACL,MAAM;YAAAuF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL3E,KAAK,iBACFV,OAAA;MAAKgF,SAAS,EAAC,+EAA+E;MAACQ,IAAI,EAAC,OAAO;MAAAP,QAAA,eACvGjF,OAAA;QAAMgF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEvE;MAAK;QAAAwE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACAzE,OAAO,iBACJZ,OAAA;MAAKgF,SAAS,EAAC,qFAAqF;MAACQ,IAAI,EAAC,OAAO;MAAAP,QAAA,eAC7GjF,OAAA;QAAMgF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAErE;MAAO;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGDrF,OAAA;MAAKgF,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAEtEjF,OAAA;QAAKgF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEjF,OAAA;UAAKgF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAC9CjF,OAAA,CAACT,OAAO;YAACyF,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YAAGgF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClFrF,OAAA;YAAIgF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACE;UAAe;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEjF,OAAA;UAAKgF,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/CjF,OAAA,CAACP,UAAU;YAACuF,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YAAGgF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnFrF,OAAA;YAAIgF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACG;UAAgB;YAAAiE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEjF,OAAA;UAAKgF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC7CjF,OAAA,CAACN,WAAW;YAACsF,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YAAGgF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrFrF,OAAA;YAAIgF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEnE,KAAK,CAACI;UAAkB;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNrF,OAAA;QAAKgF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEjF,OAAA;UAAKgF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDjF,OAAA,CAACF,aAAa;YAACkF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNrF,OAAA;UAAAiF,QAAA,gBACIjF,OAAA;YAAGgF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFrF,OAAA;YAAIgF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE3E,aAAa,CAACoC;UAAM;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNrF,OAAA;MAAKgF,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC1DjF,OAAA;QAAKgF,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAC/CjF,OAAA;UAAIgF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,EAEL7E,OAAO,IAAIJ,UAAU,CAACsC,MAAM,KAAK,CAAC,gBAC/B1C,OAAA;QAAKgF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BjF,OAAA;UAAKgF,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGrF,OAAA;UAAGgF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENrF,OAAA;QAAKgF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BjF,OAAA;UAAOgF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDjF,OAAA;YAAOgF,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBjF,OAAA;cAAAiF,QAAA,gBACIjF,OAAA;gBAAIgF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5GrF,OAAA;gBAAIgF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjHrF,OAAA;gBAAIgF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1GrF,OAAA;gBAAIgF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChHrF,OAAA;gBAAIgF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRrF,OAAA;YAAOgF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/C7E,UAAU,CAACqF,GAAG,CAAEC,QAAQ,IAAK;cAC1B,MAAMzB,IAAI,GAAGF,eAAe,CAAC2B,QAAQ,CAACC,EAAE,CAAC;cACzC,MAAMC,aAAa,GAAGtE,WAAW,KAAKoE,QAAQ,CAACC,EAAE;cAEjD,oBACI3F,OAAA;gBAAsBgF,SAAS,EAAE,GAAG,CAACU,QAAQ,CAAC3D,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;gBAAAkD,QAAA,gBAC1EjF,OAAA;kBAAIgF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACvCjF,OAAA;oBAAKgF,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BjF,OAAA;sBAAKgF,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACpCjF,OAAA;wBAAKgF,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,eAChFjF,OAAA;0BAAMgF,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAES,QAAQ,CAAC5D;wBAAe;0BAAAoD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNrF,OAAA;sBAAKgF,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACjBjF,OAAA;wBAAKgF,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAES,QAAQ,CAAC9D;sBAAa;wBAAAsD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjFrF,OAAA;wBAAKgF,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAES,QAAQ,CAAC7D;sBAAa;wBAAAqD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAELrF,OAAA;kBAAIgF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EACtChB,IAAI,GACD2B,aAAa,gBACT5F,OAAA;oBAAKgF,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACtBjF,OAAA;sBACI6F,IAAI,EAAC,QAAQ;sBACbC,IAAI,EAAC,QAAQ;sBACbjB,KAAK,EAAE7C,OAAQ;sBACf+D,QAAQ,EAAGpB,CAAC,IAAK1C,UAAU,CAAC0C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAC5CmB,WAAW,EAAC,UAAU;sBACtBhB,SAAS,EAAC;oBAAyD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACFrF,OAAA;sBACI6E,KAAK,EAAE3C,KAAM;sBACb6D,QAAQ,EAAGpB,CAAC,IAAKxC,QAAQ,CAACwC,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAC1CmB,WAAW,EAAC,yBAAyB;sBACrChB,SAAS,EAAC,yDAAyD;sBACnEiB,IAAI,EAAC;oBAAG;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACFrF,OAAA;sBAAKgF,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvBjF,OAAA;wBACIsF,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAACuC,QAAQ,CAACC,EAAE,CAAE;wBAC7CX,SAAS,EAAC,8FAA8F;wBACxGO,QAAQ,EAAE/E,OAAQ;wBAAAyE,QAAA,gBAElBjF,OAAA,CAACJ,MAAM;0BAAAsF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,QAEd;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTrF,OAAA;wBACIsF,OAAO,EAAEA,CAAA,KAAM;0BACX/D,cAAc,CAAC,IAAI,CAAC;0BACpBU,UAAU,CAAC,EAAE,CAAC;0BACdE,QAAQ,CAAC,EAAE,CAAC;wBAChB,CAAE;wBACF6C,SAAS,EAAC,4FAA4F;wBAAAC,QAAA,gBAEtGjF,OAAA,CAACH,OAAO;0BAAAqF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAEf;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENrF,OAAA;oBAAAiF,QAAA,gBACIjF,OAAA;sBAAKgF,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,SACxC,EAACS,QAAQ,CAAC5D,eAAe,EAAEmC,IAAI,CAACR,eAAe;oBAAA;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACNrF,OAAA;sBACIsF,OAAO,EAAEA,CAAA,KAAM;wBACX/D,cAAc,CAACmE,QAAQ,CAACC,EAAE,CAAC;wBAC3B1D,UAAU,CAACgC,IAAI,CAACR,eAAe,CAACyC,QAAQ,CAAC,CAAC,CAAC;sBAC/C,CAAE;sBACFlB,SAAS,EAAC,wEAAwE;sBAAAC,QAAA,gBAElFjF,OAAA,CAACR,MAAM;wBAAA0F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEd;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACR,gBAEDrF,OAAA;oBAAAiF,QAAA,gBACIjF,OAAA;sBAAMgF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1DrF,OAAA;sBACIsF,OAAO,EAAEA,CAAA,KAAM;wBACX/D,cAAc,CAACmE,QAAQ,CAACC,EAAE,CAAC;wBAC3B1D,UAAU,CAAC,EAAE,CAAC;sBAClB,CAAE;sBACF+C,SAAS,EAAC,wEAAwE;sBAAAC,QAAA,gBAElFjF,OAAA,CAACL,MAAM;wBAAAuF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAEd;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACLrF,OAAA;kBAAIgF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACvCjF,OAAA;oBAAMgF,SAAS,EAAE,4DACbU,QAAQ,CAAC3D,SAAS,GACZ,6BAA6B,GAC7B,yBAAyB,EAChC;oBAAAkD,QAAA,EACES,QAAQ,CAAC3D,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLrF,OAAA;kBAAIgF,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC5DhB,IAAI,GAAGC,UAAU,CAACD,IAAI,CAACkC,UAAU,CAAC,GAAG;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACLrF,OAAA;kBAAIgF,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,eAC3DjF,OAAA;oBACIsF,OAAO,EAAEA,CAAA,KAAM3B,oBAAoB,CAAC+B,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC3D,SAAS,CAAE;oBACrEiD,SAAS,EAAE,wEACPU,QAAQ,CAAC3D,SAAS,GACZ,0CAA0C,GAC1C,gDAAgD,EACvD;oBACHwD,QAAQ,EAAE/E,OAAQ;oBAAAyE,QAAA,GAEjBS,QAAQ,CAAC3D,SAAS,gBAAG/B,OAAA,CAACN,WAAW;sBAAAwF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGrF,OAAA,CAACP,UAAU;sBAAAyF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrDK,QAAQ,CAAC3D,SAAS,GAAG,YAAY,GAAG,UAAU;kBAAA;oBAAAmD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAlHAK,QAAQ,CAACC,EAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmHhB,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLjE,eAAe,iBACZpB,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,KAAK,CAAE;MAAA4D,QAAA,eACpEjF,OAAA;QAAKgF,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGX,CAAC,IAAKA,CAAC,CAACyB,eAAe,CAAC,CAAE;QAAAnB,QAAA,gBAC/DjF,OAAA;UAAAiF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACE,aAAc;YACjCmE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAE+C,CAAC,CAACG,MAAM,CAACD,KAAK,CAACwB,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACG,aAAc;YACjCkE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAE8C,CAAC,CAACG,MAAM,CAACD;YAAK,CAAC,CAAE;YACjFmB,WAAW,EAAC;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACI,eAAgB;YACnCiE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAE6C,CAAC,CAACG,MAAM,CAACD;YAAK,CAAC,CAAE;YACnFmB,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBjF,OAAA;YAAAiF,QAAA,gBACIjF,OAAA;cACI6F,IAAI,EAAC,UAAU;cACfU,OAAO,EAAE7E,WAAW,CAACK,SAAU;cAC/BgE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAE4C,CAAC,CAACG,MAAM,CAACyB;cAAO,CAAC;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BjF,OAAA;YACIsF,OAAO,EAAExB,iBAAkB;YAC3BkB,SAAS,EAAC,iBAAiB;YAC3BO,QAAQ,EAAE/E,OAAQ;YAAAyE,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrF,OAAA;YACIsF,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,KAAK,CAAE;YACzC2D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAClF,EAAA,CAveQD,kBAAkB;AAAAsG,EAAA,GAAlBtG,kBAAkB;AAue1B;AAED,eAAeA,kBAAkB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}