import React, { useState } from 'react';
// import { useCurrency } from '../../contexts/CurrencyContext'; // DISABLED: Currency system being recoded
// import { updateUserCurrencyPreference } from '../../utils/currencyUtils'; // DISABLED: Currency system being recoded
import './CurrencySelector.css';

/**
 * CurrencySelector Component
 * Allows users to select and update their preferred currency
 * 
 * Props:
 * - userId: User ID for updating preference
 * - onCurrencyChange: Callback when currency is changed
 * - showPreview: Show conversion preview (default: true)
 * - disabled: Disable the selector (default: false)
 * - className: Additional CSS classes
 */
const CurrencySelector = ({
    userId,
    onCurrencyChange,
    showPreview = true,
    disabled = false,
    className = ''
}) => {
    // DISABLED: Currency system being recoded - return simple placeholder
    return (
        <div className={`currency-selector disabled ${className}`}>
            <div className="selector-message">
                Currency selection temporarily disabled - system being updated
            </div>
        </div>
    );

    /* DISABLED CODE:
    const {
        currencies,
        userCurrency,
        loading: currencyLoading,
        updateUserCurrency,
        convertToUserCurrency
    } = useCurrency();
    */
    
    const [updating, setUpdating] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    const handleCurrencyChange = async (event) => {
        const newCurrencyId = parseInt(event.target.value);
        
        if (!newCurrencyId || !userId) return;
        
        // Find the selected currency
        const selectedCurrency = currencies.find(c => c.id === newCurrencyId);
        if (!selectedCurrency) return;
        
        // Don't update if it's the same currency
        if (userCurrency && userCurrency.id === newCurrencyId) return;
        
        setUpdating(true);
        setError('');
        setSuccess('');
        
        try {
            // Update currency preference via API
            const result = await updateUserCurrencyPreference(userId, newCurrencyId);
            
            // Update local context
            updateUserCurrency(result.new_currency);
            
            setSuccess(`Currency updated to ${selectedCurrency.display_name}`);
            
            // Call callback if provided
            if (onCurrencyChange) {
                onCurrencyChange(result.new_currency);
            }
            
            // Clear success message after 3 seconds
            setTimeout(() => setSuccess(''), 3000);
            
        } catch (err) {
            console.error('Error updating currency preference:', err);
            setError(err.message || 'Failed to update currency preference');
        } finally {
            setUpdating(false);
        }
    };

    if (currencyLoading) {
        return (
            <div className={`currency-selector loading ${className}`}>
                <div className="loading-placeholder">Loading currencies...</div>
            </div>
        );
    }

    return (
        <div className={`currency-selector ${className}`}>
            <div className="selector-header">
                <label htmlFor="currency-select" className="selector-label">
                    Preferred Currency
                </label>
                {userCurrency && (
                    <span className="current-currency">
                        Current: {userCurrency.currency_symbol} {userCurrency.currency_code}
                    </span>
                )}
            </div>
            
            <div className="selector-wrapper">
                <select
                    id="currency-select"
                    value={userCurrency?.id || ''}
                    onChange={handleCurrencyChange}
                    disabled={disabled || updating || currencies.length === 0}
                    className="currency-select"
                >
                    <option value="">Select Currency</option>
                    {currencies.map(currency => (
                        <option key={currency.id} value={currency.id}>
                            {currency.display_name}
                        </option>
                    ))}
                </select>
                
                {updating && (
                    <div className="updating-indicator">
                        <i className="fas fa-spinner fa-spin"></i>
                    </div>
                )}
            </div>
            
            {showPreview && userCurrency && (
                <div className="conversion-preview">
                    <h4>Conversion Examples:</h4>
                    <div className="preview-grid">
                        {[10, 50, 100, 500].map(amount => {
                            const conversion = convertToUserCurrency(amount);
                            return (
                                <div key={amount} className="preview-item">
                                    <span className="fancoin-amount">{amount} FC</span>
                                    <span className="arrow">→</span>
                                    <span className="converted-amount">
                                        {conversion.formatted_amount}
                                    </span>
                                </div>
                            );
                        })}
                    </div>
                </div>
            )}
            
            {error && (
                <div className="selector-message error">
                    <i className="fas fa-exclamation-triangle"></i>
                    {error}
                </div>
            )}
            
            {success && (
                <div className="selector-message success">
                    <i className="fas fa-check-circle"></i>
                    {success}
                </div>
            )}
            
            <div className="selector-help">
                <p>
                    Your preferred currency is used to display FanCoin amounts throughout the platform. 
                    All betting and transactions are still processed in FanCoin.
                </p>
            </div>
        </div>
    );
};

/**
 * CurrencyQuickSelector Component
 * Compact currency selector for headers/sidebars
 */
export const CurrencyQuickSelector = ({ userId, className = '' }) => {
    const { currencies, userCurrency, updateUserCurrency } = useCurrency();
    const [updating, setUpdating] = useState(false);

    const handleQuickChange = async (event) => {
        const newCurrencyId = parseInt(event.target.value);
        if (!newCurrencyId || !userId) return;

        setUpdating(true);
        try {
            const result = await updateUserCurrencyPreference(userId, newCurrencyId);
            updateUserCurrency(result.new_currency);
        } catch (err) {
            console.error('Error updating currency:', err);
        } finally {
            setUpdating(false);
        }
    };

    return (
        <div className={`currency-quick-selector ${className}`}>
            <select
                value={userCurrency?.id || ''}
                onChange={handleQuickChange}
                disabled={updating}
                className="quick-select"
                title="Change preferred currency"
            >
                {currencies.map(currency => (
                    <option key={currency.id} value={currency.id}>
                        {currency.currency_symbol} {currency.currency_code}
                    </option>
                ))}
            </select>
            {updating && <i className="fas fa-spinner fa-spin updating-icon"></i>}
        </div>
    );
};

/**
 * CurrencyInfo Component
 * Displays current currency information and exchange rate
 */
export const CurrencyInfo = ({ className = '' }) => {
    const { userCurrency, exchangeRates } = useCurrency();

    if (!userCurrency) return null;

    const currentRate = exchangeRates.find(rate => rate.currency_id === userCurrency.id);

    return (
        <div className={`currency-info ${className}`}>
            <div className="info-header">
                <span className="currency-name">
                    {userCurrency.currency_symbol} {userCurrency.currency_name}
                </span>
                <span className="currency-code">({userCurrency.currency_code})</span>
            </div>
            
            {currentRate && (
                <div className="exchange-rate">
                    <span className="rate-label">Exchange Rate:</span>
                    <span className="rate-value">
                        1 FanCoin = {userCurrency.currency_symbol}
                        {currentRate.rate_to_fancoin?.toFixed(4)} {userCurrency.currency_code}
                    </span>
                </div>
            )}
            
            {currentRate?.rate_updated_at && (
                <div className="rate-updated">
                    <span className="update-label">Last Updated:</span>
                    <span className="update-time">
                        {new Date(currentRate.rate_updated_at).toLocaleDateString()}
                    </span>
                </div>
            )}
        </div>
    );
};

export default CurrencySelector;
