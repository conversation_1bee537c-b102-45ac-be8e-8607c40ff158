[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "99", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js": "100", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js": "101", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js": "102", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js": "103", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js": "104"}, {"size": 1593, "mtime": 1739215325917, "results": "105", "hashOfConfig": "106"}, {"size": 11978, "mtime": 1751221621390, "results": "107", "hashOfConfig": "106"}, {"size": 362, "mtime": 1725527312699, "results": "108", "hashOfConfig": "106"}, {"size": 3329, "mtime": 1751190026131, "results": "109", "hashOfConfig": "106"}, {"size": 1583, "mtime": 1738380484748, "results": "110", "hashOfConfig": "106"}, {"size": 1958, "mtime": 1738907546846, "results": "111", "hashOfConfig": "106"}, {"size": 1431, "mtime": 1747472215947, "results": "112", "hashOfConfig": "106"}, {"size": 17016, "mtime": 1749744343651, "results": "113", "hashOfConfig": "106"}, {"size": 5850, "mtime": 1749736717523, "results": "114", "hashOfConfig": "106"}, {"size": 10946, "mtime": 1749736717528, "results": "115", "hashOfConfig": "106"}, {"size": 64044, "mtime": 1751218750678, "results": "116", "hashOfConfig": "106"}, {"size": 34669, "mtime": 1749746085318, "results": "117", "hashOfConfig": "106"}, {"size": 10808, "mtime": 1751196036202, "results": "118", "hashOfConfig": "106"}, {"size": 18493, "mtime": 1749736717514, "results": "119", "hashOfConfig": "106"}, {"size": 25373, "mtime": 1751220003165, "results": "120", "hashOfConfig": "106"}, {"size": 41023, "mtime": 1749746085319, "results": "121", "hashOfConfig": "106"}, {"size": 16711, "mtime": 1751220332449, "results": "122", "hashOfConfig": "106"}, {"size": 43308, "mtime": 1749118478833, "results": "123", "hashOfConfig": "106"}, {"size": 14093, "mtime": 1751214890139, "results": "124", "hashOfConfig": "106"}, {"size": 484, "mtime": 1747774257496, "results": "125", "hashOfConfig": "106"}, {"size": 3867, "mtime": 1749105548034, "results": "126", "hashOfConfig": "106"}, {"size": 19685, "mtime": 1749291761248, "results": "127", "hashOfConfig": "106"}, {"size": 14918, "mtime": 1749742045553, "results": "128", "hashOfConfig": "106"}, {"size": 6473, "mtime": 1749733659899, "results": "129", "hashOfConfig": "106"}, {"size": 398, "mtime": 1725625029363, "results": "130", "hashOfConfig": "106"}, {"size": 10599, "mtime": 1749746085318, "results": "131", "hashOfConfig": "106"}, {"size": 30570, "mtime": 1749290764752, "results": "132", "hashOfConfig": "106"}, {"size": 37469, "mtime": 1749291997233, "results": "133", "hashOfConfig": "106"}, {"size": 22327, "mtime": 1739035705052, "results": "134", "hashOfConfig": "106"}, {"size": 9112, "mtime": 1734251655762, "results": "135", "hashOfConfig": "106"}, {"size": 11975, "mtime": 1749736717528, "results": "136", "hashOfConfig": "106"}, {"size": 13027, "mtime": 1749741847696, "results": "137", "hashOfConfig": "106"}, {"size": 12071, "mtime": 1749736717513, "results": "138", "hashOfConfig": "106"}, {"size": 15644, "mtime": 1751220144047, "results": "139", "hashOfConfig": "106"}, {"size": 17527, "mtime": 1749744283332, "results": "140", "hashOfConfig": "106"}, {"size": 12891, "mtime": 1749736717512, "results": "141", "hashOfConfig": "106"}, {"size": 29746, "mtime": 1749282987484, "results": "142", "hashOfConfig": "106"}, {"size": 5324, "mtime": 1749733659747, "results": "143", "hashOfConfig": "106"}, {"size": 205, "mtime": 1732832805260, "results": "144", "hashOfConfig": "106"}, {"size": 28050, "mtime": 1738011980316, "results": "145", "hashOfConfig": "106"}, {"size": 30253, "mtime": 1737968307123, "results": "146", "hashOfConfig": "106"}, {"size": 8917, "mtime": 1738228976181, "results": "147", "hashOfConfig": "106"}, {"size": 1242, "mtime": 1732832820214, "results": "148", "hashOfConfig": "106"}, {"size": 8290, "mtime": 1751193168091, "results": "149", "hashOfConfig": "106"}, {"size": 1098, "mtime": 1732832839965, "results": "150", "hashOfConfig": "106"}, {"size": 11530, "mtime": 1732983571250, "results": "151", "hashOfConfig": "106"}, {"size": 23487, "mtime": 1749741941325, "results": "152", "hashOfConfig": "106"}, {"size": 24467, "mtime": 1732988420840, "results": "153", "hashOfConfig": "106"}, {"size": 4310, "mtime": 1734245942035, "results": "154", "hashOfConfig": "106"}, {"size": 5623, "mtime": 1734245958195, "results": "155", "hashOfConfig": "106"}, {"size": 3339, "mtime": 1734245925091, "results": "156", "hashOfConfig": "106"}, {"size": 6337, "mtime": 1736487062211, "results": "157", "hashOfConfig": "106"}, {"size": 5681, "mtime": 1734287339563, "results": "158", "hashOfConfig": "106"}, {"size": 10920, "mtime": 1739168463615, "results": "159", "hashOfConfig": "106"}, {"size": 14257, "mtime": 1739212427178, "results": "160", "hashOfConfig": "106"}, {"size": 16913, "mtime": 1738012431449, "results": "161", "hashOfConfig": "106"}, {"size": 21192, "mtime": 1738014939015, "results": "162", "hashOfConfig": "106"}, {"size": 3211, "mtime": 1747478622718, "results": "163", "hashOfConfig": "106"}, {"size": 7496, "mtime": 1751193097200, "results": "164", "hashOfConfig": "106"}, {"size": 1352, "mtime": 1738907631772, "results": "165", "hashOfConfig": "106"}, {"size": 591, "mtime": 1737714035353, "results": "166", "hashOfConfig": "106"}, {"size": 4889, "mtime": 1739089917990, "results": "167", "hashOfConfig": "106"}, {"size": 4026, "mtime": 1749114060143, "results": "168", "hashOfConfig": "106"}, {"size": 2690, "mtime": 1749742809442, "results": "169", "hashOfConfig": "106"}, {"size": 597, "mtime": 1738005020143, "results": "170", "hashOfConfig": "106"}, {"size": 2649, "mtime": 1745558530865, "results": "171", "hashOfConfig": "106"}, {"size": 856, "mtime": 1738005002533, "results": "172", "hashOfConfig": "106"}, {"size": 778, "mtime": 1737703033090, "results": "173", "hashOfConfig": "106"}, {"size": 9268, "mtime": 1739089382382, "results": "174", "hashOfConfig": "106"}, {"size": 4473, "mtime": 1749736717521, "results": "175", "hashOfConfig": "106"}, {"size": 6511, "mtime": 1747772230646, "results": "176", "hashOfConfig": "106"}, {"size": 3561, "mtime": 1747465926259, "results": "177", "hashOfConfig": "106"}, {"size": 2058, "mtime": 1745560016985, "results": "178", "hashOfConfig": "106"}, {"size": 3270, "mtime": 1747683592095, "results": "179", "hashOfConfig": "106"}, {"size": 13040, "mtime": 1749736717522, "results": "180", "hashOfConfig": "106"}, {"size": 35666, "mtime": 1749736717528, "results": "181", "hashOfConfig": "106"}, {"size": 17158, "mtime": 1749113919875, "results": "182", "hashOfConfig": "106"}, {"size": 12332, "mtime": 1749106493490, "results": "183", "hashOfConfig": "106"}, {"size": 1680, "mtime": 1749737616336, "results": "184", "hashOfConfig": "106"}, {"size": 35110, "mtime": 1749118685592, "results": "185", "hashOfConfig": "106"}, {"size": 17861, "mtime": 1749116896225, "results": "186", "hashOfConfig": "106"}, {"size": 317, "mtime": 1749231241721, "results": "187", "hashOfConfig": "106"}, {"size": 22414, "mtime": 1749237247953, "results": "188", "hashOfConfig": "106"}, {"size": 15311, "mtime": 1749239054836, "results": "189", "hashOfConfig": "106"}, {"size": 19477, "mtime": 1749236876615, "results": "190", "hashOfConfig": "106"}, {"size": 12103, "mtime": 1749736717523, "results": "191", "hashOfConfig": "106"}, {"size": 26689, "mtime": 1749237355112, "results": "192", "hashOfConfig": "106"}, {"size": 16917, "mtime": 1749736717523, "results": "193", "hashOfConfig": "106"}, {"size": 3567, "mtime": 1749282608572, "results": "194", "hashOfConfig": "106"}, {"size": 3229, "mtime": 1749749514923, "results": "195", "hashOfConfig": "106"}, {"size": 13936, "mtime": 1749750686297, "results": "196", "hashOfConfig": "106"}, {"size": 1833, "mtime": 1749750397933, "results": "197", "hashOfConfig": "106"}, {"size": 9434, "mtime": 1751190331897, "results": "198", "hashOfConfig": "106"}, {"size": 3176, "mtime": 1751190174797, "results": "199", "hashOfConfig": "106"}, {"size": 5569, "mtime": 1751190153517, "results": "200", "hashOfConfig": "106"}, {"size": 4644, "mtime": 1751190097589, "results": "201", "hashOfConfig": "106"}, {"size": 6877, "mtime": 1751190061103, "results": "202", "hashOfConfig": "106"}, {"size": 5619, "mtime": 1751190125112, "results": "203", "hashOfConfig": "106"}, {"size": 5823, "mtime": 1751190205858, "results": "204", "hashOfConfig": "106"}, {"size": 31995, "mtime": 1751217647037, "results": "205", "hashOfConfig": "106"}, {"size": 977, "mtime": 1749746063985, "results": "206", "hashOfConfig": "106"}, {"size": 8985, "mtime": 1751206912079, "results": "207", "hashOfConfig": "106"}, {"size": 7542, "mtime": 1751190440918, "results": "208", "hashOfConfig": "106"}, {"size": 7403, "mtime": 1751221680009, "results": "209", "hashOfConfig": "106"}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["522"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["523"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["524", "525", "526"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["527", "528"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["529", "530", "531", "532", "533", "534", "535", "536", "537", "538"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", ["539", "540", "541"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["542", "543"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["544", "545", "546", "547", "548"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["549"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["550", "551"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["552"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["553", "554"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["555"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["556", "557", "558", "559"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["560", "561", "562", "563"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["564", "565", "566"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["567", "568"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["569"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["570", "571", "572", "573", "574"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["575"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", ["576", "577", "578"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["579"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["580", "581", "582", "583"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["584", "585", "586", "587", "588"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["589"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["590"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["591"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["592", "593"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["594", "595", "596", "597"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["598"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["599", "600"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["601"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["602", "603", "604"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["605"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["606", "607", "608"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["609"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["610", "611"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["612", "613", "614", "615"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["616", "617"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["618", "619"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["620"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["621", "622"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CurrencyManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencySelector.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Currency\\CurrencyAmount.js", ["623"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TestLogin.js", [], [], {"ruleId": "624", "severity": 1, "message": "625", "line": 72, "column": 8, "nodeType": "626", "messageId": "627", "endLine": 72, "endColumn": 23}, {"ruleId": "628", "severity": 1, "message": "629", "line": 31, "column": 6, "nodeType": "630", "endLine": 31, "endColumn": 8, "suggestions": "631"}, {"ruleId": "628", "severity": 1, "message": "632", "line": 26, "column": 9, "nodeType": "633", "endLine": 26, "endColumn": 62}, {"ruleId": "628", "severity": 1, "message": "634", "line": 74, "column": 6, "nodeType": "630", "endLine": 74, "endColumn": 50, "suggestions": "635"}, {"ruleId": "628", "severity": 1, "message": "636", "line": 120, "column": 6, "nodeType": "630", "endLine": 120, "endColumn": 85, "suggestions": "637"}, {"ruleId": "624", "severity": 1, "message": "638", "line": 4, "column": 121, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 132}, {"ruleId": "624", "severity": 1, "message": "639", "line": 11, "column": 11, "nodeType": "626", "messageId": "627", "endLine": 11, "endColumn": 19}, {"ruleId": "624", "severity": 1, "message": "640", "line": 8, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 8, "endColumn": 16}, {"ruleId": "624", "severity": 1, "message": "641", "line": 10, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 10, "endColumn": 17}, {"ruleId": "624", "severity": 1, "message": "642", "line": 11, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 11, "endColumn": 13}, {"ruleId": "624", "severity": 1, "message": "643", "line": 12, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 12, "endColumn": 16}, {"ruleId": "624", "severity": 1, "message": "644", "line": 13, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 13, "endColumn": 8}, {"ruleId": "624", "severity": 1, "message": "645", "line": 14, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 14, "endColumn": 16}, {"ruleId": "624", "severity": 1, "message": "646", "line": 15, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 15, "endColumn": 10}, {"ruleId": "624", "severity": 1, "message": "647", "line": 16, "column": 3, "nodeType": "626", "messageId": "627", "endLine": 16, "endColumn": 9}, {"ruleId": "624", "severity": 1, "message": "648", "line": 20, "column": 16, "nodeType": "626", "messageId": "627", "endLine": 20, "endColumn": 19}, {"ruleId": "624", "severity": 1, "message": "649", "line": 47, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 47, "endColumn": 30}, {"ruleId": "624", "severity": 1, "message": "650", "line": 5, "column": 23, "nodeType": "626", "messageId": "627", "endLine": 5, "endColumn": 38}, {"ruleId": "624", "severity": 1, "message": "651", "line": 10, "column": 7, "nodeType": "626", "messageId": "627", "endLine": 10, "endColumn": 19}, {"ruleId": "628", "severity": 1, "message": "652", "line": 33, "column": 8, "nodeType": "630", "endLine": 33, "endColumn": 10, "suggestions": "653"}, {"ruleId": "624", "severity": 1, "message": "654", "line": 20, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 20, "endColumn": 20}, {"ruleId": "624", "severity": 1, "message": "655", "line": 20, "column": 22, "nodeType": "626", "messageId": "627", "endLine": 20, "endColumn": 35}, {"ruleId": "624", "severity": 1, "message": "656", "line": 3, "column": 20, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 28}, {"ruleId": "624", "severity": 1, "message": "657", "line": 3, "column": 37, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 43}, {"ruleId": "624", "severity": 1, "message": "658", "line": 3, "column": 45, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 52}, {"ruleId": "624", "severity": 1, "message": "659", "line": 13, "column": 21, "nodeType": "626", "messageId": "627", "endLine": 13, "endColumn": 31}, {"ruleId": "628", "severity": 1, "message": "660", "line": 40, "column": 8, "nodeType": "630", "endLine": 40, "endColumn": 41, "suggestions": "661"}, {"ruleId": "624", "severity": 1, "message": "662", "line": 17, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 17, "endColumn": 21}, {"ruleId": "624", "severity": 1, "message": "656", "line": 3, "column": 20, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 28}, {"ruleId": "628", "severity": 1, "message": "663", "line": 39, "column": 8, "nodeType": "630", "endLine": 39, "endColumn": 42, "suggestions": "664"}, {"ruleId": "624", "severity": 1, "message": "665", "line": 3, "column": 34, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 41}, {"ruleId": "628", "severity": 1, "message": "666", "line": 24, "column": 8, "nodeType": "630", "endLine": 24, "endColumn": 19, "suggestions": "667"}, {"ruleId": "628", "severity": 1, "message": "668", "line": 33, "column": 8, "nodeType": "630", "endLine": 33, "endColumn": 30, "suggestions": "669"}, {"ruleId": "628", "severity": 1, "message": "670", "line": 43, "column": 8, "nodeType": "630", "endLine": 43, "endColumn": 10, "suggestions": "671"}, {"ruleId": "624", "severity": 1, "message": "672", "line": 11, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 11, "endColumn": 20}, {"ruleId": "624", "severity": 1, "message": "673", "line": 16, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 16, "endColumn": 12}, {"ruleId": "624", "severity": 1, "message": "674", "line": 17, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 17, "endColumn": 17}, {"ruleId": "624", "severity": 1, "message": "665", "line": 18, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 18, "endColumn": 12}, {"ruleId": "624", "severity": 1, "message": "675", "line": 1, "column": 60, "nodeType": "626", "messageId": "627", "endLine": 1, "endColumn": 66}, {"ruleId": "624", "severity": 1, "message": "676", "line": 28, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 28, "endColumn": 26}, {"ruleId": "624", "severity": 1, "message": "677", "line": 28, "column": 28, "nodeType": "626", "messageId": "627", "endLine": 28, "endColumn": 45}, {"ruleId": "628", "severity": 1, "message": "678", "line": 83, "column": 8, "nodeType": "630", "endLine": 83, "endColumn": 10, "suggestions": "679"}, {"ruleId": "624", "severity": 1, "message": "638", "line": 3, "column": 19, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 30}, {"ruleId": "624", "severity": 1, "message": "680", "line": 14, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 14, "endColumn": 24}, {"ruleId": "624", "severity": 1, "message": "681", "line": 93, "column": 11, "nodeType": "626", "messageId": "627", "endLine": 93, "endColumn": 23}, {"ruleId": "624", "severity": 1, "message": "682", "line": 114, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 114, "endColumn": 25}, {"ruleId": "624", "severity": 1, "message": "683", "line": 148, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 148, "endColumn": 22}, {"ruleId": "624", "severity": 1, "message": "684", "line": 4, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 17}, {"ruleId": "624", "severity": 1, "message": "685", "line": 13, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 13, "endColumn": 17}, {"ruleId": "624", "severity": 1, "message": "686", "line": 14, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 14, "endColumn": 15}, {"ruleId": "624", "severity": 1, "message": "683", "line": 115, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 115, "endColumn": 22}, {"ruleId": "624", "severity": 1, "message": "687", "line": 132, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 132, "endColumn": 19}, {"ruleId": "624", "severity": 1, "message": "688", "line": 145, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 145, "endColumn": 22}, {"ruleId": "628", "severity": 1, "message": "663", "line": 44, "column": 8, "nodeType": "630", "endLine": 44, "endColumn": 21, "suggestions": "689"}, {"ruleId": "624", "severity": 1, "message": "690", "line": 10, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 10, "endColumn": 23}, {"ruleId": "624", "severity": 1, "message": "691", "line": 10, "column": 25, "nodeType": "626", "messageId": "627", "endLine": 10, "endColumn": 41}, {"ruleId": "624", "severity": 1, "message": "692", "line": 18, "column": 25, "nodeType": "626", "messageId": "627", "endLine": 18, "endColumn": 46}, {"ruleId": "628", "severity": 1, "message": "693", "line": 19, "column": 8, "nodeType": "630", "endLine": 19, "endColumn": 10, "suggestions": "694"}, {"ruleId": "624", "severity": 1, "message": "695", "line": 7, "column": 14, "nodeType": "626", "messageId": "627", "endLine": 7, "endColumn": 20}, {"ruleId": "624", "severity": 1, "message": "674", "line": 7, "column": 41, "nodeType": "626", "messageId": "627", "endLine": 7, "endColumn": 53}, {"ruleId": "624", "severity": 1, "message": "696", "line": 8, "column": 46, "nodeType": "626", "messageId": "627", "endLine": 8, "endColumn": 52}, {"ruleId": "624", "severity": 1, "message": "697", "line": 306, "column": 11, "nodeType": "626", "messageId": "627", "endLine": 306, "endColumn": 27}, {"ruleId": "624", "severity": 1, "message": "698", "line": 4, "column": 45, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 64}, {"ruleId": "624", "severity": 1, "message": "699", "line": 4, "column": 66, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 79}, {"ruleId": "624", "severity": 1, "message": "700", "line": 4, "column": 111, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 123}, {"ruleId": "628", "severity": 1, "message": "701", "line": 29, "column": 8, "nodeType": "630", "endLine": 29, "endColumn": 10, "suggestions": "702"}, {"ruleId": "624", "severity": 1, "message": "703", "line": 256, "column": 11, "nodeType": "626", "messageId": "627", "endLine": 256, "endColumn": 26}, {"ruleId": "628", "severity": 1, "message": "704", "line": 24, "column": 8, "nodeType": "630", "endLine": 24, "endColumn": 33, "suggestions": "705"}, {"ruleId": "624", "severity": 1, "message": "695", "line": 5, "column": 57, "nodeType": "626", "messageId": "627", "endLine": 5, "endColumn": 63}, {"ruleId": "628", "severity": 1, "message": "706", "line": 24, "column": 8, "nodeType": "630", "endLine": 24, "endColumn": 33, "suggestions": "707"}, {"ruleId": "624", "severity": 1, "message": "708", "line": 122, "column": 19, "nodeType": "626", "messageId": "627", "endLine": 122, "endColumn": 28}, {"ruleId": "624", "severity": 1, "message": "709", "line": 137, "column": 19, "nodeType": "626", "messageId": "627", "endLine": 137, "endColumn": 22}, {"ruleId": "624", "severity": 1, "message": "710", "line": 4, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 4, "endColumn": 14}, {"ruleId": "624", "severity": 1, "message": "711", "line": 6, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 6, "endColumn": 10}, {"ruleId": "624", "severity": 1, "message": "642", "line": 7, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 7, "endColumn": 15}, {"ruleId": "624", "severity": 1, "message": "712", "line": 8, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 8, "endColumn": 16}, {"ruleId": "624", "severity": 1, "message": "713", "line": 57, "column": 9, "nodeType": "626", "messageId": "627", "endLine": 57, "endColumn": 26}, {"ruleId": "624", "severity": 1, "message": "641", "line": 2, "column": 10, "nodeType": "626", "messageId": "627", "endLine": 2, "endColumn": 24}, {"ruleId": "624", "severity": 1, "message": "714", "line": 2, "column": 26, "nodeType": "626", "messageId": "627", "endLine": 2, "endColumn": 34}, {"ruleId": "624", "severity": 1, "message": "715", "line": 3, "column": 35, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 41}, {"ruleId": "624", "severity": 1, "message": "716", "line": 5, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 5, "endColumn": 15}, {"ruleId": "624", "severity": 1, "message": "640", "line": 6, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 6, "endColumn": 18}, {"ruleId": "628", "severity": 1, "message": "717", "line": 29, "column": 8, "nodeType": "630", "endLine": 29, "endColumn": 31, "suggestions": "718"}, {"ruleId": "628", "severity": 1, "message": "663", "line": 31, "column": 8, "nodeType": "630", "endLine": 31, "endColumn": 17, "suggestions": "719"}, {"ruleId": "624", "severity": 1, "message": "720", "line": 3, "column": 33, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 38}, {"ruleId": "624", "severity": 1, "message": "721", "line": 14, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 14, "endColumn": 21}, {"ruleId": "628", "severity": 1, "message": "722", "line": 29, "column": 8, "nodeType": "630", "endLine": 29, "endColumn": 10, "suggestions": "723"}, {"ruleId": "624", "severity": 1, "message": "724", "line": 3, "column": 41, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 62}, {"ruleId": "624", "severity": 1, "message": "724", "line": 3, "column": 40, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 61}, {"ruleId": "628", "severity": 1, "message": "725", "line": 28, "column": 8, "nodeType": "630", "endLine": 28, "endColumn": 24, "suggestions": "726"}, {"ruleId": "624", "severity": 1, "message": "665", "line": 3, "column": 51, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 58}, {"ruleId": "624", "severity": 1, "message": "711", "line": 3, "column": 102, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 107}, {"ruleId": "624", "severity": 1, "message": "727", "line": 18, "column": 12, "nodeType": "626", "messageId": "627", "endLine": 18, "endColumn": 21}, {"ruleId": "628", "severity": 1, "message": "728", "line": 23, "column": 8, "nodeType": "630", "endLine": 23, "endColumn": 17, "suggestions": "729"}, {"ruleId": "624", "severity": 1, "message": "730", "line": 3, "column": 40, "nodeType": "626", "messageId": "627", "endLine": 3, "endColumn": 46}, {"ruleId": "628", "severity": 1, "message": "731", "line": 30, "column": 8, "nodeType": "630", "endLine": 30, "endColumn": 10, "suggestions": "732"}, {"ruleId": "624", "severity": 1, "message": "733", "line": 6, "column": 5, "nodeType": "626", "messageId": "627", "endLine": 6, "endColumn": 11}, {"ruleId": "628", "severity": 1, "message": "734", "line": 38, "column": 8, "nodeType": "630", "endLine": 38, "endColumn": 16, "suggestions": "735"}, {"ruleId": "628", "severity": 1, "message": "736", "line": 51, "column": 8, "nodeType": "630", "endLine": 51, "endColumn": 10, "suggestions": "737"}, {"ruleId": "738", "severity": 1, "message": "739", "line": 93, "column": 39, "nodeType": "740", "messageId": "741", "endLine": 93, "endColumn": 86}, {"ruleId": "624", "severity": 1, "message": "742", "line": 177, "column": 15, "nodeType": "626", "messageId": "627", "endLine": 177, "endColumn": 30}, {"ruleId": "624", "severity": 1, "message": "743", "line": 144, "column": 36, "nodeType": "626", "messageId": "627", "endLine": 144, "endColumn": 48}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["744"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 98) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["745"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["746"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'currencyService' is defined but never used.", "'API_BASE_URL' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTeams'. Either include it or remove the dependency array.", ["747"], "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["748"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["749"], "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["750"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["751"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["752"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["753"], "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["754"], "'paymentMethod' is assigned a value but never used.", "'setPaymentMethod' is assigned a value but never used.", "'convertToUserCurrency' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["755"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["756"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["757"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["758"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["759"], ["760"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["761"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["762"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["763"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["764"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["765"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["766"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", "'userCurrency' is assigned a value but never used.", {"desc": "767", "fix": "768"}, {"desc": "769", "fix": "770"}, {"desc": "771", "fix": "772"}, {"desc": "773", "fix": "774"}, {"desc": "775", "fix": "776"}, {"desc": "777", "fix": "778"}, {"desc": "779", "fix": "780"}, {"desc": "781", "fix": "782"}, {"desc": "783", "fix": "784"}, {"desc": "785", "fix": "786"}, {"desc": "787", "fix": "788"}, {"desc": "789", "fix": "790"}, {"desc": "791", "fix": "792"}, {"desc": "793", "fix": "794"}, {"desc": "795", "fix": "796"}, {"desc": "797", "fix": "798"}, {"desc": "799", "fix": "800"}, {"desc": "801", "fix": "802"}, {"desc": "803", "fix": "804"}, {"desc": "805", "fix": "806"}, {"desc": "807", "fix": "808"}, {"desc": "809", "fix": "810"}, {"desc": "811", "fix": "812"}, "Update the dependencies array to be: [removeError]", {"range": "813", "text": "814"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "815", "text": "816"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", {"range": "817", "text": "818"}, "Update the dependencies array to be: [fetchTeams]", {"range": "819", "text": "820"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "821", "text": "822"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "823", "text": "824"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "825", "text": "826"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "827", "text": "828"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "829", "text": "830"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "831", "text": "832"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "833", "text": "834"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "835", "text": "836"}, "Update the dependencies array to be: [fetchFriends]", {"range": "837", "text": "838"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "839", "text": "840"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "841", "text": "842"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "843", "text": "844"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "845", "text": "846"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "847", "text": "848"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "849", "text": "850"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "851", "text": "852"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "853", "text": "854"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "855", "text": "856"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "857", "text": "858"}, [985, 987], "[removeError]", [2698, 2742], "[token, userId, setUserData, navigate, location.pathname]", [4127, 4206], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", [1143, 1145], "[fetchTeams]", [1193, 1226], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1116, 1127], "[activeTab, fetchConversations]", [1474, 1496], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1021, 1029], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1873, 1875], "[CACHE_DURATION]"]