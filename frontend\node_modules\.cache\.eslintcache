[{"C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js": "1", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js": "2", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js": "3", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js": "4", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js": "5", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js": "6", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js": "7", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js": "8", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js": "9", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js": "10", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js": "11", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js": "12", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js": "13", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js": "14", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js": "15", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js": "16", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js": "17", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js": "18", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js": "19", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js": "20", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js": "21", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js": "22", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js": "23", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js": "24", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js": "25", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js": "26", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js": "27", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js": "28", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js": "29", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js": "30", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js": "31", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js": "32", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js": "33", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js": "34", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js": "35", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js": "36", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js": "37", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js": "38", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js": "39", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js": "40", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js": "41", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js": "42", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js": "43", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js": "44", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js": "45", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js": "46", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js": "47", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js": "48", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js": "49", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js": "50", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js": "51", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js": "52", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js": "53", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js": "54", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js": "55", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js": "56", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js": "57", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js": "58", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js": "59", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js": "60", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js": "61", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js": "62", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js": "63", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js": "64", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js": "65", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js": "66", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js": "67", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js": "68", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js": "69", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js": "70", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js": "71", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js": "72", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js": "73", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js": "74", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js": "75", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js": "76", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js": "77", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js": "78", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js": "79", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js": "80", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js": "81", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js": "82", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js": "83", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js": "84", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js": "85", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js": "86", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js": "87", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js": "88", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js": "89", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js": "90", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js": "91", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js": "92", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js": "93", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js": "94", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js": "95", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js": "96", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js": "97", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js": "98", "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js": "99"}, {"size": 1593, "mtime": 1739215325917, "results": "100", "hashOfConfig": "101"}, {"size": 11481, "mtime": 1751190538960, "results": "102", "hashOfConfig": "101"}, {"size": 362, "mtime": 1725527312699, "results": "103", "hashOfConfig": "101"}, {"size": 3329, "mtime": 1751190026131, "results": "104", "hashOfConfig": "101"}, {"size": 1583, "mtime": 1738380484748, "results": "105", "hashOfConfig": "101"}, {"size": 1958, "mtime": 1738907546846, "results": "106", "hashOfConfig": "101"}, {"size": 1431, "mtime": 1747472215947, "results": "107", "hashOfConfig": "101"}, {"size": 17016, "mtime": 1749744343651, "results": "108", "hashOfConfig": "101"}, {"size": 5850, "mtime": 1749736717523, "results": "109", "hashOfConfig": "101"}, {"size": 10946, "mtime": 1749736717528, "results": "110", "hashOfConfig": "101"}, {"size": 61053, "mtime": 1749746085245, "results": "111", "hashOfConfig": "101"}, {"size": 34669, "mtime": 1749746085318, "results": "112", "hashOfConfig": "101"}, {"size": 10273, "mtime": 1749750435301, "results": "113", "hashOfConfig": "101"}, {"size": 18493, "mtime": 1749736717514, "results": "114", "hashOfConfig": "101"}, {"size": 25409, "mtime": 1739175447004, "results": "115", "hashOfConfig": "101"}, {"size": 41023, "mtime": 1749746085319, "results": "116", "hashOfConfig": "101"}, {"size": 16830, "mtime": 1749750483435, "results": "117", "hashOfConfig": "101"}, {"size": 43308, "mtime": 1749118478833, "results": "118", "hashOfConfig": "101"}, {"size": 11677, "mtime": 1747481352521, "results": "119", "hashOfConfig": "101"}, {"size": 484, "mtime": 1747774257496, "results": "120", "hashOfConfig": "101"}, {"size": 3867, "mtime": 1749105548034, "results": "121", "hashOfConfig": "101"}, {"size": 19685, "mtime": 1749291761248, "results": "122", "hashOfConfig": "101"}, {"size": 14918, "mtime": 1749742045553, "results": "123", "hashOfConfig": "101"}, {"size": 6473, "mtime": 1749733659899, "results": "124", "hashOfConfig": "101"}, {"size": 398, "mtime": 1725625029363, "results": "125", "hashOfConfig": "101"}, {"size": 10599, "mtime": 1749746085318, "results": "126", "hashOfConfig": "101"}, {"size": 30570, "mtime": 1749290764752, "results": "127", "hashOfConfig": "101"}, {"size": 37469, "mtime": 1749291997233, "results": "128", "hashOfConfig": "101"}, {"size": 22327, "mtime": 1739035705052, "results": "129", "hashOfConfig": "101"}, {"size": 9112, "mtime": 1734251655762, "results": "130", "hashOfConfig": "101"}, {"size": 11975, "mtime": 1749736717528, "results": "131", "hashOfConfig": "101"}, {"size": 13027, "mtime": 1749741847696, "results": "132", "hashOfConfig": "101"}, {"size": 12071, "mtime": 1749736717513, "results": "133", "hashOfConfig": "101"}, {"size": 15698, "mtime": 1749293184254, "results": "134", "hashOfConfig": "101"}, {"size": 17527, "mtime": 1749744283332, "results": "135", "hashOfConfig": "101"}, {"size": 12891, "mtime": 1749736717512, "results": "136", "hashOfConfig": "101"}, {"size": 29746, "mtime": 1749282987484, "results": "137", "hashOfConfig": "101"}, {"size": 5324, "mtime": 1749733659747, "results": "138", "hashOfConfig": "101"}, {"size": 205, "mtime": 1732832805260, "results": "139", "hashOfConfig": "101"}, {"size": 28050, "mtime": 1738011980316, "results": "140", "hashOfConfig": "101"}, {"size": 30253, "mtime": 1737968307123, "results": "141", "hashOfConfig": "101"}, {"size": 8917, "mtime": 1738228976181, "results": "142", "hashOfConfig": "101"}, {"size": 1242, "mtime": 1732832820214, "results": "143", "hashOfConfig": "101"}, {"size": 1134, "mtime": 1732832829902, "results": "144", "hashOfConfig": "101"}, {"size": 1098, "mtime": 1732832839965, "results": "145", "hashOfConfig": "101"}, {"size": 11530, "mtime": 1732983571250, "results": "146", "hashOfConfig": "101"}, {"size": 23487, "mtime": 1749741941325, "results": "147", "hashOfConfig": "101"}, {"size": 24467, "mtime": 1732988420840, "results": "148", "hashOfConfig": "101"}, {"size": 4310, "mtime": 1734245942035, "results": "149", "hashOfConfig": "101"}, {"size": 5623, "mtime": 1734245958195, "results": "150", "hashOfConfig": "101"}, {"size": 3339, "mtime": 1734245925091, "results": "151", "hashOfConfig": "101"}, {"size": 6337, "mtime": 1736487062211, "results": "152", "hashOfConfig": "101"}, {"size": 5681, "mtime": 1734287339563, "results": "153", "hashOfConfig": "101"}, {"size": 10920, "mtime": 1739168463615, "results": "154", "hashOfConfig": "101"}, {"size": 14257, "mtime": 1739212427178, "results": "155", "hashOfConfig": "101"}, {"size": 16913, "mtime": 1738012431449, "results": "156", "hashOfConfig": "101"}, {"size": 21192, "mtime": 1738014939015, "results": "157", "hashOfConfig": "101"}, {"size": 3211, "mtime": 1747478622718, "results": "158", "hashOfConfig": "101"}, {"size": 7400, "mtime": 1749290081222, "results": "159", "hashOfConfig": "101"}, {"size": 1352, "mtime": 1738907631772, "results": "160", "hashOfConfig": "101"}, {"size": 591, "mtime": 1737714035353, "results": "161", "hashOfConfig": "101"}, {"size": 4889, "mtime": 1739089917990, "results": "162", "hashOfConfig": "101"}, {"size": 4026, "mtime": 1749114060143, "results": "163", "hashOfConfig": "101"}, {"size": 2690, "mtime": 1749742809442, "results": "164", "hashOfConfig": "101"}, {"size": 597, "mtime": 1738005020143, "results": "165", "hashOfConfig": "101"}, {"size": 2649, "mtime": 1745558530865, "results": "166", "hashOfConfig": "101"}, {"size": 856, "mtime": 1738005002533, "results": "167", "hashOfConfig": "101"}, {"size": 778, "mtime": 1737703033090, "results": "168", "hashOfConfig": "101"}, {"size": 9268, "mtime": 1739089382382, "results": "169", "hashOfConfig": "101"}, {"size": 4473, "mtime": 1749736717521, "results": "170", "hashOfConfig": "101"}, {"size": 6511, "mtime": 1747772230646, "results": "171", "hashOfConfig": "101"}, {"size": 3561, "mtime": 1747465926259, "results": "172", "hashOfConfig": "101"}, {"size": 2058, "mtime": 1745560016985, "results": "173", "hashOfConfig": "101"}, {"size": 3270, "mtime": 1747683592095, "results": "174", "hashOfConfig": "101"}, {"size": 13040, "mtime": 1749736717522, "results": "175", "hashOfConfig": "101"}, {"size": 35666, "mtime": 1749736717528, "results": "176", "hashOfConfig": "101"}, {"size": 17158, "mtime": 1749113919875, "results": "177", "hashOfConfig": "101"}, {"size": 12332, "mtime": 1749106493490, "results": "178", "hashOfConfig": "101"}, {"size": 1680, "mtime": 1749737616336, "results": "179", "hashOfConfig": "101"}, {"size": 35110, "mtime": 1749118685592, "results": "180", "hashOfConfig": "101"}, {"size": 17861, "mtime": 1749116896225, "results": "181", "hashOfConfig": "101"}, {"size": 317, "mtime": 1749231241721, "results": "182", "hashOfConfig": "101"}, {"size": 22414, "mtime": 1749237247953, "results": "183", "hashOfConfig": "101"}, {"size": 15311, "mtime": 1749239054836, "results": "184", "hashOfConfig": "101"}, {"size": 19477, "mtime": 1749236876615, "results": "185", "hashOfConfig": "101"}, {"size": 12103, "mtime": 1749736717523, "results": "186", "hashOfConfig": "101"}, {"size": 26689, "mtime": 1749237355112, "results": "187", "hashOfConfig": "101"}, {"size": 16917, "mtime": 1749736717523, "results": "188", "hashOfConfig": "101"}, {"size": 3567, "mtime": 1749282608572, "results": "189", "hashOfConfig": "101"}, {"size": 3229, "mtime": 1749749514923, "results": "190", "hashOfConfig": "101"}, {"size": 13936, "mtime": 1749750686297, "results": "191", "hashOfConfig": "101"}, {"size": 1833, "mtime": 1749750397933, "results": "192", "hashOfConfig": "101"}, {"size": 9434, "mtime": 1751190331897, "results": "193", "hashOfConfig": "101"}, {"size": 3176, "mtime": 1751190174797, "results": "194", "hashOfConfig": "101"}, {"size": 5569, "mtime": 1751190153517, "results": "195", "hashOfConfig": "101"}, {"size": 4644, "mtime": 1751190097589, "results": "196", "hashOfConfig": "101"}, {"size": 6877, "mtime": 1751190061103, "results": "197", "hashOfConfig": "101"}, {"size": 5619, "mtime": 1751190125112, "results": "198", "hashOfConfig": "101"}, {"size": 5823, "mtime": 1751190205858, "results": "199", "hashOfConfig": "101"}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eamnk2", {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\App.js", ["497"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\reportWebVitals.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\context\\UserContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\ErrorContext.js", ["498"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserLayout.js", ["499", "500", "501"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\WelcomeSplash.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLoginPage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserManagement.js", ["502", "503"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminDashboard.js", ["504", "505", "506", "507", "508", "509", "510", "511", "512", "513"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserRegistration.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeSystem.js", ["514", "515"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TransactionManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\BetManagement.js", ["516", "517", "518", "519", "520"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserLogin.js", ["521"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeaderboardManagement.js", ["522", "523"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AddUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ReportsAnalytics.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SystemSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentMethods.js", ["524"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Messages\\Messages.js", ["525", "526"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\DebitUser.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\TeamManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChallengeManagement.js", ["527"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueManagement.js", ["528", "529", "530", "531"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditChallenge.js", ["532", "533", "534", "535"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSeasonManagement.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreateLeague.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueDetails.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueUserManagement.js", ["536", "537", "538"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDashboard.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\JoinChallenge2.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ViewBets.js", ["539", "540"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\IncomingBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\PaymentHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Profile.js", ["541"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AcceptedBets.js", ["542", "543", "544", "545", "546"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Leaderboard.js", ["547"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ChangePassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Deposit.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Withdraw.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\FriendRequests.js", ["548"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueHome.js", ["549", "550", "551", "552"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Friends.js", ["553", "554", "555", "556", "557"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserAchievements.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SeasonHistory.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\LeagueSelection.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Transfer.js", ["558"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\MyLeagues.js", ["559"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditWallet.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\CreditHistory.js", ["560"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Challenges.js", ["561", "562"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminHeader.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Sidebar.js", ["563", "564", "565", "566"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ErrorAlert.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AdminFooter.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\utils\\errorHandler.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Header.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\config.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertContainer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Layout\\MainLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AlertMessage.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\ScrollToTop.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Footer.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\OldSidebar.js", ["567"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\ChallengesList.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\RecentBets.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\WelcomePage\\HeroSlider.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\DefaultLeagueBanner.js", ["568", "569"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\GeneralSettings.js", ["570"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SecuritySettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\SMTPSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\NotificationSettings.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\SiteConfigContext.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminReports.js", ["571", "572", "573"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\AdminLeaderboard.js", ["574"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FASetup.js", ["575", "576", "577"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\Admin2FAVerification.js", ["578"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminOTPVerification.js", ["579", "580"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\Admin\\AdminAuthPreferences.js", ["581", "582", "583", "584"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\Admin2FASettings.js", ["585", "586"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\UserDetails.js", ["587", "588"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\CustomModal.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\AuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\pages\\ForgotPassword.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\components\\UserAuthLayout.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\contexts\\CurrencyContext.js", ["589"], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\index.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\betService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\currencyService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\apiService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\services\\userService.js", [], [], "C:\\MAMP\\htdocs\\FanBet247\\frontend\\src\\hooks\\useApiService.js", ["590", "591"], [], {"ruleId": "592", "severity": 1, "message": "593", "line": 70, "column": 8, "nodeType": "594", "messageId": "595", "endLine": 70, "endColumn": 23}, {"ruleId": "596", "severity": 1, "message": "597", "line": 31, "column": 6, "nodeType": "598", "endLine": 31, "endColumn": 8, "suggestions": "599"}, {"ruleId": "596", "severity": 1, "message": "600", "line": 26, "column": 9, "nodeType": "601", "endLine": 26, "endColumn": 62}, {"ruleId": "596", "severity": 1, "message": "602", "line": 74, "column": 6, "nodeType": "598", "endLine": 74, "endColumn": 50, "suggestions": "603"}, {"ruleId": "596", "severity": 1, "message": "604", "line": 120, "column": 6, "nodeType": "598", "endLine": 120, "endColumn": 85, "suggestions": "605"}, {"ruleId": "592", "severity": 1, "message": "606", "line": 4, "column": 121, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 132}, {"ruleId": "592", "severity": 1, "message": "607", "line": 11, "column": 11, "nodeType": "594", "messageId": "595", "endLine": 11, "endColumn": 19}, {"ruleId": "592", "severity": 1, "message": "608", "line": 8, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 8, "endColumn": 16}, {"ruleId": "592", "severity": 1, "message": "609", "line": 10, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 10, "endColumn": 17}, {"ruleId": "592", "severity": 1, "message": "610", "line": 11, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 11, "endColumn": 13}, {"ruleId": "592", "severity": 1, "message": "611", "line": 12, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 12, "endColumn": 16}, {"ruleId": "592", "severity": 1, "message": "612", "line": 13, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 13, "endColumn": 8}, {"ruleId": "592", "severity": 1, "message": "613", "line": 14, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 14, "endColumn": 16}, {"ruleId": "592", "severity": 1, "message": "614", "line": 15, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 15, "endColumn": 10}, {"ruleId": "592", "severity": 1, "message": "615", "line": 16, "column": 3, "nodeType": "594", "messageId": "595", "endLine": 16, "endColumn": 9}, {"ruleId": "592", "severity": 1, "message": "616", "line": 20, "column": 16, "nodeType": "594", "messageId": "595", "endLine": 20, "endColumn": 19}, {"ruleId": "592", "severity": 1, "message": "617", "line": 47, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 47, "endColumn": 30}, {"ruleId": "592", "severity": 1, "message": "618", "line": 20, "column": 10, "nodeType": "594", "messageId": "595", "endLine": 20, "endColumn": 20}, {"ruleId": "592", "severity": 1, "message": "619", "line": 20, "column": 22, "nodeType": "594", "messageId": "595", "endLine": 20, "endColumn": 35}, {"ruleId": "592", "severity": 1, "message": "620", "line": 3, "column": 20, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 28}, {"ruleId": "592", "severity": 1, "message": "621", "line": 3, "column": 37, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 43}, {"ruleId": "592", "severity": 1, "message": "622", "line": 3, "column": 45, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 52}, {"ruleId": "592", "severity": 1, "message": "623", "line": 13, "column": 21, "nodeType": "594", "messageId": "595", "endLine": 13, "endColumn": 31}, {"ruleId": "596", "severity": 1, "message": "624", "line": 40, "column": 8, "nodeType": "598", "endLine": 40, "endColumn": 41, "suggestions": "625"}, {"ruleId": "592", "severity": 1, "message": "626", "line": 19, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 19, "endColumn": 21}, {"ruleId": "592", "severity": 1, "message": "620", "line": 3, "column": 20, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 28}, {"ruleId": "596", "severity": 1, "message": "627", "line": 39, "column": 8, "nodeType": "598", "endLine": 39, "endColumn": 42, "suggestions": "628"}, {"ruleId": "592", "severity": 1, "message": "629", "line": 3, "column": 34, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 41}, {"ruleId": "596", "severity": 1, "message": "630", "line": 24, "column": 8, "nodeType": "598", "endLine": 24, "endColumn": 19, "suggestions": "631"}, {"ruleId": "596", "severity": 1, "message": "632", "line": 33, "column": 8, "nodeType": "598", "endLine": 33, "endColumn": 30, "suggestions": "633"}, {"ruleId": "596", "severity": 1, "message": "634", "line": 43, "column": 8, "nodeType": "598", "endLine": 43, "endColumn": 10, "suggestions": "635"}, {"ruleId": "592", "severity": 1, "message": "636", "line": 11, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 11, "endColumn": 20}, {"ruleId": "592", "severity": 1, "message": "637", "line": 16, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 16, "endColumn": 12}, {"ruleId": "592", "severity": 1, "message": "638", "line": 17, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 17, "endColumn": 17}, {"ruleId": "592", "severity": 1, "message": "629", "line": 18, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 18, "endColumn": 12}, {"ruleId": "592", "severity": 1, "message": "639", "line": 1, "column": 60, "nodeType": "594", "messageId": "595", "endLine": 1, "endColumn": 66}, {"ruleId": "592", "severity": 1, "message": "640", "line": 28, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 28, "endColumn": 26}, {"ruleId": "592", "severity": 1, "message": "641", "line": 28, "column": 28, "nodeType": "594", "messageId": "595", "endLine": 28, "endColumn": 45}, {"ruleId": "596", "severity": 1, "message": "642", "line": 83, "column": 8, "nodeType": "598", "endLine": 83, "endColumn": 10, "suggestions": "643"}, {"ruleId": "592", "severity": 1, "message": "606", "line": 3, "column": 19, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 30}, {"ruleId": "592", "severity": 1, "message": "644", "line": 14, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 14, "endColumn": 24}, {"ruleId": "592", "severity": 1, "message": "645", "line": 93, "column": 11, "nodeType": "594", "messageId": "595", "endLine": 93, "endColumn": 23}, {"ruleId": "592", "severity": 1, "message": "646", "line": 114, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 114, "endColumn": 25}, {"ruleId": "592", "severity": 1, "message": "647", "line": 148, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 148, "endColumn": 22}, {"ruleId": "592", "severity": 1, "message": "648", "line": 4, "column": 10, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 17}, {"ruleId": "592", "severity": 1, "message": "649", "line": 13, "column": 10, "nodeType": "594", "messageId": "595", "endLine": 13, "endColumn": 17}, {"ruleId": "592", "severity": 1, "message": "650", "line": 14, "column": 10, "nodeType": "594", "messageId": "595", "endLine": 14, "endColumn": 15}, {"ruleId": "592", "severity": 1, "message": "647", "line": 115, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 115, "endColumn": 22}, {"ruleId": "592", "severity": 1, "message": "651", "line": 132, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 132, "endColumn": 19}, {"ruleId": "592", "severity": 1, "message": "652", "line": 145, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 145, "endColumn": 22}, {"ruleId": "596", "severity": 1, "message": "627", "line": 44, "column": 8, "nodeType": "598", "endLine": 44, "endColumn": 21, "suggestions": "653"}, {"ruleId": "596", "severity": 1, "message": "654", "line": 19, "column": 8, "nodeType": "598", "endLine": 19, "endColumn": 10, "suggestions": "655"}, {"ruleId": "592", "severity": 1, "message": "656", "line": 7, "column": 14, "nodeType": "594", "messageId": "595", "endLine": 7, "endColumn": 20}, {"ruleId": "592", "severity": 1, "message": "638", "line": 7, "column": 41, "nodeType": "594", "messageId": "595", "endLine": 7, "endColumn": 53}, {"ruleId": "592", "severity": 1, "message": "657", "line": 8, "column": 46, "nodeType": "594", "messageId": "595", "endLine": 8, "endColumn": 52}, {"ruleId": "592", "severity": 1, "message": "658", "line": 306, "column": 11, "nodeType": "594", "messageId": "595", "endLine": 306, "endColumn": 27}, {"ruleId": "592", "severity": 1, "message": "659", "line": 4, "column": 45, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 64}, {"ruleId": "592", "severity": 1, "message": "660", "line": 4, "column": 66, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 79}, {"ruleId": "592", "severity": 1, "message": "661", "line": 4, "column": 111, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 123}, {"ruleId": "596", "severity": 1, "message": "662", "line": 29, "column": 8, "nodeType": "598", "endLine": 29, "endColumn": 10, "suggestions": "663"}, {"ruleId": "592", "severity": 1, "message": "664", "line": 256, "column": 11, "nodeType": "594", "messageId": "595", "endLine": 256, "endColumn": 26}, {"ruleId": "596", "severity": 1, "message": "665", "line": 24, "column": 8, "nodeType": "598", "endLine": 24, "endColumn": 33, "suggestions": "666"}, {"ruleId": "592", "severity": 1, "message": "656", "line": 5, "column": 57, "nodeType": "594", "messageId": "595", "endLine": 5, "endColumn": 63}, {"ruleId": "596", "severity": 1, "message": "667", "line": 24, "column": 8, "nodeType": "598", "endLine": 24, "endColumn": 33, "suggestions": "668"}, {"ruleId": "592", "severity": 1, "message": "669", "line": 122, "column": 19, "nodeType": "594", "messageId": "595", "endLine": 122, "endColumn": 28}, {"ruleId": "592", "severity": 1, "message": "670", "line": 137, "column": 19, "nodeType": "594", "messageId": "595", "endLine": 137, "endColumn": 22}, {"ruleId": "592", "severity": 1, "message": "671", "line": 4, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 4, "endColumn": 14}, {"ruleId": "592", "severity": 1, "message": "672", "line": 6, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 6, "endColumn": 10}, {"ruleId": "592", "severity": 1, "message": "610", "line": 7, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 7, "endColumn": 15}, {"ruleId": "592", "severity": 1, "message": "673", "line": 8, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 8, "endColumn": 16}, {"ruleId": "592", "severity": 1, "message": "674", "line": 57, "column": 9, "nodeType": "594", "messageId": "595", "endLine": 57, "endColumn": 26}, {"ruleId": "592", "severity": 1, "message": "609", "line": 2, "column": 10, "nodeType": "594", "messageId": "595", "endLine": 2, "endColumn": 24}, {"ruleId": "592", "severity": 1, "message": "675", "line": 2, "column": 26, "nodeType": "594", "messageId": "595", "endLine": 2, "endColumn": 34}, {"ruleId": "592", "severity": 1, "message": "676", "line": 3, "column": 35, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 41}, {"ruleId": "592", "severity": 1, "message": "677", "line": 5, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 5, "endColumn": 15}, {"ruleId": "592", "severity": 1, "message": "608", "line": 6, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 6, "endColumn": 18}, {"ruleId": "596", "severity": 1, "message": "678", "line": 29, "column": 8, "nodeType": "598", "endLine": 29, "endColumn": 31, "suggestions": "679"}, {"ruleId": "596", "severity": 1, "message": "627", "line": 31, "column": 8, "nodeType": "598", "endLine": 31, "endColumn": 17, "suggestions": "680"}, {"ruleId": "592", "severity": 1, "message": "681", "line": 3, "column": 33, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 38}, {"ruleId": "592", "severity": 1, "message": "682", "line": 14, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 14, "endColumn": 21}, {"ruleId": "596", "severity": 1, "message": "683", "line": 29, "column": 8, "nodeType": "598", "endLine": 29, "endColumn": 10, "suggestions": "684"}, {"ruleId": "592", "severity": 1, "message": "685", "line": 3, "column": 41, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 62}, {"ruleId": "592", "severity": 1, "message": "685", "line": 3, "column": 40, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 61}, {"ruleId": "596", "severity": 1, "message": "686", "line": 28, "column": 8, "nodeType": "598", "endLine": 28, "endColumn": 24, "suggestions": "687"}, {"ruleId": "592", "severity": 1, "message": "629", "line": 3, "column": 51, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 58}, {"ruleId": "592", "severity": 1, "message": "672", "line": 3, "column": 102, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 107}, {"ruleId": "592", "severity": 1, "message": "688", "line": 18, "column": 12, "nodeType": "594", "messageId": "595", "endLine": 18, "endColumn": 21}, {"ruleId": "596", "severity": 1, "message": "689", "line": 23, "column": 8, "nodeType": "598", "endLine": 23, "endColumn": 17, "suggestions": "690"}, {"ruleId": "592", "severity": 1, "message": "691", "line": 3, "column": 40, "nodeType": "594", "messageId": "595", "endLine": 3, "endColumn": 46}, {"ruleId": "596", "severity": 1, "message": "692", "line": 30, "column": 8, "nodeType": "598", "endLine": 30, "endColumn": 10, "suggestions": "693"}, {"ruleId": "592", "severity": 1, "message": "694", "line": 6, "column": 5, "nodeType": "594", "messageId": "595", "endLine": 6, "endColumn": 11}, {"ruleId": "596", "severity": 1, "message": "695", "line": 38, "column": 8, "nodeType": "598", "endLine": 38, "endColumn": 16, "suggestions": "696"}, {"ruleId": "596", "severity": 1, "message": "697", "line": 51, "column": 8, "nodeType": "598", "endLine": 51, "endColumn": 10, "suggestions": "698"}, {"ruleId": "699", "severity": 1, "message": "700", "line": 93, "column": 39, "nodeType": "701", "messageId": "702", "endLine": 93, "endColumn": 86}, {"ruleId": "592", "severity": 1, "message": "703", "line": 177, "column": 15, "nodeType": "594", "messageId": "595", "endLine": 177, "endColumn": 30}, "no-unused-vars", "'LeagueSelection' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'removeError'. Either include it or remove the dependency array.", "ArrayExpression", ["704"], "The 'publicRoutes' array makes the dependencies of useCallback Hook (at line 98) change on every render. To fix this, wrap the initialization of 'publicRoutes' in its own useMemo() Hook.", "VariableDeclarator", "React Hook useCallback has a missing dependency: 'setUserData'. Either include it or remove the dependency array.", ["705"], "React Hook useEffect has a missing dependency: 'publicRoutes'. Either include it or remove the dependency array.", ["706"], "'FaChartLine' is defined but never used.", "'navigate' is assigned a value but never used.", "'FaCalendarAlt' is defined but never used.", "'FaFootballBall' is defined but never used.", "'FaChartBar' is defined but never used.", "'FaExchangeAlt' is defined but never used.", "'FaEye' is defined but never used.", "'FaCheckCircle' is defined but never used.", "'FaClock' is defined but never used.", "'FaBell' is defined but never used.", "'Bar' is defined but never used.", "'teamPopularityData' is assigned a value but never used.", "'challenges' is assigned a value but never used.", "'setChallenges' is assigned a value but never used.", "'FaFilter' is defined but never used.", "'FaEdit' is defined but never used.", "'FaTrash' is defined but never used.", "'setSuccess' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAllBets'. Either include it or remove the dependency array.", ["707"], "'otpExpiry' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchLeaderboard'. Either include it or remove the dependency array.", ["708"], "'FaTimes' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchConversations'. Either include it or remove the dependency array.", ["709"], "React Hook useEffect has missing dependencies: 'fetchMessages' and 'markMessagesAsRead'. Either include them or remove the dependency array.", ["710"], "React Hook useEffect has a missing dependency: 'checkChallengeStatus'. Either include it or remove the dependency array.", ["711"], "'FaMoneyBillWave' is defined but never used.", "'FaUsers' is defined but never used.", "'FaInfoCircle' is defined but never used.", "'useRef' is defined but never used.", "'payoutPreviews' is assigned a value but never used.", "'setPayoutPreviews' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'fetchLeagueDetails'. Either include it or remove the dependency array.", ["712"], "'loadingUsers' is assigned a value but never used.", "'currentUsers' is assigned a value but never used.", "'renderPagination' is assigned a value but never used.", "'calculateOdds' is assigned a value but never used.", "'FaCoins' is defined but never used.", "'loading' is assigned a value but never used.", "'error' is assigned a value but never used.", "'formatDate' is assigned a value but never used.", "'getUserStatus' is assigned a value but never used.", ["713"], "React Hook useEffect has a missing dependency: 'fetchPendingRequests'. Either include it or remove the dependency array.", ["714"], "'FaStar' is defined but never used.", "'FaFire' is defined but never used.", "'renderNavigation' is assigned a value but never used.", "'FaExclamationCircle' is defined but never used.", "'FaUserFriends' is defined but never used.", "'FaUserCircle' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFriends'. Either include it or remove the dependency array.", ["715"], "'handleChallenge' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchInitialData'. Either include it or remove the dependency array.", ["716"], "React Hook useEffect has a missing dependency: 'fetchCreditRequests'. Either include it or remove the dependency array.", ["717"], "'matchDate' is assigned a value but never used.", "'now' is assigned a value but never used.", "'FaGamepad' is defined but never used.", "'FaCog' is defined but never used.", "'FaMoneyBill' is defined but never used.", "'handleLogoutClick' is assigned a value but never used.", "'FaTrophy' is defined but never used.", "'FaSave' is defined but never used.", "'FaDownload' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchReport'. Either include it or remove the dependency array.", ["718"], ["719"], "'FaKey' is defined but never used.", "'secretKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'initiate2FASetup'. Either include it or remove the dependency array.", ["720"], "'FaExclamationTriangle' is defined but never used.", "React Hook useEffect has a missing dependency: 'sendInitialOTP'. Either include it or remove the dependency array.", ["721"], "'adminInfo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchPreferences'. Either include it or remove the dependency array.", ["722"], "'FaCopy' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAdminData'. Either include it or remove the dependency array.", ["723"], "'FaUser' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchUserBets', 'fetchUserDetails', and 'fetchUserTransactions'. Either include them or remove the dependency array.", ["724"], "React Hook useCallback has a missing dependency: 'CACHE_DURATION'. Either include it or remove the dependency array.", ["725"], "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'attempt'.", "ArrowFunctionExpression", "unsafeRefs", "'originalExecute' is assigned a value but never used.", {"desc": "726", "fix": "727"}, {"desc": "728", "fix": "729"}, {"desc": "730", "fix": "731"}, {"desc": "732", "fix": "733"}, {"desc": "734", "fix": "735"}, {"desc": "736", "fix": "737"}, {"desc": "738", "fix": "739"}, {"desc": "740", "fix": "741"}, {"desc": "742", "fix": "743"}, {"desc": "744", "fix": "745"}, {"desc": "746", "fix": "747"}, {"desc": "748", "fix": "749"}, {"desc": "750", "fix": "751"}, {"desc": "752", "fix": "753"}, {"desc": "754", "fix": "755"}, {"desc": "756", "fix": "757"}, {"desc": "758", "fix": "759"}, {"desc": "760", "fix": "761"}, {"desc": "762", "fix": "763"}, {"desc": "764", "fix": "765"}, {"desc": "766", "fix": "767"}, {"desc": "768", "fix": "769"}, "Update the dependencies array to be: [removeError]", {"range": "770", "text": "771"}, "Update the dependencies array to be: [token, userId, setUserData, navigate, location.pathname]", {"range": "772", "text": "773"}, "Update the dependencies array to be: [token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", {"range": "774", "text": "775"}, "Update the dependencies array to be: [pagination.currentPage, filters, fetchAllBets]", {"range": "776", "text": "777"}, "Update the dependencies array to be: [pagination.current_page, filters, fetchLeaderboard]", {"range": "778", "text": "779"}, "Update the dependencies array to be: [activeTab, fetchConversations]", {"range": "780", "text": "781"}, "Update the dependencies array to be: [fetchMessages, markMessagesAsRead, selectedConversation]", {"range": "782", "text": "783"}, "Update the dependencies array to be: [checkChallengeStatus]", {"range": "784", "text": "785"}, "Update the dependencies array to be: [fetchLeagueDetails]", {"range": "786", "text": "787"}, "Update the dependencies array to be: [currentPage, fetchLeaderboard]", {"range": "788", "text": "789"}, "Update the dependencies array to be: [fetchPendingRequests]", {"range": "790", "text": "791"}, "Update the dependencies array to be: [fetchFriends]", {"range": "792", "text": "793"}, "Update the dependencies array to be: [navigate, currentUserId, fetchInitialData]", {"range": "794", "text": "795"}, "Update the dependencies array to be: [navigate, currentUserId, fetchCreditRequests]", {"range": "796", "text": "797"}, "Update the dependencies array to be: [reportType, dateRange, fetchReport]", {"range": "798", "text": "799"}, "Update the dependencies array to be: [fetchLeaderboard, filters]", {"range": "800", "text": "801"}, "Update the dependencies array to be: [initiate2FASetup]", {"range": "802", "text": "803"}, "Update the dependencies array to be: [initialOtpSent, sendInitialOTP]", {"range": "804", "text": "805"}, "Update the dependencies array to be: [adminId, fetchPreferences]", {"range": "806", "text": "807"}, "Update the dependencies array to be: [fetchAdminData]", {"range": "808", "text": "809"}, "Update the dependencies array to be: [fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", {"range": "810", "text": "811"}, "Update the dependencies array to be: [CACHE_DURATION]", {"range": "812", "text": "813"}, [985, 987], "[removeError]", [2698, 2742], "[token, userId, setUserData, navigate, location.pathname]", [4127, 4206], "[token, userId, navigate, location.pathname, fetchUserData, fetchNotifications, publicRoutes]", [1193, 1226], "[pagination.currentPage, filters, fetchAllBets]", [1249, 1283], "[pagination.current_page, filters, fetchLeaderboard]", [1116, 1127], "[activeTab, fetchConversations]", [1474, 1496], "[fetch<PERSON>essages, markMessagesAsRead, selectedConversation]", [1532, 1534], "[checkChallengeStatus]", [3030, 3032], "[fetchLeagueDetails]", [1160, 1173], "[currentPage, fetchLeaderboard]", [696, 698], "[fetchPendingRequests]", [1331, 1333], "[fetchFriends]", [856, 881], "[navigate, currentUserId, fetchInitialData]", [891, 916], "[navigate, currentUserId, fetchCreditRequests]", [864, 887], "[reportType, dateRange, fetchReport]", [887, 896], "[fetchLeaderboard, filters]", [1137, 1139], "[initiate2FASetup]", [1264, 1280], "[initialOtpSent, sendInitialOTP]", [856, 865], "[adminId, fetchPreferences]", [1269, 1271], "[fetchAdminData]", [1021, 1029], "[fetchUserBets, fetchUserDetails, fetchUserTransactions, userId]", [1873, 1875], "[CACHE_DURATION]"]