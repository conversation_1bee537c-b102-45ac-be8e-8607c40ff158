import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaUserPlus, FaUser, FaEnvelope, FaLock, FaShieldAlt, FaCoins } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function AddUser() {
    const [teams, setTeams] = useState([]);
    const [newUser, setNewUser] = useState({
        username: '',
        full_name: '',
        email: '',
        password: '',
        favorite_team: '',
        balance: 0
    });
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    useEffect(() => {
        fetchTeams();
    }, []);

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            setTeams(response.data.data || []);
        } catch (err) {
            setError('Failed to fetch teams');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewUser(prev => ({ ...prev, [name]: value }));
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setSuccess('');

        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/add_user.php`, newUser);
            if (response.data.success) {
                setSuccess('User added successfully!');
                setNewUser({
                    username: '',
                    full_name: '',
                    email: '',
                    password: '',
                    favorite_team: '',
                    balance: 0
                });
            } else {
                setError(response.data.message || 'Failed to add user');
            }
        } catch (err) {
            setError('Failed to add user');
        }
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Page Header */}
            <div className="mb-8">
                <h1 className="text-2xl font-bold text-gray-800">Add New User</h1>
                <p className="text-gray-600">Create a new user account in the system</p>
            </div>

            {/* Notification Messages */}
            {error && (
                <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}
            {success && (
                <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{success}</span>
                </div>
            )}

            {/* Form Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 w-full">
                <div className="flex items-center mb-6">
                    <div className="rounded-full bg-green-100 p-3 mr-4">
                        <FaUserPlus className="text-green-700 text-xl" />
                    </div>
                    <h2 className="text-lg font-semibold text-gray-800">User Information</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Username */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Username</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaUser className="h-4 w-4 text-green-600" />
                                </div>
                                <input
                                    type="text"
                                    name="username"
                                    value={newUser.username}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter username"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                />
                            </div>
                        </div>

                        {/* Full Name */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaUser className="h-4 w-4 text-green-600" />
                                </div>
                                <input
                                    type="text"
                                    name="full_name"
                                    value={newUser.full_name}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter full name"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                />
                            </div>
                        </div>

                        {/* Email */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaEnvelope className="h-4 w-4 text-green-600" />
                                </div>
                                <input
                                    type="email"
                                    name="email"
                                    value={newUser.email}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter email address"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                />
                            </div>
                        </div>

                        {/* Password */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaLock className="h-4 w-4 text-green-600" />
                                </div>
                                <input
                                    type="password"
                                    name="password"
                                    value={newUser.password}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter password"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                />
                            </div>
                        </div>

                        {/* Favorite Team */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Favorite Team</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaShieldAlt className="h-4 w-4 text-green-600" />
                                </div>
                                <select
                                    name="favorite_team"
                                    value={newUser.favorite_team}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                >
                                    <option value="">Select Favorite Team</option>
                                    {teams.map(team => (
                                        <option key={team.id} value={team.name}>{team.name}</option>
                                    ))}
                                </select>
                            </div>
                        </div>

                        {/* Initial Balance */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Initial Balance</label>
                            <div className="relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                    <FaCoins className="h-4 w-4 text-green-600" />
                                </div>
                                <input
                                    type="number"
                                    name="balance"
                                    value={newUser.balance}
                                    onChange={handleInputChange}
                                    className="block w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                                    placeholder="Enter initial balance"
                                    required
                                    style={{paddingLeft: '2.5rem'}}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="pt-4">
                        <button
                            type="submit"
                            className="w-full flex justify-center py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-700 hover:bg-green-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                            <FaUserPlus className="mr-2 h-5 w-5" /> Add User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}

export default AddUser;