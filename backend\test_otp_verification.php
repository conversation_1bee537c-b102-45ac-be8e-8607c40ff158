<?php
/**
 * Test OTP Verification
 * Debug the OTP verification process
 */

header('Content-Type: text/plain');

require_once 'includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    if (!$conn) {
        throw new Exception("Database connection failed");
    }

    // Get the first admin for testing
    $stmt = $conn->query("SELECT admin_id, username, email FROM admins LIMIT 1");
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        throw new Exception("No admin found for testing");
    }

    echo "Testing OTP verification for admin: {$admin['username']} ({$admin['email']})\n";
    echo "Admin ID: {$admin['admin_id']}\n\n";

    // Step 1: Send OTP
    echo "Step 1: Sending OTP...\n";
    $sendData = ['admin_id' => $admin['admin_id']];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/FanBet247/backend/handlers/admin_send_otp.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($sendData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($sendData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $sendResponse = curl_exec($ch);
    $sendHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Send OTP Response Code: $sendHttpCode\n";
    echo "Send OTP Response: $sendResponse\n\n";

    $sendResponseData = json_decode($sendResponse, true);
    
    if (!$sendResponseData || !$sendResponseData['success']) {
        throw new Exception("Failed to send OTP: " . ($sendResponseData['message'] ?? 'Unknown error'));
    }

    // Step 2: Get the OTP from database
    echo "Step 2: Retrieving OTP from database...\n";
    $stmt = $conn->prepare("
        SELECT otp, expires_at, attempts, used, created_at 
        FROM admin_otp 
        WHERE admin_id = ? 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$admin['admin_id']]);
    $otpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$otpRecord) {
        throw new Exception("OTP not found in database");
    }

    echo "OTP Code: {$otpRecord['otp']}\n";
    echo "Expires at: {$otpRecord['expires_at']}\n";
    echo "Attempts: {$otpRecord['attempts']}\n";
    echo "Used: {$otpRecord['used']}\n";
    echo "Created at: {$otpRecord['created_at']}\n\n";

    // Step 3: Test OTP verification
    echo "Step 3: Testing OTP verification...\n";
    $verifyData = [
        'admin_id' => $admin['admin_id'],
        'otp' => $otpRecord['otp']
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/FanBet247/backend/handlers/admin_verify_otp.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($verifyData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($verifyData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $verifyResponse = curl_exec($ch);
    $verifyHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Verify OTP Response Code: $verifyHttpCode\n";
    echo "Verify OTP Response: $verifyResponse\n\n";

    $verifyResponseData = json_decode($verifyResponse, true);
    
    if ($verifyResponseData && $verifyResponseData['success']) {
        echo "✅ OTP verification successful!\n";
        echo "Admin ID: {$verifyResponseData['admin_id']}\n";
        echo "Username: {$verifyResponseData['username']}\n";
        echo "Role: {$verifyResponseData['role']}\n";
        echo "Session Token: " . substr($verifyResponseData['session_token'], 0, 20) . "...\n";
    } else {
        echo "❌ OTP verification failed!\n";
        echo "Error: " . ($verifyResponseData['message'] ?? 'Unknown error') . "\n";
        
        // Check if OTP was marked as used
        $stmt = $conn->prepare("
            SELECT otp, expires_at, attempts, used 
            FROM admin_otp 
            WHERE admin_id = ? 
            ORDER BY created_at DESC 
            LIMIT 1
        ");
        $stmt->execute([$admin['admin_id']]);
        $updatedOtpRecord = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "\nOTP Record after verification attempt:\n";
        echo "OTP Code: {$updatedOtpRecord['otp']}\n";
        echo "Expires at: {$updatedOtpRecord['expires_at']}\n";
        echo "Attempts: {$updatedOtpRecord['attempts']}\n";
        echo "Used: {$updatedOtpRecord['used']}\n";
    }

    // Step 4: Test with wrong OTP
    echo "\n\nStep 4: Testing with wrong OTP...\n";
    $wrongVerifyData = [
        'admin_id' => $admin['admin_id'],
        'otp' => '999999'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/FanBet247/backend/handlers/admin_verify_otp.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($wrongVerifyData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($wrongVerifyData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $wrongVerifyResponse = curl_exec($ch);
    $wrongVerifyHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "Wrong OTP Response Code: $wrongVerifyHttpCode\n";
    echo "Wrong OTP Response: $wrongVerifyResponse\n";

    $wrongVerifyResponseData = json_decode($wrongVerifyResponse, true);
    
    if ($wrongVerifyResponseData && !$wrongVerifyResponseData['success']) {
        echo "✅ Wrong OTP correctly rejected!\n";
        echo "Error message: {$wrongVerifyResponseData['message']}\n";
    } else {
        echo "❌ Wrong OTP was incorrectly accepted!\n";
    }

    echo "\n🎉 OTP verification test completed!\n";

} catch (Exception $e) {
    echo "❌ Test failed: " . $e->getMessage() . "\n";
}
?>
