.accepted-bets-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.accepted-bets-container h2 {
  color: #333;
  margin-bottom: 20px;
}

.bets-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.bets-table th,
.bets-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.bets-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
}

.bets-table tr:hover {
  background-color: #f8f9fa;
}

.teams-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.status-badge.open {
  background-color: #e3f2fd;
  color: #1976d2;
}

.status-badge.joined {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.completed {
  background-color: #ede7f6;
  color: #5e35b1;
}

.view-details-btn {
  padding: 6px 12px;
  background-color: #1976d2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.view-details-btn:hover {
  background-color: #1565c0;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-top: 20px;
}

.pagination-button {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background-color: white;
  color: #495057;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.pagination-button.active {
  background-color: #1976d2;
  color: white;
  border-color: #1976d2;
}

.pagination-button:disabled {
  background-color: #e9ecef;
  cursor: not-allowed;
}

.error {
  color: #dc3545;
  margin: 10px 0;
  padding: 10px;
  background-color: #f8d7da;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}
