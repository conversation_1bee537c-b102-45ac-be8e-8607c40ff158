/**
 * Currency Context - Fixed Version
 * 
 * Provides currency data and conversion utilities throughout the application
 * with proper error handling, caching, and infinite loop prevention.
 */

import React, { createContext, useState, useContext, useEffect, useCallback, useMemo } from 'react';
import { currencyService } from '../services';
import { useUser } from '../context/UserContext';

const CurrencyContext = createContext();

export const CurrencyProvider = ({ children }) => {
    const [currencies, setCurrencies] = useState([]);
    const [exchangeRates, setExchangeRates] = useState([]);
    const [userCurrency, setUserCurrency] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [initialized, setInitialized] = useState(false);
    
    const { userData } = useUser();

    // Cache configuration
    const CACHE_KEY = 'fanbet247_currencies';
    const CACHE_EXPIRY_KEY = 'fanbet247_currencies_expiry';
    const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

    // Cache utilities
    const getCachedData = useCallback(() => {
        try {
            const cached = localStorage.getItem(CACHE_KEY);
            const expiry = localStorage.getItem(CACHE_EXPIRY_KEY);
            
            if (cached && expiry && Date.now() < parseInt(expiry)) {
                return JSON.parse(cached);
            }
        } catch (error) {
            console.error('Error reading cached currencies:', error);
        }
        return null;
    }, []);

    const setCachedData = useCallback((data) => {
        try {
            localStorage.setItem(CACHE_KEY, JSON.stringify(data));
            localStorage.setItem(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());
        } catch (error) {
            console.error('Error caching currencies:', error);
        }
    }, []);

    // Load currencies and exchange rates
    const loadCurrencyData = useCallback(async (forceRefresh = false) => {
        if (loading) return; // Prevent multiple simultaneous loads
        
        try {
            setLoading(true);
            setError(null);

            // Try cache first unless force refresh
            if (!forceRefresh) {
                const cached = getCachedData();
                if (cached && cached.currencies && cached.exchangeRates) {
                    setCurrencies(cached.currencies);
                    setExchangeRates(cached.exchangeRates);
                    setInitialized(true);
                    return;
                }
            }

            // Fetch fresh data
            const [currenciesResponse, ratesResponse] = await Promise.all([
                currencyService.getCurrencies(true),
                currencyService.getExchangeRates()
            ]);

            if (currenciesResponse.success && ratesResponse.success) {
                const currenciesData = currenciesResponse.data.currencies || [];
                const ratesData = ratesResponse.data.exchange_rates || [];

                setCurrencies(currenciesData);
                setExchangeRates(ratesData);

                // Cache the data
                setCachedData({
                    currencies: currenciesData,
                    exchangeRates: ratesData
                });

                setInitialized(true);
            } else {
                throw new Error(
                    currenciesResponse.message || 
                    ratesResponse.message || 
                    'Failed to load currency data'
                );
            }
        } catch (err) {
            console.error('Error loading currency data:', err);
            setError(err.message || 'Failed to load currency data');
            
            // Try to use cached data as fallback
            const cached = getCachedData();
            if (cached && cached.currencies) {
                setCurrencies(cached.currencies);
                setExchangeRates(cached.exchangeRates || []);
                setInitialized(true);
            }
        } finally {
            setLoading(false);
        }
    }, [loading, getCachedData, setCachedData]);

    // Load user currency preference
    const loadUserCurrency = useCallback(async (userId) => {
        if (!userId || !currencies.length) return;

        try {
            const response = await currencyService.getUserCurrencyPreference(userId);
            
            if (response.success && response.data) {
                setUserCurrency(response.data);
            } else {
                // Default to USD if no preference found
                const usdCurrency = currencies.find(c => c.currency_code === 'USD');
                if (usdCurrency) {
                    setUserCurrency(usdCurrency);
                }
            }
        } catch (err) {
            console.error('Error loading user currency preference:', err);
            // Default to USD on error
            const usdCurrency = currencies.find(c => c.currency_code === 'USD');
            if (usdCurrency) {
                setUserCurrency(usdCurrency);
            }
        }
    }, [currencies]);

    // Convert FanCoin to user's preferred currency
    const convertToUserCurrency = useCallback((fanCoinAmount) => {
        if (!userCurrency || !exchangeRates.length || !fanCoinAmount) {
            return {
                amount: fanCoinAmount,
                currency: 'FC',
                symbol: 'FC',
                formatted: `${fanCoinAmount} FC`
            };
        }

        const rate = exchangeRates.find(r => r.currency_id === userCurrency.id);
        if (!rate) {
            return {
                amount: fanCoinAmount,
                currency: 'FC',
                symbol: 'FC',
                formatted: `${fanCoinAmount} FC`
            };
        }

        const convertedAmount = fanCoinAmount * rate.rate_to_fancoin;
        
        return {
            amount: convertedAmount,
            currency: userCurrency.currency_code,
            symbol: userCurrency.currency_symbol,
            formatted: `${userCurrency.currency_symbol}${convertedAmount.toFixed(2)}`
        };
    }, [userCurrency, exchangeRates]);

    // Format amount for display with both currencies
    const formatAmountForDisplay = useCallback((fanCoinAmount, showBoth = true) => {
        const conversion = convertToUserCurrency(fanCoinAmount);
        
        if (!showBoth || conversion.currency === 'FC') {
            return conversion.formatted;
        }

        return `${conversion.formatted} (${fanCoinAmount} FC)`;
    }, [convertToUserCurrency]);

    // Get currency by ID
    const getCurrencyById = useCallback((id) => {
        return currencies.find(c => c.id === id);
    }, [currencies]);

    // Get currency by code
    const getCurrencyByCode = useCallback((code) => {
        return currencies.find(c => c.currency_code === code);
    }, [currencies]);

    // Update user currency preference
    const updateUserCurrency = useCallback(async (newCurrency) => {
        if (!userData?.user_id) return;

        try {
            const response = await currencyService.updateUserCurrencyPreference(
                userData.user_id, 
                newCurrency.id
            );

            if (response.success) {
                setUserCurrency(newCurrency);
                return true;
            } else {
                throw new Error(response.message || 'Failed to update currency preference');
            }
        } catch (err) {
            console.error('Error updating user currency:', err);
            setError(err.message);
            return false;
        }
    }, [userData]);

    // Initialize currency data on mount
    useEffect(() => {
        if (!initialized) {
            loadCurrencyData();
        }
    }, [initialized, loadCurrencyData]);

    // Load user currency when user data or currencies change
    useEffect(() => {
        if (userData?.user_id && currencies.length > 0 && !userCurrency) {
            loadUserCurrency(userData.user_id);
        }
    }, [userData?.user_id, currencies.length, userCurrency, loadUserCurrency]);

    // Memoized context value to prevent unnecessary re-renders
    const contextValue = useMemo(() => ({
        // Data
        currencies,
        exchangeRates,
        userCurrency,
        loading,
        error,
        initialized,

        // Functions
        convertToUserCurrency,
        formatAmountForDisplay,
        getCurrencyById,
        getCurrencyByCode,
        updateUserCurrency,
        refreshCurrencyData: () => loadCurrencyData(true),

        // State setters (for external updates)
        setCurrencies,
        setExchangeRates,
        setUserCurrency
    }), [
        currencies,
        exchangeRates,
        userCurrency,
        loading,
        error,
        initialized,
        convertToUserCurrency,
        formatAmountForDisplay,
        getCurrencyById,
        getCurrencyByCode,
        updateUserCurrency,
        loadCurrencyData
    ]);

    return (
        <CurrencyContext.Provider value={contextValue}>
            {children}
        </CurrencyContext.Provider>
    );
};

export const useCurrency = () => {
    const context = useContext(CurrencyContext);
    if (!context) {
        throw new Error('useCurrency must be used within a CurrencyProvider');
    }
    return context;
};

export default CurrencyContext;
