{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\components\\\\Sidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, NavLink } from 'react-router-dom';\nimport { FaGamepad, FaUsers, FaCog, FaChartBar, FaMoneyBill, FaTrophy, FaHome, FaUserPlus, FaCreditCard, FaFutbol, FaTasks, FaCoins, FaCalendarAlt, FaUserFriends, FaCashRegister, FaExchangeAlt, FaMedal, FaWrench, FaChartLine, FaShieldAlt, FaSignOutAlt, FaDice, FaBars, FaTimes } from 'react-icons/fa';\nimport './AdminSidebar.css';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Sidebar() {\n  _s();\n  const navigate = useNavigate();\n  const {\n    config\n  } = useSiteConfig();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Check screen size and set mobile state\n  useEffect(() => {\n    const checkScreenSize = () => {\n      const screenWidth = window.innerWidth;\n      const isMobileScreen = screenWidth <= 1024; // Tablets and mobile\n      setIsMobile(isMobileScreen);\n\n      // Auto-collapse on small screens (≤1024px)\n      if (isMobileScreen) {\n        setIsCollapsed(true);\n      } else {\n        // On larger screens, restore to expanded state unless manually collapsed\n        const wasManuallyCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';\n        setIsCollapsed(wasManuallyCollapsed);\n      }\n    };\n    checkScreenSize();\n    window.addEventListener('resize', checkScreenSize);\n    return () => window.removeEventListener('resize', checkScreenSize);\n  }, []);\n  const toggleSidebar = () => {\n    const newCollapsedState = !isCollapsed;\n    setIsCollapsed(newCollapsedState);\n\n    // Save manual collapse state for larger screens\n    if (window.innerWidth > 1024) {\n      localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n    }\n  };\n\n  // Define all menu items as a flat list\n  const menuItems = [{\n    link: '/admin/dashboard',\n    text: 'Overview',\n    icon: /*#__PURE__*/_jsxDEV(FaHome, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 61\n    }, this)\n  },\n  // Challenges\n  {\n    link: '/admin/challenge-system',\n    text: 'Challenge System',\n    icon: /*#__PURE__*/_jsxDEV(FaFutbol, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 76\n    }, this)\n  }, {\n    link: '/admin/challenge-management',\n    text: 'Challenge Management',\n    icon: /*#__PURE__*/_jsxDEV(FaTasks, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 84\n    }, this)\n  }, {\n    link: '/admin/credit-challenge',\n    text: 'Credit Challenge',\n    icon: /*#__PURE__*/_jsxDEV(FaCoins, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 76\n    }, this)\n  }, {\n    link: '/admin/team-management',\n    text: 'Team Management',\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 74\n    }, this)\n  }, {\n    link: '/admin/bets',\n    text: 'Bet Management',\n    icon: /*#__PURE__*/_jsxDEV(FaDice, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 79,\n      columnNumber: 62\n    }, this)\n  },\n  // Users\n  {\n    link: '/admin/users',\n    text: 'User Management',\n    icon: /*#__PURE__*/_jsxDEV(FaUsers, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 64\n    }, this)\n  }, {\n    link: '/admin/add-user',\n    text: 'Add User',\n    icon: /*#__PURE__*/_jsxDEV(FaUserPlus, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 60\n    }, this)\n  }, {\n    link: '/admin/credit-user',\n    text: 'Credit User',\n    icon: /*#__PURE__*/_jsxDEV(FaCreditCard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 66\n    }, this)\n  },\n  // Leagues\n  {\n    link: '/admin/league-management',\n    text: 'League Management',\n    icon: /*#__PURE__*/_jsxDEV(FaTrophy, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 78\n    }, this)\n  }, {\n    link: '/admin/league-seasons',\n    text: 'Season Management',\n    icon: /*#__PURE__*/_jsxDEV(FaCalendarAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 75\n    }, this)\n  }, {\n    link: '/admin/league-users',\n    text: 'League Users',\n    icon: /*#__PURE__*/_jsxDEV(FaUserFriends, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 68\n    }, this)\n  },\n  // Finance\n  {\n    link: '/admin/payment-methods',\n    text: 'Payment Methods',\n    icon: /*#__PURE__*/_jsxDEV(FaCashRegister, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 74\n    }, this)\n  }, {\n    link: '/admin/currency-management',\n    text: 'Currency Management',\n    icon: /*#__PURE__*/_jsxDEV(FaCoins, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 82\n    }, this)\n  }, {\n    link: '/admin/transactions',\n    text: 'Transactions',\n    icon: /*#__PURE__*/_jsxDEV(FaExchangeAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 68\n    }, this)\n  },\n  // System\n  {\n    link: '/admin/leaderboard',\n    text: 'Leaderboard Management',\n    icon: /*#__PURE__*/_jsxDEV(FaMedal, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 77\n    }, this)\n  }, {\n    link: '/admin/settings',\n    text: 'System Settings',\n    icon: /*#__PURE__*/_jsxDEV(FaWrench, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 67\n    }, this)\n  }, {\n    link: '/admin/security-settings',\n    text: 'Security Settings',\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 78\n    }, this)\n  }, {\n    link: '/admin/2fa-settings',\n    text: '2FA Settings',\n    icon: /*#__PURE__*/_jsxDEV(FaShieldAlt, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 68\n    }, this)\n  }, {\n    link: '/admin/reports',\n    text: 'Reports and Analytics',\n    icon: /*#__PURE__*/_jsxDEV(FaChartLine, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 72\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"mobile-sidebar-toggle\",\n      onClick: toggleSidebar,\n      \"aria-label\": \"Toggle sidebar\",\n      children: isCollapsed ? /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 36\n      }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 49\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `admin-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"sidebar-header\",\n        children: [!isMobile && /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"desktop-sidebar-toggle\",\n          onClick: toggleSidebar,\n          \"aria-label\": \"Toggle sidebar\",\n          children: isCollapsed ? /*#__PURE__*/_jsxDEV(FaBars, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 44\n          }, this) : /*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 57\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo\",\n          children: !isCollapsed && config.site_logo ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `/backend/${config.site_logo}`,\n            alt: config.site_name || \"Site Logo\",\n            className: \"logo-icon\",\n            onError: e => {\n              e.target.style.display = 'none';\n              e.target.nextSibling.style.display = 'block';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 29\n          }, this) : !isCollapsed ? /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-text\",\n            children: config.site_name || \"FanBet247\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 29\n          }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"logo-collapsed\",\n            children: \"F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n        className: \"admin-sidebar-nav simple-menu\",\n        children: [menuItems.map((item, index) => /*#__PURE__*/_jsxDEV(NavLink, {\n          to: item.link,\n          className: ({\n            isActive\n          }) => `simple-nav-item ${isActive ? 'active' : ''}`,\n          end: true,\n          title: isCollapsed ? item.text : '',\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"simple-nav-item-icon\",\n            children: item.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 29\n          }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"simple-nav-item-text\",\n            children: item.text\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 46\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 25\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"sidebar-footer\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"logout-button\",\n            onClick: () => navigate('/admin/login'),\n            style: {\n              backgroundColor: '#dc2626',\n              color: 'white'\n            },\n            title: isCollapsed ? 'Logout' : '',\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logout-icon\",\n              style: {\n                color: 'white'\n              },\n              children: /*#__PURE__*/_jsxDEV(FaSignOutAlt, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 29\n            }, this), !isCollapsed && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"logout-text\",\n              style: {\n                color: 'white'\n              },\n              children: \"Logout\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 46\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n}\n_s(Sidebar, \"MI7hoPuk+1REEr8M4xv1a4eeRb0=\", false, function () {\n  return [useNavigate, useSiteConfig];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "NavLink", "FaGamepad", "FaUsers", "FaCog", "FaChartBar", "FaMoneyBill", "FaTrophy", "FaHome", "FaUserPlus", "FaCreditCard", "FaFutbol", "FaTasks", "FaCoins", "FaCalendarAlt", "FaUserFriends", "FaCashRegister", "FaExchangeAlt", "FaMedal", "FaWrench", "FaChartLine", "FaShieldAlt", "FaSignOutAlt", "FaDice", "FaBars", "FaTimes", "useSiteConfig", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "_s", "navigate", "config", "isCollapsed", "setIsCollapsed", "isMobile", "setIsMobile", "checkScreenSize", "screenWidth", "window", "innerWidth", "isMobileScreen", "wasManuallyCollapsed", "localStorage", "getItem", "addEventListener", "removeEventListener", "toggleSidebar", "newCollapsedState", "setItem", "toString", "menuItems", "link", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "className", "onClick", "site_logo", "src", "alt", "site_name", "onError", "e", "target", "style", "display", "nextS<PERSON>ling", "map", "item", "index", "to", "isActive", "end", "title", "backgroundColor", "color", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, NavLink } from 'react-router-dom';\nimport {\n    FaGamepad,\n    FaUsers,\n    FaCog,\n    FaChartBar,\n    FaMoneyBill,\n    FaTrophy,\n    FaHome,\n    FaUserPlus,\n    FaCreditCard,\n    FaFutbol,\n    FaTasks,\n    FaCoins,\n    FaCalendarAlt,\n    FaUserFriends,\n    FaCashRegister,\n    FaExchangeAlt,\n    FaMedal,\n    FaWrench,\n    FaChartLine,\n    FaShieldAlt,\n    FaSignOutAlt,\n    FaDice,\n    FaBars,\n    FaTimes\n} from 'react-icons/fa';\nimport './AdminSidebar.css';\nimport { useSiteConfig } from '../contexts/SiteConfigContext';\n\nfunction Sidebar() {\n    const navigate = useNavigate();\n    const { config } = useSiteConfig();\n    const [isCollapsed, setIsCollapsed] = useState(false);\n    const [isMobile, setIsMobile] = useState(false);\n\n    // Check screen size and set mobile state\n    useEffect(() => {\n        const checkScreenSize = () => {\n            const screenWidth = window.innerWidth;\n            const isMobileScreen = screenWidth <= 1024; // Tablets and mobile\n            setIsMobile(isMobileScreen);\n\n            // Auto-collapse on small screens (≤1024px)\n            if (isMobileScreen) {\n                setIsCollapsed(true);\n            } else {\n                // On larger screens, restore to expanded state unless manually collapsed\n                const wasManuallyCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';\n                setIsCollapsed(wasManuallyCollapsed);\n            }\n        };\n\n        checkScreenSize();\n        window.addEventListener('resize', checkScreenSize);\n        return () => window.removeEventListener('resize', checkScreenSize);\n    }, []);\n\n    const toggleSidebar = () => {\n        const newCollapsedState = !isCollapsed;\n        setIsCollapsed(newCollapsedState);\n\n        // Save manual collapse state for larger screens\n        if (window.innerWidth > 1024) {\n            localStorage.setItem('sidebarCollapsed', newCollapsedState.toString());\n        }\n    };\n\n    // Define all menu items as a flat list\n    const menuItems = [\n        { link: '/admin/dashboard', text: 'Overview', icon: <FaHome /> },\n\n        // Challenges\n        { link: '/admin/challenge-system', text: 'Challenge System', icon: <FaFutbol /> },\n        { link: '/admin/challenge-management', text: 'Challenge Management', icon: <FaTasks /> },\n        { link: '/admin/credit-challenge', text: 'Credit Challenge', icon: <FaCoins /> },\n        { link: '/admin/team-management', text: 'Team Management', icon: <FaShieldAlt /> },\n        { link: '/admin/bets', text: 'Bet Management', icon: <FaDice /> },\n\n        // Users\n        { link: '/admin/users', text: 'User Management', icon: <FaUsers /> },\n        { link: '/admin/add-user', text: 'Add User', icon: <FaUserPlus /> },\n        { link: '/admin/credit-user', text: 'Credit User', icon: <FaCreditCard /> },\n\n        // Leagues\n        { link: '/admin/league-management', text: 'League Management', icon: <FaTrophy /> },\n        { link: '/admin/league-seasons', text: 'Season Management', icon: <FaCalendarAlt /> },\n        { link: '/admin/league-users', text: 'League Users', icon: <FaUserFriends /> },\n\n        // Finance\n        { link: '/admin/payment-methods', text: 'Payment Methods', icon: <FaCashRegister /> },\n        { link: '/admin/currency-management', text: 'Currency Management', icon: <FaCoins /> },\n        { link: '/admin/transactions', text: 'Transactions', icon: <FaExchangeAlt /> },\n\n        // System\n        { link: '/admin/leaderboard', text: 'Leaderboard Management', icon: <FaMedal /> },\n        { link: '/admin/settings', text: 'System Settings', icon: <FaWrench /> },\n        { link: '/admin/security-settings', text: 'Security Settings', icon: <FaShieldAlt /> },\n        { link: '/admin/2fa-settings', text: '2FA Settings', icon: <FaShieldAlt /> },\n        { link: '/admin/reports', text: 'Reports and Analytics', icon: <FaChartLine /> }\n    ];\n\n    return (\n        <>\n            {/* Mobile Toggle Button */}\n            {isMobile && (\n                <button\n                    className=\"mobile-sidebar-toggle\"\n                    onClick={toggleSidebar}\n                    aria-label=\"Toggle sidebar\"\n                >\n                    {isCollapsed ? <FaBars /> : <FaTimes />}\n                </button>\n            )}\n\n            <div className={`admin-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}>\n                <div className=\"sidebar-header\">\n                    {/* Desktop Toggle Button */}\n                    {!isMobile && (\n                        <button\n                            className=\"desktop-sidebar-toggle\"\n                            onClick={toggleSidebar}\n                            aria-label=\"Toggle sidebar\"\n                        >\n                            {isCollapsed ? <FaBars /> : <FaTimes />}\n                        </button>\n                    )}\n\n                    <div className=\"logo\">\n                        {!isCollapsed && config.site_logo ? (\n                            <img\n                                src={`/backend/${config.site_logo}`}\n                                alt={config.site_name || \"Site Logo\"}\n                                className=\"logo-icon\"\n                                onError={(e) => {\n                                    e.target.style.display = 'none';\n                                    e.target.nextSibling.style.display = 'block';\n                                }}\n                            />\n                        ) : !isCollapsed ? (\n                            <span className=\"logo-text\">{config.site_name || \"FanBet247\"}</span>\n                        ) : (\n                            <span className=\"logo-collapsed\">F</span>\n                        )}\n                    </div>\n                </div>\n\n                <nav className=\"admin-sidebar-nav simple-menu\">\n                    {menuItems.map((item, index) => (\n                        <NavLink\n                            key={index}\n                            to={item.link}\n                            className={({ isActive }) =>\n                                `simple-nav-item ${isActive ? 'active' : ''}`\n                            }\n                            end\n                            title={isCollapsed ? item.text : ''}\n                        >\n                            <span className=\"simple-nav-item-icon\">{item.icon}</span>\n                            {!isCollapsed && <span className=\"simple-nav-item-text\">{item.text}</span>}\n                        </NavLink>\n                    ))}\n\n                    {/* Logout button at bottom of sidebar */}\n                    <div className=\"sidebar-footer\">\n                        <button\n                            className=\"logout-button\"\n                            onClick={() => navigate('/admin/login')}\n                            style={{ backgroundColor: '#dc2626', color: 'white' }}\n                            title={isCollapsed ? 'Logout' : ''}\n                        >\n                            <span className=\"logout-icon\" style={{ color: 'white' }}>\n                                <FaSignOutAlt />\n                            </span>\n                            {!isCollapsed && <span className=\"logout-text\" style={{ color: 'white' }}>Logout</span>}\n                        </button>\n                    </div>\n                </nav>\n            </div>\n        </>\n    );\n}\n\nexport default Sidebar;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,OAAO,QAAQ,kBAAkB;AACvD,SACIC,SAAS,EACTC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,WAAW,EACXC,QAAQ,EACRC,MAAM,EACNC,UAAU,EACVC,YAAY,EACZC,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,OAAO,EACPC,QAAQ,EACRC,WAAW,EACXC,WAAW,EACXC,YAAY,EACZC,MAAM,EACNC,MAAM,EACNC,OAAO,QACJ,gBAAgB;AACvB,OAAO,oBAAoB;AAC3B,SAASC,aAAa,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,SAASC,OAAOA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAMC,QAAQ,GAAGjC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkC;EAAO,CAAC,GAAGR,aAAa,CAAC,CAAC;EAClC,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACuC,QAAQ,EAAEC,WAAW,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAC,SAAS,CAAC,MAAM;IACZ,MAAMwC,eAAe,GAAGA,CAAA,KAAM;MAC1B,MAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU;MACrC,MAAMC,cAAc,GAAGH,WAAW,IAAI,IAAI,CAAC,CAAC;MAC5CF,WAAW,CAACK,cAAc,CAAC;;MAE3B;MACA,IAAIA,cAAc,EAAE;QAChBP,cAAc,CAAC,IAAI,CAAC;MACxB,CAAC,MAAM;QACH;QACA,MAAMQ,oBAAoB,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,KAAK,MAAM;QAChFV,cAAc,CAACQ,oBAAoB,CAAC;MACxC;IACJ,CAAC;IAEDL,eAAe,CAAC,CAAC;IACjBE,MAAM,CAACM,gBAAgB,CAAC,QAAQ,EAAER,eAAe,CAAC;IAClD,OAAO,MAAME,MAAM,CAACO,mBAAmB,CAAC,QAAQ,EAAET,eAAe,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,aAAa,GAAGA,CAAA,KAAM;IACxB,MAAMC,iBAAiB,GAAG,CAACf,WAAW;IACtCC,cAAc,CAACc,iBAAiB,CAAC;;IAEjC;IACA,IAAIT,MAAM,CAACC,UAAU,GAAG,IAAI,EAAE;MAC1BG,YAAY,CAACM,OAAO,CAAC,kBAAkB,EAAED,iBAAiB,CAACE,QAAQ,CAAC,CAAC,CAAC;IAC1E;EACJ,CAAC;;EAED;EACA,MAAMC,SAAS,GAAG,CACd;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAE5B,OAAA,CAACpB,MAAM;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAEhE;EACA;IAAEN,IAAI,EAAE,yBAAyB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,IAAI,eAAE5B,OAAA,CAACjB,QAAQ;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACjF;IAAEN,IAAI,EAAE,6BAA6B;IAAEC,IAAI,EAAE,sBAAsB;IAAEC,IAAI,eAAE5B,OAAA,CAAChB,OAAO;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACxF;IAAEN,IAAI,EAAE,yBAAyB;IAAEC,IAAI,EAAE,kBAAkB;IAAEC,IAAI,eAAE5B,OAAA,CAACf,OAAO;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAChF;IAAEN,IAAI,EAAE,wBAAwB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE5B,OAAA,CAACP,WAAW;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAClF;IAAEN,IAAI,EAAE,aAAa;IAAEC,IAAI,EAAE,gBAAgB;IAAEC,IAAI,eAAE5B,OAAA,CAACL,MAAM;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAEjE;EACA;IAAEN,IAAI,EAAE,cAAc;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE5B,OAAA,CAACzB,OAAO;MAAAsD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACpE;IAAEN,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,eAAE5B,OAAA,CAACnB,UAAU;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACnE;IAAEN,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE,aAAa;IAAEC,IAAI,eAAE5B,OAAA,CAAClB,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAE3E;EACA;IAAEN,IAAI,EAAE,0BAA0B;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,eAAE5B,OAAA,CAACrB,QAAQ;MAAAkD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACnF;IAAEN,IAAI,EAAE,uBAAuB;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,eAAE5B,OAAA,CAACd,aAAa;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrF;IAAEN,IAAI,EAAE,qBAAqB;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAE5B,OAAA,CAACb,aAAa;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAE9E;EACA;IAAEN,IAAI,EAAE,wBAAwB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE5B,OAAA,CAACZ,cAAc;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACrF;IAAEN,IAAI,EAAE,4BAA4B;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,IAAI,eAAE5B,OAAA,CAACf,OAAO;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACtF;IAAEN,IAAI,EAAE,qBAAqB;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAE5B,OAAA,CAACX,aAAa;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC;EAE9E;EACA;IAAEN,IAAI,EAAE,oBAAoB;IAAEC,IAAI,EAAE,wBAAwB;IAAEC,IAAI,eAAE5B,OAAA,CAACV,OAAO;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACjF;IAAEN,IAAI,EAAE,iBAAiB;IAAEC,IAAI,EAAE,iBAAiB;IAAEC,IAAI,eAAE5B,OAAA,CAACT,QAAQ;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACxE;IAAEN,IAAI,EAAE,0BAA0B;IAAEC,IAAI,EAAE,mBAAmB;IAAEC,IAAI,eAAE5B,OAAA,CAACP,WAAW;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EACtF;IAAEN,IAAI,EAAE,qBAAqB;IAAEC,IAAI,EAAE,cAAc;IAAEC,IAAI,eAAE5B,OAAA,CAACP,WAAW;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,EAC5E;IAAEN,IAAI,EAAE,gBAAgB;IAAEC,IAAI,EAAE,uBAAuB;IAAEC,IAAI,eAAE5B,OAAA,CAACR,WAAW;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAE,CAAC,CACnF;EAED,oBACIhC,OAAA,CAAAE,SAAA;IAAA+B,QAAA,GAEKxB,QAAQ,iBACLT,OAAA;MACIkC,SAAS,EAAC,uBAAuB;MACjCC,OAAO,EAAEd,aAAc;MACvB,cAAW,gBAAgB;MAAAY,QAAA,EAE1B1B,WAAW,gBAAGP,OAAA,CAACJ,MAAM;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAGhC,OAAA,CAACH,OAAO;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CACX,eAEDhC,OAAA;MAAKkC,SAAS,EAAE,iBAAiB3B,WAAW,GAAG,WAAW,GAAG,EAAE,IAAIE,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAwB,QAAA,gBAC1FjC,OAAA;QAAKkC,SAAS,EAAC,gBAAgB;QAAAD,QAAA,GAE1B,CAACxB,QAAQ,iBACNT,OAAA;UACIkC,SAAS,EAAC,wBAAwB;UAClCC,OAAO,EAAEd,aAAc;UACvB,cAAW,gBAAgB;UAAAY,QAAA,EAE1B1B,WAAW,gBAAGP,OAAA,CAACJ,MAAM;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhC,OAAA,CAACH,OAAO;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CACX,eAEDhC,OAAA;UAAKkC,SAAS,EAAC,MAAM;UAAAD,QAAA,EAChB,CAAC1B,WAAW,IAAID,MAAM,CAAC8B,SAAS,gBAC7BpC,OAAA;YACIqC,GAAG,EAAE,YAAY/B,MAAM,CAAC8B,SAAS,EAAG;YACpCE,GAAG,EAAEhC,MAAM,CAACiC,SAAS,IAAI,WAAY;YACrCL,SAAS,EAAC,WAAW;YACrBM,OAAO,EAAGC,CAAC,IAAK;cACZA,CAAC,CAACC,MAAM,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;cAC/BH,CAAC,CAACC,MAAM,CAACG,WAAW,CAACF,KAAK,CAACC,OAAO,GAAG,OAAO;YAChD;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,GACF,CAACzB,WAAW,gBACZP,OAAA;YAAMkC,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAE3B,MAAM,CAACiC,SAAS,IAAI;UAAW;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,gBAEpEhC,OAAA;YAAMkC,SAAS,EAAC,gBAAgB;YAAAD,QAAA,EAAC;UAAC;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAC3C;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENhC,OAAA;QAAKkC,SAAS,EAAC,+BAA+B;QAAAD,QAAA,GACzCR,SAAS,CAACqB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACvBhD,OAAA,CAAC3B,OAAO;UAEJ4E,EAAE,EAAEF,IAAI,CAACrB,IAAK;UACdQ,SAAS,EAAEA,CAAC;YAAEgB;UAAS,CAAC,KACpB,mBAAmBA,QAAQ,GAAG,QAAQ,GAAG,EAAE,EAC9C;UACDC,GAAG;UACHC,KAAK,EAAE7C,WAAW,GAAGwC,IAAI,CAACpB,IAAI,GAAG,EAAG;UAAAM,QAAA,gBAEpCjC,OAAA;YAAMkC,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAEc,IAAI,CAACnB;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,EACxD,CAACzB,WAAW,iBAAIP,OAAA;YAAMkC,SAAS,EAAC,sBAAsB;YAAAD,QAAA,EAAEc,IAAI,CAACpB;UAAI;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GATrEgB,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUL,CACZ,CAAC,eAGFhC,OAAA;UAAKkC,SAAS,EAAC,gBAAgB;UAAAD,QAAA,eAC3BjC,OAAA;YACIkC,SAAS,EAAC,eAAe;YACzBC,OAAO,EAAEA,CAAA,KAAM9B,QAAQ,CAAC,cAAc,CAAE;YACxCsC,KAAK,EAAE;cAAEU,eAAe,EAAE,SAAS;cAAEC,KAAK,EAAE;YAAQ,CAAE;YACtDF,KAAK,EAAE7C,WAAW,GAAG,QAAQ,GAAG,EAAG;YAAA0B,QAAA,gBAEnCjC,OAAA;cAAMkC,SAAS,EAAC,aAAa;cAACS,KAAK,EAAE;gBAAEW,KAAK,EAAE;cAAQ,CAAE;cAAArB,QAAA,eACpDjC,OAAA,CAACN,YAAY;gBAAAmC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,EACN,CAACzB,WAAW,iBAAIP,OAAA;cAAMkC,SAAS,EAAC,aAAa;cAACS,KAAK,EAAE;gBAAEW,KAAK,EAAE;cAAQ,CAAE;cAAArB,QAAA,EAAC;YAAM;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA,eACR,CAAC;AAEX;AAAC5B,EAAA,CAvJQD,OAAO;EAAA,QACK/B,WAAW,EACT0B,aAAa;AAAA;AAAAyD,EAAA,GAF3BpD,OAAO;AAyJhB,eAAeA,OAAO;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}