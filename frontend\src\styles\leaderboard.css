.leaderboard-container {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.leaderboard-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.leaderboard-header h1 {
    font-size: 2rem;
    color: #2c5f2d;
    margin: 0;
}

.filters {
    display: flex;
    gap: 1rem;
}

.season-select,
.period-select {
    padding: 0.5rem 1rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 1rem;
    cursor: pointer;
}

.user-stats-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.user-stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.user-stats-header h3 {
    margin: 0;
    color: #2c5f2d;
}

.rank-badge {
    background: #2c5f2d;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.user-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: transform 0.2s;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 1.5rem;
    color: #2c5f2d;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c5f2d;
}

.achievements-section {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.achievement-badge {
    font-size: 1.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: help;
    transition: transform 0.2s;
}

.achievement-badge:hover {
    transform: scale(1.1);
}

.leaderboard-table {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.leaderboard-table table {
    width: 100%;
    border-collapse: collapse;
}

.leaderboard-table th {
    background: #f8f9fa;
    padding: 1rem;
    text-align: left;
    color: #666;
    font-weight: 600;
    border-bottom: 2px solid #eee;
}

.leaderboard-table td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.rank-cell {
    width: 80px;
    text-align: center;
}

.rank-icon {
    font-size: 1.2rem;
}

.rank-icon.gold { color: #ffd700; }
.rank-icon.silver { color: #c0c0c0; }
.rank-icon.bronze { color: #cd7f32; }

.player-cell {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.player-name {
    font-weight: 600;
    color: #2c5f2d;
}

.division-badge {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    background: #e9ecef;
    border-radius: 4px;
    color: #666;
}

.win-loss-ratio,
.total-wagered,
.total-profit {
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.current-user {
    background: #e3f2fd;
}

.current-user:hover {
    background: #bbdefb;
}

.achievements-cell {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.achievement-icon {
    font-size: 1.2rem;
    cursor: help;
}

.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.error-message {
    text-align: center;
    color: #e74c3c;
    padding: 2rem;
    font-size: 1.2rem;
}

@media (max-width: 1200px) {
    .leaderboard-container {
        padding: 1rem;
    }
    
    .user-stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .user-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .leaderboard-table {
        overflow-x: auto;
    }
}
