/* 
 * Shared Authentication Styles
 * This file contains common styles used across all authentication pages
 * Import this file in any authentication component that needs these styles
 */

:root {
    --primary-green: #2c5f2d;
    --light-green: #52b788;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-600: #718096;
    --gray-800: #2d3748;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
    --shadow-md: 0 4px 6px rgba(0,0,0,0.1);
    --shadow-lg: 0 10px 15px rgba(0,0,0,0.1);
    --border-radius: 12px;
    --transition: all 0.2s ease;
}

/* Form Styles */
.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    color: var(--gray-800);
    font-size: 13px;
    font-weight: 500;
}

.form-help-text {
    font-size: 12px;
    color: var(--gray-600);
    margin-top: 4px;
    line-height: 1.4;
}

/* Input Wrapper */
.input-wrapper {
    position: relative;
    width: 100%;
}

.input-wrapper i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-600);
    font-size: 14px;
    z-index: 1;
}

.input-wrapper input,
.input-wrapper select {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 3rem; /* Increased left padding for icon spacing */
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 14px;
    transition: var(--transition);
    background: var(--white);
    box-sizing: border-box;
}

.input-wrapper input:focus,
.input-wrapper select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(44, 95, 45, 0.1);
}

.input-wrapper input::placeholder {
    color: var(--gray-600);
}

.input-wrapper input:disabled,
.input-wrapper select:disabled {
    background-color: var(--gray-100);
    color: var(--gray-600);
    cursor: not-allowed;
}

/* Button Styles */
.auth-button,
.login-button,
.register-button,
.forgot-password-button {
    width: 100%;
    padding: 0.75rem;
    background: var(--primary-green);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-button:hover,
.login-button:hover,
.register-button:hover,
.forgot-password-button:hover {
    background: var(--light-green);
    transform: translateY(-1px);
}

.auth-button:disabled,
.login-button:disabled,
.register-button:disabled,
.forgot-password-button:disabled {
    background: var(--gray-300);
    color: var(--gray-600);
    cursor: not-allowed;
    transform: none;
}

.auth-button:disabled:hover,
.login-button:disabled:hover,
.register-button:disabled:hover,
.forgot-password-button:disabled:hover {
    background: var(--gray-300);
}

/* Secondary Button */
.auth-button-secondary {
    width: 100%;
    padding: 0.75rem;
    background: transparent;
    color: var(--primary-green);
    border: 1px solid var(--primary-green);
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.auth-button-secondary:hover {
    background: var(--primary-green);
    color: var(--white);
    transform: translateY(-1px);
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.form-options.center {
    justify-content: center;
}

.form-options.end {
    justify-content: flex-end;
}

/* Links */
.forgot-password,
.auth-link,
.register-link,
.login-link {
    color: var(--primary-green);
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    transition: var(--transition);
}

.forgot-password:hover,
.auth-link:hover,
.register-link:hover,
.login-link:hover {
    color: var(--light-green);
    text-decoration: underline;
}

/* Footer */
.auth-footer {
    margin-top: 1.5rem;
    text-align: center;
}

.auth-footer p {
    color: var(--gray-600);
    font-size: 13px;
    margin: 0;
}

/* Messages */
.error-message,
.success-message {
    padding: 0.75rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    font-size: 13px;
    text-align: center;
    font-weight: 500;
}

.error-message {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.success-message {
    background: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* OTP Input Styling */
.otp-input {
    text-align: center;
    letter-spacing: 0.5rem;
    font-size: 18px;
    font-weight: 600;
}

/* Countdown Timer */
.countdown-timer {
    font-size: 12px;
    color: var(--gray-600);
    text-align: center;
    margin-top: 0.5rem;
}

.countdown-timer.expired {
    color: #dc2626;
}

/* Resend Button */
.resend-button {
    background: none;
    border: none;
    color: var(--primary-green);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: underline;
    transition: var(--transition);
    padding: 0;
    margin-top: 0.5rem;
}

.resend-button:hover {
    color: var(--light-green);
}

.resend-button:disabled {
    color: var(--gray-600);
    cursor: not-allowed;
    text-decoration: none;
}

/* Two-Factor Authentication Buttons */
.tfa-buttons {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.tfa-button {
    flex: 1;
    padding: 0.5rem;
    background: transparent;
    color: var(--primary-green);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.tfa-button:hover {
    border-color: var(--primary-green);
    background: rgba(44, 95, 45, 0.05);
}

/* Responsive Adjustments */
@media screen and (max-width: 768px) {
    .form-options {
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }
    
    .tfa-buttons {
        flex-direction: column;
    }
    
    .tfa-button {
        width: 100%;
    }
}

@media screen and (max-width: 480px) {
    .form-group {
        margin-bottom: 0.75rem;
    }
    
    .input-wrapper input,
    .input-wrapper select {
        padding: 0.6rem 0.6rem 0.6rem 2.2rem;
        font-size: 13px;
    }
    
    .input-wrapper i {
        left: 10px;
        font-size: 13px;
    }
    
    .auth-button,
    .login-button,
    .register-button,
    .forgot-password-button {
        padding: 0.6rem;
        font-size: 13px;
    }
}
