{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt, FaTrash } from 'react-icons/fa';\nimport { API_BASE_URL } from '../config';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CurrencyManagement() {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stats\n  const [stats, setStats] = useState({\n    totalCurrencies: 0,\n    activeCurrencies: 0,\n    inactiveCurrencies: 0,\n    lastUpdated: null\n  });\n\n  // Modal states\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [editingRate, setEditingRate] = useState(null);\n  const [editingCurrency, setEditingCurrency] = useState(null);\n\n  // Form states\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  useEffect(() => {\n    loadCurrencies();\n    loadExchangeRates();\n  }, []);\n  const loadCurrencies = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n      if (response.data.success) {\n        const currencyData = response.data.data.currencies || [];\n        setCurrencies(currencyData);\n\n        // Calculate stats\n        const totalCurrencies = currencyData.length;\n        const activeCurrencies = currencyData.filter(c => c.is_active).length;\n        const inactiveCurrencies = totalCurrencies - activeCurrencies;\n        setStats({\n          totalCurrencies,\n          activeCurrencies,\n          inactiveCurrencies,\n          lastUpdated: new Date().toISOString()\n        });\n      } else {\n        setError(response.data.message || 'Failed to load currencies');\n      }\n    } catch (err) {\n      console.error('Error loading currencies:', err);\n      setError('Failed to load currencies. Please check your network connection.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadExchangeRates = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n      if (response.data.success) {\n        setExchangeRates(response.data.data.exchange_rates || []);\n      }\n    } catch (err) {\n      console.error('Error loading exchange rates:', err);\n    }\n  };\n  const handleUpdateRate = async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      setError('Please enter a valid exchange rate');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n        currency_id: currencyId,\n        rate_to_fancoin: parseFloat(newRate),\n        notes: notes\n      });\n      if (response.data.success) {\n        setSuccess('Exchange rate updated successfully!');\n        loadExchangeRates();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update exchange rate');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error updating exchange rate:', err);\n      setError('Failed to update exchange rate. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleToggleCurrency = async (currencyId, currentStatus) => {\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'toggle',\n        currency_id: currencyId\n      });\n      if (response.data.success) {\n        setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n        loadCurrencies();\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to toggle currency status');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error toggling currency:', err);\n      setError('Failed to toggle currency status. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleDeleteCurrency = async (currencyId, currencyCode) => {\n    // Confirm deletion\n    if (!window.confirm(`Are you sure you want to delete the currency \"${currencyCode}\"? This action cannot be undone and will remove all associated exchange rates.`)) {\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'delete',\n        currency_id: currencyId\n      });\n      if (response.data.success) {\n        setSuccess(`Currency \"${currencyCode}\" deleted successfully!`);\n        loadCurrencies();\n        loadExchangeRates();\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to delete currency');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error deleting currency:', err);\n      setError('Failed to delete currency. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCurrency = async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'create',\n        ...newCurrency\n      });\n      if (response.data.success) {\n        setSuccess('Currency added successfully!');\n        loadCurrencies();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to add currency');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error adding currency:', err);\n      setError('Failed to add currency. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getExchangeRate = currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCurrency(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"p-6 bg-gray-50 min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-between items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: \"Currency Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Manage supported currencies and exchange rates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n          onClick: () => setShowAddCurrency(true),\n          disabled: loading,\n          children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 25\n          }, this), \"Add New Currency\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 244,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 239,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 238,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 17\n    }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\",\n      role: \"alert\",\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        className: \"block sm:inline\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-blue-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaCoins, {\n            className: \"text-blue-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Total Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.totalCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-green-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaToggleOn, {\n            className: \"text-green-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Active Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.activeCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 281,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-red-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaToggleOff, {\n            className: \"text-red-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Inactive Currencies\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: stats.inactiveCurrencies\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-sm p-6 flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rounded-full bg-yellow-100 p-3 mr-4\",\n          children: /*#__PURE__*/_jsxDEV(FaExchangeAlt, {\n            className: \"text-yellow-500 text-xl\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500 uppercase tracking-wider\",\n            children: \"Exchange Rates\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-800\",\n            children: exchangeRates.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-sm overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-6 py-4 border-b border-gray-200\",\n        children: /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-800\",\n          children: \"Currencies & Exchange Rates\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 317,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 17\n      }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-8 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"mt-2 text-gray-600\",\n          children: \"Loading currencies...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Currency\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Exchange Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 332,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Last Updated\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 333,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 334,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 33\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: currencies.map(currency => {\n              const rate = getExchangeRate(currency.id);\n              const isEditingRate = editingRate === currency.id;\n              return /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: `${!currency.is_active ? 'opacity-60' : ''}`,\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-shrink-0 h-10 w-10\",\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\",\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-blue-600 font-semibold\",\n                          children: currency.currency_symbol\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 348,\n                          columnNumber: 61\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 347,\n                        columnNumber: 57\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"ml-4\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm font-medium text-gray-900\",\n                        children: currency.currency_code\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 352,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"text-sm text-gray-500\",\n                        children: currency.currency_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: rate ? isEditingRate ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"space-y-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      step: \"0.0001\",\n                      value: newRate,\n                      onChange: e => setNewRate(e.target.value),\n                      placeholder: \"New rate\",\n                      className: \"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 362,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                      value: notes,\n                      onChange: e => setNotes(e.target.value),\n                      placeholder: \"Update notes (optional)\",\n                      className: \"w-full px-3 py-1 border border-gray-300 rounded text-sm\",\n                      rows: \"2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 370,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex gap-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleUpdateRate(currency.id),\n                        className: \"bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\",\n                        disabled: loading,\n                        children: [/*#__PURE__*/_jsxDEV(FaSave, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 383,\n                          columnNumber: 69\n                        }, this), \"Save\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 378,\n                        columnNumber: 65\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setEditingRate(null);\n                          setNewRate('');\n                          setNotes('');\n                        },\n                        className: \"bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\",\n                        children: [/*#__PURE__*/_jsxDEV(FaTimes, {}, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 394,\n                          columnNumber: 69\n                        }, this), \"Cancel\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 386,\n                        columnNumber: 65\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 377,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 361,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-medium text-gray-900\",\n                      children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 61\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setEditingRate(currency.id);\n                        setNewRate(rate.rate_to_fancoin ? rate.rate_to_fancoin.toString() : '');\n                      },\n                      className: \"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FaEdit, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 411,\n                        columnNumber: 65\n                      }, this), \"Update\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 404,\n                      columnNumber: 61\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 400,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm text-gray-500\",\n                      children: \"No rate set\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 418,\n                      columnNumber: 57\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setEditingRate(currency.id);\n                        setNewRate('');\n                      },\n                      className: \"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\",\n                      children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 426,\n                        columnNumber: 61\n                      }, this), \"Set Rate\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 419,\n                      columnNumber: 57\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 417,\n                    columnNumber: 53\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap\",\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: `inline-flex px-2 py-1 text-xs font-semibold rounded-full ${currency.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`,\n                    children: currency.is_active ? 'Active' : 'Inactive'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                  children: rate ? formatDate(rate.updated_at) : 'Never'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n                    className: `inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${currency.is_active ? 'bg-red-100 text-red-700 hover:bg-red-200' : 'bg-green-100 text-green-700 hover:bg-green-200'}`,\n                    disabled: loading,\n                    children: [currency.is_active ? /*#__PURE__*/_jsxDEV(FaToggleOff, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 75\n                    }, this) : /*#__PURE__*/_jsxDEV(FaToggleOn, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 454,\n                      columnNumber: 93\n                    }, this), currency.is_active ? 'Deactivate' : 'Activate']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 49\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 45\n                }, this)]\n              }, currency.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 41\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 13\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-gray-900 mb-4\",\n            children: \"Add New Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 472,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Currency Code (e.g., EUR, GBP)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 476,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"currency_code\",\n                value: newCurrency.currency_code,\n                onChange: handleInputChange,\n                placeholder: \"USD\",\n                maxLength: \"3\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Currency Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"currency_name\",\n                value: newCurrency.currency_name,\n                onChange: handleInputChange,\n                placeholder: \"US Dollar\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 494,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Currency Symbol\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 505,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"currency_symbol\",\n                value: newCurrency.currency_symbol,\n                onChange: handleInputChange,\n                placeholder: \"$\",\n                maxLength: \"5\",\n                className: \"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 508,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: \"is_active\",\n                checked: newCurrency.is_active,\n                onChange: e => setNewCurrency({\n                  ...newCurrency,\n                  is_active: e.target.checked\n                }),\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 520,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"ml-2 block text-sm text-gray-900\",\n                children: \"Active\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 527,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-end gap-3 mt-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowAddCurrency(false),\n              className: \"bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg\",\n              children: \"Cancel\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 534,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleAddCurrency,\n              className: \"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\",\n              disabled: loading,\n              children: [/*#__PURE__*/_jsxDEV(FaPlus, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 37\n              }, this), \"Add Currency\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 533,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 25\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 470,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 469,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 236,\n    columnNumber: 9\n  }, this);\n}\n_s(CurrencyManagement, \"teJRouVhEaHhxEDt1jb4Qd4Sz2g=\");\n_c = CurrencyManagement;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "FaTrash", "API_BASE_URL", "jsxDEV", "_jsxDEV", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "editingRate", "setEditingRate", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadCurrencies", "loadExchangeRates", "response", "get", "data", "currencyData", "length", "filter", "c", "Date", "toISOString", "message", "err", "console", "exchange_rates", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "post", "currency_id", "rate_to_fancoin", "setTimeout", "handleToggleCurrency", "currentStatus", "action", "handleDeleteCurrency", "currencyCode", "window", "confirm", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "handleInputChange", "e", "name", "value", "target", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "role", "map", "currency", "id", "isEditingRate", "type", "step", "onChange", "placeholder", "rows", "toString", "updated_at", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt, FaTrash } from 'react-icons/fa';\nimport { API_BASE_URL } from '../config';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [editingRate, setEditingRate] = useState(null);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    useEffect(() => {\n        loadCurrencies();\n        loadExchangeRates();\n    }, []);\n\n    const loadCurrencies = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n            if (response.data.success) {\n                const currencyData = response.data.data.currencies || [];\n                setCurrencies(currencyData);\n\n                // Calculate stats\n                const totalCurrencies = currencyData.length;\n                const activeCurrencies = currencyData.filter(c => c.is_active).length;\n                const inactiveCurrencies = totalCurrencies - activeCurrencies;\n\n                setStats({\n                    totalCurrencies,\n                    activeCurrencies,\n                    inactiveCurrencies,\n                    lastUpdated: new Date().toISOString()\n                });\n            } else {\n                setError(response.data.message || 'Failed to load currencies');\n            }\n        } catch (err) {\n            console.error('Error loading currencies:', err);\n            setError('Failed to load currencies. Please check your network connection.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const loadExchangeRates = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n            if (response.data.success) {\n                setExchangeRates(response.data.data.exchange_rates || []);\n            }\n        } catch (err) {\n            console.error('Error loading exchange rates:', err);\n        }\n    };\n\n    const handleUpdateRate = async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            setError('Please enter a valid exchange rate');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n                currency_id: currencyId,\n                rate_to_fancoin: parseFloat(newRate),\n                notes: notes\n            });\n\n            if (response.data.success) {\n                setSuccess('Exchange rate updated successfully!');\n                loadExchangeRates();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update exchange rate');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error updating exchange rate:', err);\n            setError('Failed to update exchange rate. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleToggleCurrency = async (currencyId, currentStatus) => {\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'toggle',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n                loadCurrencies();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to toggle currency status');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error toggling currency:', err);\n            setError('Failed to toggle currency status. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleDeleteCurrency = async (currencyId, currencyCode) => {\n        // Confirm deletion\n        if (!window.confirm(`Are you sure you want to delete the currency \"${currencyCode}\"? This action cannot be undone and will remove all associated exchange rates.`)) {\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'delete',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency \"${currencyCode}\" deleted successfully!`);\n                loadCurrencies();\n                loadExchangeRates();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to delete currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error deleting currency:', err);\n            setError('Failed to delete currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleAddCurrency = async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            setError('Please fill in all required fields');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'create',\n                ...newCurrency\n            });\n\n            if (response.data.success) {\n                setSuccess('Currency added successfully!');\n                loadCurrencies();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to add currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error adding currency:', err);\n            setError('Failed to add currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getExchangeRate = (currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewCurrency(prev => ({ ...prev, [name]: value }));\n    };\n\n    return (\n        <div className=\"p-6 bg-gray-50 min-h-screen\">\n            {/* Page Header */}\n            <div className=\"mb-8\">\n                <div className=\"flex justify-between items-center\">\n                    <div>\n                        <h1 className=\"text-2xl font-bold text-gray-800\">Currency Management</h1>\n                        <p className=\"text-gray-600\">Manage supported currencies and exchange rates</p>\n                    </div>\n                    <button\n                        className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\"\n                        onClick={() => setShowAddCurrency(true)}\n                        disabled={loading}\n                    >\n                        <FaPlus />\n                        Add New Currency\n                    </button>\n                </div>\n            </div>\n\n            {/* Notification Messages */}\n            {error && (\n                <div className=\"mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{error}</span>\n                </div>\n            )}\n            {success && (\n                <div className=\"mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative\" role=\"alert\">\n                    <span className=\"block sm:inline\">{success}</span>\n                </div>\n            )}\n\n            {/* Stats Cards */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n                {/* Total Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-blue-100 p-3 mr-4\">\n                        <FaCoins className=\"text-blue-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Total Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.totalCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Active Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-green-100 p-3 mr-4\">\n                        <FaToggleOn className=\"text-green-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Active Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.activeCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Inactive Currencies */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-red-100 p-3 mr-4\">\n                        <FaToggleOff className=\"text-red-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Inactive Currencies</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{stats.inactiveCurrencies}</h3>\n                    </div>\n                </div>\n\n                {/* Exchange Rates */}\n                <div className=\"bg-white rounded-lg shadow-sm p-6 flex items-center\">\n                    <div className=\"rounded-full bg-yellow-100 p-3 mr-4\">\n                        <FaExchangeAlt className=\"text-yellow-500 text-xl\" />\n                    </div>\n                    <div>\n                        <p className=\"text-sm text-gray-500 uppercase tracking-wider\">Exchange Rates</p>\n                        <h3 className=\"text-2xl font-bold text-gray-800\">{exchangeRates.length}</h3>\n                    </div>\n                </div>\n            </div>\n\n            {/* Currencies Table */}\n            <div className=\"bg-white rounded-lg shadow-sm overflow-hidden\">\n                <div className=\"px-6 py-4 border-b border-gray-200\">\n                    <h2 className=\"text-lg font-semibold text-gray-800\">Currencies & Exchange Rates</h2>\n                </div>\n\n                {loading && currencies.length === 0 ? (\n                    <div className=\"p-8 text-center\">\n                        <div className=\"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n                        <p className=\"mt-2 text-gray-600\">Loading currencies...</p>\n                    </div>\n                ) : (\n                    <div className=\"overflow-x-auto\">\n                        <table className=\"min-w-full divide-y divide-gray-200\">\n                            <thead className=\"bg-gray-50\">\n                                <tr>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Currency</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Exchange Rate</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Status</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Last Updated</th>\n                                    <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                                </tr>\n                            </thead>\n                            <tbody className=\"bg-white divide-y divide-gray-200\">\n                                {currencies.map((currency) => {\n                                    const rate = getExchangeRate(currency.id);\n                                    const isEditingRate = editingRate === currency.id;\n\n                                    return (\n                                        <tr key={currency.id} className={`${!currency.is_active ? 'opacity-60' : ''}`}>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <div className=\"flex items-center\">\n                                                    <div className=\"flex-shrink-0 h-10 w-10\">\n                                                        <div className=\"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center\">\n                                                            <span className=\"text-blue-600 font-semibold\">{currency.currency_symbol}</span>\n                                                        </div>\n                                                    </div>\n                                                    <div className=\"ml-4\">\n                                                        <div className=\"text-sm font-medium text-gray-900\">{currency.currency_code}</div>\n                                                        <div className=\"text-sm text-gray-500\">{currency.currency_name}</div>\n                                                    </div>\n                                                </div>\n                                            </td>\n\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                {rate ? (\n                                                    isEditingRate ? (\n                                                        <div className=\"space-y-2\">\n                                                            <input\n                                                                type=\"number\"\n                                                                step=\"0.0001\"\n                                                                value={newRate}\n                                                                onChange={(e) => setNewRate(e.target.value)}\n                                                                placeholder=\"New rate\"\n                                                                className=\"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                                                            />\n                                                            <textarea\n                                                                value={notes}\n                                                                onChange={(e) => setNotes(e.target.value)}\n                                                                placeholder=\"Update notes (optional)\"\n                                                                className=\"w-full px-3 py-1 border border-gray-300 rounded text-sm\"\n                                                                rows=\"2\"\n                                                            />\n                                                            <div className=\"flex gap-2\">\n                                                                <button\n                                                                    onClick={() => handleUpdateRate(currency.id)}\n                                                                    className=\"bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\"\n                                                                    disabled={loading}\n                                                                >\n                                                                    <FaSave />\n                                                                    Save\n                                                                </button>\n                                                                <button\n                                                                    onClick={() => {\n                                                                        setEditingRate(null);\n                                                                        setNewRate('');\n                                                                        setNotes('');\n                                                                    }}\n                                                                    className=\"bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1\"\n                                                                >\n                                                                    <FaTimes />\n                                                                    Cancel\n                                                                </button>\n                                                            </div>\n                                                        </div>\n                                                    ) : (\n                                                        <div>\n                                                            <div className=\"text-sm font-medium text-gray-900\">\n                                                                1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                            </div>\n                                                            <button\n                                                                onClick={() => {\n                                                                    setEditingRate(currency.id);\n                                                                    setNewRate(rate.rate_to_fancoin ? rate.rate_to_fancoin.toString() : '');\n                                                                }}\n                                                                className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                            >\n                                                                <FaEdit />\n                                                                Update\n                                                            </button>\n                                                        </div>\n                                                    )\n                                                ) : (\n                                                    <div>\n                                                        <span className=\"text-sm text-gray-500\">No rate set</span>\n                                                        <button\n                                                            onClick={() => {\n                                                                setEditingRate(currency.id);\n                                                                setNewRate('');\n                                                            }}\n                                                            className=\"text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1\"\n                                                        >\n                                                            <FaPlus />\n                                                            Set Rate\n                                                        </button>\n                                                    </div>\n                                                )}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap\">\n                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                                                    currency.is_active\n                                                        ? 'bg-green-100 text-green-800'\n                                                        : 'bg-red-100 text-red-800'\n                                                }`}>\n                                                    {currency.is_active ? 'Active' : 'Inactive'}\n                                                </span>\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                                                {rate ? formatDate(rate.updated_at) : 'Never'}\n                                            </td>\n                                            <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                                                <button\n                                                    onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                                    className={`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${\n                                                        currency.is_active\n                                                            ? 'bg-red-100 text-red-700 hover:bg-red-200'\n                                                            : 'bg-green-100 text-green-700 hover:bg-green-200'\n                                                    }`}\n                                                    disabled={loading}\n                                                >\n                                                    {currency.is_active ? <FaToggleOff /> : <FaToggleOn />}\n                                                    {currency.is_active ? 'Deactivate' : 'Activate'}\n                                                </button>\n                                            </td>\n                                        </tr>\n                                    );\n                                })}\n                            </tbody>\n                        </table>\n                    </div>\n                )}\n            </div>\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50\">\n                    <div className=\"relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white\">\n                        <div className=\"mt-3\">\n                            <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Add New Currency</h3>\n\n                            <div className=\"space-y-4\">\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Code (e.g., EUR, GBP)\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_code\"\n                                        value={newCurrency.currency_code}\n                                        onChange={handleInputChange}\n                                        placeholder=\"USD\"\n                                        maxLength=\"3\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Name\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_name\"\n                                        value={newCurrency.currency_name}\n                                        onChange={handleInputChange}\n                                        placeholder=\"US Dollar\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div>\n                                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                                        Currency Symbol\n                                    </label>\n                                    <input\n                                        type=\"text\"\n                                        name=\"currency_symbol\"\n                                        value={newCurrency.currency_symbol}\n                                        onChange={handleInputChange}\n                                        placeholder=\"$\"\n                                        maxLength=\"5\"\n                                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                    />\n                                </div>\n\n                                <div className=\"flex items-center\">\n                                    <input\n                                        type=\"checkbox\"\n                                        name=\"is_active\"\n                                        checked={newCurrency.is_active}\n                                        onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                    />\n                                    <label className=\"ml-2 block text-sm text-gray-900\">\n                                        Active\n                                    </label>\n                                </div>\n                            </div>\n\n                            <div className=\"flex justify-end gap-3 mt-6\">\n                                <button\n                                    onClick={() => setShowAddCurrency(false)}\n                                    className=\"bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg\"\n                                >\n                                    Cancel\n                                </button>\n                                <button\n                                    onClick={handleAddCurrency}\n                                    className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2\"\n                                    disabled={loading}\n                                >\n                                    <FaPlus />\n                                    Add Currency\n                                </button>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n}\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,EAAEC,OAAO,QAAQ,gBAAgB;AAC1H,SAASC,YAAY,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC;IAC/B6B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,eAAe,EAAEC,kBAAkB,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC;IAC3CyC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC+C,KAAK,EAAEC,QAAQ,CAAC,GAAGhD,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACZgD,cAAc,CAAC,CAAC;IAChBC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B3B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,GAAGxC,YAAY,8BAA8B,CAAC;MAC/E,IAAIuC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvB,MAAM6B,YAAY,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACpC,UAAU,IAAI,EAAE;QACxDC,aAAa,CAACoC,YAAY,CAAC;;QAE3B;QACA,MAAMzB,eAAe,GAAGyB,YAAY,CAACC,MAAM;QAC3C,MAAMzB,gBAAgB,GAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,SAAS,CAAC,CAACW,MAAM;QACrE,MAAMxB,kBAAkB,GAAGF,eAAe,GAAGC,gBAAgB;QAE7DF,QAAQ,CAAC;UACLC,eAAe;UACfC,gBAAgB;UAChBC,kBAAkB;UAClBC,WAAW,EAAE,IAAI0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACxC,CAAC,CAAC;MACN,CAAC,MAAM;QACHnC,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,2BAA2B,EAAEsC,GAAG,CAAC;MAC/CrC,QAAQ,CAAC,kEAAkE,CAAC;IAChF,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMjD,KAAK,CAACkD,GAAG,CAAC,GAAGxC,YAAY,kCAAkC,CAAC;MACnF,IAAIuC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBL,gBAAgB,CAAC+B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;IACvD;EACJ,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC3C,IAAI,CAACpB,OAAO,IAAIqB,KAAK,CAACC,UAAU,CAACtB,OAAO,CAAC,CAAC,EAAE;MACxCrB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMjD,KAAK,CAACkE,IAAI,CAAC,GAAGxD,YAAY,oCAAoC,EAAE;QACnFyD,WAAW,EAAEJ,UAAU;QACvBK,eAAe,EAAEH,UAAU,CAACtB,OAAO,CAAC;QACpCE,KAAK,EAAEA;MACX,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,qCAAqC,CAAC;QACjDwB,iBAAiB,CAAC,CAAC;QACnBd,cAAc,CAAC,IAAI,CAAC;QACpBU,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZuB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,gCAAgC,CAAC;QACnEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;MACnDrC,QAAQ,CAAC,mDAAmD,CAAC;MAC7D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkD,oBAAoB,GAAG,MAAAA,CAAOP,UAAU,EAAEQ,aAAa,KAAK;IAC9DnD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMjD,KAAK,CAACkE,IAAI,CAAC,GAAGxD,YAAY,iCAAiC,EAAE;QAChF8D,MAAM,EAAE,QAAQ;QAChBL,WAAW,EAAEJ;MACjB,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,YAAY+C,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;QACnFxB,cAAc,CAAC,CAAC;QAChBsB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,kCAAkC,CAAC;QACrEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEsC,GAAG,CAAC;MAC9CrC,QAAQ,CAAC,qDAAqD,CAAC;MAC/D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqD,oBAAoB,GAAG,MAAAA,CAAOV,UAAU,EAAEW,YAAY,KAAK;IAC7D;IACA,IAAI,CAACC,MAAM,CAACC,OAAO,CAAC,iDAAiDF,YAAY,gFAAgF,CAAC,EAAE;MAChK;IACJ;IAEAtD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMjD,KAAK,CAACkE,IAAI,CAAC,GAAGxD,YAAY,iCAAiC,EAAE;QAChF8D,MAAM,EAAE,QAAQ;QAChBL,WAAW,EAAEJ;MACjB,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,aAAakD,YAAY,yBAAyB,CAAC;QAC9D3B,cAAc,CAAC,CAAC;QAChBC,iBAAiB,CAAC,CAAC;QACnBqB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;QAC9DW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEsC,GAAG,CAAC;MAC9CrC,QAAQ,CAAC,8CAA8C,CAAC;MACxD+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMyD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACxC,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1FnB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMjD,KAAK,CAACkE,IAAI,CAAC,GAAGxD,YAAY,iCAAiC,EAAE;QAChF8D,MAAM,EAAE,QAAQ;QAChB,GAAGnC;MACP,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CuB,cAAc,CAAC,CAAC;QAChBf,kBAAkB,CAAC,KAAK,CAAC;QACzBM,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACF2B,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,wBAAwB,CAAC;QAC3DW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,wBAAwB,EAAEsC,GAAG,CAAC;MAC5CrC,QAAQ,CAAC,2CAA2C,CAAC;MACrD+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM0D,eAAe,GAAIf,UAAU,IAAK;IACpC,OAAO9C,aAAa,CAAC8D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACb,WAAW,KAAKJ,UAAU,CAAC;EACtE,CAAC;EAED,MAAMkB,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAI1B,IAAI,CAAC0B,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCvD,cAAc,CAACwD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,oBACIhF,OAAA;IAAKmF,SAAS,EAAC,6BAA6B;IAAAC,QAAA,gBAExCpF,OAAA;MAAKmF,SAAS,EAAC,MAAM;MAAAC,QAAA,eACjBpF,OAAA;QAAKmF,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAC9CpF,OAAA;UAAAoF,QAAA,gBACIpF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzExF,OAAA;YAAGmF,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA8C;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNxF,OAAA;UACImF,SAAS,EAAC,uFAAuF;UACjGM,OAAO,EAAEA,CAAA,KAAMrE,kBAAkB,CAAC,IAAI,CAAE;UACxCsE,QAAQ,EAAEnF,OAAQ;UAAA6E,QAAA,gBAElBpF,OAAA,CAACP,MAAM;YAAA4F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEd;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAGL/E,KAAK,iBACFT,OAAA;MAAKmF,SAAS,EAAC,+EAA+E;MAACQ,IAAI,EAAC,OAAO;MAAAP,QAAA,eACvGpF,OAAA;QAAMmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAE3E;MAAK;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CACR,EACA7E,OAAO,iBACJX,OAAA;MAAKmF,SAAS,EAAC,qFAAqF;MAACQ,IAAI,EAAC,OAAO;MAAAP,QAAA,eAC7GpF,OAAA;QAAMmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAEzE;MAAO;QAAA0E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CACR,eAGDxF,OAAA;MAAKmF,SAAS,EAAC,2DAA2D;MAAAC,QAAA,gBAEtEpF,OAAA;QAAKmF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpF,OAAA;UAAKmF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,eAC9CpF,OAAA,CAACX,OAAO;YAAC8F,SAAS,EAAC;UAAuB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC,eACNxF,OAAA;UAAAoF,QAAA,gBACIpF,OAAA;YAAGmF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClFxF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEvE,KAAK,CAACE;UAAe;YAAAsE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxF,OAAA;QAAKmF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpF,OAAA;UAAKmF,SAAS,EAAC,oCAAoC;UAAAC,QAAA,eAC/CpF,OAAA,CAACT,UAAU;YAAC4F,SAAS,EAAC;UAAwB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC,eACNxF,OAAA;UAAAoF,QAAA,gBACIpF,OAAA;YAAGmF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACnFxF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEvE,KAAK,CAACG;UAAgB;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxF,OAAA;QAAKmF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpF,OAAA;UAAKmF,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC7CpF,OAAA,CAACR,WAAW;YAAC2F,SAAS,EAAC;UAAsB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CAAC,eACNxF,OAAA;UAAAoF,QAAA,gBACIpF,OAAA;YAAGmF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrFxF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAEvE,KAAK,CAACI;UAAkB;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNxF,OAAA;QAAKmF,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAChEpF,OAAA;UAAKmF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,eAChDpF,OAAA,CAACJ,aAAa;YAACuF,SAAS,EAAC;UAAyB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACNxF,OAAA;UAAAoF,QAAA,gBACIpF,OAAA;YAAGmF,SAAS,EAAC,gDAAgD;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAChFxF,OAAA;YAAImF,SAAS,EAAC,kCAAkC;YAAAC,QAAA,EAAE/E,aAAa,CAACoC;UAAM;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3E,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxF,OAAA;MAAKmF,SAAS,EAAC,+CAA+C;MAAAC,QAAA,gBAC1DpF,OAAA;QAAKmF,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAC/CpF,OAAA;UAAImF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAA2B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnF,CAAC,EAELjF,OAAO,IAAIJ,UAAU,CAACsC,MAAM,KAAK,CAAC,gBAC/BzC,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC5BpF,OAAA;UAAKmF,SAAS,EAAC;QAA2E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGxF,OAAA;UAAGmF,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,gBAENxF,OAAA;QAAKmF,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC5BpF,OAAA;UAAOmF,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBAClDpF,OAAA;YAAOmF,SAAS,EAAC,YAAY;YAAAC,QAAA,eACzBpF,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAImF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC5GxF,OAAA;gBAAImF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjHxF,OAAA;gBAAImF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC1GxF,OAAA;gBAAImF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAY;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChHxF,OAAA;gBAAImF,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACRxF,OAAA;YAAOmF,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAC/CjF,UAAU,CAACyF,GAAG,CAAEC,QAAQ,IAAK;cAC1B,MAAMzB,IAAI,GAAGF,eAAe,CAAC2B,QAAQ,CAACC,EAAE,CAAC;cACzC,MAAMC,aAAa,GAAG1E,WAAW,KAAKwE,QAAQ,CAACC,EAAE;cAEjD,oBACI9F,OAAA;gBAAsBmF,SAAS,EAAE,GAAG,CAACU,QAAQ,CAAC/D,SAAS,GAAG,YAAY,GAAG,EAAE,EAAG;gBAAAsD,QAAA,gBAC1EpF,OAAA;kBAAImF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACvCpF,OAAA;oBAAKmF,SAAS,EAAC,mBAAmB;oBAAAC,QAAA,gBAC9BpF,OAAA;sBAAKmF,SAAS,EAAC,yBAAyB;sBAAAC,QAAA,eACpCpF,OAAA;wBAAKmF,SAAS,EAAC,qEAAqE;wBAAAC,QAAA,eAChFpF,OAAA;0BAAMmF,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAES,QAAQ,CAAChE;wBAAe;0BAAAwD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAO;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC9E;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,eACNxF,OAAA;sBAAKmF,SAAS,EAAC,MAAM;sBAAAC,QAAA,gBACjBpF,OAAA;wBAAKmF,SAAS,EAAC,mCAAmC;wBAAAC,QAAA,EAAES,QAAQ,CAAClE;sBAAa;wBAAA0D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,eACjFxF,OAAA;wBAAKmF,SAAS,EAAC,uBAAuB;wBAAAC,QAAA,EAAES,QAAQ,CAACjE;sBAAa;wBAAAyD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAELxF,OAAA;kBAAImF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EACtChB,IAAI,GACD2B,aAAa,gBACT/F,OAAA;oBAAKmF,SAAS,EAAC,WAAW;oBAAAC,QAAA,gBACtBpF,OAAA;sBACIgG,IAAI,EAAC,QAAQ;sBACbC,IAAI,EAAC,QAAQ;sBACbjB,KAAK,EAAEjD,OAAQ;sBACfmE,QAAQ,EAAGpB,CAAC,IAAK9C,UAAU,CAAC8C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAC5CmB,WAAW,EAAC,UAAU;sBACtBhB,SAAS,EAAC;oBAAyD;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtE,CAAC,eACFxF,OAAA;sBACIgF,KAAK,EAAE/C,KAAM;sBACbiE,QAAQ,EAAGpB,CAAC,IAAK5C,QAAQ,CAAC4C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;sBAC1CmB,WAAW,EAAC,yBAAyB;sBACrChB,SAAS,EAAC,yDAAyD;sBACnEiB,IAAI,EAAC;oBAAG;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX,CAAC,eACFxF,OAAA;sBAAKmF,SAAS,EAAC,YAAY;sBAAAC,QAAA,gBACvBpF,OAAA;wBACIyF,OAAO,EAAEA,CAAA,KAAMvC,gBAAgB,CAAC2C,QAAQ,CAACC,EAAE,CAAE;wBAC7CX,SAAS,EAAC,8FAA8F;wBACxGO,QAAQ,EAAEnF,OAAQ;wBAAA6E,QAAA,gBAElBpF,OAAA,CAACN,MAAM;0BAAA2F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,QAEd;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC,eACTxF,OAAA;wBACIyF,OAAO,EAAEA,CAAA,KAAM;0BACXnE,cAAc,CAAC,IAAI,CAAC;0BACpBU,UAAU,CAAC,EAAE,CAAC;0BACdE,QAAQ,CAAC,EAAE,CAAC;wBAChB,CAAE;wBACFiD,SAAS,EAAC,4FAA4F;wBAAAC,QAAA,gBAEtGpF,OAAA,CAACL,OAAO;0BAAA0F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,UAEf;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAQ,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,gBAENxF,OAAA;oBAAAoF,QAAA,gBACIpF,OAAA;sBAAKmF,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,GAAC,SACxC,EAACS,QAAQ,CAAChE,eAAe,EAAEuC,IAAI,CAACZ,eAAe;oBAAA;sBAAA6B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrD,CAAC,eACNxF,OAAA;sBACIyF,OAAO,EAAEA,CAAA,KAAM;wBACXnE,cAAc,CAACuE,QAAQ,CAACC,EAAE,CAAC;wBAC3B9D,UAAU,CAACoC,IAAI,CAACZ,eAAe,GAAGY,IAAI,CAACZ,eAAe,CAAC6C,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;sBAC3E,CAAE;sBACFlB,SAAS,EAAC,wEAAwE;sBAAAC,QAAA,gBAElFpF,OAAA,CAACV,MAAM;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,UAEd;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACR,gBAEDxF,OAAA;oBAAAoF,QAAA,gBACIpF,OAAA;sBAAMmF,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAW;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC1DxF,OAAA;sBACIyF,OAAO,EAAEA,CAAA,KAAM;wBACXnE,cAAc,CAACuE,QAAQ,CAACC,EAAE,CAAC;wBAC3B9D,UAAU,CAAC,EAAE,CAAC;sBAClB,CAAE;sBACFmD,SAAS,EAAC,wEAAwE;sBAAAC,QAAA,gBAElFpF,OAAA,CAACP,MAAM;wBAAA4F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,YAEd;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR;gBACR;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACLxF,OAAA;kBAAImF,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eACvCpF,OAAA;oBAAMmF,SAAS,EAAE,4DACbU,QAAQ,CAAC/D,SAAS,GACZ,6BAA6B,GAC7B,yBAAyB,EAChC;oBAAAsD,QAAA,EACES,QAAQ,CAAC/D,SAAS,GAAG,QAAQ,GAAG;kBAAU;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC,eACLxF,OAAA;kBAAImF,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,EAC5DhB,IAAI,GAAGC,UAAU,CAACD,IAAI,CAACkC,UAAU,CAAC,GAAG;gBAAO;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACLxF,OAAA;kBAAImF,SAAS,EAAC,iDAAiD;kBAAAC,QAAA,eAC3DpF,OAAA;oBACIyF,OAAO,EAAEA,CAAA,KAAM/B,oBAAoB,CAACmC,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC/D,SAAS,CAAE;oBACrEqD,SAAS,EAAE,wEACPU,QAAQ,CAAC/D,SAAS,GACZ,0CAA0C,GAC1C,gDAAgD,EACvD;oBACH4D,QAAQ,EAAEnF,OAAQ;oBAAA6E,QAAA,GAEjBS,QAAQ,CAAC/D,SAAS,gBAAG9B,OAAA,CAACR,WAAW;sBAAA6F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAAGxF,OAAA,CAACT,UAAU;sBAAA8F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACrDK,QAAQ,CAAC/D,SAAS,GAAG,YAAY,GAAG,UAAU;kBAAA;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC3C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACT,CAAC;cAAA,GAlHAK,QAAQ,CAACC,EAAE;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmHhB,CAAC;YAEb,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,EAGLrE,eAAe,iBACZnB,OAAA;MAAKmF,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACvFpF,OAAA;QAAKmF,SAAS,EAAC,uEAAuE;QAAAC,QAAA,eAClFpF,OAAA;UAAKmF,SAAS,EAAC,MAAM;UAAAC,QAAA,gBACjBpF,OAAA;YAAImF,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAE5ExF,OAAA;YAAKmF,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACtBpF,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAOmF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxF,OAAA;gBACIgG,IAAI,EAAC,MAAM;gBACXjB,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAEvD,WAAW,CAACE,aAAc;gBACjCuE,QAAQ,EAAErB,iBAAkB;gBAC5BsB,WAAW,EAAC,KAAK;gBACjBI,SAAS,EAAC,GAAG;gBACbpB,SAAS,EAAC;cAAwG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxF,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAOmF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxF,OAAA;gBACIgG,IAAI,EAAC,MAAM;gBACXjB,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAEvD,WAAW,CAACG,aAAc;gBACjCsE,QAAQ,EAAErB,iBAAkB;gBAC5BsB,WAAW,EAAC,WAAW;gBACvBhB,SAAS,EAAC;cAAwG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxF,OAAA;cAAAoF,QAAA,gBACIpF,OAAA;gBAAOmF,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACRxF,OAAA;gBACIgG,IAAI,EAAC,MAAM;gBACXjB,IAAI,EAAC,iBAAiB;gBACtBC,KAAK,EAAEvD,WAAW,CAACI,eAAgB;gBACnCqE,QAAQ,EAAErB,iBAAkB;gBAC5BsB,WAAW,EAAC,GAAG;gBACfI,SAAS,EAAC,GAAG;gBACbpB,SAAS,EAAC;cAAwG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENxF,OAAA;cAAKmF,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAC9BpF,OAAA;gBACIgG,IAAI,EAAC,UAAU;gBACfjB,IAAI,EAAC,WAAW;gBAChByB,OAAO,EAAE/E,WAAW,CAACK,SAAU;gBAC/BoE,QAAQ,EAAGpB,CAAC,IAAKpD,cAAc,CAAC;kBAAC,GAAGD,WAAW;kBAAEK,SAAS,EAAEgD,CAAC,CAACG,MAAM,CAACuB;gBAAO,CAAC,CAAE;gBAC/ErB,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChF,CAAC,eACFxF,OAAA;gBAAOmF,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAC;cAEpD;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENxF,OAAA;YAAKmF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBACxCpF,OAAA;cACIyF,OAAO,EAAEA,CAAA,KAAMrE,kBAAkB,CAAC,KAAK,CAAE;cACzC+D,SAAS,EAAC,kEAAkE;cAAAC,QAAA,EAC/E;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTxF,OAAA;cACIyF,OAAO,EAAExB,iBAAkB;cAC3BkB,SAAS,EAAC,uFAAuF;cACjGO,QAAQ,EAAEnF,OAAQ;cAAA6E,QAAA,gBAElBpF,OAAA,CAACP,MAAM;gBAAA4F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAEd;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAACtF,EAAA,CA9hBQD,kBAAkB;AAAAwG,EAAA,GAAlBxG,kBAAkB;AAgiB3B,eAAeA,kBAAkB;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}