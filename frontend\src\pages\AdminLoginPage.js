import React, { useState } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { AdminOTPVerification, Admin2FAVerification, Admin2FASetup } from '../components/Admin';
import './AdminLoginPage.css';


const AdminLoginPage = () => {
    const navigate = useNavigate();

    // Authentication flow state
    const [authStep, setAuthStep] = useState('login'); // 'login', 'otp', '2fa', '2fa_setup'
    const [adminData, setAdminData] = useState(null);

    // Login form state
    const [identifier, setIdentifier] = useState('');
    const [password, setPassword] = useState('');
    const [error, setError] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [rememberMe, setRememberMe] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post('/backend/handlers/admin_login_handler.php', {
                identifier,
                password,
                remember_me: rememberMe
            });

            console.log('Admin login request URL:', '/backend/handlers/admin_login_handler.php');

            if (response.data.success) {
                // Store admin data for potential next steps
                setAdminData({
                    admin_id: response.data.admin_id,
                    username: response.data.username,
                    role: response.data.role
                });

                // Check if additional authentication is required
                if (response.data.requires_additional_auth) {
                    const nextStep = response.data.next_step;

                    if (nextStep === 'otp') {
                        setAuthStep('otp');
                    } else if (nextStep === '2fa') {
                        // Check if 2FA is set up
                        setAuthStep('2fa');
                    }
                } else {
                    // Complete login - no additional auth required
                    completeLogin({
                        admin_id: response.data.admin_id,
                        username: response.data.username,
                        role: response.data.role,
                        auth_method: response.data.auth_method || 'password_only'
                    });
                }
            } else {
                setError(response.data.message || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            if (error.response) {
                setError(error.response.data.message || 'Invalid credentials');
            } else if (error.request) {
                setError('Network error. Please check your connection.');
            } else {
                setError('An error occurred. Please try again.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const completeLogin = (loginData) => {
        // Store authentication data
        localStorage.setItem('adminId', loginData.admin_id);
        localStorage.setItem('adminUsername', loginData.username);
        localStorage.setItem('adminRole', loginData.role);
        localStorage.setItem('adminAuthMethod', loginData.auth_method);

        if (loginData.session_token) {
            localStorage.setItem('adminSessionToken', loginData.session_token);
        }

        // Navigate to dashboard
        navigate('/admin/dashboard');
    };

    const handleAuthSuccess = (authData) => {
        completeLogin(authData);
    };

    const handleBackToLogin = () => {
        setAuthStep('login');
        setAdminData(null);
        setError('');
        setPassword(''); // Clear password for security
    };

    // Render different authentication steps
    if (authStep === 'otp' && adminData) {
        return (
            <AdminOTPVerification
                adminId={adminData.admin_id}
                username={adminData.username}
                onSuccess={handleAuthSuccess}
                onBack={handleBackToLogin}
            />
        );
    }

    if (authStep === '2fa' && adminData) {
        return (
            <Admin2FAVerification
                adminId={adminData.admin_id}
                username={adminData.username}
                onSuccess={handleAuthSuccess}
                onBack={handleBackToLogin}
            />
        );
    }

    if (authStep === '2fa_setup' && adminData) {
        return (
            <Admin2FASetup
                adminId={adminData.admin_id}
                username={adminData.username}
                onSuccess={handleAuthSuccess}
                onBack={handleBackToLogin}
            />
        );
    }

    // Default login form
    return (
        <div className="admin-login-container">
            <div className="login-left-panel">
                <div className="login-logo">
                    <div className="logo-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                    </div>
                    <h1>FanBet247</h1>
                </div>

                <div className="login-form-container">
                    <h2>Admin Login</h2>
                    <p className="login-subtitle">Enter your credentials to access the admin dashboard</p>

                    {/* Security Notice */}
                    <div className="security-notice">
                        <div className="security-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                            </svg>
                        </div>
                        <span>Enhanced security enabled - Additional verification may be required</span>
                    </div>

                    {error && <div className="error-message">{error}</div>}

                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label htmlFor="identifier">Username or Email</label>
                            <div className="input-container">
                                <input
                                    type="text"
                                    id="identifier"
                                    value={identifier}
                                    onChange={(e) => setIdentifier(e.target.value)}
                                    disabled={isLoading}
                                    placeholder="Enter your username or email"
                                    required
                                />
                                <div className="input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="12" cy="7" r="4"></circle>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="form-group">
                            <label htmlFor="password">Password</label>
                            <div className="input-container">
                                <input
                                    type="password"
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    disabled={isLoading}
                                    placeholder="Enter your password"
                                    required
                                />
                                <div className="input-icon">
                                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <div className="form-options">
                            <div className="remember-me">
                                <input
                                    type="checkbox"
                                    id="rememberMe"
                                    checked={rememberMe}
                                    onChange={(e) => setRememberMe(e.target.checked)}
                                />
                                <label htmlFor="rememberMe">Remember me</label>
                            </div>
                            <button type="button" className="forgot-password" onClick={() => {
                                // Show temporary message instead of alert
                                const button = document.activeElement;
                                const originalText = button.textContent;
                                button.textContent = 'Coming soon!';
                                button.style.color = '#3b82f6';
                                setTimeout(() => {
                                    button.textContent = originalText;
                                    button.style.color = '';
                                }, 3000);
                            }}>Forgot password?</button>
                        </div>

                        <button type="submit" className="login-button" disabled={isLoading}>
                            {isLoading ? 'Logging in...' : 'Login'}
                        </button>
                    </form>
                </div>
            </div>

            <div className="login-right-panel"></div>
        </div>
    );
};

export default AdminLoginPage;