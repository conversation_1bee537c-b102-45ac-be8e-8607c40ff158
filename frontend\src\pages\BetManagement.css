.bet-management {
  padding: 20px;
  width: 100%;
  background-color: #f8f9fa;
}

.bet-management h1 {
  color: #2c5f2d;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
}

.bet-management h2 {
  color: #2c5f2d;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 500;
}

.credit-form-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.credit-form {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  max-width: 800px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: bold;
}

.form-group select {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

button {
  padding: 10px 20px;
  background: #2c5f2f;
}

.bets-list {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  overflow-x: auto;
}

.bets-list table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px;
}

/* New responsive table styles for BetManagement */
.bet-table-container {
  overflow-x: auto;
  position: relative;
}

.bet-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px; /* Reduced from 1200px */
}

.bet-table-actions {
  position: sticky;
  right: 0;
  background: #f9fafb;
  z-index: 10;
  box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
}

.bet-table tbody tr:hover .bet-table-actions {
  background: #f3f4f6;
}

/* Team display improvements */
.bet-table td img {
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Perfect team alignment */
.bet-table .grid-cols-3 {
  grid-template-columns: 1fr auto 1fr;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

/* Ensure consistent column widths for perfect alignment */
.bet-table .grid-cols-3 > div:first-child,
.bet-table .grid-cols-3 > div:last-child {
  min-width: 120px;
}

/* VS separator styling */
.bet-table .grid-cols-3 > div:nth-child(2) {
  min-width: 40px;
}

/* Responsive team name adjustments */
@media (max-width: 1366px) {
  .bet-table td span.uppercase {
    font-size: 0.75rem; /* Smaller text on medium screens */
  }

  .bet-table .grid-cols-3 > div:first-child,
  .bet-table .grid-cols-3 > div:last-child {
    min-width: 100px;
  }
}

@media (max-width: 1024px) {
  .bet-table td span.uppercase {
    font-size: 0.7rem; /* Even smaller on small screens */
  }

  .bet-table td img {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }

  .bet-table .grid-cols-3 {
    max-width: 300px;
  }

  .bet-table .grid-cols-3 > div:first-child,
  .bet-table .grid-cols-3 > div:last-child {
    min-width: 80px;
  }

  .bet-table .grid-cols-3 > div:nth-child(2) {
    min-width: 30px;
  }
}

.bets-list th {
  background: #2c5f2d;
  color: white;
  text-align: left;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.bets-list td {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
  font-size: 14px;
  vertical-align: middle;
}

.bets-list tr:hover {
  background-color: #f8f9fa;
}

.bets-list .team-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bets-list .team-info img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

.bets-list .team-info span {
  font-size: 14px;
  white-space: nowrap;
}

.status {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
  text-transform: capitalize;
}

.status.open { 
  background: #fff3cd; 
  color: #856404; 
}

.status.joined { 
  background: #cce5ff; 
  color: #004085; 
}

.status.completed { 
  background: #d4edda; 
  color: #155724; 
}

.returns-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 13px;
}

.returns-info div {
  white-space: nowrap;
}

.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
    padding: 20px;
}

.pagination button {
    padding: 8px 16px;
    border: 1px solid #2c5f2d;
    background: white;
    color: #2c5f2d;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.pagination button:hover {
    background: #2c5f2d;
    color: white;
}

.pagination button.active {
    background: #2c5f2d;
    color: white;
}

@media (max-width: 1200px) {
  .bets-list {
    margin: 0 -20px;
    border-radius: 0;
  }
}

/* Responsive design for 13-14 inch PC monitors */
/* Hide User columns on medium screens (1366px - 1600px) */
@media (min-width: 1200px) and (max-width: 1600px) {
  .bet-table-hide-medium {
    display: none !important;
  }

  .bet-table {
    min-width: 600px;
  }
}

/* Hide User columns on smaller desktop screens (1024px-1366px) */
@media (min-width: 1024px) and (max-width: 1366px) {
  .bet-table-hide-medium {
    display: none !important;
  }

  .bet-table {
    min-width: 700px; /* Show amounts on small screens */
  }
}

/* Hide Date and Amounts only on very small screens (below 1024px) */
@media (max-width: 1024px) {
  .bet-table-hide-very-small {
    display: none !important;
  }

  .bet-table-hide-medium {
    display: none !important;
  }

  .bet-table {
    min-width: 500px;
  }
}

/* Ensure Actions column is always visible and sticky */
@media (max-width: 1600px) {
  .bet-table-actions {
    min-width: 80px;
    width: 80px;
  }
}

/* Mobile responsive (very small screens) */
@media (max-width: 768px) {
  .bet-table-hide-very-small,
  .bet-table-hide-medium {
    display: none !important;
  }

  .bet-table {
    min-width: 400px;
  }

  .bet-table-actions {
    min-width: 60px;
    width: 60px;
  }
}
