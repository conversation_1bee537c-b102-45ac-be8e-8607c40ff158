import React from 'react';
import { Link } from 'react-router-dom';
import './AuthLayout.css';

const AuthLayout = ({ 
    title, 
    subtitle, 
    children, 
    showRightColumn = true,
    rightColumnContent = null 
}) => {
    return (
        <div className="auth-page">
            <div className="auth-main-container">
                {/* Left Column - Form */}
                <div className="auth-left-column">
                    <div className="auth-container">
                        <div className="auth-header">
                            <Link to="/" className="brand-logo">
                                FanBet247
                            </Link>
                            <h1 className="auth-title">{title}</h1>
                            {subtitle && (
                                <p className="auth-subtitle">{subtitle}</p>
                            )}
                        </div>
                        <div className="auth-content">
                            {children}
                        </div>
                    </div>
                </div>

                {/* Right Column - Image/Content */}
                {showRightColumn && (
                    <div className="auth-right-column">
                        {rightColumnContent || (
                            <div className="auth-image-container">
                                 <div className="auth-image-placeholder" style={{ backgroundImage: "url(https://images.unsplash.com/photo-1531415074968-036ba1b575da?q=80)" }}>
                                     <div className="auth-image-overlay">
                                         <div className="auth-image-content">
                                             <h2>Welcome to FanBet247</h2>
                                             <p>The ultimate sports betting experience with friends</p>
                                             <div className="auth-features">
                                                 <div className="auth-feature">
                                                     <i className="fas fa-trophy"></i>
                                                     <span>Compete with Friends</span>
                                                 </div>
                                                 <div className="auth-feature">
                                                     <i className="fas fa-coins"></i>
                                                     <span>FanCoin Currency</span>
                                                 </div>
                                                 <div className="auth-feature">
                                                     <i className="fas fa-shield-alt"></i>
                                                     <span>Secure & Safe</span>
                                                 </div>
                                             </div>
                                         </div>
                                     </div>
                                 </div>
                            </div>
                        )}
                    </div>
                )}
            </div>
        </div>
    );
};

export default AuthLayout;
