import React, { useState, useEffect } from 'react';
import axios from '../utils/axiosConfig';
import { FaUsers, FaChartLine, FaTrash } from 'react-icons/fa';
import './LeagueUserManagement.css';

function LeagueUserManagement() {
    const [leagues, setLeagues] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [selectedLeague, setSelectedLeague] = useState(null);
    const [showLeaderboard, setShowLeaderboard] = useState(false);
    const [leagueUsers, setLeagueUsers] = useState([]);
    const [loadingUsers, setLoadingUsers] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [usersPerPage] = useState(10);

    useEffect(() => {
        fetchLeagues();
    }, []);

    const fetchLeagues = async () => {
        try {
            setLoading(true);
            setError('');
            const response = await axios.get('/backend/handlers/league_management.php');
            if (response.data.status === 200) {
                setLeagues(response.data.data || []);
            } else {
                setError(response.data.message || 'Failed to fetch leagues');
            }
        } catch (err) {
            console.error('Error fetching leagues:', err);
            setError('Failed to fetch leagues. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const fetchLeagueUsers = async (leagueId) => {
        try {
            setLoadingUsers(true);
            setError('');
            const response = await axios.get(`/backend/handlers/admin/get_league_users.php?league_id=${leagueId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
                }
            });
            console.log('League users response:', response.data); // Debug log
            if (response.data.status === 200) {
                setLeagueUsers(response.data.data || []);
                setSelectedLeague(leagues.find(l => l.league_id === leagueId));
                setShowLeaderboard(true);
                setCurrentPage(1); // Reset to first page when loading new users
            } else {
                setError(response.data.message || 'Failed to fetch league users');
            }
        } catch (err) {
            console.error('Error fetching league users:', err);
            setError('Failed to fetch league users: ' + (err.response?.data?.message || err.message));
        } finally {
            setLoadingUsers(false);
        }
    };

    const handleDeleteUser = async (userId, leagueId) => {
        if (!window.confirm('Are you sure you want to remove this user from the league? Their registration fee will be refunded.')) {
            return;
        }

        try {
            setError('');
            const response = await axios.post('/backend/handlers/admin/remove_league_user.php', {
                user_id: userId,
                league_id: leagueId
            });

            if (response.data.status === 200) {
                setSuccess('User successfully removed from the league');
                await fetchLeagueUsers(leagueId);
            } else {
                setError(response.data.message || 'Failed to remove user from league');
            }
        } catch (err) {
            console.error('Error removing user:', err);
            setError('Failed to remove user. Please try again.');
        }
    };

    // Get current users for pagination
    const indexOfLastUser = currentPage * usersPerPage;
    const indexOfFirstUser = indexOfLastUser - usersPerPage;
    const currentUsers = leagueUsers.slice(indexOfFirstUser, indexOfLastUser);
    const totalPages = Math.ceil(leagueUsers.length / usersPerPage);

    // Change page
    const paginate = (pageNumber) => setCurrentPage(pageNumber);

    const Pagination = () => {
        return (
            <div className="pagination">
                <button
                    onClick={() => paginate(currentPage - 1)}
                    disabled={currentPage === 1}
                    className="pagination-button"
                >
                    Previous
                </button>
                <span className="page-info">
                    Page {currentPage} of {totalPages} ({leagueUsers.length} total users)
                </span>
                <button
                    onClick={() => paginate(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className="pagination-button"
                >
                    Next
                </button>
            </div>
        );
    };

    const LeaderboardModal = () => {
        // Get current users for pagination
        const indexOfLastUser = currentPage * usersPerPage;
        const indexOfFirstUser = indexOfLastUser - usersPerPage;
        const currentUsers = leagueUsers.slice(indexOfFirstUser, indexOfLastUser);

        return (
            <div className="modal-overlay" onClick={() => setShowLeaderboard(false)}>
                <div className="leaderboard-modal" onClick={e => e.stopPropagation()}>
                    <div className="modal-header">
                        <h2>League Users</h2>
                        <button className="close-modal" onClick={() => setShowLeaderboard(false)}>&times;</button>
                    </div>
                    <div className="leaderboard-table">
                        <table>
                            <thead>
                                <tr className="table-header">
                                    <th>#</th>
                                    <th>Rank</th>
                                    <th>User</th>
                                    <th>Points</th>
                                    <th>W/D/L</th>
                                    <th>Deposit</th>
                                    <th>Join Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {currentUsers.map((user, index) => (
                                    <tr key={user.user_id} className="table-row">
                                        <td>{indexOfFirstUser + index + 1}</td>
                                        <td>{index + 1}</td>
                                        <td>{user.username}</td>
                                        <td className="points">{user.points || 0}</td>
                                        <td className="stats">
                                            <span>{user.wins || 0}</span>/
                                            <span>{user.draws || 0}</span>/
                                            <span>{user.losses || 0}</span>
                                        </td>
                                        <td className="deposit">FC{parseFloat(user.deposit_amount || 0).toLocaleString()}</td>
                                        <td>{new Date(user.join_date).toLocaleDateString()}</td>
                                        <td>
                                            <button
                                                className="delete-btn"
                                                onClick={() => handleDeleteUser(user.user_id, selectedLeague)}
                                            >
                                                <FaTrash />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                    <div className="modal-pagination">
                        <Pagination />
                    </div>
                </div>
            </div>
        );
    };

    if (loading) {
        return <div className="loading">Loading leagues...</div>;
    }

    return (
        <div className="league-user-management">
            <header className="page-header">
                <h1>League User Management</h1>
                {error && <div className="error-message">{error}</div>}
                {success && <div className="success-message">{success}</div>}
            </header>

            {loading ? (
                <div className="flex justify-center items-center py-8">
                    <div className="loading-spinner"></div>
                    <span className="ml-3 text-gray-600">Loading leagues...</span>
                </div>
            ) : leagues.length === 0 ? (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
                    <FaUsers className="mx-auto text-gray-400 text-4xl mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No leagues found</h3>
                    <p className="text-gray-500">No leagues are available for user management.</p>
                </div>
            ) : (
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead>
                                <tr className="bg-green-600" style={{ backgroundColor: '#166534' }}>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-user-table-hide-small" style={{ backgroundColor: '#166534' }}>
                                        #
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        League Name
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-user-table-hide-medium" style={{ backgroundColor: '#166534' }}>
                                        Description
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Members
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider league-user-table-hide-small" style={{ backgroundColor: '#166534' }}>
                                        Bet Range
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Status
                                    </th>
                                    <th className="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider" style={{ backgroundColor: '#166534' }}>
                                        Actions
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {leagues.map((league, index) => (
                                    <tr key={league.league_id} className="hover:bg-gray-50 transition-colors">
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 league-user-table-hide-small">
                                            {index + 1}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="text-sm font-medium text-gray-900">{league.name}</div>
                                            <div className="text-sm text-gray-500 league-user-table-show-mobile">
                                                {league.member_count || 0} members
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 league-user-table-hide-medium">
                                            <div className="text-sm text-gray-900 max-w-xs truncate">
                                                {league.description || 'No description available'}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <div className="flex items-center text-sm text-gray-900">
                                                <FaUsers className="mr-2 text-green-600" />
                                                {league.member_count || 0}
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 league-user-table-hide-small">
                                            <div className="text-left">
                                                <div>FC{parseFloat(league.min_bet_amount).toLocaleString()}</div>
                                                <div>FC{parseFloat(league.max_bet_amount).toLocaleString()}</div>
                                            </div>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap">
                                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                league.status === 'active' ? 'bg-green-100 text-green-800' :
                                                league.status === 'inactive' ? 'bg-yellow-100 text-yellow-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                                {league.status}
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <button
                                                onClick={() => fetchLeagueUsers(league.league_id)}
                                                className="text-green-600 hover:text-green-900 hover:bg-green-50 p-2 rounded transition-colors"
                                                title="View Users"
                                                style={{ color: '#166534' }}
                                            >
                                                <FaUsers size={18} />
                                            </button>
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}

            {showLeaderboard && <LeaderboardModal />}

            <Pagination />
        </div>
    );
}

export default LeagueUserManagement;