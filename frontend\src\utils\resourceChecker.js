export const checkResources = async (urls) => {
    const results = [];
    
    for (const url of urls) {
        try {
            const response = await fetch(url);
            results.push({
                url,
                status: response.status,
                ok: response.ok,
                timestamp: new Date().toISOString()
            });
        } catch (error) {
            results.push({
                url,
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }
    }
    
    return results;
};

export const logResourceErrors = (results) => {
    const failures = results.filter(r => !r.ok || r.error);
    if (failures.length > 0) {
        console.error('Resource Loading Failures:', failures);
    }
    return failures;
};
