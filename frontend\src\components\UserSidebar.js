import React, { useState } from 'react';
import { useNavigate, NavLink, useLocation } from 'react-router-dom';
import { FaChevronDown, FaGamepad, FaUser, FaTrophy, FaMoneyBill, FaMedal, FaUsers, FaHistory } from 'react-icons/fa';
import './Sidebar.css';

function UserSidebar() {
    const navigate = useNavigate();
    const location = useLocation();
    
    const [openMenus, setOpenMenus] = useState({
        profile: false,
        challenges: false,
        leagues: false,
        wallet: false
    });

    const handleLogout = () => {
        localStorage.removeItem('userId');
        localStorage.removeItem('username');
        navigate('/login');
    };

    const toggleMenu = (menuKey) => {
        setOpenMenus(prev => ({
            ...prev,
            [menuKey]: !prev[menuKey]
        }));
    };

    const menuStructure = {
        profile: {
            title: 'My Profile',
            icon: <FaUser />,
            items: [
                { link: '/profile', text: 'View Profile' },
                { link: '/profile/edit', text: 'Edit Profile' }
            ]
        },
        challenges: {
            title: 'Challenges',
            icon: <FaGamepad />,
            items: [
                { link: '/challenges', text: 'Active Challenges' },
                { link: '/challenges/history', text: 'Challenge History' }
            ]
        },
        leagues: {
            title: 'Leagues',
            icon: <FaTrophy />,
            items: [
                { link: '/league', text: 'League Home' },
                { link: '/league/leaderboard', text: 'Leaderboard' },
                { link: '/achievements', text: 'My Achievements' },
                { link: '/season-history', text: 'Season History' },
                { link: '/user/leagues', text: 'All Leagues' },
                { link: '/user/my-leagues', text: 'My Leagues' },
                { link: '/user/achievements', text: 'Achievements' },
                { link: '/user/season-history', text: 'Season History' }
            ]
        },
        wallet: {
            title: 'Wallet',
            icon: <FaMoneyBill />,
            items: [
                { link: '/wallet', text: 'My Wallet' },
                { link: '/transfer', text: 'Transfer FanCoins' },
                { link: '/wallet/transactions', text: 'Transaction History' }
            ]
        }
    };

    return (
        <div className="sidebar">
            <div className="logo">FanBet247</div>
            <nav className="sidebar-nav">
                {Object.entries(menuStructure).map(([key, menu]) => (
                    <div key={key} className="menu-group">
                        <div 
                            className={`menu-header ${location.pathname.includes(key) ? 'active' : ''}`}
                            onClick={() => toggleMenu(key)}
                        >
                            <span className="menu-icon">{menu.icon}</span>
                            <span className="menu-title">{menu.title}</span>
                            <FaChevronDown 
                                className={`menu-arrow ${openMenus[key] ? 'rotated' : ''}`} 
                            />
                        </div>
                        <div className={`menu-items ${openMenus[key] ? 'open' : ''}`}>
                            {menu.items.map((item, index) => (
                                <NavLink
                                    key={index}
                                    to={item.link}
                                    className={({ isActive }) => isActive ? 'active' : ''}
                                >
                                    {item.text}
                                </NavLink>
                            ))}
                        </div>
                    </div>
                ))}
            </nav>
            <div className="sidebar-footer">
                <button onClick={handleLogout} className="logout-button">
                    Logout
                </button>
            </div>
        </div>
    );
}

export default UserSidebar;
