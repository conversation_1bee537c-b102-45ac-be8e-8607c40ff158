{"ast": null, "code": "/**\n * Centralized API Service for FanBet247\n * \n * This service provides a consistent interface for all API calls,\n * ensuring proper error handling, response formatting, and request consistency.\n */\n\nimport axios from '../utils/axiosConfig';\n\n// Standard response format interface\nclass ApiResponse {\n  constructor(success, data = null, message = '', error = null, meta = {}) {\n    this.success = success;\n    this.data = data;\n    this.message = message;\n    this.error = error;\n    this.meta = meta;\n  }\n}\n\n// API Service class\nclass ApiService {\n  /**\n   * Generic GET request handler\n   * @param {string} endpoint - API endpoint (without /handlers/ prefix)\n   * @param {object} params - Query parameters\n   * @param {object} options - Additional axios options\n   * @returns {Promise<ApiResponse>}\n   */\n  async get(endpoint, params = {}, options = {}) {\n    try {\n      const response = await axios.get(endpoint, {\n        params,\n        ...options\n      });\n      return this.formatResponse(response);\n    } catch (error) {\n      return this.handleError(error, 'GET', endpoint);\n    }\n  }\n\n  /**\n   * Generic POST request handler\n   * @param {string} endpoint - API endpoint\n   * @param {object} data - Request body data\n   * @param {object} options - Additional axios options\n   * @returns {Promise<ApiResponse>}\n   */\n  async post(endpoint, data = {}, options = {}) {\n    try {\n      const response = await axios.post(endpoint, data, options);\n      return this.formatResponse(response);\n    } catch (error) {\n      return this.handleError(error, 'POST', endpoint);\n    }\n  }\n\n  /**\n   * Generic PUT request handler\n   * @param {string} endpoint - API endpoint\n   * @param {object} data - Request body data\n   * @param {object} options - Additional axios options\n   * @returns {Promise<ApiResponse>}\n   */\n  async put(endpoint, data = {}, options = {}) {\n    try {\n      const response = await axios.put(endpoint, data, options);\n      return this.formatResponse(response);\n    } catch (error) {\n      return this.handleError(error, 'PUT', endpoint);\n    }\n  }\n\n  /**\n   * Generic DELETE request handler\n   * @param {string} endpoint - API endpoint\n   * @param {object} options - Additional axios options\n   * @returns {Promise<ApiResponse>}\n   */\n  async delete(endpoint, options = {}) {\n    try {\n      const response = await axios.delete(endpoint, options);\n      return this.formatResponse(response);\n    } catch (error) {\n      return this.handleError(error, 'DELETE', endpoint);\n    }\n  }\n\n  /**\n   * Format response to consistent structure\n   * @param {object} response - Axios response object\n   * @returns {ApiResponse}\n   */\n  formatResponse(response) {\n    const {\n      data,\n      status,\n      statusText\n    } = response;\n\n    // Handle different backend response formats\n    if (data && typeof data === 'object') {\n      // Backend returns {success: true/false, data: ..., message: ...}\n      if (data.hasOwnProperty('success')) {\n        return new ApiResponse(data.success, data.data || data, data.message || statusText, data.error || null, {\n          status,\n          statusText\n        });\n      }\n\n      // Backend returns {status: 200, data: ..., message: ...}\n      if (data.hasOwnProperty('status')) {\n        return new ApiResponse(data.status >= 200 && data.status < 300, data.data || data, data.message || statusText, data.error || null, {\n          status: data.status,\n          statusText\n        });\n      }\n    }\n\n    // Default success response\n    return new ApiResponse(status >= 200 && status < 300, data, statusText, null, {\n      status,\n      statusText\n    });\n  }\n\n  /**\n   * Handle API errors consistently\n   * @param {object} error - Axios error object\n   * @param {string} method - HTTP method\n   * @param {string} endpoint - API endpoint\n   * @returns {ApiResponse}\n   */\n  handleError(error, method, endpoint) {\n    console.error(`API Error [${method} ${endpoint}]:`, error);\n    if (error.response) {\n      // Server responded with error status\n      const {\n        data,\n        status,\n        statusText\n      } = error.response;\n      return new ApiResponse(false, null, (data === null || data === void 0 ? void 0 : data.message) || statusText || 'Server error occurred', {\n        type: 'server_error',\n        status,\n        statusText,\n        data: data || null\n      }, {\n        status,\n        statusText\n      });\n    } else if (error.request) {\n      // Network error\n      return new ApiResponse(false, null, 'Network error - please check your connection', {\n        type: 'network_error',\n        message: error.message\n      });\n    } else {\n      // Request setup error\n      return new ApiResponse(false, null, 'Request configuration error', {\n        type: 'config_error',\n        message: error.message\n      });\n    }\n  }\n\n  /**\n   * Upload file with progress tracking\n   * @param {string} endpoint - Upload endpoint\n   * @param {FormData} formData - Form data with file\n   * @param {function} onProgress - Progress callback\n   * @returns {Promise<ApiResponse>}\n   */\n  async upload(endpoint, formData, onProgress = null) {\n    try {\n      const response = await axios.post(endpoint, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        },\n        onUploadProgress: onProgress ? progressEvent => {\n          const percentCompleted = Math.round(progressEvent.loaded * 100 / progressEvent.total);\n          onProgress(percentCompleted);\n        } : undefined\n      });\n      return this.formatResponse(response);\n    } catch (error) {\n      return this.handleError(error, 'UPLOAD', endpoint);\n    }\n  }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\nexport default apiService;\nexport { ApiResponse };", "map": {"version": 3, "names": ["axios", "ApiResponse", "constructor", "success", "data", "message", "error", "meta", "ApiService", "get", "endpoint", "params", "options", "response", "formatResponse", "handleError", "post", "put", "delete", "status", "statusText", "hasOwnProperty", "method", "console", "type", "request", "upload", "formData", "onProgress", "headers", "onUploadProgress", "progressEvent", "percentCompleted", "Math", "round", "loaded", "total", "undefined", "apiService"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/services/apiService.js"], "sourcesContent": ["/**\n * Centralized API Service for FanBet247\n * \n * This service provides a consistent interface for all API calls,\n * ensuring proper error handling, response formatting, and request consistency.\n */\n\nimport axios from '../utils/axiosConfig';\n\n// Standard response format interface\nclass ApiResponse {\n    constructor(success, data = null, message = '', error = null, meta = {}) {\n        this.success = success;\n        this.data = data;\n        this.message = message;\n        this.error = error;\n        this.meta = meta;\n    }\n}\n\n// API Service class\nclass ApiService {\n    /**\n     * Generic GET request handler\n     * @param {string} endpoint - API endpoint (without /handlers/ prefix)\n     * @param {object} params - Query parameters\n     * @param {object} options - Additional axios options\n     * @returns {Promise<ApiResponse>}\n     */\n    async get(endpoint, params = {}, options = {}) {\n        try {\n            const response = await axios.get(endpoint, {\n                params,\n                ...options\n            });\n            \n            return this.formatResponse(response);\n        } catch (error) {\n            return this.handleError(error, 'GET', endpoint);\n        }\n    }\n\n    /**\n     * Generic POST request handler\n     * @param {string} endpoint - API endpoint\n     * @param {object} data - Request body data\n     * @param {object} options - Additional axios options\n     * @returns {Promise<ApiResponse>}\n     */\n    async post(endpoint, data = {}, options = {}) {\n        try {\n            const response = await axios.post(endpoint, data, options);\n            return this.formatResponse(response);\n        } catch (error) {\n            return this.handleError(error, 'POST', endpoint);\n        }\n    }\n\n    /**\n     * Generic PUT request handler\n     * @param {string} endpoint - API endpoint\n     * @param {object} data - Request body data\n     * @param {object} options - Additional axios options\n     * @returns {Promise<ApiResponse>}\n     */\n    async put(endpoint, data = {}, options = {}) {\n        try {\n            const response = await axios.put(endpoint, data, options);\n            return this.formatResponse(response);\n        } catch (error) {\n            return this.handleError(error, 'PUT', endpoint);\n        }\n    }\n\n    /**\n     * Generic DELETE request handler\n     * @param {string} endpoint - API endpoint\n     * @param {object} options - Additional axios options\n     * @returns {Promise<ApiResponse>}\n     */\n    async delete(endpoint, options = {}) {\n        try {\n            const response = await axios.delete(endpoint, options);\n            return this.formatResponse(response);\n        } catch (error) {\n            return this.handleError(error, 'DELETE', endpoint);\n        }\n    }\n\n    /**\n     * Format response to consistent structure\n     * @param {object} response - Axios response object\n     * @returns {ApiResponse}\n     */\n    formatResponse(response) {\n        const { data, status, statusText } = response;\n        \n        // Handle different backend response formats\n        if (data && typeof data === 'object') {\n            // Backend returns {success: true/false, data: ..., message: ...}\n            if (data.hasOwnProperty('success')) {\n                return new ApiResponse(\n                    data.success,\n                    data.data || data,\n                    data.message || statusText,\n                    data.error || null,\n                    { status, statusText }\n                );\n            }\n            \n            // Backend returns {status: 200, data: ..., message: ...}\n            if (data.hasOwnProperty('status')) {\n                return new ApiResponse(\n                    data.status >= 200 && data.status < 300,\n                    data.data || data,\n                    data.message || statusText,\n                    data.error || null,\n                    { status: data.status, statusText }\n                );\n            }\n        }\n        \n        // Default success response\n        return new ApiResponse(\n            status >= 200 && status < 300,\n            data,\n            statusText,\n            null,\n            { status, statusText }\n        );\n    }\n\n    /**\n     * Handle API errors consistently\n     * @param {object} error - Axios error object\n     * @param {string} method - HTTP method\n     * @param {string} endpoint - API endpoint\n     * @returns {ApiResponse}\n     */\n    handleError(error, method, endpoint) {\n        console.error(`API Error [${method} ${endpoint}]:`, error);\n        \n        if (error.response) {\n            // Server responded with error status\n            const { data, status, statusText } = error.response;\n            \n            return new ApiResponse(\n                false,\n                null,\n                data?.message || statusText || 'Server error occurred',\n                {\n                    type: 'server_error',\n                    status,\n                    statusText,\n                    data: data || null\n                },\n                { status, statusText }\n            );\n        } else if (error.request) {\n            // Network error\n            return new ApiResponse(\n                false,\n                null,\n                'Network error - please check your connection',\n                {\n                    type: 'network_error',\n                    message: error.message\n                }\n            );\n        } else {\n            // Request setup error\n            return new ApiResponse(\n                false,\n                null,\n                'Request configuration error',\n                {\n                    type: 'config_error',\n                    message: error.message\n                }\n            );\n        }\n    }\n\n    /**\n     * Upload file with progress tracking\n     * @param {string} endpoint - Upload endpoint\n     * @param {FormData} formData - Form data with file\n     * @param {function} onProgress - Progress callback\n     * @returns {Promise<ApiResponse>}\n     */\n    async upload(endpoint, formData, onProgress = null) {\n        try {\n            const response = await axios.post(endpoint, formData, {\n                headers: {\n                    'Content-Type': 'multipart/form-data'\n                },\n                onUploadProgress: onProgress ? (progressEvent) => {\n                    const percentCompleted = Math.round(\n                        (progressEvent.loaded * 100) / progressEvent.total\n                    );\n                    onProgress(percentCompleted);\n                } : undefined\n            });\n            \n            return this.formatResponse(response);\n        } catch (error) {\n            return this.handleError(error, 'UPLOAD', endpoint);\n        }\n    }\n}\n\n// Create singleton instance\nconst apiService = new ApiService();\n\nexport default apiService;\nexport { ApiResponse };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,sBAAsB;;AAExC;AACA,MAAMC,WAAW,CAAC;EACdC,WAAWA,CAACC,OAAO,EAAEC,IAAI,GAAG,IAAI,EAAEC,OAAO,GAAG,EAAE,EAAEC,KAAK,GAAG,IAAI,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IACrE,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;EACpB;AACJ;;AAEA;AACA,MAAMC,UAAU,CAAC;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMC,GAAGA,CAACC,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC3C,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMb,KAAK,CAACS,GAAG,CAACC,QAAQ,EAAE;QACvCC,MAAM;QACN,GAAGC;MACP,CAAC,CAAC;MAEF,OAAO,IAAI,CAACE,cAAc,CAACD,QAAQ,CAAC;IACxC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZ,OAAO,IAAI,CAACS,WAAW,CAACT,KAAK,EAAE,KAAK,EAAEI,QAAQ,CAAC;IACnD;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMM,IAAIA,CAACN,QAAQ,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAEQ,OAAO,GAAG,CAAC,CAAC,EAAE;IAC1C,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMb,KAAK,CAACgB,IAAI,CAACN,QAAQ,EAAEN,IAAI,EAAEQ,OAAO,CAAC;MAC1D,OAAO,IAAI,CAACE,cAAc,CAACD,QAAQ,CAAC;IACxC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZ,OAAO,IAAI,CAACS,WAAW,CAACT,KAAK,EAAE,MAAM,EAAEI,QAAQ,CAAC;IACpD;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMO,GAAGA,CAACP,QAAQ,EAAEN,IAAI,GAAG,CAAC,CAAC,EAAEQ,OAAO,GAAG,CAAC,CAAC,EAAE;IACzC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMb,KAAK,CAACiB,GAAG,CAACP,QAAQ,EAAEN,IAAI,EAAEQ,OAAO,CAAC;MACzD,OAAO,IAAI,CAACE,cAAc,CAACD,QAAQ,CAAC;IACxC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZ,OAAO,IAAI,CAACS,WAAW,CAACT,KAAK,EAAE,KAAK,EAAEI,QAAQ,CAAC;IACnD;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;EACI,MAAMQ,MAAMA,CAACR,QAAQ,EAAEE,OAAO,GAAG,CAAC,CAAC,EAAE;IACjC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMb,KAAK,CAACkB,MAAM,CAACR,QAAQ,EAAEE,OAAO,CAAC;MACtD,OAAO,IAAI,CAACE,cAAc,CAACD,QAAQ,CAAC;IACxC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZ,OAAO,IAAI,CAACS,WAAW,CAACT,KAAK,EAAE,QAAQ,EAAEI,QAAQ,CAAC;IACtD;EACJ;;EAEA;AACJ;AACA;AACA;AACA;EACII,cAAcA,CAACD,QAAQ,EAAE;IACrB,MAAM;MAAET,IAAI;MAAEe,MAAM;MAAEC;IAAW,CAAC,GAAGP,QAAQ;;IAE7C;IACA,IAAIT,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAClC;MACA,IAAIA,IAAI,CAACiB,cAAc,CAAC,SAAS,CAAC,EAAE;QAChC,OAAO,IAAIpB,WAAW,CAClBG,IAAI,CAACD,OAAO,EACZC,IAAI,CAACA,IAAI,IAAIA,IAAI,EACjBA,IAAI,CAACC,OAAO,IAAIe,UAAU,EAC1BhB,IAAI,CAACE,KAAK,IAAI,IAAI,EAClB;UAAEa,MAAM;UAAEC;QAAW,CACzB,CAAC;MACL;;MAEA;MACA,IAAIhB,IAAI,CAACiB,cAAc,CAAC,QAAQ,CAAC,EAAE;QAC/B,OAAO,IAAIpB,WAAW,CAClBG,IAAI,CAACe,MAAM,IAAI,GAAG,IAAIf,IAAI,CAACe,MAAM,GAAG,GAAG,EACvCf,IAAI,CAACA,IAAI,IAAIA,IAAI,EACjBA,IAAI,CAACC,OAAO,IAAIe,UAAU,EAC1BhB,IAAI,CAACE,KAAK,IAAI,IAAI,EAClB;UAAEa,MAAM,EAAEf,IAAI,CAACe,MAAM;UAAEC;QAAW,CACtC,CAAC;MACL;IACJ;;IAEA;IACA,OAAO,IAAInB,WAAW,CAClBkB,MAAM,IAAI,GAAG,IAAIA,MAAM,GAAG,GAAG,EAC7Bf,IAAI,EACJgB,UAAU,EACV,IAAI,EACJ;MAAED,MAAM;MAAEC;IAAW,CACzB,CAAC;EACL;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIL,WAAWA,CAACT,KAAK,EAAEgB,MAAM,EAAEZ,QAAQ,EAAE;IACjCa,OAAO,CAACjB,KAAK,CAAC,cAAcgB,MAAM,IAAIZ,QAAQ,IAAI,EAAEJ,KAAK,CAAC;IAE1D,IAAIA,KAAK,CAACO,QAAQ,EAAE;MAChB;MACA,MAAM;QAAET,IAAI;QAAEe,MAAM;QAAEC;MAAW,CAAC,GAAGd,KAAK,CAACO,QAAQ;MAEnD,OAAO,IAAIZ,WAAW,CAClB,KAAK,EACL,IAAI,EACJ,CAAAG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,OAAO,KAAIe,UAAU,IAAI,uBAAuB,EACtD;QACII,IAAI,EAAE,cAAc;QACpBL,MAAM;QACNC,UAAU;QACVhB,IAAI,EAAEA,IAAI,IAAI;MAClB,CAAC,EACD;QAAEe,MAAM;QAAEC;MAAW,CACzB,CAAC;IACL,CAAC,MAAM,IAAId,KAAK,CAACmB,OAAO,EAAE;MACtB;MACA,OAAO,IAAIxB,WAAW,CAClB,KAAK,EACL,IAAI,EACJ,8CAA8C,EAC9C;QACIuB,IAAI,EAAE,eAAe;QACrBnB,OAAO,EAAEC,KAAK,CAACD;MACnB,CACJ,CAAC;IACL,CAAC,MAAM;MACH;MACA,OAAO,IAAIJ,WAAW,CAClB,KAAK,EACL,IAAI,EACJ,6BAA6B,EAC7B;QACIuB,IAAI,EAAE,cAAc;QACpBnB,OAAO,EAAEC,KAAK,CAACD;MACnB,CACJ,CAAC;IACL;EACJ;;EAEA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,MAAMqB,MAAMA,CAAChB,QAAQ,EAAEiB,QAAQ,EAAEC,UAAU,GAAG,IAAI,EAAE;IAChD,IAAI;MACA,MAAMf,QAAQ,GAAG,MAAMb,KAAK,CAACgB,IAAI,CAACN,QAAQ,EAAEiB,QAAQ,EAAE;QAClDE,OAAO,EAAE;UACL,cAAc,EAAE;QACpB,CAAC;QACDC,gBAAgB,EAAEF,UAAU,GAAIG,aAAa,IAAK;UAC9C,MAAMC,gBAAgB,GAAGC,IAAI,CAACC,KAAK,CAC9BH,aAAa,CAACI,MAAM,GAAG,GAAG,GAAIJ,aAAa,CAACK,KACjD,CAAC;UACDR,UAAU,CAACI,gBAAgB,CAAC;QAChC,CAAC,GAAGK;MACR,CAAC,CAAC;MAEF,OAAO,IAAI,CAACvB,cAAc,CAACD,QAAQ,CAAC;IACxC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACZ,OAAO,IAAI,CAACS,WAAW,CAACT,KAAK,EAAE,QAAQ,EAAEI,QAAQ,CAAC;IACtD;EACJ;AACJ;;AAEA;AACA,MAAM4B,UAAU,GAAG,IAAI9B,UAAU,CAAC,CAAC;AAEnC,eAAe8B,UAAU;AACzB,SAASrC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}