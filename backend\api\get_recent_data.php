<?php
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json');

require_once '../includes/db_connect.php';

try {
    // Get recent bets (last 5)
    $betsQuery = "SELECT b.*, u1.username as user1_name, u2.username as user2_name 
                  FROM bets b 
                  LEFT JOIN users u1 ON b.user1_id = u1.user_id 
                  LEFT JOIN users u2 ON b.user2_id = u2.user_id 
                  ORDER BY b.created_at DESC LIMIT 5";
    $betsStmt = $conn->query($betsQuery);
    $recentBets = $betsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get recent users (last 5)
    $usersQuery = "SELECT user_id, username, created_at 
                   FROM users 
                   ORDER BY created_at DESC LIMIT 5";
    $usersStmt = $conn->query($usersQuery);
    $recentUsers = $usersStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get recent challenges (last 5)
    $challengesQuery = "SELECT b.*, u1.username as creator_name 
                       FROM bets b 
                       LEFT JOIN users u1 ON b.user1_id = u1.user_id 
                       WHERE b.bet_status = 'open' 
                       ORDER BY b.created_at DESC LIMIT 5";
    $challengesStmt = $conn->query($challengesQuery);
    $recentChallenges = $challengesStmt->fetchAll(PDO::FETCH_ASSOC);

    $response = [
        'status' => 'success',
        'data' => [
            'recentBets' => $recentBets,
            'recentUsers' => $recentUsers,
            'recentChallenges' => $recentChallenges
        ]
    ];

    echo json_encode($response);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
