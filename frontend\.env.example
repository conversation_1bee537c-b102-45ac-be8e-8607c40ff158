# FanBet247 Environment Configuration
# Copy this file to .env.local and customize for your environment

# ===========================================
# API Configuration
# ===========================================

# Backend API Base URL (auto-detected if not set)
# Examples:
# For local development: /FanBet247/backend
# For production: https://yourdomain.com/backend
# For subdomain: https://api.yourdomain.com
REACT_APP_API_BASE_URL=

# Project path (auto-detected if not set)
# Examples:
# For root installation: (leave empty)
# For subfolder: /FanBet247
# For custom folder: /your-custom-folder
REACT_APP_PROJECT_PATH=

# Backend path relative to project (default: /backend)
REACT_APP_BACKEND_PATH=/backend

# ===========================================
# Asset Configuration
# ===========================================

# Asset base URL (auto-detected if not set)
REACT_APP_ASSET_BASE_URL=

# ===========================================
# Development Configuration
# ===========================================

# Force development mode (useful for testing)
REACT_APP_FORCE_DEV_MODE=false

# Enable debug logging
REACT_APP_DEBUG=true

# ===========================================
# Production Configuration
# ===========================================

# Production API URL (overrides auto-detection)
REACT_APP_PROD_API_URL=

# CDN URL for assets
REACT_APP_CDN_URL=

# ===========================================
# Database Configuration (for reference)
# ===========================================
# These are used by the backend, not the frontend
# DB_HOST=localhost
# DB_NAME=fanbet247
# DB_USER=your_db_user
# DB_PASS=your_db_password

# ===========================================
# Usage Examples
# ===========================================

# Example 1: Local MAMP development
# REACT_APP_PROJECT_PATH=/FanBet247
# REACT_APP_BACKEND_PATH=/backend

# Example 2: Local XAMPP development
# REACT_APP_PROJECT_PATH=/fanbet247
# REACT_APP_BACKEND_PATH=/backend

# Example 3: Production with custom domain
# REACT_APP_API_BASE_URL=https://api.fanbet247.com
# REACT_APP_ASSET_BASE_URL=https://cdn.fanbet247.com

# Example 4: Production in subfolder
# REACT_APP_PROJECT_PATH=/betting-app
# REACT_APP_BACKEND_PATH=/api

# Example 5: Completely custom setup
# REACT_APP_API_BASE_URL=https://myserver.com/custom-api
# REACT_APP_ASSET_BASE_URL=https://mycdn.com/assets
