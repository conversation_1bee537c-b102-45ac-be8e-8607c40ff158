import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { 
    FaUser, 
    FaEnvelope, 
    FaCalendarAlt, 
    FaCoins, 
    FaChartLine, 
    FaTrophy, 
    FaArrowLeft,
    FaEdit,
    FaBan,
    FaUserSlash,
    FaHistory,
    FaGamepad
} from 'react-icons/fa';

const API_BASE_URL = '/backend';

function UserDetails() {
    const { userId } = useParams();
    const navigate = useNavigate();
    const [user, setUser] = useState(null);
    const [teams, setTeams] = useState([]);
    const [userBets, setUserBets] = useState([]);
    const [userTransactions, setUserTransactions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    useEffect(() => {
        if (userId) {
            fetchUserDetails();
            fetchTeams();
            fetchUserBets();
            fetchUserTransactions();
        }
    }, [userId]);

    const fetchUserDetails = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/user_details.php?id=${userId}`);
            if (response.data.success) {
                setUser(response.data.user);
            } else {
                setError('User not found');
            }
        } catch (err) {
            setError('Failed to fetch user details');
            console.error('Error fetching user details:', err);
        }
    };

    const fetchTeams = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/team_management.php`);
            setTeams(response.data.data || []);
        } catch (err) {
            console.error('Error fetching teams:', err);
        }
    };

    const fetchUserBets = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/user_bets.php?user_id=${userId}`);
            if (response.data.success) {
                setUserBets(response.data.bets || []);
            }
        } catch (err) {
            console.error('Error fetching user bets:', err);
        }
    };

    const fetchUserTransactions = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/user_transactions.php?user_id=${userId}`);
            if (response.data.success) {
                setUserTransactions(response.data.transactions || []);
            }
        } catch (err) {
            console.error('Error fetching user transactions:', err);
        } finally {
            setLoading(false);
        }
    };

    const getTeamLogo = (teamName) => {
        const team = teams.find(team => team.name === teamName);
        return team ? `${API_BASE_URL}/${team.logo}` : null;
    };

    const getDefaultAvatar = () => {
        return "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='80' height='80' viewBox='0 0 80 80'%3E%3Ccircle cx='40' cy='40' r='40' fill='%23e5e7eb'/%3E%3Cpath d='M40 40c6.6 0 12-5.4 12-12s-5.4-12-12-12-12 5.4-12 12 5.4 12 12 12zm0 6c-8 0-24 4-24 12v6h48v-6c0-8-16-12-24-12z' fill='%23374151'/%3E%3C/svg%3E";
    };

    const handleSuspendUser = async () => {
        if (window.confirm('Are you sure you want to suspend this user?')) {
            try {
                const response = await axios.post(`${API_BASE_URL}/handlers/suspend_user.php`, {
                    user_id: userId,
                    action: 'suspend'
                });
                if (response.data.success) {
                    setSuccess('User suspended successfully');
                    fetchUserDetails();
                } else {
                    setError(response.data.message || 'Failed to suspend user');
                }
            } catch (err) {
                setError('Failed to suspend user');
            }
        }
    };

    const handleBanUser = async () => {
        if (window.confirm('Are you sure you want to ban this user? This action cannot be undone.')) {
            try {
                const response = await axios.post(`${API_BASE_URL}/handlers/ban_user.php`, {
                    user_id: userId,
                    action: 'ban'
                });
                if (response.data.success) {
                    setSuccess('User banned successfully');
                    fetchUserDetails();
                } else {
                    setError(response.data.message || 'Failed to ban user');
                }
            } catch (err) {
                setError('Failed to ban user');
            }
        }
    };

    if (loading) {
        return (
            <div className="flex justify-center items-center min-h-screen">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    if (!user) {
        return (
            <div className="p-6 bg-gray-50 min-h-screen">
                <div className="text-center">
                    <h1 className="text-2xl font-bold text-gray-800 mb-4">User Not Found</h1>
                    <button
                        onClick={() => navigate('/admin/users')}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                        Back to Users
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Header */}
            <div className="mb-8">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <button
                            onClick={() => navigate('/admin/users')}
                            className="flex items-center text-blue-600 hover:text-blue-800"
                        >
                            <FaArrowLeft className="mr-2" />
                            Back to Users
                        </button>
                        <h1 className="text-2xl font-bold text-gray-800">User Details</h1>
                    </div>
                    <div className="flex space-x-2">
                        <button
                            onClick={() => navigate(`/admin/users/edit/${userId}`)}
                            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                        >
                            <FaEdit className="mr-2" />
                            Edit User
                        </button>
                        <button
                            onClick={handleSuspendUser}
                            className="flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700"
                        >
                            <FaUserSlash className="mr-2" />
                            Suspend
                        </button>
                        <button
                            onClick={handleBanUser}
                            className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
                        >
                            <FaBan className="mr-2" />
                            Ban User
                        </button>
                    </div>
                </div>
            </div>

            {/* Notification Messages */}
            {error && (
                <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
                    {error}
                </div>
            )}
            {success && (
                <div className="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative">
                    {success}
                </div>
            )}

            {/* User Profile Card */}
            <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
                <div className="flex items-center space-x-6">
                    <div className="flex-shrink-0">
                        <img
                            src={getTeamLogo(user.favorite_team) || getDefaultAvatar()}
                            alt={user.favorite_team || 'User Avatar'}
                            className="w-20 h-20 rounded-full object-contain border-2 border-gray-200"
                            onError={(e) => {
                                e.target.src = getDefaultAvatar();
                            }}
                        />
                    </div>
                    <div className="flex-1">
                        <h2 className="text-2xl font-bold text-gray-900">{user.full_name}</h2>
                        <p className="text-gray-600">@{user.username}</p>
                        <div className="flex items-center space-x-4 mt-2">
                            <div className="flex items-center text-gray-500">
                                <FaEnvelope className="mr-1" />
                                {user.email}
                            </div>
                            <div className="flex items-center text-gray-500">
                                <FaCalendarAlt className="mr-1" />
                                Joined {new Date(user.created_at).toLocaleDateString()}
                            </div>
                        </div>
                    </div>
                    <div className="text-right">
                        <div className="text-3xl font-bold text-green-600">{user.balance}</div>
                        <div className="text-gray-500">FanCoins</div>
                    </div>
                </div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                        <div className="rounded-full bg-blue-100 p-3 mr-4">
                            <FaGamepad className="text-blue-500 text-xl" />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 uppercase tracking-wider">Total Bets</p>
                            <h3 className="text-2xl font-bold text-gray-800">{user.total_bets || 0}</h3>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                        <div className="rounded-full bg-green-100 p-3 mr-4">
                            <FaTrophy className="text-green-500 text-xl" />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 uppercase tracking-wider">Wins</p>
                            <h3 className="text-2xl font-bold text-gray-800">{user.wins || 0}</h3>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                        <div className="rounded-full bg-yellow-100 p-3 mr-4">
                            <FaChartLine className="text-yellow-500 text-xl" />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 uppercase tracking-wider">Current Streak</p>
                            <h3 className="text-2xl font-bold text-gray-800">{user.current_streak || 0}</h3>
                        </div>
                    </div>
                </div>

                <div className="bg-white rounded-lg shadow-sm p-6">
                    <div className="flex items-center">
                        <div className="rounded-full bg-purple-100 p-3 mr-4">
                            <FaCoins className="text-purple-500 text-xl" />
                        </div>
                        <div>
                            <p className="text-sm text-gray-500 uppercase tracking-wider">Total Points</p>
                            <h3 className="text-2xl font-bold text-gray-800">{user.total_points || 0}</h3>
                        </div>
                    </div>
                </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Recent Bets */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <FaGamepad className="mr-2" />
                        Recent Bets
                    </h3>
                    <div className="space-y-4">
                        {userBets.length > 0 ? (
                            userBets.slice(0, 5).map((bet) => (
                                <div key={bet.bet_id} className="border-l-4 border-blue-500 pl-4 py-2">
                                    <div className="flex justify-between items-start">
                                        <div>
                                            <p className="font-medium text-gray-900">
                                                {bet.team_a} vs {bet.team_b}
                                            </p>
                                            <p className="text-sm text-gray-500">
                                                Amount: {bet.amount_user1} FC
                                            </p>
                                        </div>
                                        <span className={`px-2 py-1 text-xs rounded-full ${
                                            bet.bet_status === 'completed' ? 'bg-green-100 text-green-800' :
                                            bet.bet_status === 'open' ? 'bg-yellow-100 text-yellow-800' :
                                            'bg-blue-100 text-blue-800'
                                        }`}>
                                            {bet.bet_status}
                                        </span>
                                    </div>
                                    <p className="text-xs text-gray-400 mt-1">
                                        {new Date(bet.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            ))
                        ) : (
                            <p className="text-gray-500 text-center py-4">No bets found</p>
                        )}
                    </div>
                </div>

                {/* Recent Transactions */}
                <div className="bg-white rounded-lg shadow-sm p-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                        <FaHistory className="mr-2" />
                        Recent Transactions
                    </h3>
                    <div className="space-y-4">
                        {userTransactions.length > 0 ? (
                            userTransactions.slice(0, 5).map((transaction) => (
                                <div key={transaction.transaction_id} className="flex justify-between items-center py-2 border-b border-gray-100">
                                    <div>
                                        <p className="font-medium text-gray-900 capitalize">
                                            {transaction.type}
                                        </p>
                                        <p className="text-xs text-gray-400">
                                            {new Date(transaction.created_at).toLocaleDateString()}
                                        </p>
                                    </div>
                                    <div className="text-right">
                                        <p className={`font-medium ${
                                            transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                                        }`}>
                                            {transaction.amount > 0 ? '+' : ''}{transaction.amount} FC
                                        </p>
                                        <p className="text-xs text-gray-500 capitalize">
                                            {transaction.status}
                                        </p>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <p className="text-gray-500 text-center py-4">No transactions found</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default UserDetails;
