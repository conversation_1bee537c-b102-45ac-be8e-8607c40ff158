<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';
$conn = getDBConnection();

if (!$conn) {
    http_response_code(500);
    echo json_encode(["success" => false, "message" => "Database connection failed"]);
    exit;
}

// Accept both 'id' and 'userId' parameters
$userId = isset($_GET['userId']) ? $_GET['userId'] : (isset($_GET['id']) ? $_GET['id'] : null);

if ($userId) {
    try {
        $query = "SELECT u.user_id, u.username, u.full_name, u.email, u.favorite_team, u.balance, 
                COALESCE((SELECT SUM(points) FROM user_achievements WHERE user_id = u.user_id), 0) as points 
                FROM users u WHERE u.user_id = :userId";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(":userId", $userId);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            // Ensure numeric values are properly formatted
            $user['balance'] = floatval($user['balance']);
            $user['points'] = intval($user['points']);
            
            echo json_encode([
                "success" => true, 
                "balance" => $user['balance'],
                "points" => $user['points'],
                "username" => $user['username'],
                "user" => $user
            ]);
        } else {
            http_response_code(404);
            echo json_encode(["success" => false, "message" => "User not found"]);
        }
    } catch (PDOException $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        http_response_code(500);
        echo json_encode(["success" => false, "message" => "Error fetching user data"]);
    }
} else {
    http_response_code(400);
    echo json_encode(["success" => false, "message" => "User ID not provided"]);
}
?>
