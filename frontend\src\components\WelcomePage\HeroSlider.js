import React, { useState, useEffect, useCallback } from 'react';

const HeroSlider = ({ sliderImages }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const nextSlide = useCallback(() => {
    setCurrentSlide((prevSlide) => (prevSlide + 1) % sliderImages.length);
  }, [sliderImages.length]);

  const prevSlide = useCallback(() => {
    setCurrentSlide((prevSlide) =>
      prevSlide === 0 ? sliderImages.length - 1 : prevSlide - 1
    );
  }, [sliderImages.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
  };

  useEffect(() => {
    const timer = setInterval(nextSlide, 5000);
    return () => clearInterval(timer);
  }, [nextSlide]);

  return (
    <section className="hero">
      <div className="hero__slider">
        {sliderImages.map((image, index) => (
          <img
            key={index}
            src={image}
            alt={`Slide ${index + 1}`}
            className={`hero__image ${currentSlide === index ? 'active' : ''}`}
            style={{
              transform: `translateX(${100 * (index - currentSlide)}%)`,
              opacity: currentSlide === index ? 1 : 0.7,
              display: Math.abs(currentSlide - index) <= 1 ? 'block' : 'none'
            }}
            onError={(e) => {
              e.target.src = process.env.PUBLIC_URL + '/slider/Slider1.png';
            }}
          />
        ))}

        <button className="hero__nav hero__nav--prev" onClick={prevSlide}>
          <span>&#10094;</span>
        </button>
        <button className="hero__nav hero__nav--next" onClick={nextSlide}>
          <span>&#10095;</span>
        </button>

        <div className="hero__indicators">
          {sliderImages.map((_, index) => (
            <button
              key={index}
              className={`hero__indicator ${currentSlide === index ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

export default HeroSlider;
