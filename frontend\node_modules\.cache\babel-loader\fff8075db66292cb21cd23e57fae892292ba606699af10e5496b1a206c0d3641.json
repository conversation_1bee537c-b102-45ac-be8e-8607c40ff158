{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\CurrencyManagement.js\",\n  _s = $RefreshSig$();\n/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction CurrencyManagement() {\n  _s();\n  const [currencies, setCurrencies] = useState([]);\n  const [exchangeRates, setExchangeRates] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n\n  // Stats\n  const [stats, setStats] = useState({\n    totalCurrencies: 0,\n    activeCurrencies: 0,\n    inactiveCurrencies: 0,\n    lastUpdated: null\n  });\n\n  // Modal states\n  const [showAddCurrency, setShowAddCurrency] = useState(false);\n  const [editingRate, setEditingRate] = useState(null);\n  const [editingCurrency, setEditingCurrency] = useState(null);\n\n  // Form states\n  const [newCurrency, setNewCurrency] = useState({\n    currency_code: '',\n    currency_name: '',\n    currency_symbol: '',\n    is_active: true\n  });\n  const [newRate, setNewRate] = useState('');\n  const [notes, setNotes] = useState('');\n  useEffect(() => {\n    loadCurrencies();\n    loadExchangeRates();\n  }, []);\n  const loadCurrencies = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n      if (response.data.success) {\n        const currencyData = response.data.data.currencies || [];\n        setCurrencies(currencyData);\n\n        // Calculate stats\n        const totalCurrencies = currencyData.length;\n        const activeCurrencies = currencyData.filter(c => c.is_active).length;\n        const inactiveCurrencies = totalCurrencies - activeCurrencies;\n        setStats({\n          totalCurrencies,\n          activeCurrencies,\n          inactiveCurrencies,\n          lastUpdated: new Date().toISOString()\n        });\n      } else {\n        setError(response.data.message || 'Failed to load currencies');\n      }\n    } catch (err) {\n      console.error('Error loading currencies:', err);\n      setError('Failed to load currencies. Please check your network connection.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const loadExchangeRates = async () => {\n    try {\n      const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n      if (response.data.success) {\n        setExchangeRates(response.data.data.exchange_rates || []);\n      }\n    } catch (err) {\n      console.error('Error loading exchange rates:', err);\n    }\n  };\n  const handleUpdateRate = async currencyId => {\n    if (!newRate || isNaN(parseFloat(newRate))) {\n      setError('Please enter a valid exchange rate');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n        currency_id: currencyId,\n        rate_to_fancoin: parseFloat(newRate),\n        notes: notes\n      });\n      if (response.data.success) {\n        setSuccess('Exchange rate updated successfully!');\n        loadExchangeRates();\n        setEditingRate(null);\n        setNewRate('');\n        setNotes('');\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to update exchange rate');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error updating exchange rate:', err);\n      setError('Failed to update exchange rate. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleToggleCurrency = async (currencyId, currentStatus) => {\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'toggle',\n        currency_id: currencyId\n      });\n      if (response.data.success) {\n        setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n        loadCurrencies();\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to toggle currency status');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error toggling currency:', err);\n      setError('Failed to toggle currency status. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleAddCurrency = async () => {\n    if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n      setError('Please fill in all required fields');\n      return;\n    }\n    setLoading(true);\n    try {\n      const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n        action: 'create',\n        ...newCurrency\n      });\n      if (response.data.success) {\n        setSuccess('Currency added successfully!');\n        loadCurrencies();\n        setShowAddCurrency(false);\n        setNewCurrency({\n          currency_code: '',\n          currency_name: '',\n          currency_symbol: '',\n          is_active: true\n        });\n        setTimeout(() => setSuccess(''), 3000);\n      } else {\n        setError(response.data.message || 'Failed to add currency');\n        setTimeout(() => setError(''), 3000);\n      }\n    } catch (err) {\n      console.error('Error adding currency:', err);\n      setError('Failed to add currency. Please try again.');\n      setTimeout(() => setError(''), 3000);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const getExchangeRate = currencyId => {\n    return exchangeRates.find(rate => rate.currency_id === currencyId);\n  };\n  const formatDate = dateString => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewCurrency(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"currency-management\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"page-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"Currency Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 208,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Manage supported currencies and exchange rates\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"btn btn-primary\",\n        onClick: () => setShowAddCurrency(true),\n        children: \"Add New Currency\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 210,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 207,\n      columnNumber: 13\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"\\u26A0\\uFE0F \", error.message || 'An error occurred']\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: loadData,\n        className: \"btn btn-secondary\",\n        children: \"Retry\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 219,\n      columnNumber: 17\n    }, this), loading && currencies.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-spinner\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"spinner\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 229,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading currencies...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 21\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 228,\n      columnNumber: 17\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"currencies-grid\",\n      children: currencies.map(currency => {\n        const rate = getExchangeRate(currency.id);\n        const isEditing = editingRate === currency.id;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `currency-card ${!currency.is_active ? 'inactive' : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: currency.currency_code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: currency.currency_name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"currency-symbol\",\n                children: currency.currency_symbol\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 37\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"currency-status\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `status-badge ${currency.is_active ? 'active' : 'inactive'}`,\n                children: currency.is_active ? 'Active' : 'Inactive'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 41\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 37\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"exchange-rate-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: \"Exchange Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 37\n            }, this), rate ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"rate-info\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"current-rate\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"rate-value\",\n                  children: [\"1 FC = \", currency.currency_symbol, rate.rate_to_fancoin]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n                  children: [\"Updated: \", formatDate(rate.updated_at)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 261,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 257,\n                columnNumber: 45\n              }, this), isEditing ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"rate-edit-form\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"number\",\n                  step: \"0.0001\",\n                  value: newRate,\n                  onChange: e => setNewRate(e.target.value),\n                  placeholder: \"New rate\",\n                  className: \"rate-input\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                  value: notes,\n                  onChange: e => setNotes(e.target.value),\n                  placeholder: \"Update notes (optional)\",\n                  className: \"notes-input\",\n                  rows: \"2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 53\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"edit-actions\",\n                  children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => handleUpdateRate(currency.id),\n                    className: \"btn btn-success btn-sm\",\n                    disabled: loading,\n                    children: \"Save\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 282,\n                    columnNumber: 57\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setEditingRate(null);\n                      setNewRate('');\n                      setNotes('');\n                    },\n                    className: \"btn btn-secondary btn-sm\",\n                    children: \"Cancel\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 289,\n                    columnNumber: 57\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 281,\n                  columnNumber: 53\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 49\n              }, this) : /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate(rate.rate_to_fancoin.toString());\n                },\n                className: \"btn btn-outline btn-sm\",\n                children: \"Update Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 302,\n                columnNumber: 49\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 256,\n              columnNumber: 41\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"no-rate\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"No exchange rate set\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setEditingRate(currency.id);\n                  setNewRate('');\n                },\n                className: \"btn btn-primary btn-sm\",\n                children: \"Set Rate\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 314,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"currency-actions\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleToggleCurrency(currency.id, currency.is_active),\n              className: `btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`,\n              disabled: loading,\n              children: currency.is_active ? 'Deactivate' : 'Activate'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 37\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 329,\n            columnNumber: 33\n          }, this)]\n        }, currency.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 29\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 17\n    }, this), showAddCurrency && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modal-overlay\",\n      onClick: () => setShowAddCurrency(false),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"modal-content\",\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Add New Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Code (e.g., EUR, GBP)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_code,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_code: e.target.value.toUpperCase()\n            }),\n            placeholder: \"USD\",\n            maxLength: \"3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 351,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_name,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_name: e.target.value\n            }),\n            placeholder: \"US Dollar\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 359,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"Currency Symbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            value: newCurrency.currency_symbol,\n            onChange: e => setNewCurrency({\n              ...newCurrency,\n              currency_symbol: e.target.value\n            }),\n            placeholder: \"$\",\n            maxLength: \"5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 370,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 368,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: newCurrency.is_active,\n              onChange: e => setNewCurrency({\n                ...newCurrency,\n                is_active: e.target.checked\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 33\n            }, this), \"Active\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 378,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"modal-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAddCurrency,\n            className: \"btn btn-primary\",\n            disabled: loading,\n            children: \"Add Currency\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 389,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowAddCurrency(false),\n            className: \"btn btn-secondary\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 21\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 206,\n    columnNumber: 9\n  }, this);\n}\n_s(CurrencyManagement, \"teJRouVhEaHhxEDt1jb4Qd4Sz2g=\");\n_c = CurrencyManagement;\n;\nexport default CurrencyManagement;\nvar _c;\n$RefreshReg$(_c, \"CurrencyManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "FaCoins", "FaEdit", "FaToggleOn", "FaToggleOff", "FaPlus", "FaSave", "FaTimes", "FaExchangeAlt", "jsxDEV", "_jsxDEV", "API_BASE_URL", "CurrencyManagement", "_s", "currencies", "setCurrencies", "exchangeRates", "setExchangeRates", "loading", "setLoading", "error", "setError", "success", "setSuccess", "stats", "setStats", "totalCurrencies", "activeCurrencies", "inactiveCurrencies", "lastUpdated", "showAddCurrency", "setShowAddCurrency", "editingRate", "setEditingRate", "editing<PERSON><PERSON><PERSON>cy", "setEditingCurrency", "newCurrency", "setNewCurrency", "currency_code", "currency_name", "currency_symbol", "is_active", "newRate", "setNewRate", "notes", "setNotes", "loadCurrencies", "loadExchangeRates", "response", "get", "data", "currencyData", "length", "filter", "c", "Date", "toISOString", "message", "err", "console", "exchange_rates", "handleUpdateRate", "currencyId", "isNaN", "parseFloat", "post", "currency_id", "rate_to_fancoin", "setTimeout", "handleToggleCurrency", "currentStatus", "action", "handleAddCurrency", "getExchangeRate", "find", "rate", "formatDate", "dateString", "toLocaleDateString", "year", "month", "day", "hour", "minute", "handleInputChange", "e", "name", "value", "target", "prev", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "loadData", "map", "currency", "id", "isEditing", "updated_at", "type", "step", "onChange", "placeholder", "rows", "disabled", "toString", "stopPropagation", "toUpperCase", "max<PERSON><PERSON><PERSON>", "checked", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/CurrencyManagement.js"], "sourcesContent": ["/**\n * Currency Management Admin Page\n *\n * Provides admin interface for managing currencies and exchange rates\n * Follows standard admin page patterns used throughout the application\n */\n\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';\n\nconst API_BASE_URL = '/backend';\n\nfunction CurrencyManagement() {\n    const [currencies, setCurrencies] = useState([]);\n    const [exchangeRates, setExchangeRates] = useState([]);\n    const [loading, setLoading] = useState(false);\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n\n    // Stats\n    const [stats, setStats] = useState({\n        totalCurrencies: 0,\n        activeCurrencies: 0,\n        inactiveCurrencies: 0,\n        lastUpdated: null\n    });\n\n    // Modal states\n    const [showAddCurrency, setShowAddCurrency] = useState(false);\n    const [editingRate, setEditingRate] = useState(null);\n    const [editingCurrency, setEditingCurrency] = useState(null);\n\n    // Form states\n    const [newCurrency, setNewCurrency] = useState({\n        currency_code: '',\n        currency_name: '',\n        currency_symbol: '',\n        is_active: true\n    });\n    const [newRate, setNewRate] = useState('');\n    const [notes, setNotes] = useState('');\n\n    useEffect(() => {\n        loadCurrencies();\n        loadExchangeRates();\n    }, []);\n\n    const loadCurrencies = async () => {\n        setLoading(true);\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);\n            if (response.data.success) {\n                const currencyData = response.data.data.currencies || [];\n                setCurrencies(currencyData);\n\n                // Calculate stats\n                const totalCurrencies = currencyData.length;\n                const activeCurrencies = currencyData.filter(c => c.is_active).length;\n                const inactiveCurrencies = totalCurrencies - activeCurrencies;\n\n                setStats({\n                    totalCurrencies,\n                    activeCurrencies,\n                    inactiveCurrencies,\n                    lastUpdated: new Date().toISOString()\n                });\n            } else {\n                setError(response.data.message || 'Failed to load currencies');\n            }\n        } catch (err) {\n            console.error('Error loading currencies:', err);\n            setError('Failed to load currencies. Please check your network connection.');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const loadExchangeRates = async () => {\n        try {\n            const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);\n            if (response.data.success) {\n                setExchangeRates(response.data.data.exchange_rates || []);\n            }\n        } catch (err) {\n            console.error('Error loading exchange rates:', err);\n        }\n    };\n\n    const handleUpdateRate = async (currencyId) => {\n        if (!newRate || isNaN(parseFloat(newRate))) {\n            setError('Please enter a valid exchange rate');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {\n                currency_id: currencyId,\n                rate_to_fancoin: parseFloat(newRate),\n                notes: notes\n            });\n\n            if (response.data.success) {\n                setSuccess('Exchange rate updated successfully!');\n                loadExchangeRates();\n                setEditingRate(null);\n                setNewRate('');\n                setNotes('');\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to update exchange rate');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error updating exchange rate:', err);\n            setError('Failed to update exchange rate. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleToggleCurrency = async (currencyId, currentStatus) => {\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'toggle',\n                currency_id: currencyId\n            });\n\n            if (response.data.success) {\n                setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);\n                loadCurrencies();\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to toggle currency status');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error toggling currency:', err);\n            setError('Failed to toggle currency status. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const handleAddCurrency = async () => {\n        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {\n            setError('Please fill in all required fields');\n            return;\n        }\n\n        setLoading(true);\n        try {\n            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {\n                action: 'create',\n                ...newCurrency\n            });\n\n            if (response.data.success) {\n                setSuccess('Currency added successfully!');\n                loadCurrencies();\n                setShowAddCurrency(false);\n                setNewCurrency({\n                    currency_code: '',\n                    currency_name: '',\n                    currency_symbol: '',\n                    is_active: true\n                });\n                setTimeout(() => setSuccess(''), 3000);\n            } else {\n                setError(response.data.message || 'Failed to add currency');\n                setTimeout(() => setError(''), 3000);\n            }\n        } catch (err) {\n            console.error('Error adding currency:', err);\n            setError('Failed to add currency. Please try again.');\n            setTimeout(() => setError(''), 3000);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    const getExchangeRate = (currencyId) => {\n        return exchangeRates.find(rate => rate.currency_id === currencyId);\n    };\n\n    const formatDate = (dateString) => {\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewCurrency(prev => ({ ...prev, [name]: value }));\n    };\n\n    return (\n        <div className=\"currency-management\">\n            <div className=\"page-header\">\n                <h1>Currency Management</h1>\n                <p>Manage supported currencies and exchange rates</p>\n                <button \n                    className=\"btn btn-primary\"\n                    onClick={() => setShowAddCurrency(true)}\n                >\n                    Add New Currency\n                </button>\n            </div>\n\n            {error && (\n                <div className=\"error-message\">\n                    <p>⚠️ {error.message || 'An error occurred'}</p>\n                    <button onClick={loadData} className=\"btn btn-secondary\">\n                        Retry\n                    </button>\n                </div>\n            )}\n\n            {loading && currencies.length === 0 ? (\n                <div className=\"loading-spinner\">\n                    <div className=\"spinner\"></div>\n                    <p>Loading currencies...</p>\n                </div>\n            ) : (\n                <div className=\"currencies-grid\">\n                    {currencies.map((currency) => {\n                        const rate = getExchangeRate(currency.id);\n                        const isEditing = editingRate === currency.id;\n\n                        return (\n                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>\n                                <div className=\"currency-header\">\n                                    <div className=\"currency-info\">\n                                        <h3>{currency.currency_code}</h3>\n                                        <p>{currency.currency_name}</p>\n                                        <span className=\"currency-symbol\">{currency.currency_symbol}</span>\n                                    </div>\n                                    <div className=\"currency-status\">\n                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>\n                                            {currency.is_active ? 'Active' : 'Inactive'}\n                                        </span>\n                                    </div>\n                                </div>\n\n                                <div className=\"exchange-rate-section\">\n                                    <h4>Exchange Rate</h4>\n                                    {rate ? (\n                                        <div className=\"rate-info\">\n                                            <div className=\"current-rate\">\n                                                <span className=\"rate-value\">\n                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}\n                                                </span>\n                                                <small>Updated: {formatDate(rate.updated_at)}</small>\n                                            </div>\n\n                                            {isEditing ? (\n                                                <div className=\"rate-edit-form\">\n                                                    <input\n                                                        type=\"number\"\n                                                        step=\"0.0001\"\n                                                        value={newRate}\n                                                        onChange={(e) => setNewRate(e.target.value)}\n                                                        placeholder=\"New rate\"\n                                                        className=\"rate-input\"\n                                                    />\n                                                    <textarea\n                                                        value={notes}\n                                                        onChange={(e) => setNotes(e.target.value)}\n                                                        placeholder=\"Update notes (optional)\"\n                                                        className=\"notes-input\"\n                                                        rows=\"2\"\n                                                    />\n                                                    <div className=\"edit-actions\">\n                                                        <button \n                                                            onClick={() => handleUpdateRate(currency.id)}\n                                                            className=\"btn btn-success btn-sm\"\n                                                            disabled={loading}\n                                                        >\n                                                            Save\n                                                        </button>\n                                                        <button \n                                                            onClick={() => {\n                                                                setEditingRate(null);\n                                                                setNewRate('');\n                                                                setNotes('');\n                                                            }}\n                                                            className=\"btn btn-secondary btn-sm\"\n                                                        >\n                                                            Cancel\n                                                        </button>\n                                                    </div>\n                                                </div>\n                                            ) : (\n                                                <button \n                                                    onClick={() => {\n                                                        setEditingRate(currency.id);\n                                                        setNewRate(rate.rate_to_fancoin.toString());\n                                                    }}\n                                                    className=\"btn btn-outline btn-sm\"\n                                                >\n                                                    Update Rate\n                                                </button>\n                                            )}\n                                        </div>\n                                    ) : (\n                                        <div className=\"no-rate\">\n                                            <p>No exchange rate set</p>\n                                            <button \n                                                onClick={() => {\n                                                    setEditingRate(currency.id);\n                                                    setNewRate('');\n                                                }}\n                                                className=\"btn btn-primary btn-sm\"\n                                            >\n                                                Set Rate\n                                            </button>\n                                        </div>\n                                    )}\n                                </div>\n\n                                <div className=\"currency-actions\">\n                                    <button \n                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}\n                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}\n                                        disabled={loading}\n                                    >\n                                        {currency.is_active ? 'Deactivate' : 'Activate'}\n                                    </button>\n                                </div>\n                            </div>\n                        );\n                    })}\n                </div>\n            )}\n\n            {/* Add Currency Modal */}\n            {showAddCurrency && (\n                <div className=\"modal-overlay\" onClick={() => setShowAddCurrency(false)}>\n                    <div className=\"modal-content\" onClick={(e) => e.stopPropagation()}>\n                        <h3>Add New Currency</h3>\n                        <div className=\"form-group\">\n                            <label>Currency Code (e.g., EUR, GBP)</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_code}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}\n                                placeholder=\"USD\"\n                                maxLength=\"3\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Name</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_name}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}\n                                placeholder=\"US Dollar\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>Currency Symbol</label>\n                            <input\n                                type=\"text\"\n                                value={newCurrency.currency_symbol}\n                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}\n                                placeholder=\"$\"\n                                maxLength=\"5\"\n                            />\n                        </div>\n                        <div className=\"form-group\">\n                            <label>\n                                <input\n                                    type=\"checkbox\"\n                                    checked={newCurrency.is_active}\n                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}\n                                />\n                                Active\n                            </label>\n                        </div>\n                        <div className=\"modal-actions\">\n                            <button \n                                onClick={handleAddCurrency}\n                                className=\"btn btn-primary\"\n                                disabled={loading}\n                            >\n                                Add Currency\n                            </button>\n                            <button \n                                onClick={() => setShowAddCurrency(false)}\n                                className=\"btn btn-secondary\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </div>\n                </div>\n            )}\n        </div>\n    );\n};\n\nexport default CurrencyManagement;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,MAAM,EAAEC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElH,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,kBAAkBA,CAAA,EAAG;EAAAC,EAAA;EAC1B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACkB,aAAa,EAAEC,gBAAgB,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsB,KAAK,EAAEC,QAAQ,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;;EAE1C;EACA,MAAM,CAAC0B,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC;IAC/B4B,eAAe,EAAE,CAAC;IAClBC,gBAAgB,EAAE,CAAC;IACnBC,kBAAkB,EAAE,CAAC;IACrBC,WAAW,EAAE;EACjB,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACkC,WAAW,EAAEC,cAAc,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACoC,eAAe,EAAEC,kBAAkB,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;;EAE5D;EACA,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAC;IAC3CwC,aAAa,EAAE,EAAE;IACjBC,aAAa,EAAE,EAAE;IACjBC,eAAe,EAAE,EAAE;IACnBC,SAAS,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EAEtCC,SAAS,CAAC,MAAM;IACZ+C,cAAc,CAAC,CAAC;IAChBC,iBAAiB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,cAAc,GAAG,MAAAA,CAAA,KAAY;IAC/B3B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,8BAA8B,CAAC;MAC/E,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvB,MAAM6B,YAAY,GAAGH,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACpC,UAAU,IAAI,EAAE;QACxDC,aAAa,CAACoC,YAAY,CAAC;;QAE3B;QACA,MAAMzB,eAAe,GAAGyB,YAAY,CAACC,MAAM;QAC3C,MAAMzB,gBAAgB,GAAGwB,YAAY,CAACE,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACb,SAAS,CAAC,CAACW,MAAM;QACrE,MAAMxB,kBAAkB,GAAGF,eAAe,GAAGC,gBAAgB;QAE7DF,QAAQ,CAAC;UACLC,eAAe;UACfC,gBAAgB;UAChBC,kBAAkB;UAClBC,WAAW,EAAE,IAAI0B,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;QACxC,CAAC,CAAC;MACN,CAAC,MAAM;QACHnC,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,2BAA2B,CAAC;MAClE;IACJ,CAAC,CAAC,OAAOC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,2BAA2B,EAAEsC,GAAG,CAAC;MAC/CrC,QAAQ,CAAC,kEAAkE,CAAC;IAChF,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMhD,KAAK,CAACiD,GAAG,CAAC,GAAGtC,YAAY,kCAAkC,CAAC;MACnF,IAAIqC,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBL,gBAAgB,CAAC+B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU,cAAc,IAAI,EAAE,CAAC;MAC7D;IACJ,CAAC,CAAC,OAAOF,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;IACvD;EACJ,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC3C,IAAI,CAACpB,OAAO,IAAIqB,KAAK,CAACC,UAAU,CAACtB,OAAO,CAAC,CAAC,EAAE;MACxCrB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,oCAAoC,EAAE;QACnFuD,WAAW,EAAEJ,UAAU;QACvBK,eAAe,EAAEH,UAAU,CAACtB,OAAO,CAAC;QACpCE,KAAK,EAAEA;MACX,CAAC,CAAC;MAEF,IAAII,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,qCAAqC,CAAC;QACjDwB,iBAAiB,CAAC,CAAC;QACnBd,cAAc,CAAC,IAAI,CAAC;QACpBU,UAAU,CAAC,EAAE,CAAC;QACdE,QAAQ,CAAC,EAAE,CAAC;QACZuB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,gCAAgC,CAAC;QACnEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,EAAEsC,GAAG,CAAC;MACnDrC,QAAQ,CAAC,mDAAmD,CAAC;MAC7D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMkD,oBAAoB,GAAG,MAAAA,CAAOP,UAAU,EAAEQ,aAAa,KAAK;IAC9DnD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,iCAAiC,EAAE;QAChF4D,MAAM,EAAE,QAAQ;QAChBL,WAAW,EAAEJ;MACjB,CAAC,CAAC;MAEF,IAAId,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,YAAY+C,aAAa,GAAG,aAAa,GAAG,WAAW,gBAAgB,CAAC;QACnFxB,cAAc,CAAC,CAAC;QAChBsB,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,kCAAkC,CAAC;QACrEW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,0BAA0B,EAAEsC,GAAG,CAAC;MAC9CrC,QAAQ,CAAC,qDAAqD,CAAC;MAC/D+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMqD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI,CAACpC,WAAW,CAACE,aAAa,IAAI,CAACF,WAAW,CAACG,aAAa,IAAI,CAACH,WAAW,CAACI,eAAe,EAAE;MAC1FnB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C;IACJ;IAEAF,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACA,MAAM6B,QAAQ,GAAG,MAAMhD,KAAK,CAACiE,IAAI,CAAC,GAAGtD,YAAY,iCAAiC,EAAE;QAChF4D,MAAM,EAAE,QAAQ;QAChB,GAAGnC;MACP,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACE,IAAI,CAAC5B,OAAO,EAAE;QACvBC,UAAU,CAAC,8BAA8B,CAAC;QAC1CuB,cAAc,CAAC,CAAC;QAChBf,kBAAkB,CAAC,KAAK,CAAC;QACzBM,cAAc,CAAC;UACXC,aAAa,EAAE,EAAE;UACjBC,aAAa,EAAE,EAAE;UACjBC,eAAe,EAAE,EAAE;UACnBC,SAAS,EAAE;QACf,CAAC,CAAC;QACF2B,UAAU,CAAC,MAAM7C,UAAU,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM;QACHF,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAACO,OAAO,IAAI,wBAAwB,CAAC;QAC3DW,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;MACxC;IACJ,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACVC,OAAO,CAACvC,KAAK,CAAC,wBAAwB,EAAEsC,GAAG,CAAC;MAC5CrC,QAAQ,CAAC,2CAA2C,CAAC;MACrD+C,UAAU,CAAC,MAAM/C,QAAQ,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;IACxC,CAAC,SAAS;MACNF,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,MAAMsD,eAAe,GAAIX,UAAU,IAAK;IACpC,OAAO9C,aAAa,CAAC0D,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACT,WAAW,KAAKJ,UAAU,CAAC;EACtE,CAAC;EAED,MAAMc,UAAU,GAAIC,UAAU,IAAK;IAC/B,OAAO,IAAItB,IAAI,CAACsB,UAAU,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MACpDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,OAAO;MACdC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACZ,CAAC,CAAC;EACN,CAAC;EAED,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCnD,cAAc,CAACoD,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACxD,CAAC;EAED,oBACI7E,OAAA;IAAKgF,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAChCjF,OAAA;MAAKgF,SAAS,EAAC,aAAa;MAAAC,QAAA,gBACxBjF,OAAA;QAAAiF,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5BrF,OAAA;QAAAiF,QAAA,EAAG;MAA8C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACrDrF,OAAA;QACIgF,SAAS,EAAC,iBAAiB;QAC3BM,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,IAAI,CAAE;QAAA4D,QAAA,EAC3C;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,EAEL3E,KAAK,iBACFV,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC1BjF,OAAA;QAAAiF,QAAA,GAAG,eAAG,EAACvE,KAAK,CAACqC,OAAO,IAAI,mBAAmB;MAAA;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAChDrF,OAAA;QAAQsF,OAAO,EAAEC,QAAS;QAACP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEzD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACR,EAEA7E,OAAO,IAAIJ,UAAU,CAACsC,MAAM,KAAK,CAAC,gBAC/B1C,OAAA;MAAKgF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC5BjF,OAAA;QAAKgF,SAAS,EAAC;MAAS;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC/BrF,OAAA;QAAAiF,QAAA,EAAG;MAAqB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,gBAENrF,OAAA;MAAKgF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC3B7E,UAAU,CAACoF,GAAG,CAAEC,QAAQ,IAAK;QAC1B,MAAMxB,IAAI,GAAGF,eAAe,CAAC0B,QAAQ,CAACC,EAAE,CAAC;QACzC,MAAMC,SAAS,GAAGrE,WAAW,KAAKmE,QAAQ,CAACC,EAAE;QAE7C,oBACI1F,OAAA;UAAuBgF,SAAS,EAAE,iBAAiB,CAACS,QAAQ,CAAC1D,SAAS,GAAG,UAAU,GAAG,EAAE,EAAG;UAAAkD,QAAA,gBACvFjF,OAAA;YAAKgF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC5BjF,OAAA;cAAKgF,SAAS,EAAC,eAAe;cAAAC,QAAA,gBAC1BjF,OAAA;gBAAAiF,QAAA,EAAKQ,QAAQ,CAAC7D;cAAa;gBAAAsD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACjCrF,OAAA;gBAAAiF,QAAA,EAAIQ,QAAQ,CAAC5D;cAAa;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/BrF,OAAA;gBAAMgF,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAEQ,QAAQ,CAAC3D;cAAe;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClE,CAAC,eACNrF,OAAA;cAAKgF,SAAS,EAAC,iBAAiB;cAAAC,QAAA,eAC5BjF,OAAA;gBAAMgF,SAAS,EAAE,gBAAgBS,QAAQ,CAAC1D,SAAS,GAAG,QAAQ,GAAG,UAAU,EAAG;gBAAAkD,QAAA,EACzEQ,QAAQ,CAAC1D,SAAS,GAAG,QAAQ,GAAG;cAAU;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eAENrF,OAAA;YAAKgF,SAAS,EAAC,uBAAuB;YAAAC,QAAA,gBAClCjF,OAAA;cAAAiF,QAAA,EAAI;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,EACrBpB,IAAI,gBACDjE,OAAA;cAAKgF,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtBjF,OAAA;gBAAKgF,SAAS,EAAC,cAAc;gBAAAC,QAAA,gBACzBjF,OAAA;kBAAMgF,SAAS,EAAC,YAAY;kBAAAC,QAAA,GAAC,SAClB,EAACQ,QAAQ,CAAC3D,eAAe,EAAEmC,IAAI,CAACR,eAAe;gBAAA;kBAAAyB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD,CAAC,eACPrF,OAAA;kBAAAiF,QAAA,GAAO,WAAS,EAACf,UAAU,CAACD,IAAI,CAAC2B,UAAU,CAAC;gBAAA;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpD,CAAC,EAELM,SAAS,gBACN3F,OAAA;gBAAKgF,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC3BjF,OAAA;kBACI6F,IAAI,EAAC,QAAQ;kBACbC,IAAI,EAAC,QAAQ;kBACbjB,KAAK,EAAE7C,OAAQ;kBACf+D,QAAQ,EAAGpB,CAAC,IAAK1C,UAAU,CAAC0C,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;kBAC5CmB,WAAW,EAAC,UAAU;kBACtBhB,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACFrF,OAAA;kBACI6E,KAAK,EAAE3C,KAAM;kBACb6D,QAAQ,EAAGpB,CAAC,IAAKxC,QAAQ,CAACwC,CAAC,CAACG,MAAM,CAACD,KAAK,CAAE;kBAC1CmB,WAAW,EAAC,yBAAyB;kBACrChB,SAAS,EAAC,aAAa;kBACvBiB,IAAI,EAAC;gBAAG;kBAAAf,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACX,CAAC,eACFrF,OAAA;kBAAKgF,SAAS,EAAC,cAAc;kBAAAC,QAAA,gBACzBjF,OAAA;oBACIsF,OAAO,EAAEA,CAAA,KAAMnC,gBAAgB,CAACsC,QAAQ,CAACC,EAAE,CAAE;oBAC7CV,SAAS,EAAC,wBAAwB;oBAClCkB,QAAQ,EAAE1F,OAAQ;oBAAAyE,QAAA,EACrB;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACTrF,OAAA;oBACIsF,OAAO,EAAEA,CAAA,KAAM;sBACX/D,cAAc,CAAC,IAAI,CAAC;sBACpBU,UAAU,CAAC,EAAE,CAAC;sBACdE,QAAQ,CAAC,EAAE,CAAC;oBAChB,CAAE;oBACF6C,SAAS,EAAC,0BAA0B;oBAAAC,QAAA,EACvC;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,gBAENrF,OAAA;gBACIsF,OAAO,EAAEA,CAAA,KAAM;kBACX/D,cAAc,CAACkE,QAAQ,CAACC,EAAE,CAAC;kBAC3BzD,UAAU,CAACgC,IAAI,CAACR,eAAe,CAAC0C,QAAQ,CAAC,CAAC,CAAC;gBAC/C,CAAE;gBACFnB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACX;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,gBAENrF,OAAA;cAAKgF,SAAS,EAAC,SAAS;cAAAC,QAAA,gBACpBjF,OAAA;gBAAAiF,QAAA,EAAG;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAC3BrF,OAAA;gBACIsF,OAAO,EAAEA,CAAA,KAAM;kBACX/D,cAAc,CAACkE,QAAQ,CAACC,EAAE,CAAC;kBAC3BzD,UAAU,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACF+C,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACrC;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CACR;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eAENrF,OAAA;YAAKgF,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC7BjF,OAAA;cACIsF,OAAO,EAAEA,CAAA,KAAM3B,oBAAoB,CAAC8B,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC1D,SAAS,CAAE;cACrEiD,SAAS,EAAE,cAAcS,QAAQ,CAAC1D,SAAS,GAAG,aAAa,GAAG,aAAa,EAAG;cAC9EmE,QAAQ,EAAE1F,OAAQ;cAAAyE,QAAA,EAEjBQ,QAAQ,CAAC1D,SAAS,GAAG,YAAY,GAAG;YAAU;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA,GAlGAI,QAAQ,CAACC,EAAE;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmGhB,CAAC;MAEd,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,EAGAjE,eAAe,iBACZpB,OAAA;MAAKgF,SAAS,EAAC,eAAe;MAACM,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,KAAK,CAAE;MAAA4D,QAAA,eACpEjF,OAAA;QAAKgF,SAAS,EAAC,eAAe;QAACM,OAAO,EAAGX,CAAC,IAAKA,CAAC,CAACyB,eAAe,CAAC,CAAE;QAAAnB,QAAA,gBAC/DjF,OAAA;UAAAiF,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAA8B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC7CrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACE,aAAc;YACjCmE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEE,aAAa,EAAE+C,CAAC,CAACG,MAAM,CAACD,KAAK,CAACwB,WAAW,CAAC;YAAC,CAAC,CAAE;YAC/FL,WAAW,EAAC,KAAK;YACjBM,SAAS,EAAC;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5BrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACG,aAAc;YACjCkE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEG,aAAa,EAAE8C,CAAC,CAACG,MAAM,CAACD;YAAK,CAAC,CAAE;YACjFmB,WAAW,EAAC;UAAW;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACvBjF,OAAA;YAAAiF,QAAA,EAAO;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC9BrF,OAAA;YACI6F,IAAI,EAAC,MAAM;YACXhB,KAAK,EAAEnD,WAAW,CAACI,eAAgB;YACnCiE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;cAAC,GAAGD,WAAW;cAAEI,eAAe,EAAE6C,CAAC,CAACG,MAAM,CAACD;YAAK,CAAC,CAAE;YACnFmB,WAAW,EAAC,GAAG;YACfM,SAAS,EAAC;UAAG;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,YAAY;UAAAC,QAAA,eACvBjF,OAAA;YAAAiF,QAAA,gBACIjF,OAAA;cACI6F,IAAI,EAAC,UAAU;cACfU,OAAO,EAAE7E,WAAW,CAACK,SAAU;cAC/BgE,QAAQ,EAAGpB,CAAC,IAAKhD,cAAc,CAAC;gBAAC,GAAGD,WAAW;gBAAEK,SAAS,EAAE4C,CAAC,CAACG,MAAM,CAACyB;cAAO,CAAC;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClF,CAAC,UAEN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNrF,OAAA;UAAKgF,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC1BjF,OAAA;YACIsF,OAAO,EAAExB,iBAAkB;YAC3BkB,SAAS,EAAC,iBAAiB;YAC3BkB,QAAQ,EAAE1F,OAAQ;YAAAyE,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTrF,OAAA;YACIsF,OAAO,EAAEA,CAAA,KAAMjE,kBAAkB,CAAC,KAAK,CAAE;YACzC2D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAChC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACR;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEd;AAAClF,EAAA,CA1YQD,kBAAkB;AAAAsG,EAAA,GAAlBtG,kBAAkB;AA0Y1B;AAED,eAAeA,kBAAkB;AAAC,IAAAsG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}