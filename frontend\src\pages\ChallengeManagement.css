.challenge-management {
    padding: 20px;
    width: 100%;
    margin: 0 auto;
}

.challenge-management h1 {
    color: #333;
    margin-bottom: 20px;
    font-size: 24px;
}

.table-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    border: 1px solid #e0e0e0;
    margin-bottom: 0;
}

.challenge-management-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.challenge-management-table th {
    background: #166534;
    padding: 12px;
    font-weight: 600;
    color: white;
    text-align: left;
    border-bottom: 2px solid #0d4a26;
    text-transform: uppercase;
    font-size: 13px;
    letter-spacing: 0.5px;
}

.challenge-management-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: middle;
}

/* Column widths */
.index-column {
    width: 50px;
    text-align: center;
    font-weight: 600;
    color: #166534;
    background-color: #f8f9fa;
}

/* Matchup styling */
.matchup-grid {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 10px;
    align-items: center;
    width: 100%;
}

.team-block {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.team-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
    border-radius: 50%;
    background: #f8f9fa;
    padding: 2px;
}

.team-name {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    text-align: center;
    font-style: normal;
    text-transform: uppercase;
}

.team-odds {
    font-size: 13px;
    color: #666;
    background: #f8f9fa;
    padding: 2px 8px;
    border-radius: 12px;
}

.vs-center {
    color: #999;
    font-size: 14px;
    font-weight: 500;
}

/* Time and Date styling */
.time-date-cell {
    width: 180px;
}

.time-date-stack {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.match-time {
    font-weight: 500;
    color: #333;
}

.end-time {
    color: #666;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 4px;
}

/* Status styling */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    display: inline-block;
    text-align: center;
    min-width: 100px;
    text-transform: capitalize;
}

.status-badge.Open {
    background-color: #e3f2fd;
    color: #1976d2;
}

.status-badge.Closed {
    background-color: #ffebee;
    color: #d32f2f;
}

.status-badge.Completed,
.status-badge.Settled {
    background-color: #e8f5e9;
    color: #2e7d32;
}

.status-badge.Expired {
    background-color: #fafafa;
    color: #616161;
}

/* Action buttons */
.action-buttons {
    display: flex;
    flex-direction: row;
    gap: 4px;
    justify-content: center;
}

.icon-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    padding: 0;
}

.icon-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.icon-btn:active {
    transform: translateY(0);
}

.btn-preview {
    background: #166534;
}

.btn-edit {
    background: #15803d;
}

.btn-delete {
    background: #b91c1c;
}

/* Common Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.close-button {
    position: absolute !important;
    top: 20px !important;
    right: 20px !important;
    background: none !important;
    border: none !important;
    font-size: 28px !important;
    color: #666 !important;
    cursor: pointer !important;
    padding: 8px 12px !important;
    border-radius: 4px !important;
    transition: all 0.2s !important;
    z-index: 1001 !important;
}

.close-button:hover {
    background: #f5f5f5 !important;
    color: #333 !important;
}

/* Preview Modal Specific Styles */
.preview-modal {
    background: white;
    border-radius: 12px;
    padding: 24px;
    width: 90%;
    max-width: 600px;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.preview-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 2px solid #f8f9fa;
}

.preview-header h2 {
    color: #166534;
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Edit Modal Specific Styles */
.edit-modal {
    background: white;
    border-radius: 12px;
    padding: 32px;
    width: 98%;
    position: relative;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-height: fit-content;
}

.edit-modal h2 {
    color: #333;
    margin: 0 0 32px;
    text-align: center;
    font-size: 28px;
    padding-right: 40px;
}

.edit-modal .form-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
    width: 100%;
}

.form-group {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #495057;
    font-weight: 500;
    font-size: 15px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 15px;
    color: #495057;
    transition: all 0.2s;
    background: #fff;
}

.form-group input:focus,
.form-group select:focus {
    border-color: #2196f3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
    outline: none;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid #dee2e6;
}

.form-actions button {
    padding: 12px 32px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.form-actions button.save-button {
    background: #4caf50;
    color: white;
}

.form-actions button.cancel-button {
    background: #f44336;
    color: white;
}

.form-actions button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.form-actions button:active {
    transform: translateY(0);
}

/* Match Preview Display */
.match-preview-display {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    width: 100%;
    border: 1px solid #e9ecef;
}

.match-team-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 12px;
}

.team-logo-large {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 50%;
    background: white;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid #e9ecef;
}

.match-team-preview .team-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.match-team-preview .team-odds {
    font-size: 13px;
    color: #666;
    background: white;
    padding: 4px 10px;
    border-radius: 12px;
    margin: 0;
    font-weight: 500;
    border: 1px solid #e9ecef;
}

.match-vs-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.vs-text {
    font-size: 20px;
    font-weight: 700;
    color: #166534;
    background: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid #166534;
}

.match-odds {
    text-align: center;
    margin-top: 8px;
}

.draw-odds, .lost-odds {
    margin: 2px 0;
    font-size: 12px;
    color: #666;
    background: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-weight: 500;
    border: 1px solid #e9ecef;
}

/* Match Details */
.match-details {
    background: white;
    border-radius: 12px;
    padding: 16px;
    width: 100%;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.detail-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 12px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item .label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

.detail-item .value {
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

/* Match Time Display */
.match-time-display {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: center;
}

.match-time {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    white-space: nowrap;
    background: #f5f7fa;
    padding: 6px 12px;
    border-radius: 4px;
}

/* Countdown Timer */
.countdown-container {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.countdown-time {
    font-family: 'Roboto Mono', monospace;
    font-size: 14px;
    font-weight: 500;
    color: #1976d2;
    background: #e3f2fd;
    padding: 4px 8px;
    border-radius: 4px;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.countdown-expired {
    font-size: 14px;
    font-weight: 500;
    color: #616161;
    background: #fafafa;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
}

/* Table Styles */
.challenge-table td {
    padding: 16px;
    vertical-align: middle;
    border-bottom: 1px solid #eee;
}

.challenge-table .time-cell {
    min-width: 280px;
    text-align: center;
    white-space: nowrap;
}

.challenge-table .status-cell {
    text-align: center;
    min-width: 120px;
}

/* Match Type Section */
.match-type-section {
    text-align: center;
    margin-bottom: 0;
}

.match-type-badge {
    background: linear-gradient(135deg, #166534 0%, #15803d 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(22, 101, 52, 0.2);
}

/* Messages */
.error-message {
    background: #ffe8e8;
    color: #d32f2f;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.success-message {
    background: #e7f5e7;
    color: #1e7e1e;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    margin-top: 0;
    width: 100%;
    padding: 12px 0;
    background-color: #3b82f6;
    border-radius: 0 0 8px 8px;
    border: 1px solid #e0e0e0;
    border-top: none;
}

.pagination-btn {
    width: 36px;
    height: 36px;
    background-color: #ffffff;
    color: #3b82f6;
    border: 1px solid #3b82f6;
}

.pagination-btn:hover {
    background-color: #1d4ed8;
    color: #ffffff;
}

.pagination-btn:disabled {
    background-color: #e5e7eb;
    color: #9ca3af;
    border-color: #e5e7eb;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.page-info {
    font-size: 14px;
    color: #ffffff;
    font-weight: 500;
}

/* Responsive styles */
@media (max-width: 1200px) {
    .challenge-management {
        padding: 15px;
    }

    .challenge-management-table {
        font-size: 0.95rem;
    }

    .action-buttons {
        width: auto;
    }
}

@media (max-width: 992px) {
    .challenge-management-table {
        font-size: 0.9rem;
    }

    .team-logo {
        width: 35px;
        height: 35px;
    }

    .team-name {
        font-size: 13px;
    }

    .team-odds {
        font-size: 12px;
        padding: 2px 6px;
    }

    .action-buttons {
        gap: 3px;
    }

    .icon-btn {
        width: 30px;
        height: 30px;
        font-size: 13px;
    }
}

@media (max-width: 768px) {
    .challenge-management {
        padding: 10px;
    }

    .challenge-management-table {
        font-size: 0.85rem;
    }

    .team-logo {
        width: 30px;
        height: 30px;
    }

    .team-name {
        font-size: 12px;
    }

    .team-odds {
        font-size: 11px;
        padding: 2px 6px;
    }

    .match-time-display {
        font-size: 12px;
    }

    .countdown-time {
        font-size: 12px;
    }

    .edit-modal, .preview-modal {
        padding: 20px;
        width: 95%;
        max-width: none;
    }

    .preview-header h2 {
        font-size: 20px;
    }

    .team-logo-large {
        width: 50px;
        height: 50px;
    }

    .match-team-preview .team-name {
        font-size: 14px;
    }

    .icon-btn {
        width: 26px;
        height: 26px;
        font-size: 11px;
    }

    .action-buttons {
        gap: 2px;
    }

    .pagination-btn {
        width: 36px;
        height: 36px;
    }

    .edit-modal .form-content {
        grid-template-columns: 1fr;
    }

    .detail-row {
        grid-template-columns: 1fr;
    }
}