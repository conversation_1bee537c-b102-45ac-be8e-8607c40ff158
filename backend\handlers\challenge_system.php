<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

include_once '../includes/db_connect.php';

try {
    $conn = getDBConnection();
    
    // For GET requests - fetch active challenges
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $stmt = $conn->query("
            SELECT 
                c.*,
                t1.logo as team_a_logo,
                t2.logo as team_b_logo,
                COUNT(b.bet_id) as total_bets,
                TIMESTAMPDIFF(SECOND, NOW(), c.end_time) as seconds_remaining
            FROM challenges c
            LEFT JOIN teams t1 ON c.team_a = t1.name
            LEFT JOIN teams t2 ON c.team_b = t2.name
            LEFT JOIN bets b ON c.challenge_id = b.challenge_id
            WHERE c.status = 'Open' 
            AND NOW() < c.end_time
            GROUP BY c.challenge_id
            ORDER BY c.challenge_date DESC"
        );
        
        $challenges = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['success' => true, 'challenges' => $challenges]);
    }
    
    // For POST requests - create new challenge
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $data = json_decode(file_get_contents("php://input"), true);
        
        // Validate times
        $now = new DateTime();
        $startTime = new DateTime($data['startTime']);
        $endTime = new DateTime($data['endTime']);
        $matchTime = new DateTime($data['matchTime']);
        
        if ($endTime > $matchTime) {
            throw new Exception("Challenge end time must be before match time");
        }
        
        $stmt = $conn->prepare("
            INSERT INTO challenges (
                team_a, team_b, 
                odds_team_a, odds_team_b,
                team_a_goal_advantage, team_b_goal_advantage,
                start_time, end_time, match_date,
                status, challenge_date
            ) VALUES (
                :team_a, :team_b,
                :odds_team_a, :odds_team_b,
                :goal_adv_a, :goal_adv_b,
                :start_time, :end_time, :match_time,
                'Open', NOW()
            )
        ");
        
        $stmt->execute([
            ':team_a' => $data['team1'],
            ':team_b' => $data['team2'],
            ':odds_team_a' => $data['odds1'],
            ':odds_team_b' => $data['odds2'],
            ':goal_adv_a' => $data['goalAdvantage1'],
            ':goal_adv_b' => $data['goalAdvantage2'],
            ':start_time' => $data['startTime'],
            ':end_time' => $data['endTime'],
            ':match_time' => $data['matchTime']
        ]);
        
        echo json_encode(['success' => true, 'message' => 'Challenge created successfully']);
    }
    
} catch(Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
