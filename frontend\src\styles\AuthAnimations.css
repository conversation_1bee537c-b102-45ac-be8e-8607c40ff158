/* 
 * Authentication Micro-Interactions and Animations
 * Enhanced user experience through subtle animations
 */

/* Page Entry Animations */
.user-auth-page {
    animation: fadeInScale 0.6s ease-out;
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Form Entry Animation */
.user-auth-form {
    animation: slideInUp 0.5s ease-out 0.2s both;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Input Focus Animations */
.user-auth-input-wrapper input:focus {
    animation: inputFocus 0.3s ease-out;
}

@keyframes inputFocus {
    0% {
        transform: translateY(0);
        box-shadow: 0 0 0 0 rgba(44, 95, 45, 0);
    }
    50% {
        transform: translateY(-1px);
    }
    100% {
        transform: translateY(-1px);
        box-shadow: 0 0 0 4px rgba(44, 95, 45, 0.1);
    }
}

/* <PERSON><PERSON> Hover Animations */
.user-auth-button:hover {
    animation: buttonHover 0.3s ease-out;
}

@keyframes buttonHover {
    0% {
        transform: translateY(0);
        box-shadow: var(--shadow-sm);
    }
    100% {
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }
}

/* Button Click Animation */
.user-auth-button:active {
    animation: buttonClick 0.15s ease-out;
}

@keyframes buttonClick {
    0% {
        transform: translateY(-2px);
    }
    50% {
        transform: translateY(1px);
    }
    100% {
        transform: translateY(0);
    }
}

/* Loading Button Animation */
.user-auth-button:disabled {
    animation: loadingPulse 2s ease-in-out infinite;
}

@keyframes loadingPulse {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 0.9;
    }
}

/* Success Animation */
.user-auth-form-group.success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.02);
    }
    100% {
        transform: scale(1);
    }
}

/* Error Shake Animation */
.user-auth-form-group.error {
    animation: errorShake 0.5s ease-out;
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Message Slide In Animation */
.user-auth-error-message,
.user-auth-success-message {
    animation: messageSlideIn 0.4s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(-15px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 100px;
    }
}

/* OTP Input Animation */
.user-auth-otp-input {
    animation: otpFocus 0.3s ease-out;
}

@keyframes otpFocus {
    0% {
        letter-spacing: 0.2rem;
    }
    100% {
        letter-spacing: 0.5rem;
    }
}

/* Countdown Timer Animation */
.user-auth-countdown-timer {
    animation: timerPulse 1s ease-in-out infinite;
}

@keyframes timerPulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.user-auth-countdown-timer.expired {
    animation: expiredBlink 0.5s ease-in-out infinite;
}

@keyframes expiredBlink {
    0%, 100% {
        background-color: #FEF2F2;
    }
    50% {
        background-color: #FECACA;
    }
}

/* Link Hover Animation */
.user-auth-link,
.user-auth-forgot-password,
.user-auth-register-link {
    position: relative;
    overflow: hidden;
}

.user-auth-link::before,
.user-auth-forgot-password::before,
.user-auth-register-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
    transition: var(--transition-normal);
}

.user-auth-link:hover::before,
.user-auth-forgot-password:hover::before,
.user-auth-register-link:hover::before {
    left: 0;
}

/* Feature Card Hover Animation */
.user-auth-feature {
    transition: var(--transition-normal);
}

.user-auth-feature:hover {
    animation: featureHover 0.3s ease-out;
}

@keyframes featureHover {
    0% {
        transform: translateY(0);
        background: rgba(255, 255, 255, 0.1);
    }
    100% {
        transform: translateY(-2px);
        background: rgba(255, 255, 255, 0.15);
    }
}

/* Logo Hover Animation */
.user-auth-logo:hover {
    animation: logoHover 0.3s ease-out;
}

@keyframes logoHover {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1.02);
    }
}

/* Form Group Stagger Animation */
.user-auth-form-group:nth-child(1) { animation-delay: 0.1s; }
.user-auth-form-group:nth-child(2) { animation-delay: 0.2s; }
.user-auth-form-group:nth-child(3) { animation-delay: 0.3s; }
.user-auth-form-group:nth-child(4) { animation-delay: 0.4s; }
.user-auth-form-group:nth-child(5) { animation-delay: 0.5s; }

.user-auth-form-group {
    animation: formGroupSlideIn 0.4s ease-out both;
}

@keyframes formGroupSlideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Right Panel Content Animation */
.user-auth-right-content > * {
    animation: rightPanelSlideIn 0.6s ease-out both;
}

.user-auth-brand-section { animation-delay: 0.2s; }
.user-auth-features { animation-delay: 0.4s; }
.user-auth-trust-indicators { animation-delay: 0.6s; }

@keyframes rightPanelSlideIn {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Trust Indicator Hover */
.user-auth-trust-item {
    transition: var(--transition-normal);
}

.user-auth-trust-item:hover {
    animation: trustHover 0.3s ease-out;
}

@keyframes trustHover {
    0% {
        transform: translateY(0);
        opacity: 0.8;
    }
    100% {
        transform: translateY(-2px);
        opacity: 1;
    }
}

/* Spinner Animation Enhancement */
.user-auth-loading-spinner {
    animation: enhancedSpin 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

@keyframes enhancedSpin {
    0% {
        transform: rotate(0deg);
        opacity: 0.8;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: rotate(360deg);
        opacity: 0.8;
    }
}

/* Resend Button Animation */
.user-auth-resend-button:hover {
    animation: resendHover 0.3s ease-out;
}

@keyframes resendHover {
    0% {
        background: none;
        transform: scale(1);
    }
    100% {
        background: rgba(44, 95, 45, 0.05);
        transform: scale(1.02);
    }
}

/* Mobile Specific Animations */
@media screen and (max-width: 768px) {
    .user-auth-page {
        animation: mobileSlideUp 0.5s ease-out;
    }
    
    @keyframes mobileSlideUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Reduce motion for mobile performance */
    .user-auth-feature:hover {
        animation: none;
        transform: none;
    }
    
    .user-auth-trust-item:hover {
        animation: none;
        transform: none;
    }
}

/* Reduced Motion Preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .user-auth-page,
    .user-auth-form,
    .user-auth-form-group,
    .user-auth-right-content > * {
        animation: none;
    }
}
