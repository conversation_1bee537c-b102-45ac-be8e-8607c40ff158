/**
 * Currency Management Admin Page
 * 
 * Provides admin interface for managing currencies and exchange rates
 */

import React, { useState, useEffect, useCallback } from 'react';
import { currencyService } from '../services';
import useApiService from '../hooks/useApiService';
import { useCurrency } from '../contexts/CurrencyContext';
import './CurrencyManagement.css';

const CurrencyManagement = () => {
    const [currencies, setCurrencies] = useState([]);
    const [exchangeRates, setExchangeRates] = useState([]);
    const [editingRate, setEditingRate] = useState(null);
    const [newRate, setNewRate] = useState('');
    const [notes, setNotes] = useState('');
    const [showAddCurrency, setShowAddCurrency] = useState(false);
    const [newCurrency, setNewCurrency] = useState({
        currency_code: '',
        currency_name: '',
        currency_symbol: '',
        is_active: true
    });

    const { loading, error, execute } = useApiService({
        onError: (error) => {
            console.error('Currency Management Error:', error);
        }
    });

    const { refreshCurrencyData } = useCurrency();

    // Load currencies and exchange rates
    const loadData = useCallback(async () => {
        try {
            const [currenciesResponse, ratesResponse] = await Promise.all([
                execute(() => currencyService.getCurrencies(false)), // Get all currencies including inactive
                execute(() => currencyService.getExchangeRates())
            ]);

            if (currenciesResponse.success) {
                setCurrencies(currenciesResponse.data.currencies || []);
            }

            if (ratesResponse.success) {
                setExchangeRates(ratesResponse.data.exchange_rates || []);
            }
        } catch (error) {
            console.error('Failed to load currency data:', error);
        }
    }, [execute]);

    // Update exchange rate
    const handleUpdateRate = useCallback(async (currencyId) => {
        if (!newRate || isNaN(parseFloat(newRate))) {
            alert('Please enter a valid exchange rate');
            return;
        }

        try {
            const response = await execute(() => 
                currencyService.updateExchangeRate(currencyId, parseFloat(newRate), notes)
            );

            if (response.success) {
                await loadData();
                await refreshCurrencyData();
                setEditingRate(null);
                setNewRate('');
                setNotes('');
                alert('Exchange rate updated successfully!');
            } else {
                alert(response.message || 'Failed to update exchange rate');
            }
        } catch (error) {
            alert('Error updating exchange rate: ' + error.message);
        }
    }, [newRate, notes, execute, loadData, refreshCurrencyData]);

    // Toggle currency active status
    const handleToggleCurrency = useCallback(async (currencyId, currentStatus) => {
        try {
            const response = await execute(() => 
                currencyService.manageCurrencies('toggle', { currency_id: currencyId })
            );

            if (response.success) {
                await loadData();
                await refreshCurrencyData();
                alert(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);
            } else {
                alert(response.message || 'Failed to toggle currency status');
            }
        } catch (error) {
            alert('Error toggling currency: ' + error.message);
        }
    }, [execute, loadData, refreshCurrencyData]);

    // Add new currency
    const handleAddCurrency = useCallback(async () => {
        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {
            alert('Please fill in all required fields');
            return;
        }

        try {
            const response = await execute(() => 
                currencyService.manageCurrencies('create', newCurrency)
            );

            if (response.success) {
                await loadData();
                await refreshCurrencyData();
                setShowAddCurrency(false);
                setNewCurrency({
                    currency_code: '',
                    currency_name: '',
                    currency_symbol: '',
                    is_active: true
                });
                alert('Currency added successfully!');
            } else {
                alert(response.message || 'Failed to add currency');
            }
        } catch (error) {
            alert('Error adding currency: ' + error.message);
        }
    }, [newCurrency, execute, loadData, refreshCurrencyData]);

    // Get exchange rate for currency
    const getExchangeRate = useCallback((currencyId) => {
        return exchangeRates.find(rate => rate.currency_id === currencyId);
    }, [exchangeRates]);

    // Format date
    const formatDate = useCallback((dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }, []);

    // Load data on component mount
    useEffect(() => {
        loadData();
    }, [loadData]);

    return (
        <div className="currency-management">
            <div className="page-header">
                <h1>Currency Management</h1>
                <p>Manage supported currencies and exchange rates</p>
                <button 
                    className="btn btn-primary"
                    onClick={() => setShowAddCurrency(true)}
                >
                    Add New Currency
                </button>
            </div>

            {error && (
                <div className="error-message">
                    <p>⚠️ {error.message || 'An error occurred'}</p>
                    <button onClick={loadData} className="btn btn-secondary">
                        Retry
                    </button>
                </div>
            )}

            {loading && currencies.length === 0 ? (
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading currencies...</p>
                </div>
            ) : (
                <div className="currencies-grid">
                    {currencies.map((currency) => {
                        const rate = getExchangeRate(currency.id);
                        const isEditing = editingRate === currency.id;

                        return (
                            <div key={currency.id} className={`currency-card ${!currency.is_active ? 'inactive' : ''}`}>
                                <div className="currency-header">
                                    <div className="currency-info">
                                        <h3>{currency.currency_code}</h3>
                                        <p>{currency.currency_name}</p>
                                        <span className="currency-symbol">{currency.currency_symbol}</span>
                                    </div>
                                    <div className="currency-status">
                                        <span className={`status-badge ${currency.is_active ? 'active' : 'inactive'}`}>
                                            {currency.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                </div>

                                <div className="exchange-rate-section">
                                    <h4>Exchange Rate</h4>
                                    {rate ? (
                                        <div className="rate-info">
                                            <div className="current-rate">
                                                <span className="rate-value">
                                                    1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}
                                                </span>
                                                <small>Updated: {formatDate(rate.updated_at)}</small>
                                            </div>

                                            {isEditing ? (
                                                <div className="rate-edit-form">
                                                    <input
                                                        type="number"
                                                        step="0.0001"
                                                        value={newRate}
                                                        onChange={(e) => setNewRate(e.target.value)}
                                                        placeholder="New rate"
                                                        className="rate-input"
                                                    />
                                                    <textarea
                                                        value={notes}
                                                        onChange={(e) => setNotes(e.target.value)}
                                                        placeholder="Update notes (optional)"
                                                        className="notes-input"
                                                        rows="2"
                                                    />
                                                    <div className="edit-actions">
                                                        <button 
                                                            onClick={() => handleUpdateRate(currency.id)}
                                                            className="btn btn-success btn-sm"
                                                            disabled={loading}
                                                        >
                                                            Save
                                                        </button>
                                                        <button 
                                                            onClick={() => {
                                                                setEditingRate(null);
                                                                setNewRate('');
                                                                setNotes('');
                                                            }}
                                                            className="btn btn-secondary btn-sm"
                                                        >
                                                            Cancel
                                                        </button>
                                                    </div>
                                                </div>
                                            ) : (
                                                <button 
                                                    onClick={() => {
                                                        setEditingRate(currency.id);
                                                        setNewRate(rate.rate_to_fancoin.toString());
                                                    }}
                                                    className="btn btn-outline btn-sm"
                                                >
                                                    Update Rate
                                                </button>
                                            )}
                                        </div>
                                    ) : (
                                        <div className="no-rate">
                                            <p>No exchange rate set</p>
                                            <button 
                                                onClick={() => {
                                                    setEditingRate(currency.id);
                                                    setNewRate('');
                                                }}
                                                className="btn btn-primary btn-sm"
                                            >
                                                Set Rate
                                            </button>
                                        </div>
                                    )}
                                </div>

                                <div className="currency-actions">
                                    <button 
                                        onClick={() => handleToggleCurrency(currency.id, currency.is_active)}
                                        className={`btn btn-sm ${currency.is_active ? 'btn-warning' : 'btn-success'}`}
                                        disabled={loading}
                                    >
                                        {currency.is_active ? 'Deactivate' : 'Activate'}
                                    </button>
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}

            {/* Add Currency Modal */}
            {showAddCurrency && (
                <div className="modal-overlay" onClick={() => setShowAddCurrency(false)}>
                    <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                        <h3>Add New Currency</h3>
                        <div className="form-group">
                            <label>Currency Code (e.g., EUR, GBP)</label>
                            <input
                                type="text"
                                value={newCurrency.currency_code}
                                onChange={(e) => setNewCurrency({...newCurrency, currency_code: e.target.value.toUpperCase()})}
                                placeholder="USD"
                                maxLength="3"
                            />
                        </div>
                        <div className="form-group">
                            <label>Currency Name</label>
                            <input
                                type="text"
                                value={newCurrency.currency_name}
                                onChange={(e) => setNewCurrency({...newCurrency, currency_name: e.target.value})}
                                placeholder="US Dollar"
                            />
                        </div>
                        <div className="form-group">
                            <label>Currency Symbol</label>
                            <input
                                type="text"
                                value={newCurrency.currency_symbol}
                                onChange={(e) => setNewCurrency({...newCurrency, currency_symbol: e.target.value})}
                                placeholder="$"
                                maxLength="5"
                            />
                        </div>
                        <div className="form-group">
                            <label>
                                <input
                                    type="checkbox"
                                    checked={newCurrency.is_active}
                                    onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}
                                />
                                Active
                            </label>
                        </div>
                        <div className="modal-actions">
                            <button 
                                onClick={handleAddCurrency}
                                className="btn btn-primary"
                                disabled={loading}
                            >
                                Add Currency
                            </button>
                            <button 
                                onClick={() => setShowAddCurrency(false)}
                                className="btn btn-secondary"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default CurrencyManagement;
