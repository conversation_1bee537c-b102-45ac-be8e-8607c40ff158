/**
 * Currency Management Admin Page
 *
 * Provides admin interface for managing currencies and exchange rates
 * Follows standard admin page patterns used throughout the application
 */

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { FaCoins, FaEdit, FaToggleOn, FaToggleOff, FaPlus, FaSave, FaTimes, FaExchangeAlt } from 'react-icons/fa';

const API_BASE_URL = '/backend';

function CurrencyManagement() {
    const [currencies, setCurrencies] = useState([]);
    const [exchangeRates, setExchangeRates] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');

    // Stats
    const [stats, setStats] = useState({
        totalCurrencies: 0,
        activeCurrencies: 0,
        inactiveCurrencies: 0,
        lastUpdated: null
    });

    // Modal states
    const [showAddCurrency, setShowAddCurrency] = useState(false);
    const [editingRate, setEditingRate] = useState(null);
    const [editingCurrency, setEditingCurrency] = useState(null);

    // Form states
    const [newCurrency, setNewCurrency] = useState({
        currency_code: '',
        currency_name: '',
        currency_symbol: '',
        is_active: true
    });
    const [newRate, setNewRate] = useState('');
    const [notes, setNotes] = useState('');

    useEffect(() => {
        loadCurrencies();
        loadExchangeRates();
    }, []);

    const loadCurrencies = async () => {
        setLoading(true);
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/get_currencies.php`);
            if (response.data.success) {
                const currencyData = response.data.data.currencies || [];
                setCurrencies(currencyData);

                // Calculate stats
                const totalCurrencies = currencyData.length;
                const activeCurrencies = currencyData.filter(c => c.is_active).length;
                const inactiveCurrencies = totalCurrencies - activeCurrencies;

                setStats({
                    totalCurrencies,
                    activeCurrencies,
                    inactiveCurrencies,
                    lastUpdated: new Date().toISOString()
                });
            } else {
                setError(response.data.message || 'Failed to load currencies');
            }
        } catch (err) {
            console.error('Error loading currencies:', err);
            setError('Failed to load currencies. Please check your network connection.');
        } finally {
            setLoading(false);
        }
    };

    const loadExchangeRates = async () => {
        try {
            const response = await axios.get(`${API_BASE_URL}/handlers/get_exchange_rates.php`);
            if (response.data.success) {
                setExchangeRates(response.data.data.exchange_rates || []);
            }
        } catch (err) {
            console.error('Error loading exchange rates:', err);
        }
    };

    const handleUpdateRate = async (currencyId) => {
        if (!newRate || isNaN(parseFloat(newRate))) {
            setError('Please enter a valid exchange rate');
            return;
        }

        setLoading(true);
        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/update_exchange_rate.php`, {
                currency_id: currencyId,
                rate_to_fancoin: parseFloat(newRate),
                notes: notes
            });

            if (response.data.success) {
                setSuccess('Exchange rate updated successfully!');
                loadExchangeRates();
                setEditingRate(null);
                setNewRate('');
                setNotes('');
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to update exchange rate');
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            console.error('Error updating exchange rate:', err);
            setError('Failed to update exchange rate. Please try again.');
            setTimeout(() => setError(''), 3000);
        } finally {
            setLoading(false);
        }
    };

    const handleToggleCurrency = async (currencyId, currentStatus) => {
        setLoading(true);
        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {
                action: 'toggle',
                currency_id: currencyId
            });

            if (response.data.success) {
                setSuccess(`Currency ${currentStatus ? 'deactivated' : 'activated'} successfully!`);
                loadCurrencies();
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to toggle currency status');
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            console.error('Error toggling currency:', err);
            setError('Failed to toggle currency status. Please try again.');
            setTimeout(() => setError(''), 3000);
        } finally {
            setLoading(false);
        }
    };

    const handleAddCurrency = async () => {
        if (!newCurrency.currency_code || !newCurrency.currency_name || !newCurrency.currency_symbol) {
            setError('Please fill in all required fields');
            return;
        }

        setLoading(true);
        try {
            const response = await axios.post(`${API_BASE_URL}/handlers/manage_currencies.php`, {
                action: 'create',
                ...newCurrency
            });

            if (response.data.success) {
                setSuccess('Currency added successfully!');
                loadCurrencies();
                setShowAddCurrency(false);
                setNewCurrency({
                    currency_code: '',
                    currency_name: '',
                    currency_symbol: '',
                    is_active: true
                });
                setTimeout(() => setSuccess(''), 3000);
            } else {
                setError(response.data.message || 'Failed to add currency');
                setTimeout(() => setError(''), 3000);
            }
        } catch (err) {
            console.error('Error adding currency:', err);
            setError('Failed to add currency. Please try again.');
            setTimeout(() => setError(''), 3000);
        } finally {
            setLoading(false);
        }
    };

    const getExchangeRate = (currencyId) => {
        return exchangeRates.find(rate => rate.currency_id === currencyId);
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewCurrency(prev => ({ ...prev, [name]: value }));
    };

    return (
        <div className="p-6 bg-gray-50 min-h-screen">
            {/* Page Header */}
            <div className="mb-8">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-800">Currency Management</h1>
                        <p className="text-gray-600">Manage supported currencies and exchange rates</p>
                    </div>
                    <button
                        className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                        onClick={() => setShowAddCurrency(true)}
                        disabled={loading}
                    >
                        <FaPlus />
                        Add New Currency
                    </button>
                </div>
            </div>

            {/* Notification Messages */}
            {error && (
                <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{error}</span>
                </div>
            )}
            {success && (
                <div className="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span className="block sm:inline">{success}</span>
                </div>
            )}

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Total Currencies */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-blue-100 p-3 mr-4">
                        <FaCoins className="text-blue-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Total Currencies</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.totalCurrencies}</h3>
                    </div>
                </div>

                {/* Active Currencies */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-green-100 p-3 mr-4">
                        <FaToggleOn className="text-green-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Active Currencies</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.activeCurrencies}</h3>
                    </div>
                </div>

                {/* Inactive Currencies */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-red-100 p-3 mr-4">
                        <FaToggleOff className="text-red-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Inactive Currencies</p>
                        <h3 className="text-2xl font-bold text-gray-800">{stats.inactiveCurrencies}</h3>
                    </div>
                </div>

                {/* Exchange Rates */}
                <div className="bg-white rounded-lg shadow-sm p-6 flex items-center">
                    <div className="rounded-full bg-yellow-100 p-3 mr-4">
                        <FaExchangeAlt className="text-yellow-500 text-xl" />
                    </div>
                    <div>
                        <p className="text-sm text-gray-500 uppercase tracking-wider">Exchange Rates</p>
                        <h3 className="text-2xl font-bold text-gray-800">{exchangeRates.length}</h3>
                    </div>
                </div>
            </div>

            {/* Currencies Table */}
            <div className="bg-white rounded-lg shadow-sm overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200">
                    <h2 className="text-lg font-semibold text-gray-800">Currencies & Exchange Rates</h2>
                </div>

                {loading && currencies.length === 0 ? (
                    <div className="p-8 text-center">
                        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p className="mt-2 text-gray-600">Loading currencies...</p>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange Rate</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {currencies.map((currency) => {
                                    const rate = getExchangeRate(currency.id);
                                    const isEditingRate = editingRate === currency.id;

                                    return (
                                        <tr key={currency.id} className={`${!currency.is_active ? 'opacity-60' : ''}`}>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="flex items-center">
                                                    <div className="flex-shrink-0 h-10 w-10">
                                                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                                            <span className="text-blue-600 font-semibold">{currency.currency_symbol}</span>
                                                        </div>
                                                    </div>
                                                    <div className="ml-4">
                                                        <div className="text-sm font-medium text-gray-900">{currency.currency_code}</div>
                                                        <div className="text-sm text-gray-500">{currency.currency_name}</div>
                                                    </div>
                                                </div>
                                            </td>

                                            <td className="px-6 py-4 whitespace-nowrap">
                                                {rate ? (
                                                    isEditingRate ? (
                                                        <div className="space-y-2">
                                                            <input
                                                                type="number"
                                                                step="0.0001"
                                                                value={newRate}
                                                                onChange={(e) => setNewRate(e.target.value)}
                                                                placeholder="New rate"
                                                                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                                                            />
                                                            <textarea
                                                                value={notes}
                                                                onChange={(e) => setNotes(e.target.value)}
                                                                placeholder="Update notes (optional)"
                                                                className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
                                                                rows="2"
                                                            />
                                                            <div className="flex gap-2">
                                                                <button
                                                                    onClick={() => handleUpdateRate(currency.id)}
                                                                    className="bg-green-500 hover:bg-green-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1"
                                                                    disabled={loading}
                                                                >
                                                                    <FaSave />
                                                                    Save
                                                                </button>
                                                                <button
                                                                    onClick={() => {
                                                                        setEditingRate(null);
                                                                        setNewRate('');
                                                                        setNotes('');
                                                                    }}
                                                                    className="bg-gray-500 hover:bg-gray-600 text-white px-2 py-1 rounded text-xs flex items-center gap-1"
                                                                >
                                                                    <FaTimes />
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                1 FC = {currency.currency_symbol}{rate.rate_to_fancoin}
                                                            </div>
                                                            <button
                                                                onClick={() => {
                                                                    setEditingRate(currency.id);
                                                                    setNewRate(rate.rate_to_fancoin.toString());
                                                                }}
                                                                className="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1"
                                                            >
                                                                <FaEdit />
                                                                Update
                                                            </button>
                                                        </div>
                                                    )
                                                ) : (
                                                    <div>
                                                        <span className="text-sm text-gray-500">No rate set</span>
                                                        <button
                                                            onClick={() => {
                                                                setEditingRate(currency.id);
                                                                setNewRate('');
                                                            }}
                                                            className="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 mt-1"
                                                        >
                                                            <FaPlus />
                                                            Set Rate
                                                        </button>
                                                    </div>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                                    currency.is_active
                                                        ? 'bg-green-100 text-green-800'
                                                        : 'bg-red-100 text-red-800'
                                                }`}>
                                                    {currency.is_active ? 'Active' : 'Inactive'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {rate ? formatDate(rate.updated_at) : 'Never'}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <button
                                                    onClick={() => handleToggleCurrency(currency.id, currency.is_active)}
                                                    className={`inline-flex items-center gap-1 px-3 py-1 rounded text-xs font-medium ${
                                                        currency.is_active
                                                            ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                                                    }`}
                                                    disabled={loading}
                                                >
                                                    {currency.is_active ? <FaToggleOff /> : <FaToggleOn />}
                                                    {currency.is_active ? 'Deactivate' : 'Activate'}
                                                </button>
                                            </td>
                                        </tr>
                                    );
                                })}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* Add Currency Modal */}
            {showAddCurrency && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Add New Currency</h3>

                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Currency Code (e.g., EUR, GBP)
                                    </label>
                                    <input
                                        type="text"
                                        name="currency_code"
                                        value={newCurrency.currency_code}
                                        onChange={handleInputChange}
                                        placeholder="USD"
                                        maxLength="3"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Currency Name
                                    </label>
                                    <input
                                        type="text"
                                        name="currency_name"
                                        value={newCurrency.currency_name}
                                        onChange={handleInputChange}
                                        placeholder="US Dollar"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Currency Symbol
                                    </label>
                                    <input
                                        type="text"
                                        name="currency_symbol"
                                        value={newCurrency.currency_symbol}
                                        onChange={handleInputChange}
                                        placeholder="$"
                                        maxLength="5"
                                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    />
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="is_active"
                                        checked={newCurrency.is_active}
                                        onChange={(e) => setNewCurrency({...newCurrency, is_active: e.target.checked})}
                                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                    />
                                    <label className="ml-2 block text-sm text-gray-900">
                                        Active
                                    </label>
                                </div>
                            </div>

                            <div className="flex justify-end gap-3 mt-6">
                                <button
                                    onClick={() => setShowAddCurrency(false)}
                                    className="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg"
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={handleAddCurrency}
                                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                                    disabled={loading}
                                >
                                    <FaPlus />
                                    Add Currency
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

export default CurrencyManagement;
