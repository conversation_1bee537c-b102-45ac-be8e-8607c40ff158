{"ast": null, "code": "var _jsxFileName = \"C:\\\\MAMP\\\\htdocs\\\\FanBet247\\\\frontend\\\\src\\\\pages\\\\UserRegistration.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst API_BASE_URL = '/backend';\nfunction UserRegistration() {\n  _s();\n  const [teams, setTeams] = useState([]);\n  const [newUser, setNewUser] = useState({\n    username: '',\n    full_name: '',\n    email: '',\n    password: '',\n    favorite_team: '',\n    preferred_currency_id: 1 // Default to USD\n  });\n  const [error, setError] = useState('');\n  const [success, setSuccess] = useState('');\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n\n  // Currency system integration\n  const {\n    currencies,\n    loading: currenciesLoading,\n    error: currencyError\n  } = useCurrency();\n  const {\n    execute\n  } = useApiService();\n  useEffect(() => {\n    fetchTeams();\n  }, []);\n  const fetchTeams = async () => {\n    try {\n      const response = await execute(() => userService.get('team_management.php'));\n      if (response.success) {\n        setTeams(response.data.data || []);\n      } else {\n        setError('Failed to fetch teams');\n      }\n    } catch (err) {\n      setError('Failed to fetch teams');\n    }\n  };\n\n  // Handle currency error from context\n  useEffect(() => {\n    if (currencyError) {\n      setError('Failed to load currencies. Please refresh the page.');\n    }\n  }, [currencyError]);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setNewUser(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setError('');\n    setSuccess('');\n    setLoading(true);\n    try {\n      const response = await execute(() => userService.post('user_registration.php', newUser));\n      if (response.success) {\n        const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n        setSuccess(`Registration successful! Your preferred currency is set to ${(selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.display_name) || (selectedCurrency === null || selectedCurrency === void 0 ? void 0 : selectedCurrency.currency_code) || 'USD'}. Redirecting to login...`);\n        setTimeout(() => navigate('/login'), 3000);\n      } else {\n        setError(response.message || 'Registration failed');\n      }\n    } catch (err) {\n      console.error('Registration error:', err);\n      setError(err.message || 'An error occurred during registration');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(UserAuthLayout, {\n    title: \"Create Account\",\n    subtitle: \"Join the FanBet247 community today\",\n    variant: \"registration\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      onSubmit: handleSubmit,\n      className: \"user-auth-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          children: \"Username\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"username\",\n            type: \"text\",\n            name: \"username\",\n            value: newUser.username,\n            onChange: handleInputChange,\n            placeholder: \"Choose a unique username\",\n            required: true,\n            minLength: \"3\",\n            maxLength: \"20\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-user\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"full_name\",\n          children: \"Full Name\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"full_name\",\n            type: \"text\",\n            name: \"full_name\",\n            value: newUser.full_name,\n            onChange: handleInputChange,\n            placeholder: \"Enter your full name\",\n            required: true,\n            minLength: \"2\",\n            maxLength: \"50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-id-card\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email Address\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"email\",\n            type: \"email\",\n            name: \"email\",\n            value: newUser.email,\n            onChange: handleInputChange,\n            placeholder: \"Enter your email address\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-envelope\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"password\",\n          children: \"Password\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            id: \"password\",\n            type: \"password\",\n            name: \"password\",\n            value: newUser.password,\n            onChange: handleInputChange,\n            placeholder: \"Create a strong password\",\n            required: true,\n            minLength: \"6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-lock\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: \"Password must be at least 6 characters long\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"favorite_team\",\n          children: \"Favorite Team\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"favorite_team\",\n            name: \"favorite_team\",\n            value: newUser.favorite_team,\n            onChange: handleInputChange,\n            required: true,\n            children: [/*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Select your favorite team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 33\n            }, this), teams.map(team => /*#__PURE__*/_jsxDEV(\"option\", {\n              value: team.name,\n              children: team.name\n            }, team.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 37\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-futbol\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 21\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"preferred_currency_id\",\n          children: \"Preferred Currency\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-auth-input-wrapper\",\n          children: [/*#__PURE__*/_jsxDEV(\"select\", {\n            id: \"preferred_currency_id\",\n            name: \"preferred_currency_id\",\n            value: newUser.preferred_currency_id,\n            onChange: handleInputChange,\n            required: true,\n            disabled: currenciesLoading,\n            children: currenciesLoading ? /*#__PURE__*/_jsxDEV(\"option\", {\n              value: \"\",\n              children: \"Loading currencies...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 37\n            }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"1\",\n                children: \"USD - US Dollar (Default)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 41\n              }, this), currencies.map(currency => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: currency.id,\n                children: currency.display_name\n              }, currency.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 45\n              }, this))]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"fas fa-coins\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"small\", {\n          className: \"form-help-text\",\n          children: \"This will be used to display FanCoin amounts in your local currency. You can change this later in your profile settings.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 21\n      }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 27\n      }, this), success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-success-message\",\n        children: success\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 29\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"user-auth-button\",\n        disabled: loading || currenciesLoading,\n        children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-auth-loading-spinner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 29\n          }, this), \"Creating Account...\"]\n        }, void 0, true) : 'Create Account'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 223,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-auth-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Already have an account? \", /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"user-auth-link\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 49\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 91,\n    columnNumber: 9\n  }, this);\n}\n_s(UserRegistration, \"jtCJRMM+VBnEuT/9rkETwHUrbv4=\", false, function () {\n  return [useNavigate, useCurrency, useApiService];\n});\n_c = UserRegistration;\nexport default UserRegistration;\nvar _c;\n$RefreshReg$(_c, \"UserRegistration\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "Link", "useCurrency", "UserAuthLayout", "userService", "currencyService", "useApiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "API_BASE_URL", "UserRegistration", "_s", "teams", "setTeams", "newUser", "setNewUser", "username", "full_name", "email", "password", "favorite_team", "preferred_currency_id", "error", "setError", "success", "setSuccess", "loading", "setLoading", "navigate", "currencies", "currenciesLoading", "currencyError", "execute", "fetchTeams", "response", "get", "data", "err", "handleInputChange", "e", "name", "value", "target", "prev", "handleSubmit", "preventDefault", "post", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "c", "id", "parseInt", "display_name", "currency_code", "setTimeout", "message", "console", "title", "subtitle", "variant", "children", "onSubmit", "className", "htmlFor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "onChange", "placeholder", "required", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "map", "team", "disabled", "currency", "to", "_c", "$RefreshReg$"], "sources": ["C:/MAMP/htdocs/FanBet247/frontend/src/pages/UserRegistration.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate, Link } from 'react-router-dom';\nimport { useCurrency } from '../contexts/CurrencyContext';\nimport UserAuthLayout from '../components/UserAuthLayout';\nimport { userService, currencyService } from '../services';\nimport useApiService from '../hooks/useApiService';\nimport '../styles/UserAuth.css';\nimport '../styles/AuthAnimations.css';\n\nconst API_BASE_URL = '/backend';\n\nfunction UserRegistration() {\n    const [teams, setTeams] = useState([]);\n    const [newUser, setNewUser] = useState({\n        username: '',\n        full_name: '',\n        email: '',\n        password: '',\n        favorite_team: '',\n        preferred_currency_id: 1 // Default to USD\n    });\n    const [error, setError] = useState('');\n    const [success, setSuccess] = useState('');\n    const [loading, setLoading] = useState(false);\n    const navigate = useNavigate();\n\n    // Currency system integration\n    const { currencies, loading: currenciesLoading, error: currencyError } = useCurrency();\n    const { execute } = useApiService();\n\n    useEffect(() => {\n        fetchTeams();\n    }, []);\n\n    const fetchTeams = async () => {\n        try {\n            const response = await execute(() =>\n                userService.get('team_management.php')\n            );\n            if (response.success) {\n                setTeams(response.data.data || []);\n            } else {\n                setError('Failed to fetch teams');\n            }\n        } catch (err) {\n            setError('Failed to fetch teams');\n        }\n    };\n\n    // Handle currency error from context\n    useEffect(() => {\n        if (currencyError) {\n            setError('Failed to load currencies. Please refresh the page.');\n        }\n    }, [currencyError]);\n\n    const handleInputChange = (e) => {\n        const { name, value } = e.target;\n        setNewUser(prev => ({ ...prev, [name]: value }));\n    };\n\n    const handleSubmit = async (e) => {\n        e.preventDefault();\n        setError('');\n        setSuccess('');\n        setLoading(true);\n\n        try {\n            const response = await execute(() =>\n                userService.post('user_registration.php', newUser)\n            );\n\n            if (response.success) {\n                const selectedCurrency = currencies.find(c => c.id === parseInt(newUser.preferred_currency_id));\n                setSuccess(\n                    `Registration successful! Your preferred currency is set to ${selectedCurrency?.display_name || selectedCurrency?.currency_code || 'USD'}. Redirecting to login...`\n                );\n                setTimeout(() => navigate('/login'), 3000);\n            } else {\n                setError(response.message || 'Registration failed');\n            }\n        } catch (err) {\n            console.error('Registration error:', err);\n            setError(err.message || 'An error occurred during registration');\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    return (\n        <UserAuthLayout\n            title=\"Create Account\"\n            subtitle=\"Join the FanBet247 community today\"\n            variant=\"registration\"\n        >\n            <form onSubmit={handleSubmit} className=\"user-auth-form\">\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"username\">Username</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"username\"\n                                type=\"text\"\n                                name=\"username\"\n                                value={newUser.username}\n                                onChange={handleInputChange}\n                                placeholder=\"Choose a unique username\"\n                                required\n                                minLength=\"3\"\n                                maxLength=\"20\"\n                            />\n                            <i className=\"fas fa-user\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"full_name\">Full Name</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"full_name\"\n                                type=\"text\"\n                                name=\"full_name\"\n                                value={newUser.full_name}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your full name\"\n                                required\n                                minLength=\"2\"\n                                maxLength=\"50\"\n                            />\n                            <i className=\"fas fa-id-card\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"email\">Email Address</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"email\"\n                                type=\"email\"\n                                name=\"email\"\n                                value={newUser.email}\n                                onChange={handleInputChange}\n                                placeholder=\"Enter your email address\"\n                                required\n                            />\n                            <i className=\"fas fa-envelope\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"password\">Password</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <input\n                                id=\"password\"\n                                type=\"password\"\n                                name=\"password\"\n                                value={newUser.password}\n                                onChange={handleInputChange}\n                                placeholder=\"Create a strong password\"\n                                required\n                                minLength=\"6\"\n                            />\n                            <i className=\"fas fa-lock\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            Password must be at least 6 characters long\n                        </small>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"favorite_team\">Favorite Team</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"favorite_team\"\n                                name=\"favorite_team\"\n                                value={newUser.favorite_team}\n                                onChange={handleInputChange}\n                                required\n                            >\n                                <option value=\"\">Select your favorite team</option>\n                                {teams.map(team => (\n                                    <option key={team.id} value={team.name}>{team.name}</option>\n                                ))}\n                            </select>\n                            <i className=\"fas fa-futbol\"></i>\n                        </div>\n                    </div>\n\n                    <div className=\"user-auth-form-group\">\n                        <label htmlFor=\"preferred_currency_id\">Preferred Currency</label>\n                        <div className=\"user-auth-input-wrapper\">\n                            <select\n                                id=\"preferred_currency_id\"\n                                name=\"preferred_currency_id\"\n                                value={newUser.preferred_currency_id}\n                                onChange={handleInputChange}\n                                required\n                                disabled={currenciesLoading}\n                            >\n                                {currenciesLoading ? (\n                                    <option value=\"\">Loading currencies...</option>\n                                ) : (\n                                    <>\n                                        <option value=\"1\">USD - US Dollar (Default)</option>\n                                        {currencies.map(currency => (\n                                            <option key={currency.id} value={currency.id}>\n                                                {currency.display_name}\n                                            </option>\n                                        ))}\n                                    </>\n                                )}\n                            </select>\n                            <i className=\"fas fa-coins\"></i>\n                        </div>\n                        <small className=\"form-help-text\">\n                            This will be used to display FanCoin amounts in your local currency.\n                            You can change this later in your profile settings.\n                        </small>\n                    </div>\n\n                {error && <div className=\"user-auth-error-message\">{error}</div>}\n                {success && <div className=\"user-auth-success-message\">{success}</div>}\n\n                <button\n                    type=\"submit\"\n                    className=\"user-auth-button\"\n                    disabled={loading || currenciesLoading}\n                >\n                    {loading ? (\n                        <>\n                            <span className=\"user-auth-loading-spinner\"></span>\n                            Creating Account...\n                        </>\n                    ) : (\n                        'Create Account'\n                    )}\n                </button>\n\n                <div className=\"user-auth-footer\">\n                    <p>Already have an account? <Link to=\"/login\" className=\"user-auth-link\">Sign in here</Link></p>\n                </div>\n            </form>\n        </UserAuthLayout>\n    );\n}\n\nexport default UserRegistration;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACpD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,OAAOC,cAAc,MAAM,8BAA8B;AACzD,SAASC,WAAW,EAAEC,eAAe,QAAQ,aAAa;AAC1D,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAO,wBAAwB;AAC/B,OAAO,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtC,MAAMC,YAAY,GAAG,UAAU;AAE/B,SAASC,gBAAgBA,CAAA,EAAG;EAAAC,EAAA;EACxB,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC;IACnCoB,QAAQ,EAAE,EAAE;IACZC,SAAS,EAAE,EAAE;IACbC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,aAAa,EAAE,EAAE;IACjBC,qBAAqB,EAAE,CAAC,CAAC;EAC7B,CAAC,CAAC;EACF,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC4B,OAAO,EAAEC,UAAU,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC8B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMgC,QAAQ,GAAG9B,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAE+B,UAAU;IAAEH,OAAO,EAAEI,iBAAiB;IAAER,KAAK,EAAES;EAAc,CAAC,GAAG/B,WAAW,CAAC,CAAC;EACtF,MAAM;IAAEgC;EAAQ,CAAC,GAAG5B,aAAa,CAAC,CAAC;EAEnCP,SAAS,CAAC,MAAM;IACZoC,UAAU,CAAC,CAAC;EAChB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC3B,IAAI;MACA,MAAMC,QAAQ,GAAG,MAAMF,OAAO,CAAC,MAC3B9B,WAAW,CAACiC,GAAG,CAAC,qBAAqB,CACzC,CAAC;MACD,IAAID,QAAQ,CAACV,OAAO,EAAE;QAClBX,QAAQ,CAACqB,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MACtC,CAAC,MAAM;QACHb,QAAQ,CAAC,uBAAuB,CAAC;MACrC;IACJ,CAAC,CAAC,OAAOc,GAAG,EAAE;MACVd,QAAQ,CAAC,uBAAuB,CAAC;IACrC;EACJ,CAAC;;EAED;EACA1B,SAAS,CAAC,MAAM;IACZ,IAAIkC,aAAa,EAAE;MACfR,QAAQ,CAAC,qDAAqD,CAAC;IACnE;EACJ,CAAC,EAAE,CAACQ,aAAa,CAAC,CAAC;EAEnB,MAAMO,iBAAiB,GAAIC,CAAC,IAAK;IAC7B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChC3B,UAAU,CAAC4B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACH,IAAI,GAAGC;IAAM,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMG,YAAY,GAAG,MAAOL,CAAC,IAAK;IAC9BA,CAAC,CAACM,cAAc,CAAC,CAAC;IAClBtB,QAAQ,CAAC,EAAE,CAAC;IACZE,UAAU,CAAC,EAAE,CAAC;IACdE,UAAU,CAAC,IAAI,CAAC;IAEhB,IAAI;MACA,MAAMO,QAAQ,GAAG,MAAMF,OAAO,CAAC,MAC3B9B,WAAW,CAAC4C,IAAI,CAAC,uBAAuB,EAAEhC,OAAO,CACrD,CAAC;MAED,IAAIoB,QAAQ,CAACV,OAAO,EAAE;QAClB,MAAMuB,gBAAgB,GAAGlB,UAAU,CAACmB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKC,QAAQ,CAACrC,OAAO,CAACO,qBAAqB,CAAC,CAAC;QAC/FI,UAAU,CACN,8DAA8D,CAAAsB,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEK,YAAY,MAAIL,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAEM,aAAa,KAAI,KAAK,2BAC5I,CAAC;QACDC,UAAU,CAAC,MAAM1B,QAAQ,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;MAC9C,CAAC,MAAM;QACHL,QAAQ,CAACW,QAAQ,CAACqB,OAAO,IAAI,qBAAqB,CAAC;MACvD;IACJ,CAAC,CAAC,OAAOlB,GAAG,EAAE;MACVmB,OAAO,CAAClC,KAAK,CAAC,qBAAqB,EAAEe,GAAG,CAAC;MACzCd,QAAQ,CAACc,GAAG,CAACkB,OAAO,IAAI,uCAAuC,CAAC;IACpE,CAAC,SAAS;MACN5B,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;EAED,oBACIrB,OAAA,CAACL,cAAc;IACXwD,KAAK,EAAC,gBAAgB;IACtBC,QAAQ,EAAC,oCAAoC;IAC7CC,OAAO,EAAC,cAAc;IAAAC,QAAA,eAEtBtD,OAAA;MAAMuD,QAAQ,EAAEjB,YAAa;MAACkB,SAAS,EAAC,gBAAgB;MAAAF,QAAA,gBAChDtD,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,UAAU;YACbkB,IAAI,EAAC,MAAM;YACX5B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE3B,OAAO,CAACE,QAAS;YACxBqD,QAAQ,EAAE/B,iBAAkB;YAC5BgC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF7D,OAAA;YAAGwD,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,WAAW;UAAAH,QAAA,EAAC;QAAS;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5C7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,WAAW;YACdkB,IAAI,EAAC,MAAM;YACX5B,IAAI,EAAC,WAAW;YAChBC,KAAK,EAAE3B,OAAO,CAACG,SAAU;YACzBoD,QAAQ,EAAE/B,iBAAkB;YAC5BgC,WAAW,EAAC,sBAAsB;YAClCC,QAAQ;YACRC,SAAS,EAAC,GAAG;YACbC,SAAS,EAAC;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eACF7D,OAAA;YAAGwD,SAAS,EAAC;UAAgB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,OAAO;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC5C7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,OAAO;YACVkB,IAAI,EAAC,OAAO;YACZ5B,IAAI,EAAC,OAAO;YACZC,KAAK,EAAE3B,OAAO,CAACI,KAAM;YACrBmD,QAAQ,EAAE/B,iBAAkB;YAC5BgC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,eACF7D,OAAA;YAAGwD,SAAS,EAAC;UAAiB;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,UAAU;UAAAH,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1C7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,UAAU;YACbkB,IAAI,EAAC,UAAU;YACf5B,IAAI,EAAC,UAAU;YACfC,KAAK,EAAE3B,OAAO,CAACK,QAAS;YACxBkD,QAAQ,EAAE/B,iBAAkB;YAC5BgC,WAAW,EAAC,0BAA0B;YACtCC,QAAQ;YACRC,SAAS,EAAC;UAAG;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eACF7D,OAAA;YAAGwD,SAAS,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACN7D,OAAA;UAAOwD,SAAS,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAElC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,eAAe;UAAAH,QAAA,EAAC;QAAa;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACpD7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,eAAe;YAClBV,IAAI,EAAC,eAAe;YACpBC,KAAK,EAAE3B,OAAO,CAACM,aAAc;YAC7BiD,QAAQ,EAAE/B,iBAAkB;YAC5BiC,QAAQ;YAAAX,QAAA,gBAERtD,OAAA;cAAQmC,KAAK,EAAC,EAAE;cAAAmB,QAAA,EAAC;YAAyB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,EAClDvD,KAAK,CAAC8D,GAAG,CAACC,IAAI,iBACXrE,OAAA;cAAsBmC,KAAK,EAAEkC,IAAI,CAACnC,IAAK;cAAAoB,QAAA,EAAEe,IAAI,CAACnC;YAAI,GAArCmC,IAAI,CAACzB,EAAE;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAuC,CAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACT7D,OAAA;YAAGwD,SAAS,EAAC;UAAe;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEN7D,OAAA;QAAKwD,SAAS,EAAC,sBAAsB;QAAAF,QAAA,gBACjCtD,OAAA;UAAOyD,OAAO,EAAC,uBAAuB;UAAAH,QAAA,EAAC;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACjE7D,OAAA;UAAKwD,SAAS,EAAC,yBAAyB;UAAAF,QAAA,gBACpCtD,OAAA;YACI4C,EAAE,EAAC,uBAAuB;YAC1BV,IAAI,EAAC,uBAAuB;YAC5BC,KAAK,EAAE3B,OAAO,CAACO,qBAAsB;YACrCgD,QAAQ,EAAE/B,iBAAkB;YAC5BiC,QAAQ;YACRK,QAAQ,EAAE9C,iBAAkB;YAAA8B,QAAA,EAE3B9B,iBAAiB,gBACdxB,OAAA;cAAQmC,KAAK,EAAC,EAAE;cAAAmB,QAAA,EAAC;YAAqB;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,gBAE/C7D,OAAA,CAAAE,SAAA;cAAAoD,QAAA,gBACItD,OAAA;gBAAQmC,KAAK,EAAC,GAAG;gBAAAmB,QAAA,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACnDtC,UAAU,CAAC6C,GAAG,CAACG,QAAQ,iBACpBvE,OAAA;gBAA0BmC,KAAK,EAAEoC,QAAQ,CAAC3B,EAAG;gBAAAU,QAAA,EACxCiB,QAAQ,CAACzB;cAAY,GADbyB,QAAQ,CAAC3B,EAAE;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEhB,CACX,CAAC;YAAA,eACJ;UACL;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eACT7D,OAAA;YAAGwD,SAAS,EAAC;UAAc;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACN7D,OAAA;UAAOwD,SAAS,EAAC,gBAAgB;UAAAF,QAAA,EAAC;QAGlC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAET7C,KAAK,iBAAIhB,OAAA;QAAKwD,SAAS,EAAC,yBAAyB;QAAAF,QAAA,EAAEtC;MAAK;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,EAC/D3C,OAAO,iBAAIlB,OAAA;QAAKwD,SAAS,EAAC,2BAA2B;QAAAF,QAAA,EAAEpC;MAAO;QAAAwC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAEtE7D,OAAA;QACI8D,IAAI,EAAC,QAAQ;QACbN,SAAS,EAAC,kBAAkB;QAC5Bc,QAAQ,EAAElD,OAAO,IAAII,iBAAkB;QAAA8B,QAAA,EAEtClC,OAAO,gBACJpB,OAAA,CAAAE,SAAA;UAAAoD,QAAA,gBACItD,OAAA;YAAMwD,SAAS,EAAC;UAA2B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,uBAEvD;QAAA,eAAE,CAAC,GAEH;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAET7D,OAAA;QAAKwD,SAAS,EAAC,kBAAkB;QAAAF,QAAA,eAC7BtD,OAAA;UAAAsD,QAAA,GAAG,2BAAyB,eAAAtD,OAAA,CAACP,IAAI;YAAC+E,EAAE,EAAC,QAAQ;YAAChB,SAAS,EAAC,gBAAgB;YAAAF,QAAA,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEzB;AAACxD,EAAA,CAxOQD,gBAAgB;EAAA,QAaJZ,WAAW,EAG6CE,WAAW,EAChEI,aAAa;AAAA;AAAA2E,EAAA,GAjB5BrE,gBAAgB;AA0OzB,eAAeA,gBAAgB;AAAC,IAAAqE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}