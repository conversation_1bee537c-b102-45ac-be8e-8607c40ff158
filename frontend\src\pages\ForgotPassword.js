import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate, Link } from 'react-router-dom';
import UserAuthLayout from '../components/UserAuthLayout';
import '../styles/UserAuth.css';
import '../styles/AuthAnimations.css';

const API_BASE_URL = '/backend';

function ForgotPassword() {
    const [step, setStep] = useState('email'); // 'email', 'otp', 'reset'
    const [email, setEmail] = useState('');
    const [otpCode, setOtpCode] = useState('');
    const [newPassword, setNewPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    const [success, setSuccess] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [otpCountdown, setOtpCountdown] = useState(0);
    const [otpResending, setOtpResending] = useState(false);
    const navigate = useNavigate();

    // OTP countdown timer
    useEffect(() => {
        let timer;
        if (otpCountdown > 0) {
            timer = setTimeout(() => setOtpCountdown(otpCountdown - 1), 1000);
        }
        return () => clearTimeout(timer);
    }, [otpCountdown]);

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
    };

    const handleEmailSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post(
                `${API_BASE_URL}/handlers/send_otp.php`,
                { email }
            );

            if (response.data.success) {
                setStep('otp');
                setOtpCountdown(response.data.expiresIn);
                setSuccess('Password reset code sent to your email. Please check your inbox.');
            } else {
                setError(response.data.message || 'Failed to send reset code. Please try again.');
            }
        } catch (err) {
            console.error('Send OTP error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else if (err.response?.status === 404) {
                setError('No account found with this email address.');
            } else {
                setError('An error occurred. Please try again later.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleOtpSubmit = async (e) => {
        e.preventDefault();
        setError('');
        setIsLoading(true);

        try {
            const response = await axios.post(
                `${API_BASE_URL}/handlers/verify_otp.php`,
                {
                    email: email,
                    otp: otpCode
                }
            );

            if (response.data.success) {
                setStep('reset');
                setSuccess('Code verified! Please enter your new password.');
            } else {
                setError(response.data.message || 'Invalid or expired code. Please try again.');
            }
        } catch (err) {
            console.error('OTP verification error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred during verification. Please try again.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handlePasswordReset = async (e) => {
        e.preventDefault();
        setError('');

        if (newPassword !== confirmPassword) {
            setError('Passwords do not match.');
            return;
        }

        if (newPassword.length < 6) {
            setError('Password must be at least 6 characters long.');
            return;
        }

        setIsLoading(true);

        try {
            const response = await axios.post(
                `${API_BASE_URL}/handlers/reset_password.php`,
                {
                    email: email,
                    new_password: newPassword
                }
            );

            if (response.data.success) {
                setSuccess('Password reset successfully! Redirecting to login...');
                setTimeout(() => navigate('/login'), 3000);
            } else {
                setError(response.data.message || 'Failed to reset password. Please try again.');
            }
        } catch (err) {
            console.error('Password reset error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred. Please try again later.');
            }
        } finally {
            setIsLoading(false);
        }
    };

    const handleResendOtp = async () => {
        setError('');
        setOtpResending(true);

        try {
            const response = await axios.post(
                `${API_BASE_URL}/handlers/send_otp.php`,
                { email }
            );

            if (response.data.success) {
                setOtpCountdown(response.data.expiresIn);
                setSuccess('New code sent to your email.');
            } else {
                setError(response.data.message || 'Failed to resend code. Please try again.');
            }
        } catch (err) {
            console.error('Resend OTP error:', err);
            if (err.response?.data?.message) {
                setError(err.response.data.message);
            } else {
                setError('An error occurred while resending code. Please try again.');
            }
        } finally {
            setOtpResending(false);
        }
    };

    // Render email input step
    if (step === 'email') {
        return (
            <UserAuthLayout
                title="Reset Password"
                subtitle="Enter your email address to receive a password reset code"
                variant="login"
            >
                {error && <div className="user-auth-error-message">{error}</div>}
                {success && <div className="user-auth-success-message">{success}</div>}
                <form onSubmit={handleEmailSubmit} className="user-auth-form">
                    <div className="user-auth-form-group">
                        <label htmlFor="email">Email Address</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                type="email"
                                id="email"
                                name="email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder="Enter your email address"
                                required
                            />
                            <i className="fas fa-envelope"></i>
                        </div>
                    </div>
                    <button type="submit" className="user-auth-button" disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <span className="user-auth-loading-spinner"></span>
                                Sending Code...
                            </>
                        ) : (
                            'Send Reset Code'
                        )}
                    </button>
                    <div className="user-auth-footer">
                        <p>Remember your password? <Link to="/login" className="user-auth-link">Back to Login</Link></p>
                    </div>
                </form>
            </UserAuthLayout>
        );
    }

    // Render OTP verification step
    if (step === 'otp') {
        return (
            <UserAuthLayout
                title="Verify Reset Code"
                subtitle="Enter the verification code sent to your email"
                variant="login"
            >
                {error && <div className="user-auth-error-message">{error}</div>}
                {success && <div className="user-auth-success-message">{success}</div>}
                <form onSubmit={handleOtpSubmit} className="user-auth-form">
                    <div className="user-auth-form-group">
                        <label htmlFor="otpCode">Verification Code</label>
                        <div className="user-auth-input-wrapper">
                            <input
                                type="text"
                                id="otpCode"
                                name="otpCode"
                                value={otpCode}
                                onChange={(e) => setOtpCode(e.target.value)}
                                placeholder="Enter verification code"
                                required
                                className="user-auth-otp-input"
                                maxLength="6"
                            />
                            <i className="fas fa-key"></i>
                        </div>
                    </div>
                    {otpCountdown > 0 && (
                        <div className="user-auth-countdown-timer">
                            Code expires in: {formatTime(otpCountdown)}
                        </div>
                    )}
                    <button type="submit" className="user-auth-button" disabled={isLoading}>
                        {isLoading ? (
                            <>
                                <span className="user-auth-loading-spinner"></span>
                                Verifying...
                            </>
                        ) : (
                            'Verify Code'
                        )}
                    </button>
                    <div className="user-auth-form-options center">
                        <button
                            type="button"
                            className="user-auth-resend-button"
                            onClick={handleResendOtp}
                            disabled={otpResending || otpCountdown > 0}
                        >
                            {otpResending ? (
                                <>
                                    <span className="user-auth-loading-spinner"></span>
                                    Resending...
                                </>
                            ) : (
                                'Resend Code'
                            )}
                        </button>
                    </div>
                    <div className="user-auth-footer">
                        <button
                            type="button"
                            className="user-auth-button-secondary"
                            onClick={() => {
                                setStep('email');
                                setOtpCode('');
                                setError('');
                                setSuccess('');
                            }}
                        >
                            Back to Email
                        </button>
                    </div>
                </form>
            </UserAuthLayout>
        );
    }

    // Render password reset step
    return (
        <UserAuthLayout
            title="Set New Password"
            subtitle="Enter your new password"
            variant="login"
        >
            {error && <div className="user-auth-error-message">{error}</div>}
            {success && <div className="user-auth-success-message">{success}</div>}
            <form onSubmit={handlePasswordReset} className="user-auth-form">
                <div className="user-auth-form-group">
                    <label htmlFor="newPassword">New Password</label>
                    <div className="user-auth-input-wrapper">
                        <input
                            type="password"
                            id="newPassword"
                            name="newPassword"
                            value={newPassword}
                            onChange={(e) => setNewPassword(e.target.value)}
                            placeholder="Enter new password"
                            required
                            minLength="6"
                        />
                        <i className="fas fa-lock"></i>
                    </div>
                </div>
                <div className="user-auth-form-group">
                    <label htmlFor="confirmPassword">Confirm Password</label>
                    <div className="user-auth-input-wrapper">
                        <input
                            type="password"
                            id="confirmPassword"
                            name="confirmPassword"
                            value={confirmPassword}
                            onChange={(e) => setConfirmPassword(e.target.value)}
                            placeholder="Confirm new password"
                            required
                            minLength="6"
                        />
                        <i className="fas fa-lock"></i>
                    </div>
                </div>
                <button type="submit" className="user-auth-button" disabled={isLoading}>
                    {isLoading ? (
                        <>
                            <span className="user-auth-loading-spinner"></span>
                            Resetting Password...
                        </>
                    ) : (
                        'Reset Password'
                    )}
                </button>
                <div className="user-auth-footer">
                    <p>Remember your password? <Link to="/login" className="user-auth-link">Back to Login</Link></p>
                </div>
            </form>
        </UserAuthLayout>
    );
}

export default ForgotPassword;
