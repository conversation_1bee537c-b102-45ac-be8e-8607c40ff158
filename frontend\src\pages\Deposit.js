import React, { useState, useEffect, useCallback } from 'react';
import { useCurrency } from '../contexts/CurrencyContext';
import { CurrencyInput, CurrencyAmount } from '../components/Currency';
import { userService } from '../services';
import useApiService from '../hooks/useApiService';
import './Deposit.css';

function Deposit() {
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('');
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [step, setStep] = useState(1);
  const [selectedMethod, setSelectedMethod] = useState(null);
  const [proofImage, setProofImage] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const { userCurrency, convertToUserCurrency, formatAmountForDisplay } = useCurrency();
  const { loading, execute } = useApiService();
  const userId = localStorage.getItem('userId');

  // Load payment methods
  const loadPaymentMethods = useCallback(async () => {
    try {
      const response = await execute(() =>
        userService.get('get_payment_methods.php')
      );

      if (response.success) {
        setPaymentMethods(response.data.payment_methods || []);
      }
    } catch (error) {
      console.error('Failed to load payment methods:', error);
    }
  }, [execute]);

  useEffect(() => {
    loadPaymentMethods();
  }, [loadPaymentMethods]);

  // Calculate FanCoin equivalent
  const calculateFanCoinAmount = useCallback((localAmount) => {
    if (!userCurrency || !localAmount) return 0;

    // Convert from user currency to FanCoin
    // If rate is 18 ZAR per FanCoin, then 1800 ZAR = 100 FanCoin
    const rate = userCurrency.rate_to_fancoin || 1;
    return parseFloat(localAmount) / rate;
  }, [userCurrency]);

  const handleAmountSubmit = (e) => {
    e.preventDefault();
    if (!amount || parseFloat(amount) <= 0) {
      setError('Please enter a valid amount');
      return;
    }
    setError('');
    setStep(2);
  };

  const handleMethodSelect = (method) => {
    setSelectedMethod(method);
    setStep(3);
  };

  const handleProofSubmit = async (e) => {
    e.preventDefault();

    if (!proofImage) {
      setError('Please upload payment proof');
      return;
    }

    try {
      setError('');
      const formData = new FormData();
      formData.append('amount', amount);
      formData.append('payment_method_id', selectedMethod.id);
      formData.append('proof_image', proofImage);
      formData.append('user_id', userId);
      formData.append('currency_id', userCurrency?.id || 1);
      formData.append('fancoin_amount', calculateFanCoinAmount(amount));

      const response = await execute(() =>
        userService.upload('credit_request.php', formData)
      );

      if (response.success) {
        setSuccess('Deposit request submitted successfully. Please wait for admin approval.');
        setStep(4);
      } else {
        throw new Error(response.message || 'Failed to submit deposit request');
      }
    } catch (err) {
      console.error('Error submitting deposit request:', err);
      setError(err.message || 'Failed to submit deposit request. Please try again.');
    }
  };

  const fanCoinAmount = calculateFanCoinAmount(amount);

  return (
    <div className="deposit-container">
      <h1>Deposit Funds</h1>
      <p className="deposit-subtitle">
        Add funds to your FanBet247 wallet in your preferred currency
      </p>

      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}

      {step === 1 && (
        <div className="step-container">
          <h2>Enter Amount</h2>
          <form onSubmit={handleAmountSubmit}>
            <div className="form-group">
              <label>
                Amount ({userCurrency?.currency_symbol || '$'})
              </label>
              <CurrencyInput
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder={`Enter amount in ${userCurrency?.currency_code || 'USD'}`}
                showConversion={false}
              />
              {amount && fanCoinAmount > 0 && (
                <div className="conversion-info">
                  <p>
                    You will receive: <CurrencyAmount amount={fanCoinAmount} showFanCoinOnly={true} />
                  </p>
                </div>
              )}
            </div>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              Next
            </button>
          </form>
        </div>
      )}

      {step === 2 && (
        <div className="step-container">
          <h2>Select Payment Method</h2>
          <div className="amount-summary">
            <p>Amount: {formatAmountForDisplay(fanCoinAmount, true)}</p>
          </div>

          {paymentMethods.length === 0 ? (
            <div className="no-methods">
              <p>No payment methods available. Please contact support.</p>
            </div>
          ) : (
            <div className="payment-methods-grid">
              {paymentMethods.map((method) => (
                <div
                  key={method.id}
                  className="payment-method-card"
                  onClick={() => handleMethodSelect(method)}
                >
                  <h3>{method.method_name}</h3>
                  <p>{method.description}</p>
                  <div className="method-details">
                    <p><strong>Account:</strong> {method.account_details}</p>
                    <p><strong>Instructions:</strong> {method.instructions}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          <button
            onClick={() => setStep(1)}
            className="btn btn-secondary"
          >
            Back
          </button>
        </div>
      )}

      {step === 3 && selectedMethod && (
        <div className="step-container">
          <h2>Upload Payment Proof</h2>
          <div className="payment-summary">
            <p><strong>Amount:</strong> {formatAmountForDisplay(fanCoinAmount, true)}</p>
            <p><strong>Method:</strong> {selectedMethod.method_name}</p>
            <p><strong>Account:</strong> {selectedMethod.account_details}</p>
          </div>

          <div className="instructions">
            <h3>Payment Instructions:</h3>
            <p>{selectedMethod.instructions}</p>
          </div>

          <form onSubmit={handleProofSubmit}>
            <div className="form-group">
              <label>Upload Payment Proof</label>
              <input
                type="file"
                accept="image/*"
                onChange={(e) => setProofImage(e.target.files[0])}
                required
              />
              <small>Upload a screenshot or photo of your payment confirmation</small>
            </div>

            <div className="form-actions">
              <button
                type="button"
                onClick={() => setStep(2)}
                className="btn btn-secondary"
              >
                Back
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? 'Submitting...' : 'Submit Deposit Request'}
              </button>
            </div>
          </form>
        </div>
      )}

      {step === 4 && (
        <div className="step-container success-step">
          <h2>Deposit Request Submitted</h2>
          <div className="success-content">
            <p>Your deposit request has been submitted successfully!</p>
            <p>Amount: {formatAmountForDisplay(fanCoinAmount, true)}</p>
            <p>An admin will review your payment proof and credit your account within 24 hours.</p>
          </div>
          <button
            onClick={() => {
              setStep(1);
              setAmount('');
              setSelectedMethod(null);
              setProofImage(null);
              setSuccess('');
            }}
            className="btn btn-primary"
          >
            Make Another Deposit
          </button>
        </div>
      )}
    </div>
  );
}

export default Deposit;
