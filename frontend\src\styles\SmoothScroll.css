/* Preserve smooth scrolling but remove global scrollbar hiding */
html {
  scroll-behavior: smooth;
}

/* Remove all global scrollbar hiding rules */
/* Admin sidebar keeps its specific scrollbar hiding */
.admin-sidebar-nav {
  scrollbar-width: none;
  scrollbar-color: transparent transparent;
  -ms-overflow-style: none;
}

.admin-sidebar-nav::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none;
}

.admin-sidebar-nav::-webkit-scrollbar-thumb {
  display: none;
}

.admin-sidebar-nav::-webkit-scrollbar-track {
  display: none;
}

/* Keep form element transitions */
input, select, textarea, button {
  transition: all 0.2s ease-in-out;
}

/* Keep focus states */
input:focus, select:focus, textarea:focus {
  box-shadow: 0 0 0 3px rgba(22, 101, 52, 0.2) !important;
}
