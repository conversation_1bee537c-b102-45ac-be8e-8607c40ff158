/**
 * Betting API Service
 * 
 * Handles all betting-related API calls with consistent error handling
 * and response formatting.
 */

import apiService from './apiService';

class BetService {
    /**
     * Get user bets with pagination
     * @param {number} userId - User ID
     * @param {number} page - Page number
     * @param {number} limit - Items per page
     * @param {string} search - Search term
     * @returns {Promise<ApiResponse>}
     */
    async getUserBets(userId, page = 1, limit = 10, search = '') {
        return await apiService.get('get_bets.php', {
            userId,
            page,
            limit,
            search
        });
    }

    /**
     * Get accepted bets for user
     * @param {number} userId - User ID
     * @param {number} page - Page number
     * @param {number} limit - Items per page
     * @returns {Promise<ApiResponse>}
     */
    async getAcceptedBets(userId, page = 1, limit = 10) {
        return await apiService.get('get_accepted_bets.php', {
            userId,
            page,
            limit
        });
    }

    /**
     * Get incoming bets for user
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getIncomingBets(userId) {
        return await apiService.get('get_bets.php', { userId });
    }

    /**
     * Create a new bet
     * @param {object} betData - Bet creation data
     * @returns {Promise<ApiResponse>}
     */
    async createBet(betData) {
        return await apiService.post('create_bet.php', betData);
    }

    /**
     * Accept a bet
     * @param {number} betId - Bet ID
     * @param {number} userId - User ID accepting the bet
     * @returns {Promise<ApiResponse>}
     */
    async acceptBet(betId, userId) {
        return await apiService.post('accept_bet.php', {
            bet_id: betId,
            user_id: userId
        });
    }

    /**
     * Reject a bet
     * @param {number} betId - Bet ID
     * @param {number} userId - User ID rejecting the bet
     * @returns {Promise<ApiResponse>}
     */
    async rejectBet(betId, userId) {
        return await apiService.post('reject_bet.php', {
            bet_id: betId,
            user_id: userId
        });
    }

    /**
     * Cancel a bet
     * @param {number} betId - Bet ID
     * @param {number} userId - User ID canceling the bet
     * @returns {Promise<ApiResponse>}
     */
    async cancelBet(betId, userId) {
        return await apiService.post('cancel_bet.php', {
            bet_id: betId,
            user_id: userId
        });
    }

    /**
     * Get bet details
     * @param {number} betId - Bet ID
     * @returns {Promise<ApiResponse>}
     */
    async getBetDetails(betId) {
        return await apiService.get('get_bet_details.php', { bet_id: betId });
    }

    /**
     * Get recent bets for welcome page
     * @param {number} limit - Number of bets to fetch
     * @returns {Promise<ApiResponse>}
     */
    async getRecentBets(limit = 5) {
        return await apiService.get('welcome_recent_bets.php', { limit });
    }

    /**
     * Get bet statistics for user
     * @param {number} userId - User ID
     * @returns {Promise<ApiResponse>}
     */
    async getBetStats(userId) {
        return await apiService.get('get_bet_stats.php', { user_id: userId });
    }

    /**
     * Settle a bet (Admin only)
     * @param {number} betId - Bet ID
     * @param {number} winnerId - Winner user ID
     * @param {string} notes - Settlement notes
     * @returns {Promise<ApiResponse>}
     */
    async settleBet(betId, winnerId, notes = '') {
        return await apiService.post('settle_bet.php', {
            bet_id: betId,
            winner_id: winnerId,
            notes
        });
    }

    /**
     * Get all bets for admin management
     * @param {object} filters - Filter options
     * @returns {Promise<ApiResponse>}
     */
    async getAllBets(filters = {}) {
        const params = new URLSearchParams({
            page: filters.page || 1,
            limit: filters.limit || 20,
            sortBy: filters.sortBy || 'created_at',
            order: filters.order || 'DESC',
            ...(filters.search && { search: filters.search }),
            ...(filters.status && { status: filters.status }),
            ...(filters.dateFrom && { dateFrom: filters.dateFrom }),
            ...(filters.dateTo && { dateTo: filters.dateTo })
        });

        return await apiService.get(`get_all_bets.php?${params.toString()}`);
    }

    /**
     * Update bet status (Admin only)
     * @param {number} betId - Bet ID
     * @param {string} status - New status
     * @param {string} reason - Reason for status change
     * @returns {Promise<ApiResponse>}
     */
    async updateBetStatus(betId, status, reason = '') {
        return await apiService.post('update_bet_status.php', {
            bet_id: betId,
            status,
            reason
        });
    }

    /**
     * Get bet history for user
     * @param {number} userId - User ID
     * @param {string} type - Bet type filter ('all', 'won', 'lost', 'pending')
     * @param {number} page - Page number
     * @param {number} limit - Items per page
     * @returns {Promise<ApiResponse>}
     */
    async getBetHistory(userId, type = 'all', page = 1, limit = 20) {
        return await apiService.get('get_bet_history.php', {
            user_id: userId,
            type,
            page,
            limit
        });
    }
}

// Create singleton instance
const betService = new BetService();

export default betService;
